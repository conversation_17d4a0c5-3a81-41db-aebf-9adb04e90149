group = 'de.adesso.fischereiregister'
version = project.version


dependencies {
    api project(':core')
    //noinspection DependencyNotationArgument
    api project(':core').sourceSets.test.output

    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'

    implementation platform('org.junit:junit-bom:5.10.0')
    implementation 'org.junit.jupiter:junit-jupiter'

    api 'org.assertj:assertj-core:3.26.3'
}