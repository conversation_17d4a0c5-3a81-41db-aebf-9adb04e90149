plugins {
    id 'java'
    id 'org.springframework.boot' version '3.4.6'
    id 'io.spring.dependency-management' version '1.1.6'
}

group = 'de.adesso.fischereiregister.message-service'
version = parent.project.version

dependencies {
    implementation project(':core') // TODO -ghe- hier die Abhängigkeit rausnehmen falls möglich

    implementation "org.keycloak:keycloak-spring-security-adapter:24.0.5"
    constraints {
        implementation('org.bouncycastle:bcprov-jdk18on:1.78') {
            because "CVE-2024-29857, CVE-2024-30171, CVE-2024-30172, CVE-2024-34447"
        }
    }
    implementation "org.springframework.boot:spring-boot-starter-oauth2-client:3.4.6"
    implementation("org.springframework.boot:spring-boot-starter-security:3.4.6")
    constraints {
        implementation("org.springframework.security:spring-security-web:6.2.7") {
            because "CVE-2024-38821"
        }
    }
    implementation "org.springframework.boot:spring-boot-starter-oauth2-resource-server:3.4.6"
    implementation 'org.mapstruct:mapstruct:1.5.5.Final'

    compileOnly 'org.projectlombok:lombok:1.18.32'
    annotationProcessor 'org.projectlombok:lombok:1.18.32'

    testImplementation platform('org.junit:junit-bom:5.10.0')
    testImplementation 'org.junit.jupiter:junit-jupiter'

    testImplementation 'org.mockito:mockito-core'
    testImplementation 'org.mockito:mockito-junit-jupiter'

    testImplementation project(':testutils')

    implementation 'org.springframework:spring-context-support:6.2.7'
    constraints {
        implementation('org.apache.ant:ant:1.9.16') {
            because "CVE-2020-1945, CVE-2021-36373"
        }
    }
    compileOnly 'org.projectlombok:lombok:1.18.36'
    annotationProcessor 'org.projectlombok:lombok:1.18.36'
    constraints {
        implementation('com.thoughtworks.xstream:xstream:1.4.21') {
            because "CVE-2024-47072"
        }
        implementation('com.google.protobuf:protobuf-java:3.25.5') {
            because "CVE-2024-7254"
        }
    }
    testImplementation 'org.hamcrest:hamcrest:2.2'
    implementation 'org.springframework.boot:spring-boot-starter-actuator:3.4.6'
    implementation 'org.springframework.boot:spring-boot-starter-webflux:3.4.6'
    testImplementation 'io.projectreactor:reactor-test:3.1.0.RELEASE'
}

test {
    useJUnitPlatform()
}

