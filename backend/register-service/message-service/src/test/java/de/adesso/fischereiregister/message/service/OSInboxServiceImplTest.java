package de.adesso.fischereiregister.message.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import de.adesso.fischereiregister.message.service.model.OSAttachement;
import de.adesso.fischereiregister.message.service.model.OSMessage;
import de.adesso.fischereiregister.message.service.model.OSMessageWithAttachments;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class OSInboxServiceImplTest {

    public static final String SUBJECT ="Fischereidokumente online: Ihr digitaler Fischereischein";
    public static final String DISPLAY_NAME = "DigiFischDok Fachverfahren und Register";

    private WebClient webClientMock;
    private WebClient.RequestBodyUriSpec requestBodyUriSpecMock;
    private WebClient.RequestHeadersSpec requestHeadersSpecMock;
    private WebClient.RequestBodySpec requestBodySpecMock;
    private WebClient.ResponseSpec responseSpecMock;

    private OSInboxServiceImpl osInboxService;

    @BeforeEach
    void setup() {
        webClientMock = mock(WebClient.class);
        requestBodyUriSpecMock = mock(WebClient.RequestBodyUriSpec.class);
        requestBodySpecMock = mock(WebClient.RequestBodySpec.class);
        requestHeadersSpecMock = mock(WebClient.RequestHeadersSpec.class);
        responseSpecMock = mock(WebClient.ResponseSpec.class);

        osInboxService = new OSInboxServiceImpl(webClientMock);
    }

    @Test
    void testSendMessage() {
        String inboxReference = "Inbox123";
        OSMessage message = new OSMessage(SUBJECT, DISPLAY_NAME);

        // Mocking WebClient behavior
        when(webClientMock.post()).thenReturn(requestBodyUriSpecMock);
        when(requestBodyUriSpecMock.uri(anyString())).thenReturn(requestBodySpecMock);
        when(requestBodySpecMock.contentType(MediaType.APPLICATION_JSON)).thenReturn(requestBodySpecMock);
        when(requestBodySpecMock.bodyValue(any(OSMessage.class))).thenReturn(requestHeadersSpecMock);
        when(requestHeadersSpecMock.retrieve()).thenReturn(responseSpecMock);

        // Mock the onStatus method to return the mocked ResponseSpec
        when(responseSpecMock.onStatus(any(), any())).thenReturn(responseSpecMock);
        when(responseSpecMock.bodyToMono(String.class)).thenReturn(Mono.just("OSMessage sent"));

        // Act
        osInboxService.sendMessage(inboxReference, message);

        // Verify interactions
        ArgumentCaptor<String> uriCaptor = ArgumentCaptor.forClass(String.class);
        verify(requestBodyUriSpecMock).uri(uriCaptor.capture());
        assertEquals("/api/osi_postfach/1.0.0/MessageExchange/v1/Send/Inbox123", uriCaptor.getValue());

        verify(requestBodySpecMock).contentType(MediaType.APPLICATION_JSON);
        verify(requestBodySpecMock).bodyValue(message);
        verify(responseSpecMock).bodyToMono(String.class);
    }


    @Test
    void testHandleError() {
        // Simulate a 500 Internal Server Error response
        ClientResponse clientResponseMock = mock(ClientResponse.class);
        when(clientResponseMock.statusCode()).thenReturn(HttpStatus.INTERNAL_SERVER_ERROR);
        when(clientResponseMock.bodyToMono(String.class)).thenReturn(Mono.just("Internal Server Error"));

        // Act & Assert
        StepVerifier.create(osInboxService.handleError(clientResponseMock))
                .expectErrorMatches(throwable -> throwable instanceof RuntimeException &&
                        throwable.getMessage().contains("Error: Internal Server Error"))
                .verify();
    }

    @Test
    void testSendMessageWithAttachment() throws JsonProcessingException {
        // Given
        String inboxReference = "Inbox123";
        OSMessageWithAttachments message = new OSMessageWithAttachments("Subject", DISPLAY_NAME,"Body");
        OSAttachement attachment = new OSAttachement("tax-payment-file.pdf", new byte[]{1, 2, 3, 4});

        // Mock WebClient behavior
        when(webClientMock.post()).thenReturn(requestBodyUriSpecMock);
        when(requestBodyUriSpecMock.uri(anyString())).thenReturn(requestBodySpecMock);
        when(requestBodySpecMock.contentType(MediaType.MULTIPART_FORM_DATA)).thenReturn(requestBodySpecMock);

        // Fix: Correctly mock body() to return requestHeadersSpecMock
        when(requestBodySpecMock.body(any(BodyInserters.FormInserter.class))).thenReturn(requestHeadersSpecMock);

        when(requestHeadersSpecMock.retrieve()).thenReturn(responseSpecMock);
        when(responseSpecMock.onStatus(any(), any())).thenReturn(responseSpecMock);
        when(responseSpecMock.bodyToMono(String.class)).thenReturn(Mono.just("OSMessage sent"));

        // Act
        osInboxService.sendMessageWithAttachments(inboxReference, message, List.of(attachment));

        // Verify interactions
        ArgumentCaptor<String> uriCaptor = ArgumentCaptor.forClass(String.class);
        verify(requestBodyUriSpecMock).uri(uriCaptor.capture());
        assertEquals("/api/osi_postfach/1.0.0/MessageExchange/v1/Send/MessageWithAttachments/Inbox123", uriCaptor.getValue());

        verify(requestBodySpecMock).contentType(MediaType.MULTIPART_FORM_DATA);

        // Fix: Verify correct interaction with the multipart request
        verify(requestBodySpecMock).body(any(BodyInserters.FormInserter.class));

        verify(responseSpecMock).bodyToMono(String.class);
    }


    @Test
    void testHandleErrorMessageWithAttachments() {
        // Simulate a 500 Internal Server Error response
        ClientResponse clientResponseMock = mock(ClientResponse.class);
        when(clientResponseMock.statusCode()).thenReturn(HttpStatus.INTERNAL_SERVER_ERROR);
        when(clientResponseMock.bodyToMono(String.class)).thenReturn(Mono.just("Internal Server Error"));

        // Act & Assert
        StepVerifier.create(osInboxService.handleError(clientResponseMock))
                .expectErrorMatches(throwable -> throwable instanceof RuntimeException &&
                        throwable.getMessage().contains("Error: Internal Server Error"))
                .verify();
    }
}