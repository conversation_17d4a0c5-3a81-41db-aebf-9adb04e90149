package de.adesso.fischereiregister.message.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import de.adesso.fischereiregister.message.service.model.OSAttachement;
import de.adesso.fischereiregister.message.service.model.OSMessage;
import de.adesso.fischereiregister.message.service.model.OSMessageWithAttachments;

import java.util.List;

public interface OSInboxService {

    void sendMessageWithAttachments(String inboxReference, OSMessageWithAttachments osMessageWithAttachments, List<OSAttachement> osAttachements) throws JsonProcessingException;

    void sendMessage(String inboxReference, OSMessage osMessage);
}
