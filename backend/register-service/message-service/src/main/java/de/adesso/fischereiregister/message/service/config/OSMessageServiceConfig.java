package de.adesso.fischereiregister.message.service.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.security.oauth2.client.AuthorizedClientServiceReactiveOAuth2AuthorizedClientManager;
import org.springframework.security.oauth2.client.InMemoryReactiveOAuth2AuthorizedClientService;
import org.springframework.security.oauth2.client.ReactiveOAuth2AuthorizedClientProvider;
import org.springframework.security.oauth2.client.ReactiveOAuth2AuthorizedClientProviderBuilder;
import org.springframework.security.oauth2.client.endpoint.OAuth2ClientCredentialsGrantRequest;
import org.springframework.security.oauth2.client.endpoint.ReactiveOAuth2AccessTokenResponseClient;
import org.springframework.security.oauth2.client.registration.ClientRegistration;
import org.springframework.security.oauth2.client.registration.InMemoryReactiveClientRegistrationRepository;
import org.springframework.security.oauth2.client.registration.ReactiveClientRegistrationRepository;
import org.springframework.security.oauth2.client.web.reactive.function.client.ServerOAuth2AuthorizedClientExchangeFilterFunction;
import org.springframework.security.oauth2.core.AuthorizationGrantType;
import org.springframework.security.oauth2.core.OAuth2AccessToken;
import org.springframework.security.oauth2.core.endpoint.OAuth2AccessTokenResponse;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.netty.http.client.HttpClient;
import reactor.netty.transport.ProxyProvider;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Configuration
public class OSMessageServiceConfig {

    @Value("${message-service.authorization-grant-type}")
    private String authorizationGrantType;

    @Value("${message-service.base-urn}")
    private String baseUrl;

    @Value("${message-service.resource}")
    private String resource;

    @Value("#{'${message-service.scope}'.split(',')}")
    private List<String> scopes;

    @Value("${message-service.client-secret}")
    String clientSecret;

    @Value("${message-service.client-id}")
    String clientId;

    @Value("${message-service.token-uri}")
    String tokenUri;

    @Bean(name = "messageService")
    ReactiveClientRegistrationRepository getRegistration() {
        ClientRegistration registration = ClientRegistration
                .withRegistrationId("messageService")
                .tokenUri(tokenUri)
                .clientId(clientId)
                .clientSecret(clientSecret)
                .scope(scopes)
                .authorizationGrantType(AuthorizationGrantType.CLIENT_CREDENTIALS)
                .build();
        return new InMemoryReactiveClientRegistrationRepository(registration);
    }

    @Bean(name = "messageServiceWebClient")
    public WebClient webClient(ReactiveClientRegistrationRepository clientRegistrations) {
        ReactiveOAuth2AccessTokenResponseClient<OAuth2ClientCredentialsGrantRequest> customTokenResponseClient = createCustomTokenResponseClient();
        AuthorizedClientServiceReactiveOAuth2AuthorizedClientManager authorizedClientManager = createAuthorizedClientManager(clientRegistrations, customTokenResponseClient);
        ServerOAuth2AuthorizedClientExchangeFilterFunction oauthFilter = createOAuthFilter(authorizedClientManager);

        return WebClient.builder()
                .clientConnector(new ReactorClientHttpConnector(createHttpClient()))
                .baseUrl(baseUrl)
                .filter(oauthFilter)
                .build();
    }

    private ReactiveOAuth2AccessTokenResponseClient<OAuth2ClientCredentialsGrantRequest> createCustomTokenResponseClient() {
        return grantRequest -> {
            ClientRegistration registration = grantRequest.getClientRegistration();
            MultiValueMap<String, String> formData = createFormData(registration);

            return WebClient.builder()
                    .clientConnector(new ReactorClientHttpConnector(createHttpClient()))
                    .defaultHeader("Content-Type", MediaType.APPLICATION_FORM_URLENCODED_VALUE)
                    .build()
                    .post()
                    .uri(registration.getProviderDetails().getTokenUri())
                    .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                    .body(BodyInserters.fromFormData(formData))
                    .retrieve()
                    .bodyToMono(Map.class)
                    .map(this::extractTokenResponse);
        };
    }

    private HttpClient createHttpClient() {
        String proxyHost = System.getProperty("http.proxyHost");
        String proxyPort = System.getProperty("http.proxyPort");
        String nonProxyHosts = System.getProperty("http.nonProxyHosts");

        HttpClient httpClient = HttpClient.create();

        if (proxyHost != null && proxyPort != null && nonProxyHosts != null) {
            httpClient = httpClient.proxy(proxy -> proxy
                    .type(ProxyProvider.Proxy.HTTP)
                    .host(proxyHost)
                    .port(Integer.parseInt(proxyPort))
                    .nonProxyHosts(nonProxyHosts
                            .replace("*", ".*")
                            .replace("|", "\\|")));
        }
        return httpClient;
    }

    private MultiValueMap<String, String> createFormData(ClientRegistration registration) {
        MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
        formData.add("grant_type", authorizationGrantType);
        formData.add("client_id", registration.getClientId());
        formData.add("client_secret", registration.getClientSecret());
        formData.add("resource", resource);
        formData.add("scope", String.join(" ", scopes));
        return formData;
    }

    private OAuth2AccessTokenResponse extractTokenResponse(Map<String, Object> tokenResponse) {
        String accessToken = (String) tokenResponse.get("access_token");
        Long expiresIn = Long.valueOf(tokenResponse.get("expires_in").toString());
        String scope = (String) tokenResponse.get("scope");

        return OAuth2AccessTokenResponse.withToken(accessToken)
                .tokenType(OAuth2AccessToken.TokenType.BEARER)
                .expiresIn(expiresIn)
                .scopes(Set.of(scope))
                .build();
    }

    private AuthorizedClientServiceReactiveOAuth2AuthorizedClientManager createAuthorizedClientManager(
            ReactiveClientRegistrationRepository clientRegistrations,
            ReactiveOAuth2AccessTokenResponseClient<OAuth2ClientCredentialsGrantRequest> tokenResponseClient
    ) {
        ReactiveOAuth2AuthorizedClientProvider authorizedClientProvider = ReactiveOAuth2AuthorizedClientProviderBuilder.builder()
                .clientCredentials(configurer -> configurer.accessTokenResponseClient(tokenResponseClient))
                .build();

        InMemoryReactiveOAuth2AuthorizedClientService clientService = new InMemoryReactiveOAuth2AuthorizedClientService(clientRegistrations);
        AuthorizedClientServiceReactiveOAuth2AuthorizedClientManager clientManager = new AuthorizedClientServiceReactiveOAuth2AuthorizedClientManager(clientRegistrations, clientService);
        clientManager.setAuthorizedClientProvider(authorizedClientProvider);
        return clientManager;
    }

    private ServerOAuth2AuthorizedClientExchangeFilterFunction createOAuthFilter(
            AuthorizedClientServiceReactiveOAuth2AuthorizedClientManager authorizedClientManager
    ) {
        ServerOAuth2AuthorizedClientExchangeFilterFunction oauthFilter = new ServerOAuth2AuthorizedClientExchangeFilterFunction(authorizedClientManager);
        oauthFilter.setDefaultClientRegistrationId("messageService");
        return oauthFilter;
    }
}
