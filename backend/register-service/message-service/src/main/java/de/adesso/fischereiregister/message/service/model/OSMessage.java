package de.adesso.fischereiregister.message.service.model;

import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

@Getter
@EqualsAndHashCode(callSuper = true)
public class OSMessage extends OSBaseMessage {
    private List<OSFile> osFiles = new ArrayList<>();
    private List<OSReference> osReferences = new ArrayList<>();

    public OSMessage(String subject, String displayName, String body) {
        super(subject, displayName, body);
    }

    public OSMessage(String subject, String displayName) {
        super(subject, displayName);
    }
}
