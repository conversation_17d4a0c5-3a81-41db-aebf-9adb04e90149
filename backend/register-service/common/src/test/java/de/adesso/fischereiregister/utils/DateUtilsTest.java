package de.adesso.fischereiregister.utils;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.time.format.DateTimeParseException;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;

class DateUtilsTest {

    @Test
    @DisplayName("DateUtils.parseGermanDate should correctly parse a valid German date string")
    public void testParseGermanDateWithValidDate() {
        // Given
        String validDate = "29.07.2024";

        // When
        LocalDate result = DateUtils.parseGermanDate(validDate);

        // Then
        LocalDate expectedDate = LocalDate.of(2024, 7, 29);
        assertEquals(expectedDate, result);
    }

    @Test
    @DisplayName("DateUtils.parseGermanDate should throw DateTimeParseException for an invalid date format")
    public void testParseGermanDateWithInvalidDateFormat() {
        // Given
        String invalidDate = "2024-07-29"; // Wrong format

        // When & Then
        assertThrows(DateTimeParseException.class, () -> DateUtils.parseGermanDate(invalidDate));
    }

    @Test
    @DisplayName("DateUtils.parseGermanDate should throw DateTimeParseException for an invalid date")
    public void testParseGermanDateWithInvalidDate() {
        // Given
        String invalidDate = "55.20.2024";

        // When & Then
        assertThrows(DateTimeParseException.class, () -> DateUtils.parseGermanDate(invalidDate));
    }

    @Test
    @DisplayName("DateUtils.parseGermanDate should return null for an empty date string")
    public void testParseGermanDateWithEmptyString() {
        // Given
        String emptyDate = "";

        // When
        LocalDate result = DateUtils.parseGermanDate(emptyDate);

        // Then
        assertEquals(null, result);
    }

    @Test
    @DisplayName("DateUtils.parseGermanDate should return null for null input")
    public void testParseGermanDateWithNullInput() {
        // Given
        String nullDate = null;

        // When
        LocalDate result = DateUtils.parseGermanDate(nullDate);

        // Then
        assertNull(result);
    }

    @Test
    @DisplayName("DateUtils.parseGermanDate should handle date strings with leading and trailing whitespaces")
    public void testParseGermanDateWithWhitespaces() {
        // Given
        String dateWithWhitespaces = " 29.07.2024 ";

        // When
        LocalDate result = DateUtils.parseGermanDate(dateWithWhitespaces.trim());

        // Then
        LocalDate expectedDate = LocalDate.of(2024, 7, 29);
        assertEquals(expectedDate, result);
    }

    @Test
    @DisplayName("DateUtils.parseGermanDate should correctly parse a valid leap year date")
    public void testParseGermanDateWithLeapYear() {
        // Given
        String leapYearDate = "29.02.2020";

        // When
        LocalDate result = DateUtils.parseGermanDate(leapYearDate);

        // Then
        LocalDate expectedDate = LocalDate.of(2020, 2, 29);
        assertEquals(expectedDate, result);
    }

    @Test
    @DisplayName("DateUtils.parseGermanDate should throw DateTimeParseException for date with invalid separators")
    public void testParseGermanDateWithInvalidSeparators() {
        // Given
        String invalidSeparatorDate = "29/07/2024";

        // When & Then
        assertThrows(DateTimeParseException.class, () -> DateUtils.parseGermanDate(invalidSeparatorDate));
    }

    @Test
    @DisplayName("DateUtils.parseGermanDate should handle date strings with mixed valid and invalid characters")
    public void testParseGermanDateWithMixedCharacters() {
        // Given
        String mixedDate = "29.Juli.2024"; // Using "Juli" instead of "07"

        // When & Then
        assertThrows(DateTimeParseException.class, () -> DateUtils.parseGermanDate(mixedDate));
    }
}
