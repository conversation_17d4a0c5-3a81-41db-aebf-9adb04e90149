package de.adesso.fischereiregister.utils;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

public class StringNormalizerTest {

    @Test
    @DisplayName("StringNormalizer.normalize should return null, if argument is null")
    public void testNormalizeNull() {
        //GIVEN
        //WHEN
        String result = StringNormalizer.normalize(null);
        //THEN
        assertNull(result);
    }


    @Test
    @DisplayName("StringNormalizer.normalize properly normalize the given string")
    public void testNormalize() {
        //GIVEN
        //WHEN
        String result = StringNormalizer.normalize(" Test-Test    test ");
        //THEN
        assertEquals("test test test", result);
    }


}
