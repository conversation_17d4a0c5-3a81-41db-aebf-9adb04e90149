package de.adesso.fischereiregister.utils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

public class DateUtils {

    public static final String            GERMAN_DATE_TIME_PATTERN   = "dd.MM.yyyy";
    public static final DateTimeFormatter GERMAN_DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern(GERMAN_DATE_TIME_PATTERN);

    // Private constructor to prevent instantiation
    private DateUtils() {
        throw new UnsupportedOperationException("Utility class should not be instantiated");
    }

    /**
     * Converts a date string in German format (dd.MM.yyyy) to a LocalDate.
     *
     * @param dateOfBirth the date string in German format
     * @return the corresponding LocalDate
     * @throws DateTimeParseException if the date string is invalid
     */
    public static LocalDate parseGermanDate(String dateOfBirth) throws DateTimeParseException {
        if (dateOfBirth == null || dateOfBirth.isEmpty() || dateOfBirth.equals("null")) {
            return null;
        }
        return LocalDate.parse(dateOfBirth, GERMAN_DATE_TIME_FORMATTER);
    }

    public static boolean isDate(String field) {
        return field.matches("\\d{2}\\.\\d{2}\\.\\d{4}");
    }
}