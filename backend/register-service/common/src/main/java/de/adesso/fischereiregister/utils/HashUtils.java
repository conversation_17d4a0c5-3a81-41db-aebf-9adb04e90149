package de.adesso.fischereiregister.utils;

import de.adesso.fischereiregister.utils.exceptions.HashingException;
import org.mindrot.jbcrypt.BCrypt;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.Objects;


public class HashUtils {

    private HashUtils() {
    }

    private static final int SALT_SUBSTRING_LENGTH = 10;
    public static final int HASH_SUBSTRING_LENGTH = 10;

    public static String sha512(String input) {
        try {
            MessageDigest messageDigest = MessageDigest.getInstance("SHA-512");
            byte[] hashedBytes = messageDigest.digest(input.getBytes(StandardCharsets.UTF_8));

            // Convert byte array into signum representation
            StringBuilder hexString = new StringBuilder();
            for (byte b : hashedBytes) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) hexString.append('0');
                hexString.append(hex);
            }

            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new HashingException("SHA-512 algorithm not found!", e);
        }
    }

    public static String gensalt() {
        String salt = BCrypt.gensalt();
        return salt.substring(salt.length() - SALT_SUBSTRING_LENGTH);
    }

    public static String calculateHash(String... inputs) {
        String concatenatedInputs = java.lang.String.join(";",
                Arrays.stream(inputs)
                        .filter(Objects::nonNull)
                        .toArray(String[]::new)
        );
        return sha512(concatenatedInputs).substring(0, HASH_SUBSTRING_LENGTH);
    }
}
