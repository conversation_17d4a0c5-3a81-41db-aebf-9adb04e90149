package de.adesso.fischereiregister.registerservice.validate_fishing_license;

import de.adesso.fischereiregister.registerservice.validate_fishing_license.model.FishingLicenseValidationResult;

/**
 * Service interface for validating fishing licenses.
 * <p>
 * This interface provides methods to validate fishing licenses based on
 * specified request criteria and return the corresponding validation results.
 * </p>
 */
public interface ValidateFishingLicenseService {

    /**
     * Finds and validates a fishing license based on the provided request data.
     */
    FishingLicenseValidationResult validateFishingLicense(String licenseNumber, String identificationDocumentId, String hash);

}
