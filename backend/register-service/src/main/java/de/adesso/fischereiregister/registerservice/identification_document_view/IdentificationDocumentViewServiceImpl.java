package de.adesso.fischereiregister.registerservice.identification_document_view;

import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.ports.FishingLicenseNumberService;
import de.adesso.fischereiregister.registerservice.utils.NumberGenerationUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

@Service
@Profile("!dev & !localdev & !test & !stage")
@Slf4j
public class IdentificationDocumentViewServiceImpl implements IdentificationDocumentViewService, FishingLicenseNumberService {

    private final IdentificationDocumentViewRepository identificationDocumentViewRepository;

    public IdentificationDocumentViewServiceImpl(IdentificationDocumentViewRepository identificationDocumentViewRepository) {
        this.identificationDocumentViewRepository = identificationDocumentViewRepository;

    }

    @Override
    public void deleteAll() {
        identificationDocumentViewRepository.deleteAll();
    }

    @Override
    public String createNewAvailableFishingLicenseNumber(UUID registerEntryId, FederalState federalState) {
        return this.createNewAvailableFishingLicenseNumber(federalState);
    }

    @Override
    public Optional<IdentificationDocumentView> findByLicenseNumber(String licenseId) {
        return identificationDocumentViewRepository.findByLicenseNumber(licenseId);
    }

    @Override
    public void createIdentificationDocumentViews(UUID registerId,
                                                  String salt,
                                                  List<IdentificationDocument> identificationDocuments) {
        final List<IdentificationDocumentView> identificationDocumentViews = assembleIdentificationDocumentViews(registerId,
                salt,
                identificationDocuments);


        identificationDocumentViewRepository.saveAll(identificationDocumentViews);


    }

    private List<IdentificationDocumentView> assembleIdentificationDocumentViews(UUID registerId,
                                                                                 String salt,
                                                                                 List<IdentificationDocument> identificationDocuments) {
        return identificationDocuments.stream()
                .map(identificationDocument -> {
                    final IdentificationDocumentView view = new IdentificationDocumentView();
                    final FishingLicense license = identificationDocument.getFishingLicense();
                    String licenseNumber = license != null ? license.getNumber() : null;

                    view.setIdentificationDocumentId(identificationDocument.getDocumentId());
                    view.setRegisterId(registerId);
                    view.setLicenseNumber(licenseNumber);
                    view.setSalt(salt);
                    view.setDocumentType(identificationDocument.getType().toString());

                    return view;
                }).toList();
    }

    protected String createNewAvailableFishingLicenseNumber(FederalState federalState) {
        final Set<String> existingNumbers = new HashSet<>(identificationDocumentViewRepository.findAllNumbers());


        String newAvailableNumber;
        do {
            newAvailableNumber = NumberGenerationUtils.generateLicenseNumber(federalState);
        } while (existingNumbers.contains(newAvailableNumber));

        return newAvailableNumber;
    }


    public IdentificationDocumentView getIdentificationDocumentView(String identificationDocumentId) {
        return identificationDocumentViewRepository.findByIdentificationDocumentId(identificationDocumentId).orElseThrow(
                () -> new IllegalStateException("IdentificationDocumentView not found for identificationDocumentId: " + identificationDocumentId));

    }

}
