package de.adesso.fischereiregister.registerservice.online_services.message;

import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.registerservice.online_services.message.enums.OSMessageTemplate;

/**
 * Service interface for resolving message templates such as subject, text, and display name.
 *
 * The templates can be used for posting messages to the citizen portal inbox,
 * and are resolved based on the tenant (federal state) and the specific type of message.
 */
public interface OSMessageTemplateResolutionService {

    /**
     * Resolves the subject line for a message (portal inbox)
     * based on the given federal state (tenant) and message template type.
     *
     * @param federalState the federal state (tenant) for which the subject should be resolved
     * @param osMessageTemplate the type of message to resolve
     * @return the resolved subject
     */
    String getSubject(FederalState federalState, OSMessageTemplate osMessageTemplate);

    /**
     * Resolves the message body text for a given federal state, message template,
     * and optional person. If a person is provided, their full name is inserted
     * where the placeholder (e.g., {{citizenFullname}}) appears in the template.
     * Otherwise a default token will be used for example "Bürger".
     *
     * @param federalState the federal state (tenant) for which the text is resolved
     * @param osMessageTemplate the type of message template to resolve
     * @param person the person whose full name (if not null) should be inserted into the message
     * @return the resolved message text with the citizen's name, or the template text if person is null
     */
    String getText(FederalState federalState, OSMessageTemplate osMessageTemplate, Person person);

    /**
     * Resolves the display name (sender name) to be shown for a message, if available,
     * based on the given federal state (tenant) and message template type.
     *
     * @param federalState the federal state (tenant) for which the display name should be resolved
     * @param osMessageTemplate the type of message to resolve
     * @return the resolved display name
     */
    String getDisplayName(FederalState federalState, OSMessageTemplate osMessageTemplate);

}
