package de.adesso.fischereiregister.registerservice.domain.mapper;

import de.adesso.fischereiregister.registerservice.statistics.taxes_statistics.TaxesStatistics;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "default")
public interface TaxesStatisticsMapper {

    TaxesStatisticsMapper INSTANCE = Mappers.getMapper(TaxesStatisticsMapper.class);

    List<org.openapitools.model.TaxesStatistics> taxesStatisticsListToApiTaxesStatisticsList(List<TaxesStatistics> taxesStatisticsList);
}
