package de.adesso.fischereiregister.registerservice.drools.dmn.mapper;

import de.adesso.fischereiregister.core.ports.contracts.LicenseInformation;
import de.adesso.fischereiregister.registerservice.drools.dmn.license.configuration.output.LicenseInformationOutput;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "default")
public interface LicenseInformationOutputMapper {
    LicenseInformationOutputMapper INSTANCE = Mappers.getMapper(LicenseInformationOutputMapper.class);

    @Mapping(target = "feePrice", expression = "java(java.math.BigDecimal.valueOf(result.getFeeAmount()))")
    @Mapping(target = "isExtendable", source = "extendable")
    @Mapping(target = "isAvailable", source = "available")
    LicenseInformation toCoreModel(LicenseInformationOutput result);
}
