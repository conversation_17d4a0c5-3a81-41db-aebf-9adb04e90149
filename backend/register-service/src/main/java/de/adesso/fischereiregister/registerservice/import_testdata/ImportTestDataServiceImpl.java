package de.adesso.fischereiregister.registerservice.import_testdata;

import de.adesso.fischereiregister.card_orders.persistence.CardOrderRepository;
import de.adesso.fischereiregister.registerservice.identification_document_view.IdentificationDocumentViewRepository;
import de.adesso.fischereiregister.registerservice.import_testdata.number_services.TestDataImportCertificateNumbersViewServiceImpl;
import de.adesso.fischereiregister.registerservice.import_testdata.number_services.TestDataImportDocumentNumberServiceImpl;
import de.adesso.fischereiregister.registerservice.import_testdata.number_services.TestDataImportIdentificationDocumentViewServiceImpl;
import de.adesso.fischereiregister.registerservice.import_testdata.repository.DomainEventEntryRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVRecord;
import org.axonframework.commandhandling.gateway.CommandGateway;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Profile;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.nio.file.Paths;
import java.time.format.DateTimeParseException;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ExecutionException;

@Slf4j
@Service
@Profile({"dev", "localdev", "test", "stage"})
class ImportTestDataServiceImpl implements ImportTestDataService, CommandLineRunner {

    @Value("${test-data-import.upload-endpoint-enabled}")
    private boolean importUploadEnabled;

    @Value("${test-data-import.local-file-at-startup-enabled}")
    private boolean importLocalFileEnabled;

    @Value("${test-data-import.path}")
    private String testDataFilePath;

    private final CardOrderRepository cardOrderRepository;
    private final CommandGateway commandGateway;
    private final DomainEventEntryRepository domainEventEntryRepository;
    private final ResourceLoader resourceLoader;
    private final IdentificationDocumentViewRepository identificationDocumentViewRepository;
    private final TestDataImportIdentificationDocumentViewServiceImpl fishingLicenseNumberService;
    private final TestDataImportCertificateNumbersViewServiceImpl fishingCertificateNumberService;
    private final TestDataImportDocumentNumberServiceImpl documentNumberService;
    private final CsvCommandChainAssemblerFactory csvCommandChainAssemblerFactory;


    ImportTestDataServiceImpl(
            CommandGateway commandGateway,
            DomainEventEntryRepository domainEventEntryRepository,
            ResourceLoader resourceLoader,
            IdentificationDocumentViewRepository identificationDocumentViewRepository,
            TestDataImportIdentificationDocumentViewServiceImpl fishingLicenseNumberService,
            TestDataImportCertificateNumbersViewServiceImpl fishingCertificateNumberService,
            TestDataImportDocumentNumberServiceImpl documentNumberService,
            CardOrderRepository cardOrderRepository,
            CsvCommandChainAssemblerFactory csvCommandChainAssemblerFactory) {
        this.commandGateway = commandGateway;
        this.domainEventEntryRepository = domainEventEntryRepository;
        this.resourceLoader = resourceLoader;
        this.identificationDocumentViewRepository = identificationDocumentViewRepository;
        this.fishingLicenseNumberService = fishingLicenseNumberService;
        this.fishingCertificateNumberService = fishingCertificateNumberService;
        this.documentNumberService = documentNumberService;
        this.cardOrderRepository = cardOrderRepository;
        this.csvCommandChainAssemblerFactory = csvCommandChainAssemblerFactory;
    }


    @Override
    @Profile({"dev", "localdev", "test", "stage"})
    public void run(String... args) {
        log.info("Executing in 'dev'|'test' profile. Attempting test data Import from local file.");
        importFromLocalFile();
    }

    private void importFromLocalFile() {
        if (importLocalFileEnabled) {
            try {
                log.info("Starting import from local file: {}", testDataFilePath);
                Iterable<CSVRecord> records = loadData();
                initializeTestNumberServices(records);
                log.info("Successfully loaded {} records from local file.", ((List<CSVRecord>) records).size());
                deleteOldData(records);
                sendCommands(records);
                log.info("Import from local file completed successfully.");
            } catch (IOException e) {
                log.error(
                        "Failed to load test data from the local CSV file '{}'. Ensure the file exists and is accessible.",
                        testDataFilePath, e);
            } catch (IllegalArgumentException e) {
                log.error("Invalid argument encountered while processing test data from local CSV file '{}'.",
                        testDataFilePath, e);
            } catch (ExecutionException e) {
                log.error(
                        "ExecutionException occurred while executing commands for the test data from the local CSV file '{}'.",
                        testDataFilePath, e);
            } catch (InterruptedException e) {
                log.error("Processing was interrupted while handling test data from the local CSV file '{}'.",
                        testDataFilePath, e);
                Thread.currentThread().interrupt(); // Preserve interrupt status
            } catch (RuntimeException e) {
                log.error("Unexpected error occurred while processing test data from local CSV file '{}'.",
                        testDataFilePath, e);
            }
        } else {
            log.info("Test data import from local file at startup is disabled. No import action taken.");
        }
    }

    private void initializeTestNumberServices(Iterable<CSVRecord> csvRecords) {
        for (CSVRecord csvRecord : csvRecords) {
            final UUID registerId = UUID.fromString(csvRecord.get(HeaderConstants.REGISTER_ID));
            String licenseNumber = csvRecord.get(HeaderConstants.LICENSE_NUMBER).replace(Character.toString('-'), "");
            String fishingCertificateId = csvRecord.get(HeaderConstants.CERTIFICATE_ID).replace(Character.toString('-'), "");
            final String documentId = csvRecord.get(HeaderConstants.IDENTIFICATION_DOCUMENT_ID);
            fishingLicenseNumberService.addFishingLicenseNumber(registerId, licenseNumber);
            fishingCertificateNumberService.addFishingCertificateNumber(registerId, fishingCertificateId);
            documentNumberService.addDocumentNumber(registerId, documentId);
        }
    }

    @Override
    public void importFromUploadedFile(InputStream inputStream)
            throws IOException, ExecutionException, InterruptedException {
        if (importUploadEnabled) {
            log.info("Starting import from uploaded file.");
            Iterable<CSVRecord> records = loadData(inputStream);
            initializeTestNumberServices(records);
            log.info("Successfully loaded {} records from uploaded file.", ((List<CSVRecord>) records).size());
            deleteOldData(records);
            sendCommands(records);
            log.info("Import from uploaded file completed successfully.");
        } else {
            log.warn("Test data import from uploaded file is disabled. Cannot perform import.");
            throw new IllegalStateException("Test data import from uploaded file is not enabled.");
        }
    }

    private Iterable<CSVRecord> loadData() throws IOException {
        Resource fileResource = getResource(testDataFilePath);
        return parseCSVData(fileResource);
    }

    private Iterable<CSVRecord> parseCSVData(Resource resource) throws IOException {
        try (InputStream inputStream = resource.getInputStream()) {
            return loadData(inputStream);
        }
    }

    private Resource getResource(String filePath) {
        String absolutePath = resolveAbsolutePath(filePath);
        return resourceLoader.getResource("file:" + absolutePath);
    }

    private String resolveAbsolutePath(String filePath) {
        return Paths.get(filePath).toAbsolutePath().toString();
    }

    private Iterable<CSVRecord> loadData(InputStream inputStream) throws IOException {
        try (Reader reader = new InputStreamReader(inputStream)) {
            CSVFormat csvFormat = CSVFormat.DEFAULT.builder()
                    .setHeader(HeaderConstants.HEADERS)
                    .setSkipHeaderRecord(true)
                    .setDelimiter(';')
                    .build();

            return csvFormat.parse(reader).getRecords();
        }
    }

    private void deleteOldData(Iterable<CSVRecord> csvRecords) {
        for (CSVRecord csvRecord : csvRecords) {

            domainEventEntryRepository.findByAggregateIdentifier(csvRecord.get(HeaderConstants.REGISTER_ID))
                    .forEach(domainEventEntry -> {
                        log.debug("Deleting DomainEventEntry: {}", domainEventEntry);
                        domainEventEntryRepository.delete(domainEventEntry);
                    });

            final String licenseNumber = csvRecord.get(HeaderConstants.LICENSE_NUMBER).replace(Character.toString('-'), "");
            identificationDocumentViewRepository
                    .findAllByLicenseNumber(licenseNumber)
                    .forEach(fishingLicenseView -> {
                        log.debug("deleting IdentificationDocument: {}", fishingLicenseView);
                        identificationDocumentViewRepository.delete(fishingLicenseView);
                    });

            cardOrderRepository.findByLicenseNumber(licenseNumber)
                    .forEach(cardOrder -> {
                        log.debug("deleting CardOrder: {}", cardOrder);
                        cardOrderRepository.delete(cardOrder);
                    });
        }
    }

    private void sendCommands(Iterable<CSVRecord> csvRecords) throws ExecutionException, InterruptedException {
        for (CSVRecord csvRecord : csvRecords) {
            try {
                Iterable<Record> commands = csvCommandChainAssemblerFactory.createAssembler(csvRecord)
                        .addBanCommands()
                        .assemble();

                for (Record command : commands) {
                    commandGateway.send(command).get();
                }
            } catch (DateTimeParseException e) {
                String dateTimeParseError = String.format("Tried formatting invalid date in CSV record: %s", csvRecord);
                throw new IllegalArgumentException(dateTimeParseError, e);
            } catch (ExecutionException e) {
                String executionExceptionError = String
                        .format("ExecutionException occurred while sending command for CSV record: %s", csvRecord);
                throw new ExecutionException(executionExceptionError, e);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt(); // Preserve interrupt status
                String interruptedExceptionError = String
                        .format("InterruptedException occurred while sending command for CSV record: %s", csvRecord);
                throw new InterruptedException(interruptedExceptionError + "\n" + e.getMessage());
            }
        }
    }
}
