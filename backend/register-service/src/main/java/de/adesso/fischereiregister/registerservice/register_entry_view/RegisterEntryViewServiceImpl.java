package de.adesso.fischereiregister.registerservice.register_entry_view;

import de.adesso.fischereiregister.core.exceptions.LicenseNotFoundException;
import de.adesso.fischereiregister.core.model.Address;
import de.adesso.fischereiregister.core.model.Ban;
import de.adesso.fischereiregister.core.model.Fee;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.LimitedLicenseApplication;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.ValidityPeriod;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.IdentificationDocumentType;
import de.adesso.fischereiregister.core.model.type.LimitedLicenseApplicationStatus;
import de.adesso.fischereiregister.core.model.user.UserRole;
import de.adesso.fischereiregister.core.utils.PersonUtils;
import jakarta.persistence.EntityNotFoundException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

@Service
@AllArgsConstructor
@Slf4j
class RegisterEntryViewServiceImpl implements RegisterEntryViewService {

    private final RegisterEntryViewRepository repository;
    private final JdbcTemplate jdbcTemplate;

    @Override
    public void truncateView() {
        // Use jdbcTemplate for performance, also repository breaks due to json parsing
        jdbcTemplate.execute("TRUNCATE TABLE register_entry_view");
    }

    @Override
    public void reorderCardAndUpdateRegisterEntry(UUID registerEntryId,
                                                  Person person,
                                                  List<Fee> fees,
                                                  List<Tax> taxes,
                                                  List<IdentificationDocument> identificationDocuments,
                                                  String fishingLicenseNumber,
                                                  FederalState issuingFederalState
    ) {
        final RegisterEntryView registerEntryView = findByRegisterId(registerEntryId);
        final RegisterEntry registerEntry = registerEntryView.getData();

        registerEntry.setPerson(person);

        registerEntry.getTaxes().addAll(taxes);
        registerEntry.getFees().addAll(fees);

        registerEntry.getIdentificationDocuments().stream()
                .filter(identificationDocument -> identificationDocument.getFishingLicense() != null
                        && identificationDocument.getType() == IdentificationDocumentType.PDF
                        && identificationDocument.getValidTo() == null)
                .forEach(identificationDocument -> identificationDocument.setValidTo(LocalDate.now()));

        registerEntry.getIdentificationDocuments().addAll(identificationDocuments);

        FishingLicense reorderedLicense = registerEntry.getFishingLicenses().stream()
                .filter(license -> license.getNumber().equals(fishingLicenseNumber))
                .findAny()
                .orElseThrow(IllegalStateException::new);

        reorderedLicense.setIssuingFederalState(issuingFederalState);

        registerEntryView.setData(registerEntry);

        repository.save(registerEntryView);
    }

    protected RegisterEntryView assembleRegisterEntryView(RegisterEntry entry) {
        final RegisterEntryView view = new RegisterEntryView();

        view.setRegisterId(entry.getRegisterId());

        view.setData(entry);

        return view;
    }

    @Override
    @Transactional
    public RegisterEntryView findByRegisterId(UUID registerEntryId) {
        return repository.findByRegisterId(registerEntryId).orElseThrow(EntityNotFoundException::new);
    }

    @Override
    public Optional<RegisterEntryView> findOptionalByRegisterId(UUID registerEntryId) {
        return repository.findByRegisterId(registerEntryId);
    }

    @Override
    @Transactional(readOnly = true)
    public RegisterEntryView findByRegisterId(UUID registerEntryId, Collection<UserRole> userRoles) {
        final RegisterEntryView view = findByRegisterId(registerEntryId);

        // Filter out limited license approval documents if the user is not allowed to see them
        if (!userRoles.contains(UserRole.LIMITED_LICENSE_READER)) {
            view.getData().getIdentificationDocuments().removeIf(document -> document.getLimitedLicenseApproval() != null);
        }
        return view;
    }

    @Override
    public void deleteBan(UUID registerEntryId) {

        final RegisterEntryView view = findByRegisterId(registerEntryId);

        final Ban banFromView = view.getData().getBan();
        if (banFromView != null) {
            view.getData().setBan(null);
            repository.save(view);
        }
    }

    @Override
    public void createOrUpdateRegisterEntryView(RegisterEntryViewServiceParameters parameters) {
        RegisterEntryView view = repository.findByRegisterId(parameters.getRegisterEntryId()).orElse(null);

        if (view == null) {
            view = new RegisterEntryView();
            view.setRegisterId(parameters.getRegisterEntryId());
            view.setData(new RegisterEntry());
        }

        RegisterEntry registerEntry = view.getData();
        overWriteExistingProperties(registerEntry, parameters);

        repository.save(view);
    }

    @Override
    public void updateRegisterEntryView(RegisterEntryViewServiceParameters parameters) {
        final RegisterEntryView view = findByRegisterId(parameters.getRegisterEntryId());

        final RegisterEntry registerEntry = view.getData();
        overWriteExistingProperties(registerEntry, parameters);

        repository.save(view);
    }

    @Override
    public void createRegisterEntryView(RegisterEntryViewServiceParameters parameters) {
        final RegisterEntry registerEntry = new RegisterEntry();

        overWriteExistingProperties(registerEntry, parameters);

        final RegisterEntryView view = assembleRegisterEntryView(registerEntry);
        repository.save(view);
    }

    @Override
    public void extendFishingLicense(UUID registerEntryId, String licenseNumber, ValidityPeriod validityPeriod) {
        final RegisterEntryView view = findByRegisterId(registerEntryId);

        view.getData().getFishingLicenses().stream()
                .filter(fishingLicense -> fishingLicense.getNumber().equals(licenseNumber))
                .findFirst()
                .ifPresentOrElse(
                        fishingLicense -> fishingLicense.getValidityPeriods().add(validityPeriod),
                        () -> {
                            throw new LicenseNotFoundException("Tried extending the license with number " + licenseNumber + ", but was not found");
                        }
                );

        repository.save(view);
    }

    @Override
    public void rejectLimitedLicenseApplication(UUID registerEntryId, UUID limitedLicenseApplicationId) {
        final RegisterEntryView view = findByRegisterId(registerEntryId);

        final LimitedLicenseApplication limitedLicenseApplication = view.getData().getLimitedLicenseApplication();

        // In this case the view was invalid or the event was called on an another limited license application which should not have been possible
        // since this is only view data, we ignore this and just log a warning to notify weird unexpected behaviour
        if (limitedLicenseApplication == null || !Objects.equals(limitedLicenseApplicationId, limitedLicenseApplication.getId())) {
            log.warn("Tried rejecting limited license application in RegisterEntryView with id {}, but no such license application could be found.", limitedLicenseApplicationId);

            return;
        }

        limitedLicenseApplication.setStatus(LimitedLicenseApplicationStatus.REJECTED);

        repository.save(view);

    }

    @Override
    public void acceptLimitedLicenseApplication(UUID registerEntryId) {
        Optional<RegisterEntryView> optionalView = findOptionalByRegisterId(registerEntryId);
        if(!optionalView.isEmpty()) {
            final RegisterEntryView view = optionalView.get();

            view.getData().setLimitedLicenseApplication(null);
            repository.save(view);
        }
    }

    private void overWriteExistingProperties(RegisterEntry registerEntry, RegisterEntryViewServiceParameters parameters) {
        registerEntry.setRegisterId(parameters.getRegisterEntryId());

        // Only overwrite address if address is set
        if (parameters.getPerson() != null) {
            if (registerEntry.getPerson() == null) {
                registerEntry.setPerson(parameters.getPerson());
            } else {
                Address newAddress = parameters.getPerson().getAddress();
                if (newAddress == null) {
                    newAddress = registerEntry.getPerson().getAddress();
                }

                registerEntry.setPerson(parameters.getPerson());
                PersonUtils.setBirthnameIfMissing(registerEntry.getPerson());
                registerEntry.getPerson().setAddress(newAddress);
            }
        }

        if (parameters.getJurisdiction() != null) {
            registerEntry.setJurisdiction(parameters.getJurisdiction());
        }

        if (parameters.getBan() != null) {
            registerEntry.setBan(parameters.getBan());
        }

        if (parameters.getTaxes() != null) {
            registerEntry.getTaxes().addAll(parameters.getTaxes());
        }

        if (parameters.getFees() != null) {
            registerEntry.getFees().addAll(parameters.getFees());
        }

        if (parameters.getQualificationsProofs() != null) {
            registerEntry.getQualificationsProofs().addAll(parameters.getQualificationsProofs());
        }

        if (parameters.getIdentificationDocuments() != null) {
            registerEntry.getIdentificationDocuments().addAll(parameters.getIdentificationDocuments());
        }

        if (parameters.getFishingLicense() != null) {
            registerEntry.getFishingLicenses().add(parameters.getFishingLicense());
        }

        if (parameters.getLimitedLicenseApplication() != null) {
            registerEntry.setLimitedLicenseApplication(parameters.getLimitedLicenseApplication());
        }
    }

}