package de.adesso.fischereiregister.registerservice.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.net.ssl.TrustManager;
import javax.net.ssl.TrustManagerFactory;
import javax.net.ssl.X509TrustManager;
import java.io.InputStream;
import java.security.KeyStore;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Custom X509TrustManager that trusts both system certificates and additional certificates
 * from the certs folder in the classpath.
 * <p>
 * This trust manager combines the default system trust manager with custom certificates
 * loaded from the classpath certs folder, allowing the application to trust both
 * standard CA certificates and custom certificates for specific S3 endpoints.
 */
@Slf4j
public class CustomX509TrustManager implements X509TrustManager {

    private final X509TrustManager defaultTrustManager;
    private final List<X509Certificate> customCertificates;

    /**
     * Creates a new CustomX509TrustManager instance.
     * Initializes the default trust manager and loads custom certificates from the certs folder.
     *
     * @throws RuntimeException if the trust manager cannot be initialized
     */
    public CustomX509TrustManager() {
        try {
            this.defaultTrustManager = initializeDefaultTrustManager();
            this.customCertificates = loadCertificatesFromCertsFolder();
        } catch (Exception e) {
            throw new RuntimeException("Failed to initialize CustomX509TrustManager", e);
        }
    }

    /**
     * Initializes the default system trust manager.
     *
     * @return the default X509TrustManager
     * @throws Exception if initialization fails
     */
    private X509TrustManager initializeDefaultTrustManager() throws Exception {
        TrustManagerFactory defaultTmf = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
        defaultTmf.init((KeyStore) null); // null means use the default keystore

        for (TrustManager tm : defaultTmf.getTrustManagers()) {
            if (tm instanceof X509TrustManager) {
                return (X509TrustManager) tm;
            }
        }
        throw new IllegalStateException("Default X509TrustManager not found");
    }

    /**
     * Loads certificates from the certs folder in the classpath.
     *
     * @return list of loaded X509 certificates
     */
    private List<X509Certificate> loadCertificatesFromCertsFolder() {
        List<X509Certificate> certificates = new ArrayList<>();
        try {
            String certsPath = "certs";
            PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
            org.springframework.core.io.Resource[] resources = resolver.getResources("classpath:" + certsPath + "/*");

            if (resources.length == 0) {
                log.warn("No files found in the certs directory");
                return certificates;
            }

            CertificateFactory cf = CertificateFactory.getInstance("X.509");

            for (org.springframework.core.io.Resource resource : resources) {
                String filename = resource.getFilename();
                if (filename == null) continue;

                if (filename.toLowerCase().endsWith(".crt") ||
                        filename.toLowerCase().endsWith(".cer") ||
                        filename.toLowerCase().endsWith(".pem")) {

                    X509Certificate cert = loadCertificateFromResource(cf, resource, filename);
                    if (cert != null) {
                        certificates.add(cert);
                    }
                }
            }

            if (certificates.isEmpty()) {
                log.warn("No valid certificates were found in the certs folder");
            } else {
                log.info("Loaded {} custom certificates from certs folder", certificates.size());
            }
        } catch (Exception e) {
            log.error("Error scanning certificates from certs folder", e);
        }
        return certificates;
    }

    /**
     * Loads a certificate from a resource.
     *
     * @param cf       Certificate factory to use for parsing
     * @param resource Resource containing the certificate
     * @param filename Filename for logging purposes
     * @return the loaded X509Certificate or null if loading failed
     */
    private X509Certificate loadCertificateFromResource(
            CertificateFactory cf,
            org.springframework.core.io.Resource resource,
            String filename) {
        try (InputStream is = resource.getInputStream()) {
            X509Certificate cert = (X509Certificate) cf.generateCertificate(is);
            log.info("Loaded certificate: {} from file: {}", cert.getSubjectX500Principal().getName(), filename);
            return cert;
        } catch (Exception e) {
            log.error("Error loading certificate from {}", filename, e);
            return null;
        }
    }

    @Override
    public X509Certificate[] getAcceptedIssuers() {
        // Combine custom certificates with default accepted issuers
        List<X509Certificate> allIssuers = new ArrayList<>(customCertificates);
        allIssuers.addAll(Arrays.asList(defaultTrustManager.getAcceptedIssuers()));
        return allIssuers.toArray(new X509Certificate[0]);
    }

    @Override
    public void checkClientTrusted(X509Certificate[] chain, String authType) throws CertificateException {
        defaultTrustManager.checkClientTrusted(chain, authType);
    }

    @Override
    public void checkServerTrusted(X509Certificate[] chain, String authType) throws CertificateException {
        // First, check if any certificate in the chain matches our custom certificates
        if (!customCertificates.isEmpty()) {
            boolean foundMatch = false;
            for (X509Certificate cert : chain) {
                for (X509Certificate customCert : customCertificates) {
                    if (cert.equals(customCert)) {
                        foundMatch = true;
                        break;
                    }
                }
                if (foundMatch) break;
            }

            if (foundMatch) {
                log.debug("Certificate chain trusted via custom certificate");
                return;
            }
        }

        // If no custom certificate match, fall back to default trust manager
        defaultTrustManager.checkServerTrusted(chain, authType);
    }
}
