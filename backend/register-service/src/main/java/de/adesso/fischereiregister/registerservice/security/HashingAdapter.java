package de.adesso.fischereiregister.registerservice.security;

import de.adesso.fischereiregister.card_orders.ports.HashingPort;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.utils.HashUtils;
import org.springframework.stereotype.Component;

import java.util.UUID;

@Component
public class HashingAdapter implements HashingPort {
    @Override
    public String calculateHash(UUID registerEntryId, Person person, String salt) {
        return HashUtils.calculateHash(registerEntryId.toString(),
                person.getFirstname(),
                person.getLastname(),
                person.getBirthdate().toString(),
                salt);
    }

    @Override
    public String getQROrNFCDataForLicense(UUID registerEntryId, String licenseNumber, Person person, String documentId, String salt) {
        String hash = calculateHash(registerEntryId, person, salt);
        return documentId + ";" + hash + ";" + licenseNumber;
    }

    @Override
    public String getQROrNFCDataForTax(UUID registerEntryId, Person person, String documentId, String salt) {
        return getQROrNFCDataForLicense(registerEntryId, "", person, documentId, salt);
    }
}
