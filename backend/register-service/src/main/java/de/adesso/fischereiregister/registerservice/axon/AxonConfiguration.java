package de.adesso.fischereiregister.registerservice.axon;

import com.zaxxer.hikari.HikariDataSource;
import de.adesso.fischereiregister.registerservice.security.UserDetailsService;
import de.adesso.fischereiregister.registerservice.security.UserTrackingInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.axonframework.commandhandling.CommandMessage;
import org.axonframework.commandhandling.SimpleCommandBus;
import org.axonframework.common.transaction.TransactionManager;
import org.axonframework.config.ConfigurerModule;
import org.axonframework.messaging.correlation.CorrelationDataProvider;
import org.axonframework.messaging.correlation.MessageOriginProvider;
import org.axonframework.messaging.correlation.MultiCorrelationDataProvider;
import org.axonframework.messaging.correlation.SimpleCorrelationDataProvider;
import org.axonframework.messaging.interceptors.CorrelationDataInterceptor;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

import javax.sql.DataSource;
import java.util.Arrays;


@Configuration
@EnableJpaRepositories(basePackages = {"de.adesso.fischereiregister.*"})
@EntityScan(basePackages = {
        "de.adesso.fischereiregister.*",
        "org.axonframework.eventsourcing.eventstore.jpa",
        "org.axonframework.eventhandling.saga.repository.jpa",
        "org.axonframework.eventhandling.tokenstore.jpa"
})
@Slf4j
public class AxonConfiguration {

    @Bean("dataSource")
    @ConfigurationProperties("spring.datasource")
    public DataSource axon() {
        return DataSourceBuilder.create().type(HikariDataSource.class).build();
    }

    @Bean
    public ConfigurerModule processorErrorHandlingConfigurerModule() {
        return configurer -> configurer.eventProcessing(
                processingConfigurer -> processingConfigurer.registerDefaultErrorHandler(conf -> errorContext -> {
                    var error = errorContext.error();
                    log.error(error.getMessage(), error);
                })
        );
    }

    /**
     * registers the correlationDataProviders, which modify the Metadata of Event-Messages as they are processed by Axon
     * <p>
     * SimpleCorrelationDataProvider copies metadata values for the given keys from parent to child messages
     * <p>
     * The MessageOriginProvider takes care of the tracing metadata. It's the default CorrelationDataProvider
     * that we have to add here manually because providing any CorrelationDataProvider overrides the default
     */
    @Bean
    public CorrelationDataProvider correlationDataProviders() {
        return new MultiCorrelationDataProvider<CommandMessage<?>>(
                Arrays.asList(
                        new SimpleCorrelationDataProvider(
                                UserTrackingInterceptor.TIMESTAMP_METADATA_TAG,
                                UserTrackingInterceptor.USER_ID_METADATA_TAG
                        ),
                        new MessageOriginProvider()
                )
        );
    }

    /**
     * setups the commandBus so we can register it with a CommandDispatchInterceptor
     * </p>
     * we also register the same HandlerInterceptor that would be registered on the default
     * command bus setup and which adds tracing information in the metadata of commands.
     */
    @Bean
    public SimpleCommandBus commandBus(TransactionManager txManager, AxonConfiguration axonConfiguration, UserDetailsService userDetailsService) {
        SimpleCommandBus commandBus =
                SimpleCommandBus.builder()
                        .transactionManager(txManager)
                        .build();
        commandBus.registerDispatchInterceptor(new UserTrackingInterceptor(userDetailsService));
        commandBus.registerHandlerInterceptor(
                new CorrelationDataInterceptor<>(axonConfiguration.correlationDataProviders())
        );
        return commandBus;
    }

}
