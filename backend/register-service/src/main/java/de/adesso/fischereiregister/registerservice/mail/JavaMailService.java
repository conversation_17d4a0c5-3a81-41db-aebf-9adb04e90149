package de.adesso.fischereiregister.registerservice.mail;

import de.adesso.fischereiregister.registerservice.fishing_license_export.model.RenderedContent;
import jakarta.mail.util.ByteArrayDataSource;
import lombok.AllArgsConstructor;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.mail.javamail.MimeMessagePreparator;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Mail Service Adapter for sending emails.
 * Implements MailService
 */
@Service
@AllArgsConstructor
public class JavaMailService implements MailService {

    private final JavaMailSender javaMailSender;

    /**
     * Sends an email with an attachment.
     * Attachment should not be null.
     */
    @Override
    public void sendMail(String to, String from, String subject, String text, List<RenderedContent> attachments) {
        MimeMessagePreparator preparator = mimeMessage -> {
            MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true, "UTF-8"); // Ensure UTF-8 encoding

            for (RenderedContent attachment : attachments) {
                ByteArrayDataSource attachmentInputStream = new ByteArrayDataSource(attachment.content(), "application/octet-stream");
                helper.addAttachment(attachment.getFullFilename(), attachmentInputStream);
            }

            helper.setFrom(from);
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(text, false);

        };
        javaMailSender.send(preparator);
    }
}