package de.adesso.fischereiregister.registerservice.online_services.message.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum OSMessageTemplate {
    REGULAR_FISHING_LICENSE_CREATED_OS(
            "os_message_template.regular_fishing_license.created.subject",
            "os_message_template.regular_fishing_license.created.text",
            "os_message_template.regular_fishing_license.created.display_name"
    ),
    REGULAR_FISHING_LICENSE_CREATED_OS_FAILURE(
            "os_message_template.regular_fishing_license.created_failure.subject",
            "os_message_template.regular_fishing_license.created_failure.text",
            "os_message_template.regular_fishing_license.created_failure.display_name"
    ),
    REGULAR_FISHING_LICENSE_REPLACED_OS(
            "os_message_template.regular_fishing_license.replaced.subject",
            "os_message_template.regular_fishing_license.replaced.text",
            "os_message_template.regular_fishing_license.replaced.display_name"
    ),
    REGULAR_FISHING_LICENSE_REPLACED_OS_FAILURE(
            "os_message_template.regular_fishing_license.replaced_failure.subject",
            "os_message_template.regular_fishing_license.replaced_failure.text",
            "os_message_template.regular_fishing_license.replaced_failure.display_name"
    ),
    VACATION_FISHING_LICENSE_CREATED_OS(
            "os_message_template.vacation_fishing_license.created.subject",
            "os_message_template.vacation_fishing_license.created.text",
            "os_message_template.vacation_fishing_license.created.display_name"
    ),
    VACATION_FISHING_LICENSE_CREATED_OS_FAILURE(
            "os_message_template.vacation_fishing_license.created_failure.subject",
            "os_message_template.vacation_fishing_license.created_failure.text",
            "os_message_template.vacation_fishing_license.created_failure.display_name"
    ),
    VACATION_FISHING_LICENSE_EXTENDED_OS(
            "os_message_template.vacation_fishing_license.extended.subject",
            "os_message_template.vacation_fishing_license.extended.text",
            "os_message_template.vacation_fishing_license.extended.display_name"
    ),
    VACATION_FISHING_LICENSE_EXTENDED_OS_FAILURE(
            "os_message_template.vacation_fishing_license.extended_failure.subject",
            "os_message_template.vacation_fishing_license.extended_failure.text",
            "os_message_template.vacation_fishing_license.extended_failure.display_name"
    ),
    LIMITED_FISHING_LICENSE_PENDING_OS(
            "os_message_template.limited_fishing_license.pending.subject",
            "os_message_template.limited_fishing_license.pending.text",
            "os_message_template.limited_fishing_license.pending.display_name"
    ),
    LIMITED_FISHING_LICENSE_CREATED_OS(
            "os_message_template.limited_fishing_license.created.subject",
            "os_message_template.limited_fishing_license.created.text",
            "os_message_template.limited_fishing_license.created.display_name"
    ),
    LIMITED_FISHING_LICENSE_CREATED_OS_FAILURE(
            "os_message_template.limited_fishing_license.created_failure.subject",
            "os_message_template.limited_fishing_license.created_failure.text",
            "os_message_template.limited_fishing_license.created_failure.display_name"
    ),
    LIMITED_FISHING_LICENSE_REJECTED(
            "os_message_template.limited_fishing_license.rejected.subject",
            "os_message_template.limited_fishing_license.rejected.text",
            "os_message_template.limited_fishing_license.rejected.display_name"
    ),
    FISHING_TAX_CREATED_OS(
            "os_message_template.fishing_tax.created.subject",
            "os_message_template.fishing_tax.created.text",
            "os_message_template.fishing_tax.created.display_name"
    ),
    FISHING_TAX_CREATED_OS_FAILURE(
            "os_message_template.fishing_tax.created_failure.subject",
            "os_message_template.fishing_tax.created_failure.text",
            "os_message_template.fishing_tax.created_failure.display_name"
    ),
    FAILURE_MESSAGE_NO_PERSON_OS(
            "os_message_template.failure_without_person_data.subject",
            "os_message_template.failure_without_person_data.text",
            "os_message_template.failure_without_person_data.display_name"
    );

    private final String subjectKey;
    private final String textKey;
    private final String displayNameKey;
}