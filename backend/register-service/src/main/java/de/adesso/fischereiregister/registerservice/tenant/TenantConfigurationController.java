package de.adesso.fischereiregister.registerservice.tenant;

import api.TenantConfigurationApi;
import com.fasterxml.jackson.databind.JsonNode;
import de.adesso.fischereiregister.core.exceptions.RulesProcessingException;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.registerservice.domain.mapper.dmn.LicenseInformationResponseMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.dmn.TaxInformationResponseMapper;
import de.adesso.fischereiregister.registerservice.drools.dmn.license.configuration.output.LicenseInformationOutput;
import de.adesso.fischereiregister.registerservice.drools.dmn.tax.configuration.TaxInformationOutput;
import de.adesso.fischereiregister.registerservice.security.UserDetailsService;
import de.adesso.fischereiregister.registerservice.tenant.port.TenantRulesConfigurationPort;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping()
@AllArgsConstructor
@Slf4j
public class TenantConfigurationController implements TenantConfigurationApi {

    private final UserDetailsService userDetailsService;
    private final TenantConfigurationService tenantConfigurationService;
    private final TenantRulesConfigurationPort tenantRulesConfigurationService;

    @GetMapping("/tenant-configuration/load")
    public ResponseEntity<JsonNode> loadTenantConfiguration() {
        final FederalState federalState = FederalState.valueOf(userDetailsService.getFederalState());
        final JsonNode tenantConfiguration = tenantConfigurationService.getTenantConfiguration(federalState);
        return ResponseEntity.ok(tenantConfiguration);
    }

    @SneakyThrows
    @Override
    public ResponseEntity<?> tenantConfigurationControllerGetLicenseInformation() {
        final FederalState federalState = FederalState.valueOf(userDetailsService.getFederalState());
        try {
            List<LicenseInformationOutput> licenseInformationResults = tenantRulesConfigurationService.getLicenseFeeInformation(federalState);

            return ResponseEntity.ok(LicenseInformationResponseMapper.INSTANCE.toResponse(licenseInformationResults));
        } catch (RulesProcessingException e) {
            log.error("Failed to process tennant configuration", e);
            return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @SneakyThrows
    @Override
    public ResponseEntity<?> tenantConfigurationControllerGetTaxInformation(@Valid Boolean officeFeeAlreadyPayed) {
        final FederalState federalState = FederalState.valueOf(userDetailsService.getFederalState());
        try {

            List<TaxInformationOutput> taxInformationResults = tenantRulesConfigurationService.getTaxPriceInformation(federalState, officeFeeAlreadyPayed);
            return ResponseEntity.ok(TaxInformationResponseMapper.INSTANCE.toResponse(taxInformationResults));

        } catch (RulesProcessingException e) {
            log.error("Failed to process tennant configuration", e);
            return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
