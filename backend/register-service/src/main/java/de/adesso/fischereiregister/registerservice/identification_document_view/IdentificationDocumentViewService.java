package de.adesso.fischereiregister.registerservice.identification_document_view;

import de.adesso.fischereiregister.core.model.IdentificationDocument;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface IdentificationDocumentViewService {

    /**
     * Will delete all entries in the IdentificationDocumentView. Use only if replay is ensured to happen after.
     */
    void deleteAll();

    Optional<IdentificationDocumentView> findByLicenseNumber(String licenseId);

    void createIdentificationDocumentViews(UUID registerId,
                                           String salt,
                                           List<IdentificationDocument> identificationDocuments);

    IdentificationDocumentView getIdentificationDocumentView(String identificationDocumentId);

}
