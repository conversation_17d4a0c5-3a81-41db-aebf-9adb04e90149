package de.adesso.fischereiregister.registerservice.import_testdata;

import de.adesso.fischereiregister.core.commands.CreateLimitedLicenseCommand;
import de.adesso.fischereiregister.core.commands.DigitizeRegularLicenseCommand;
import de.adesso.fischereiregister.core.commands.MoveJurisdictionCommand;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang3.NotImplementedException;

import java.util.ArrayList;
import java.util.List;

public class CsvCommandChainAssembler {
    private final List<Record> commands = new ArrayList<>();
    private final CSVRecord csvRecord;
    private final CsvCommandCreationService csvCommandCreationService;
    private final CsvDomainCreationService csvDomainCreationService;

    public CsvCommandChainAssembler(CsvDomainCreationService csvDomainCreationService, CsvCommandCreationService csvCommandCreationService, CSVRecord csvRecord) {
        this.csvRecord = csvRecord;
        this.csvCommandCreationService = csvCommandCreationService;
        this.csvDomainCreationService = csvDomainCreationService;

        initializeBaseCommand();
    }

    private void initializeBaseCommand() {
        String licenseNumber = csvRecord.get(HeaderConstants.LICENSE_NUMBER);
        String fishingCertificateId = csvRecord.get(HeaderConstants.CERTIFICATE_ID);

        if (isCellDefined(licenseNumber)) {
            LicenseType licenseType = csvDomainCreationService.buildLicenseType(csvRecord);
            switch (licenseType) {
                case REGULAR -> commands.add(csvCommandCreationService.toDigitizeFishingLicenseCommand(csvRecord));
                case VACATION -> commands.add(csvCommandCreationService.toCreateVacationLicenseCommand(csvRecord));
                case LIMITED -> commands.add(csvCommandCreationService.toCreateLimitedlicenseCommand(csvRecord));
                default -> throw new NotImplementedException("License type LIMITED is not implemented yet.");
            }
        } else if (isCellDefined(fishingCertificateId)) {
            commands.add(csvCommandCreationService.toCreateFishingCertificateCommand(csvRecord));
        } else {
            commands.add(csvCommandCreationService.toCreatePersonCommand(csvRecord));
        }
    }

    public CsvCommandChainAssembler addBanCommands() {
        String banUntil = csvRecord.get(HeaderConstants.BAN_UNTIL);
        String banFrom = csvRecord.get(HeaderConstants.BAN_FROM);

        if (!isCellDefined(banFrom)) {
            return this;
        }

        boolean isJurisdictionSet = commands.stream()
                .anyMatch(command -> command instanceof DigitizeRegularLicenseCommand ||
                        command instanceof MoveJurisdictionCommand ||
                        command instanceof CreateLimitedLicenseCommand);

        if (!isJurisdictionSet) {
            commands.add(csvCommandCreationService.toMoveJurisdictionCommand(csvRecord));
        }

        if (isCellDefined(banUntil)) {
            commands.add(csvCommandCreationService.toCreateLimitedBanCommand(csvRecord));
        } else {
            commands.add(csvCommandCreationService.toCreateIndefiniteBanCommand(csvRecord));
        }

        return this;
    }

    public List<Record> assemble() {
        return commands;
    }

    private boolean isCellDefined(String cellValue) {
        return cellValue != null && !cellValue.isEmpty() && !cellValue.trim().equals("null");
    }
}