package de.adesso.fischereiregister.registerservice.domain.mapper.response;

import de.adesso.fischereiregister.core.commands.results.PersonCommandResult;
import de.adesso.fischereiregister.registerservice.domain.mapper.DocumentMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.JurisdictionMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.PersonMapper;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.openapitools.model.CreatePersonResponse;

@Mapper(componentModel = "default", uses = {DocumentMapper.class, JurisdictionMapper.class, PersonMapper.class})
public interface CreatePersonResponseMapper {
    CreatePersonResponseMapper INSTANCE = Mappers.getMapper(CreatePersonResponseMapper.class);

    CreatePersonResponse toResponse(PersonCommandResult commandResult);
}
