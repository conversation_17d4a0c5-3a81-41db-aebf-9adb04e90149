package de.adesso.fischereiregister.registerservice.admin;

import lombok.Builder;
import lombok.Getter;
import org.axonframework.messaging.MetaData;

import java.lang.reflect.Type;
import java.time.Instant;

@Getter
@Builder
public class EventDataDto {
    private MetaData metaData;
    private Type payloadType;
    private Object payload;
    private Instant timestamp;
    private String aggregateIdentifier;
}
