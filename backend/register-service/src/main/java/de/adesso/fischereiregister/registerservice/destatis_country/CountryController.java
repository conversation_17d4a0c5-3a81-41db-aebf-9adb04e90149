package de.adesso.fischereiregister.registerservice.destatis_country;

import de.adesso.fischereiregister.registerservice.domain.mapper.CountryMapper;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class CountryController implements api.NationalitiesApi {

    private final CountryApplicationService countryApplicationService;

    public CountryController(CountryApplicationService countryApplicationService) {
        this.countryApplicationService = countryApplicationService;
    }

    @Override
    public ResponseEntity<?> nationalityControllerGetAll() {
        return ResponseEntity.ok(CountryMapper.INSTANCE.toNationalities(countryApplicationService.getAllCountryInformationData()));
    }
}
