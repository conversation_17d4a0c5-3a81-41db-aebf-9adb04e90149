package de.adesso.fischereiregister.registerservice.domain.mapper;

import de.adesso.fischereiregister.registerservice.statistics.bans_statistics.BansStatistics;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "default")
public interface BansStatisticsMapper {

    BansStatisticsMapper INSTANCE = Mappers.getMapper(BansStatisticsMapper.class);

    List<org.openapitools.model.BansStatistics> bansStatisticsListToApiBansStatisticsList(List<BansStatistics> source);
}
