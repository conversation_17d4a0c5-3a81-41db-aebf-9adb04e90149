package de.adesso.fischereiregister.registerservice.domain.mapper;

import de.adesso.fischereiregister.core.model.Jurisdiction;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.openapitools.model.FederalStateAbbreviation;

@Mapper(componentModel = "default")
public interface JurisdictionMapper {

    JurisdictionMapper INSTANCE = Mappers.getMapper(JurisdictionMapper.class);

    default org.openapitools.model.Jurisdiction toJurisdictionApi(Jurisdiction jurisdiction) {
        if (jurisdiction == null || jurisdiction.getFederalState() == null) {
            return null;
        }
        FederalStateAbbreviation stateAbbreviation = FederalStateAbbreviation.fromValue(jurisdiction.getFederalState());
        return new org.openapitools.model.Jurisdiction(stateAbbreviation);
    }
}
