package de.adesso.fischereiregister.registerservice.security.model;

import lombok.Getter;
import lombok.Setter;

/**
 * Die Adresse der Behörde zu der der Mitarbeiter gehört: zB.
 *
 * office:          "Fischereibehörde Kiel"
 * street:          "Fleethörn"
 * streetNumber:    "29-31"
 * city:            "Kiel"
 * postcode:        "24103"
 */
@Getter
@Setter
public class OfficeAddress {
    private String office;
    private String street;
    private String streetNumber;
    private String postcode;
    private String city;

    public OfficeAddress(String office, String street, String streetNumber, String postcode, String city) {
        this.office = office;
        this.street = street;
        this.streetNumber = streetNumber;
        this.postcode = postcode;
        this.city = city;
    }

    @Override
    public String toString() {
        return String.format("%s %s %s %s %s", office, street, streetNumber, postcode, city);
    }
}
