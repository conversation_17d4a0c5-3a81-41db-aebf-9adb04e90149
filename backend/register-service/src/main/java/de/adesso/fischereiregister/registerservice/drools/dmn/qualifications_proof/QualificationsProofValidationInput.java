package de.adesso.fischereiregister.registerservice.drools.dmn.qualifications_proof;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;

import java.time.LocalDate;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class QualificationsProofValidationInput {
    private LocalDate legislativeValidityDate; // DMN Table input: Gesetzliche Gültigkeit (date)
    private Integer age; // alter der beantragenden person

    public QualificationsProofValidationInput(LocalDate legislativeValidityDate, Integer age) {
        this.legislativeValidityDate = legislativeValidityDate;
        this.age = age;
    }
}
