package de.adesso.fischereiregister.registerservice.import_testdata.number_services;

import de.adesso.fischereiregister.registerservice.certificate_numbers_view.CertificateNumbersViewRepository;
import de.adesso.fischereiregister.registerservice.certificate_numbers_view.CertificateNumbersViewServiceImpl;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.UUID;

@Service
@Profile({"dev", "localdev", "test", "stage"})
public class TestDataImportCertificateNumbersViewServiceImpl extends CertificateNumbersViewServiceImpl {

    private final HashMap<UUID, String> fishingCertificateNumbers = new HashMap<>();

    public TestDataImportCertificateNumbersViewServiceImpl(CertificateNumbersViewRepository repository) {
        super(repository);
    }

    /**
     * Adds a single fishing license number to the queue.
     */
    public void addFishingCertificateNumber(UUID registerEntryId, String newNumber) {
        if(newNumber != null && !newNumber.isEmpty()) {
            this.fishingCertificateNumbers.put(registerEntryId, newNumber);
        }
    }

    @Override
    public String createNewAvailableFishingCertificateNumber(UUID registerEntryId) {
        if(this.fishingCertificateNumbers.containsKey(registerEntryId)) {
            return this.fishingCertificateNumbers.remove(registerEntryId);
        }else{
            return this.createNewAvailableFishingCertificateNumber();
        }
    }
}
