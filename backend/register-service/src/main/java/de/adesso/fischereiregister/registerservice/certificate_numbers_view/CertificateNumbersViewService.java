package de.adesso.fischereiregister.registerservice.certificate_numbers_view;

import de.adesso.fischereiregister.core.model.type.QualificationsProofType;

import java.util.UUID;

/**
 * Service interface for handling operations related to
 * {@link CertificateNumbersView}.
 */
public interface CertificateNumbersViewService {

	void createCertificateNumbersView(UUID registerEntryId, String fishingCertificateId, QualificationsProofType type);

	String createNewAvailableFishingCertificateNumber(UUID registerEntryId);
}
