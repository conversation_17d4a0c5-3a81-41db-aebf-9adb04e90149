package de.adesso.fischereiregister.registerservice.domain.mapper.response;

import de.adesso.fischereiregister.core.commands.results.JurisdictionCommandResult;
import de.adesso.fischereiregister.registerservice.domain.mapper.DocumentMapper;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.openapitools.model.JurisdictionResponse;

@Mapper(componentModel = "default", uses = {DocumentMapper.class})
public interface JurisdictionResponseMapper {

    JurisdictionResponseMapper INSTANCE = Mappers.getMapper(JurisdictionResponseMapper.class);

    JurisdictionResponse toResponse(JurisdictionCommandResult commandResult);

}
