package de.adesso.fischereiregister.registerservice.security;

import de.adesso.fischereiregister.core.model.user.UserDetails;
import de.adesso.fischereiregister.core.model.user.UserRole;
import de.adesso.fischereiregister.registerservice.security.model.OfficeAddress;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.NotImplementedException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AnonymousAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
class UserDetailsServiceImpl implements UserDetailsService {

    private static final String UNSUPPORTED_AUTH_TYPE_ERROR_MESSAGE = "Unsupported authentication type: ";

    @Autowired
    private String federalStateIdentifier;
    @Autowired
    private String idClaimIdentifier;
    @Autowired
    private String examinationClaimIdentifier;
    @Autowired
    private String issuerClaimIdentifier;
    @Autowired
    private String officeAddressClaimIdentifier;
    @Autowired
    public SecurityProperties securityProperties;

    @Override
    public String getFederalState() {
        return getJWTAuthentication()
                .flatMap(jwtAuth -> getJwtClaim(federalStateIdentifier, jwtAuth))
                .orElse("");
    }

    @Override
    public Optional<UserDetails> getUserDetails() {
        return getUserId().map(userId -> new UserDetails(
                userId,
                getFederalState(),
                getOffice().orElse(null),
                getParsedOfficeAddress().orElse(null),
                getCertificationIssuer().orElse(null),
                getUserRoles()
        ));
    }

    @Override
    public Optional<String> getUserId() {
        return getJWTAuthentication().flatMap(jwtAuth -> getJwtClaim(idClaimIdentifier, jwtAuth));
    }

    @Override
    public Optional<String> getCertificationIssuer() {
        return getJWTAuthentication()
                .map(jwtAuth -> getJwtClaims(examinationClaimIdentifier, jwtAuth)) // Returns a map, never null
                .map(claims -> claims.get(issuerClaimIdentifier)) // Get the issuer claim value
                .filter(Objects::nonNull) // Filter out null values
                .map(Object::toString); // Convert to String
    }

    @Override
    public Optional<String> getOffice() {
        return getOfficeAddress().map(OfficeAddress::getOffice);
    }

    @Override
    public Optional<String> getParsedOfficeAddress() {
        return getOfficeAddress().map(OfficeAddress::toString);
    }

    private Optional<JwtAuthenticationToken> getJWTAuthentication() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

        if (authentication instanceof JwtAuthenticationToken jwtAuth) {
            return Optional.of(jwtAuth);
        } else if (authentication == null || authentication instanceof AnonymousAuthenticationToken) {
            log.warn("There was an attempt to get the User Authentication, but it couldn't be accessed");
            return Optional.empty();
        } else {
            throw new NotImplementedException(UNSUPPORTED_AUTH_TYPE_ERROR_MESSAGE + authentication.getClass());
        }
    }

    private Optional<String> getJwtClaim(String claimIdentifier, JwtAuthenticationToken authentication) {
        return Optional.ofNullable(((Jwt) authentication.getPrincipal()).getClaimAsString(claimIdentifier));
    }

    private Map<String, Object> getJwtClaims(String claimIdentifier, JwtAuthenticationToken authentication) {
        Map<String, Object> claims = ((Jwt) authentication.getPrincipal()).getClaimAsMap(claimIdentifier);
        return claims != null ? claims : Collections.emptyMap(); // Return empty map if null
    }

    private Optional<OfficeAddress> getOfficeAddress() {
        return getJWTAuthentication().flatMap(jwtAuth -> {
            Map<String, Object> officeAddressMap = getJwtClaims(officeAddressClaimIdentifier, jwtAuth);

            if (officeAddressMap.isEmpty()) {
                return Optional.empty(); // Return empty if the office address map is empty
            }

            // Build OfficeAddress
            return Optional.of(new OfficeAddress(
                    (String) officeAddressMap.get("office"),
                    (String) officeAddressMap.get("street"),
                    (String) officeAddressMap.get("streetNumber"),
                    (String) officeAddressMap.get("postcode"),
                    (String) officeAddressMap.get("city")
            ));
        });
    }

    private Collection<UserRole> getUserRoles() {
        return getJWTAuthentication().map(authentication -> {
            List<String> relevantRoles = UserRole.ALL_ROLES.stream().map(UserRole::getValue).toList();
            return authentication.getAuthorities().stream()
                    .map(GrantedAuthority::getAuthority)
                    .map(role -> role.substring(securityProperties.getPrefixRealmRole().length()))
                    .filter(relevantRoles::contains)
                    .map(UserRole::valueOf)
                    .collect(Collectors.toSet());
        }).orElse(Collections.emptySet());
    }
}
