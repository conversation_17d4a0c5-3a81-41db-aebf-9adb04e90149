package de.adesso.fischereiregister.registerservice.domain.mapper;

import de.adesso.fischereiregister.core.model.LimitedLicenseApproval;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "default")
public interface LimitedLicenseApprovalMapper {

    LimitedLicenseApprovalMapper INSTANCE = Mappers.getMapper(LimitedLicenseApprovalMapper.class);

    @Mapping(target = "createdAt", source = "createdAt")
    LimitedLicenseApproval toDomainObject(org.openapitools.model.LimitedLicenseApproval dto);
}
