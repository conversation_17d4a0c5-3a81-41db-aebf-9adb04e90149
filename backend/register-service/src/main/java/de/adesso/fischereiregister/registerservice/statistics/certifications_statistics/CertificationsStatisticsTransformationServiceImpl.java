package de.adesso.fischereiregister.registerservice.statistics.certifications_statistics;

import de.adesso.fischereiregister.view.certifications_statistics.persistence.CertificationsStatisticsView;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Implementation of the CertificationsStatisticsTransformationService.
 * Transforms certification statistics view objects into domain model objects.
 */
@Service
@Slf4j
public class CertificationsStatisticsTransformationServiceImpl implements CertificationsStatisticsTransformationService {

    @Override
    public List<CertificationsStatistics> transformToCertificationsStatistics(List<CertificationsStatisticsView> statisticsViews, List<Integer> yearsToQuery) {
        try {
            // Extract all unique issuers from the provided views
            Set<String> allIssuers = statisticsViews.stream()
                    .map(CertificationsStatisticsView::getIssuer)
                    .collect(Collectors.toSet());

            // Group statistics by year
            Map<Integer, List<CertificationsStatisticsView>> statsByYear = statisticsViews.stream()
                    .collect(Collectors.groupingBy(CertificationsStatisticsView::getYear));

            // Transform each requested year's statistics
            List<CertificationsStatistics> result = new ArrayList<>();
            for (Integer year : yearsToQuery) {
                List<CertificationsStatisticsView> yearStats = statsByYear.get(year);
                List<CertificationsStatisticsDataEntry> dataEntries = new ArrayList<>();

                // Group existing data by issuer
                Map<String, Integer> countsByIssuer = new HashMap<>();
                if (yearStats != null && !yearStats.isEmpty()) {
                    countsByIssuer = yearStats.stream()
                            .collect(Collectors.groupingBy(
                                    CertificationsStatisticsView::getIssuer,
                                    Collectors.summingInt(CertificationsStatisticsView::getCount)
                            ));
                }

                // Create data entries for all issuers found in the views (ensuring consistent offices across all years)
                for (String issuer : allIssuers) {
                    int count = countsByIssuer.getOrDefault(issuer, 0);
                    dataEntries.add(new CertificationsStatisticsDataEntry(issuer, count));
                }

                // Sort data entries by issuer name for consistent ordering
                dataEntries.sort(Comparator.comparing(CertificationsStatisticsDataEntry::issuer));

                // Create statistics object for this year
                result.add(new CertificationsStatistics(year, dataEntries));
            }

            // Sort the result by year in descending order
            result.sort(Comparator.comparingInt(CertificationsStatistics::year).reversed());

            return result;
        } catch (Exception e) {
            log.error("Error transforming certification statistics views: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to transform certification statistics views", e);
        }
    }
}
