package de.adesso.fischereiregister.registerservice.drools.dmn.license.configuration.input;

import com.fasterxml.jackson.annotation.JsonValue;

public enum DmnLicenseType {
    REGULAR("FISCHEREISCHEIN"),
    VACATION("URLAUBE<PERSON>FI<PERSON>H<PERSON>EISCHEIN"),
    LIMITED("SONDERFISCHEREISCHEIN");

    private String value;

    DmnLicenseType(String value) {
        this.value = value;
    }

    @JsonValue
    public String getValue() {
        return this.value;
    }

    @Override
    public String toString() {
        return String.valueOf(this.value);
    }
}
