package de.adesso.fischereiregister.registerservice.drools.dmn.license.validation.output;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@Setter
public class LicenseValidationOutput {

    private String errorMessage;

    public LicenseValidationOutput(Map<String, Object> result) {
        this.errorMessage = (String) result.get("Fehlermeldung");
    }

}