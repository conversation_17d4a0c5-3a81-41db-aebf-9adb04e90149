package de.adesso.fischereiregister.registerservice.fishing_license;

import de.adesso.fischereiregister.core.commands.CreateLimitedLicenseCommand;
import de.adesso.fischereiregister.core.commands.CreateRegularLicenseCommand;
import de.adesso.fischereiregister.core.commands.CreateVacationLicenseCommand;
import de.adesso.fischereiregister.core.commands.DigitizeRegularLicenseCommand;
import de.adesso.fischereiregister.core.commands.ExtendLicenseCommand;
import de.adesso.fischereiregister.core.commands.results.DigitizeRegularLicenseCommandResult;
import de.adesso.fischereiregister.core.commands.results.LicenseCommandResult;
import de.adesso.fischereiregister.core.exceptions.CommandResultMismatchException;
import de.adesso.fischereiregister.core.model.Fee;
import de.adesso.fischereiregister.core.model.LimitedLicenseApproval;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.QualificationsProof;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.ValidityPeriod;
import de.adesso.fischereiregister.core.model.consent.ConsentInfo;
import de.adesso.fischereiregister.core.model.consent.LimitedLicenseConsentInfo;
import de.adesso.fischereiregister.core.model.user.UserDetails;
import de.adesso.fischereiregister.registerservice.domain.mapper.ConsentInfoMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.FeeMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.LimitedLicenseApprovalMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.LimitedLicenseConsentInfoMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.PersonMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.QualificationsProofMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.TaxMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.ValidityPeriodMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.response.CreateFishingLicenseResponseMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.response.CreateLimitedFishingLicenseResponseMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.response.DigitizeFishingLicenseResponseMapper;
import de.adesso.fischereiregister.registerservice.fishing_license.mapper.FishingLicenseValidationResultMapper;
import de.adesso.fischereiregister.registerservice.security.UserDetailsService;
import de.adesso.fischereiregister.registerservice.validate_fishing_license.ValidateFishingLicenseService;
import de.adesso.fischereiregister.registerservice.validation.ValidationResponseDto;
import de.adesso.fischereiregister.utils.HashUtils;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.axonframework.commandhandling.gateway.CommandGateway;
import org.openapitools.model.CreateFishingLicenseResponse;
import org.openapitools.model.CreateLimitedFishingLicenseRequest;
import org.openapitools.model.CreateLimitedLicenseResponse;
import org.openapitools.model.CreateRegularFishingLicenseRequest;
import org.openapitools.model.CreateVacationFishingLicenseRequest;
import org.openapitools.model.DigitizeFishingLicenseRequest;
import org.openapitools.model.DigitizeFishingLicenseResponse;
import org.openapitools.model.ExtendFishingLicenseRequest;
import org.openapitools.model.ValidationResponse;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.util.UriComponentsBuilder;

import javax.validation.Valid;
import java.net.URI;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ExecutionException;

@RestController
@RequestMapping
@Slf4j
@AllArgsConstructor
public class FishingLicenseController implements api.FishingLicenseApi {

    private static final String LICENSE_LOCATION_PATH = "/api/register-entries/{registerEntryId}/fishing-licenses/{licenseNumber}";

    private final CommandGateway commandGateway;
    private final ValidateFishingLicenseService validateFishingLicenseService;
    private final UserDetailsService userDetailsService;

    @SneakyThrows
    @Override
    public ResponseEntity<?> digitizeControllerDigitize(DigitizeFishingLicenseRequest request) {
        final DigitizeRegularLicenseCommand command = createDigitizeFishingLicenseCommand(request);

        try {
            final Object result = commandGateway.send(command).get();

            if (result instanceof DigitizeRegularLicenseCommandResult commandResult) {
                final DigitizeFishingLicenseResponse response = DigitizeFishingLicenseResponseMapper.INSTANCE.toResponse(commandResult);

                final URI location = UriComponentsBuilder.fromPath("/api/register-entries/{registerEntryId}")
                        .buildAndExpand(command.registerId())
                        .toUri();

                return ResponseEntity.created(location).body(response);
            } else {
                throw new CommandResultMismatchException(DigitizeRegularLicenseCommandResult.class, result.getClass());
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Digitize Fishing License thread interrupted for RegisterID {}", command.registerId(), e);
            throw new IllegalStateException("InterruptedException for registerEntryId:" + command.registerId(), e);
        } catch (ExecutionException e) {
            Throwable cause = e.getCause();

            log.error("Digitize Fishing License thread ExecutionException for RegisterID {}", command.registerId(),
                    e);

            if (cause != null) {
                throw cause;
            } else {
                throw e;
            }
        }
    }

    private DigitizeRegularLicenseCommand createDigitizeFishingLicenseCommand(DigitizeFishingLicenseRequest request) {

        final Person person = PersonMapper.INSTANCE.toPerson(request.getPerson());
        final ConsentInfo consentInfo = ConsentInfoMapper.INSTANCE.toConsentInfo(request.getConsentInfo());
        final List<Fee> fees = FeeMapper.INSTANCE.toFees(request.getFees());
        final List<Tax> taxes = TaxMapper.INSTANCE.toTaxes(request.getTaxes());
        final List<Tax> payedTaxes = TaxMapper.INSTANCE.toTaxes(request.getPayedTaxes());
        final List<QualificationsProof> qualificationsProofs = QualificationsProofMapper.INSTANCE.toQualificationsProofs(
                request.getQualificationsProofs()
        );


        final UserDetails userDetails = getUserDetails();

        return new DigitizeRegularLicenseCommand(
                UUID.randomUUID(),
                HashUtils.gensalt(),
                person,
                fees,
                taxes,
                payedTaxes,
                qualificationsProofs,
                consentInfo,
                userDetails
        );
    }

    @SneakyThrows
    @Override
    public ResponseEntity<?> fishingLicenseRootControllerCreateVacation(CreateVacationFishingLicenseRequest request) {
        final CreateVacationLicenseCommand command = createCreateVacationLicenseCommand(request);

        try {
            final Object result = commandGateway.send(command).get();

            if (result instanceof LicenseCommandResult commandResult) {
                final CreateFishingLicenseResponse response = CreateFishingLicenseResponseMapper.INSTANCE.toResponse(commandResult);

                final URI location = UriComponentsBuilder.fromPath("/api/register-entries/{registerEntryId}")
                        .buildAndExpand(command.registerId())
                        .toUri();

                return ResponseEntity.created(location).body(response);
            } else {
                throw new CommandResultMismatchException(LicenseCommandResult.class, result.getClass());
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Create Person with Vacation License thread interrupted for RegisterID {}", command.registerId(), e);
            throw new IllegalStateException("InterruptedException for registerEntryId:" + command.registerId(), e);
        } catch (ExecutionException e) {
            Throwable cause = e.getCause();

            log.error("Create Person with Vacation License thread ExecutionException for RegisterID {}", command.registerId(), e);

            if (cause != null) {
                throw cause;
            } else {
                throw e;
            }
        }
    }

    private CreateVacationLicenseCommand createCreateVacationLicenseCommand(CreateVacationFishingLicenseRequest request) {
        return this .createCreateVacationLicenseCommand(UUID.randomUUID().toString(), request); // this gets called when creating a new aggregate
    }

    private CreateVacationLicenseCommand createCreateVacationLicenseCommand(String registerEntryId, CreateVacationFishingLicenseRequest request) {
        final ConsentInfo consentInfo = ConsentInfoMapper.INSTANCE.toConsentInfo(request.getConsentInfo());
        final Person person = PersonMapper.INSTANCE.toPerson(request.getPerson());
        final List<Fee> fees = FeeMapper.INSTANCE.toFees(request.getFees());
        final List<Tax> taxes = TaxMapper.INSTANCE.toTaxes(request.getTaxes());
        final ValidityPeriod validityPeriod = ValidityPeriodMapper.INSTANCE.toValidityPeriod(request.getValidityPeriod());

        final UserDetails userDetails = getUserDetails();

        return new CreateVacationLicenseCommand(
                UUID.fromString(registerEntryId),
                HashUtils.gensalt(),
                person,
                fees,
                taxes,
                consentInfo,
                validityPeriod,
                userDetails
        );
    }

    @Override
    public ResponseEntity<?> fishingLicenseRootControllerValidate(String identificationDocumentId, String hash, String licenseNumber) {

        log.info("Received request to validate fishing license with licenseNumber: {}, identificationDocumentId: {}, hash:{} ", licenseNumber, identificationDocumentId, hash);

        ValidationResponseDto validationResponseDto = validateRequiredFields(identificationDocumentId, hash);
        if (!validationResponseDto.getErrorNotes().isEmpty()) {
            log.warn("Request Validation failed with errors: {}", validationResponseDto.getErrorNotes());
            return ResponseEntity.badRequest().body(validationResponseDto);
        }

        try {
            ValidationResponse validationResponse = FishingLicenseValidationResultMapper.INSTANCE.toResponse(
                    validateFishingLicenseService.validateFishingLicense(licenseNumber, identificationDocumentId, hash)
                        );
            log.info("Fishing License Validation successful, returning response: {}", validationResponse);
            return ResponseEntity.ok(validationResponse);
        } catch (IllegalStateException ex) {
            log.error("Fishing License Validation unsuccessful, error response: {}", ex.getMessage());
            return ResponseEntity.notFound().build(); // HTTP 404
        } catch (Exception ex) {
            log.error("Unexpected Exception: {}", ex.getMessage());
            return ResponseEntity.badRequest().build(); // HTTP 400
        }
    }

    /**
     * In this exceptional case, the mandatory fields are validated in the application layer because no command is send.
     * Normally we use CommandValidators which will make a complete validation including the mandatory fields. The CommandValidators belong to the Core. An example command validator is:
     * de.adesso.fischereiregister.core.validation.CreatePersonCommandValidator
     *
     * @param identificationDocumentId required field
     * @param hash                     required field
     * @return ValidationResponse DTO object containing error messages if any of the above fields were missing.
     */
    private ValidationResponseDto validateRequiredFields(String identificationDocumentId, String hash) {
        ValidationResponseDto validationResponseDto = new ValidationResponseDto();

        if (identificationDocumentId == null || identificationDocumentId.isEmpty()) {
            validationResponseDto.addErrorNote("Missing required field: identificationDocumentId");
        }

        if (hash == null || hash.isEmpty()) {
            validationResponseDto.addErrorNote("Missing required field: hash");
        }

        return validationResponseDto;
    }

    @SneakyThrows
    @Override
    public ResponseEntity<?> fishingLicenseControllerCreateRegular(String registerEntryId, CreateRegularFishingLicenseRequest request) {
        final CreateRegularLicenseCommand command = createRegularFishingLicenseCommand(registerEntryId, request);

        try {
            final Object result = commandGateway.send(command).get();

            if (result instanceof LicenseCommandResult commandResult) {
                CreateFishingLicenseResponse response = CreateFishingLicenseResponseMapper.INSTANCE.toResponse(commandResult);

                final URI location = getLicenseLocation(registerEntryId, response.getFishingLicense().getNumber());

                return ResponseEntity.created(location).body(response);
            } else {
                throw new CommandResultMismatchException(LicenseCommandResult.class, result.getClass());
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error(" create fishing license thread interrupted for registerEntryId {} ", command.registerId(), e);
            throw new IllegalStateException("InterruptedException for registerEntryId: " + command.registerId(), e);
        } catch (ExecutionException e) {
            Throwable cause = e.getCause();
            log.error(" create fishing license thread ExecutionException for RegisterID {}", command.registerId(),
                    e);
            if (cause != null) {
                throw cause;
            } else {
                throw e;
            }
        }

    }

    private CreateRegularLicenseCommand createRegularFishingLicenseCommand(String registerEntryId, CreateRegularFishingLicenseRequest request) {
        ConsentInfo consentInfo = ConsentInfoMapper.INSTANCE.toConsentInfo(request.getConsentInfo());
        Person person = PersonMapper.INSTANCE.toPerson(request.getPerson());
        List<Fee> fees = FeeMapper.INSTANCE.toFees(request.getFees());
        List<Tax> taxes = TaxMapper.INSTANCE.toTaxes(request.getTaxes());

        String salt = HashUtils.gensalt();

        final UserDetails userDetails = getUserDetails();

        return new CreateRegularLicenseCommand(
                UUID.fromString(registerEntryId),
                salt,
                consentInfo,
                person,
                fees,
                taxes,
                userDetails
        );
    }


    @SneakyThrows
    @Override
    public ResponseEntity<?> fishingLicenseControllerCreateVacation(String registerEntryId, CreateVacationFishingLicenseRequest request) {
        final CreateVacationLicenseCommand command = createCreateVacationLicenseCommand(registerEntryId, request);

        try {
            final Object result = commandGateway.send(command).get();

            if (result instanceof LicenseCommandResult commandResult) {
                CreateFishingLicenseResponse response = CreateFishingLicenseResponseMapper.INSTANCE.toResponse(commandResult);

                final URI location = getLicenseLocation(registerEntryId, response.getFishingLicense().getNumber());

                return ResponseEntity.created(location).body(response);
            } else {
                throw new CommandResultMismatchException(LicenseCommandResult.class, result.getClass());
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("create vacation fishing license thread interrupted for registerEntryId {} ", command.registerId(), e);
            throw new IllegalStateException("InterruptedException for registerEntryId: " + command.registerId(), e);
        } catch (ExecutionException e) {
            Throwable cause = e.getCause();
            log.error("create vacation fishing license thread ExecutionException for RegisterID {}", command.registerId(),
                    e);
            if (cause != null) {
                throw cause;
            } else {
                throw e;
            }
        }
    }

    @SneakyThrows
    @Override
    public ResponseEntity<?> fishingLicenseControllerExtend(String registerEntryId, String licenseNumber, ExtendFishingLicenseRequest request) {
        final ExtendLicenseCommand command = createExtendLicenseCommand(registerEntryId, licenseNumber, request);

        try {
            final Object result = commandGateway.send(command).get();

            if (result instanceof LicenseCommandResult commandResult) {
                CreateFishingLicenseResponse response = CreateFishingLicenseResponseMapper.INSTANCE.toResponse(commandResult);

                final URI location = getLicenseLocation(registerEntryId, licenseNumber);

                return ResponseEntity.created(location).body(response);
            } else {
                throw new CommandResultMismatchException(ExtendLicenseCommand.class, result.getClass());
            }
        } catch (InterruptedException e) {
            log.error("Failed to extend a license", e);
            Thread.currentThread().interrupt();
            throw e;
        } catch (ExecutionException e) {
            Throwable cause = e.getCause();

            log.error("Failed to extend a license", e);

            if (cause != null) {
                throw cause;
            } else {
                throw e;
            }
        }
    }

    private ExtendLicenseCommand createExtendLicenseCommand(String registerEntryId, String licenseNumber, ExtendFishingLicenseRequest request) {
        final ConsentInfo consentInfo = ConsentInfoMapper.INSTANCE.toConsentInfo(request.getConsentInfo());
        final Person person = PersonMapper.INSTANCE.toPerson(request.getPerson());
        final List<Fee> fees = FeeMapper.INSTANCE.toFees(request.getFees());
        final List<Tax> taxes = TaxMapper.INSTANCE.toTaxes(request.getTaxes());
        final ValidityPeriod validityPeriod = ValidityPeriodMapper.INSTANCE.toValidityPeriod(request.getValidityPeriod());

        final UserDetails userDetails = getUserDetails();

        return new ExtendLicenseCommand(
                UUID.fromString(registerEntryId),
                licenseNumber,
                HashUtils.gensalt(),
                person,
                fees,
                taxes,
                validityPeriod,
                consentInfo,
                userDetails
        );
    }

    private UserDetails getUserDetails() {
        return userDetailsService.getUserDetails()
                .orElseThrow(() -> {
                    log.error("Failed to retrieve user details: User details are missing or corrupted.");
                    return new IllegalStateException("The User Auth Information is missing or corrupted");
                });
    }

    private URI getLicenseLocation(String registerEntryId, String licenseNumber) {
        return UriComponentsBuilder.fromPath(LICENSE_LOCATION_PATH)
                .buildAndExpand(registerEntryId, licenseNumber)
                .toUri();
    }

    @Override
    @SneakyThrows
    public ResponseEntity<?> fishingLicenseControllerCreateLimited(String registerEntryId, @Valid CreateLimitedFishingLicenseRequest request) {
        CreateLimitedLicenseCommand command = createLimitedLicenseCommand(UUID.fromString(registerEntryId), request);
        try {
            final Object result = commandGateway.send(command).get();

            if (result instanceof LicenseCommandResult commandResult) {
                CreateLimitedLicenseResponse response = CreateLimitedFishingLicenseResponseMapper.INSTANCE.toResponse(commandResult);

                final URI location = getLicenseLocation(registerEntryId, response.getFishingLicense().getNumber());

                return ResponseEntity.created(location).body(response);
            } else {
                throw new CommandResultMismatchException(CreateLimitedLicenseCommand.class, result.getClass());
            }
        } catch (InterruptedException e) {
            log.error("Failed to create a limited license", e);
            Thread.currentThread().interrupt();
            throw e;
        } catch (ExecutionException e) {
            Throwable cause = e.getCause();

            log.error("Failed to create a limited license", e);

            if (cause != null) {
                throw cause;
            } else {
                throw e;
            }
        }
    }

    private CreateLimitedLicenseCommand createLimitedLicenseCommand(UUID registerEntryId, CreateLimitedFishingLicenseRequest request) {
        final LimitedLicenseConsentInfo consentInfo = LimitedLicenseConsentInfoMapper.INSTANCE.toLimitedLicenseConsentInfo(request.getConsentInfo());
        final Person person = PersonMapper.INSTANCE.toPerson(request.getPerson());
        final List<Fee> fees = FeeMapper.INSTANCE.toFees(request.getFees());
        final List<Tax> taxes = TaxMapper.INSTANCE.toTaxes(request.getTaxes());
        final ValidityPeriod validityPeriod = ValidityPeriodMapper.INSTANCE.toValidityPeriod(request.getValidityPeriod());
        final LimitedLicenseApproval limitedLicenseApproval = LimitedLicenseApprovalMapper.INSTANCE.toDomainObject(request.getLimitedLicenseApproval());

        final UserDetails userDetails = getUserDetails();
        return new CreateLimitedLicenseCommand(
                registerEntryId,
                HashUtils.gensalt(),
                consentInfo,
                person,
                fees,
                taxes,
                validityPeriod,
                limitedLicenseApproval,
                userDetails
        );
    }

    @Override
    public ResponseEntity<?> fishingLicenseRootControllerCreateLimited(@Valid CreateLimitedFishingLicenseRequest createLimitedFishingLicenseRequest) {
        return fishingLicenseControllerCreateLimited(UUID.randomUUID().toString(), createLimitedFishingLicenseRequest);
    }
}
