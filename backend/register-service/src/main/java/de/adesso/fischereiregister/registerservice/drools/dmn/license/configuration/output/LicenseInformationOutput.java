package de.adesso.fischereiregister.registerservice.drools.dmn.license.configuration.output;

import de.adesso.fischereiregister.core.model.type.LicenseType;
import lombok.Getter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@Getter
public class LicenseInformationOutput {
    public static final String GEBUEHR_IN_EURO = "Gebühr in €";
    public static final String SPAETESTES_BESTELLDATUM = "spätestes Bestelldatum";
    public static final String GUELTIGKEITSTYP = "Gültigkeitstyp";
    public static final String GUELTIGKEITSOPTIONEN = "Gültigkeitsoptionen";
    public static final String IST_VERLAENGERBAR = "Ist verlängerbar?";
    public static final String IST_VERFUEGBAR = "Ist verfügbar?";
    public static final String IST_BEFRISTBAR = "Ist befristbar?";

    private LicenseType licenseType; // Lizenztyp, which is not an output but has to be set later :-(
    private final DmnValidityInformation durationType; // Gültigkeitstyp
    private final List<BigDecimal> durationOptions; //Gültigkeitsoptionen
    private final int feeAmount; // Gebühr in €
    private final LocalDate latestPossibleIssueDate; // spätestes Bestelldatum
    private final boolean isExtendable; // Ist verlängerbar?
    private final boolean isAvailable; // "Ist verfügbar?"
    private final boolean isTimeLimitable; // Ist befristbar?

    public LicenseInformationOutput(Map<String, Object> result) {
        this.feeAmount = ((BigDecimal) result.get(GEBUEHR_IN_EURO)).intValue();
        this.latestPossibleIssueDate = ((LocalDate) result.get(SPAETESTES_BESTELLDATUM));
        this.durationType = DmnValidityInformation.fromValue((String) result.get(GUELTIGKEITSTYP));
        this.durationOptions = (java.util.ArrayList) result.get(GUELTIGKEITSOPTIONEN);
        this.isExtendable = (boolean) result.get(IST_VERLAENGERBAR);
        this.isAvailable = (boolean) result.get(IST_VERFUEGBAR);
        this.isTimeLimitable = (boolean) result.get(IST_BEFRISTBAR);
    }

    public void setLicenseType(LicenseType licenseType) {
        this.licenseType = licenseType;
    }
}
