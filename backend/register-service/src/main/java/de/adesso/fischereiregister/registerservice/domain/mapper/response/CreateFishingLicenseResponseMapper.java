package de.adesso.fischereiregister.registerservice.domain.mapper.response;

import de.adesso.fischereiregister.core.commands.results.LicenseCommandResult;
import de.adesso.fischereiregister.registerservice.domain.mapper.DocumentMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.FishingLicenseMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.PersonMapper;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.openapitools.model.CreateFishingLicenseResponse;

@Mapper(componentModel = "default", uses = {DocumentMapper.class, FishingLicenseMapper.class, PersonMapper.class})
public interface CreateFishingLicenseResponseMapper {
    CreateFishingLicenseResponseMapper INSTANCE = Mappers.getMapper(CreateFishingLicenseResponseMapper.class);

    CreateFishingLicenseResponse toResponse(LicenseCommandResult commandResult);
}
