package de.adesso.fischereiregister.registerservice.axon;

import de.adesso.fischereiregister.migrations.ports.EventsReplayPort;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.axonframework.config.EventProcessingConfiguration;
import org.axonframework.eventhandling.TrackingEventProcessor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
@Slf4j
public class EventsReplayAdapter implements EventsReplayPort {
    private final EventProcessingConfiguration eventProcessingConfiguration;

    public void replayAllEvents() {
        log.info("Starting replay of all events...");

        eventProcessingConfiguration.eventProcessors().forEach((name, processor) -> {
            if (processor instanceof TrackingEventProcessor trackingEventProcessor) {
                replayEventsForProcessor(name, trackingEventProcessor);
            }
        });
    }

    private void replayEventsForProcessor(String processorName, TrackingEventProcessor trackingEventProcessor) {
        try {
            log.info("Stopping processor: {}", processorName);
            trackingEventProcessor.shutDown();

            log.info("Resetting tracking tokens for processor: {}", processorName);
            trackingEventProcessor.resetTokens();

            log.info("Starting processor: {}", processorName);
            trackingEventProcessor.start();

            log.info("Successfully initiated replay for processor: {}", processorName);
        } catch (Exception e) {
            log.error("Failed to replay events for processor: {}", processorName, e);
            throw new RuntimeException("Event replay failed for processor: " + processorName, e);
        }
    }
}
