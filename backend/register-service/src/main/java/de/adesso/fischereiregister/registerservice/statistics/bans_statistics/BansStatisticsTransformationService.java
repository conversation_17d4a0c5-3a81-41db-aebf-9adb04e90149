package de.adesso.fischereiregister.registerservice.statistics.bans_statistics;

import de.adesso.fischereiregister.view.bans_statistics.persistance.BansStatisticsView;

import java.util.List;

/**
 * Transforms {@link BansStatisticsView} objects into {@link BansStatistics} objects, grouped by year.
 */
public interface BansStatisticsTransformationService {

    /**
     * Converts a list of {@link BansStatisticsView} objects into a list of {@link BansStatistics} objects.
     * Returns entries for all requested years, filling missing data with zeroes.
     *
     * @param statisticsViews The list of raw statistical data to transform.
     * @param yearsToQuery The complete list of years that should be included in the response.
     * @return A list of transformed {@link BansStatistics} objects for all requested years.
     */
    List<BansStatistics> transformToBansStatistics(List<BansStatisticsView> statisticsViews, List<Integer> yearsToQuery);
}
