package de.adesso.fischereiregister.registerservice.validate_fishing_license;

import de.adesso.fischereiregister.core.model.Ban;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.ports.InspectorProtocolServicePort;
import de.adesso.fischereiregister.registerservice.identification_document_view.IdentificationDocumentView;
import de.adesso.fischereiregister.registerservice.identification_document_view.IdentificationDocumentViewService;
import de.adesso.fischereiregister.registerservice.register_entry_view.RegisterEntryViewService;
import de.adesso.fischereiregister.registerservice.security.HashingAdapter;
import de.adesso.fischereiregister.registerservice.security.UserDetailsService;
import de.adesso.fischereiregister.registerservice.tenant.TenantConfigurationService;
import de.adesso.fischereiregister.registerservice.validate_fishing_license.model.FishingLicenseValidationStatus;
import de.adesso.fischereiregister.registerservice.validate_fishing_license.model.FishingLicenseValidationResult;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

import static de.adesso.fischereiregister.core.model.type.LicenseType.REGULAR;


@Service
@AllArgsConstructor
class ValidateFishingLicenseServiceImpl implements ValidateFishingLicenseService {

    public static final String LICENSE_NOT_FOUND_NOTE = "validate_license.license_note";
    private final UserDetailsService userDetailsService;
    private final RegisterEntryViewService registerEntryViewService;
    private final IdentificationDocumentViewService identificationDocumentViewService;
    private final TenantConfigurationService tenantConfigurationService;
    private final HashingAdapter hashingAdapter;
    private final InspectorProtocolServicePort inspectorProtocolService;

    /**
     * Professional fisherman (Berufsfischer) = fishing license not existent, tax is paid (inspector has to do further checks)
     * here we now write a tenant specific note for the controll application
     *
     * <p>
     * LicenseStatus = BANNED when: person is banned
     * LicenseStatus = INVALID when: fishing license not existent or expired
     * LicenseStatus = VALID when: fishing licence existent && Not expired && person NOT banned
     */
    @Override
    public FishingLicenseValidationResult validateFishingLicense(String licenseNumber, String identificationDocumentId, String hash) {
        try {
            String federalState = userDetailsService.getFederalState();

            IdentificationDocumentView identificationDocumentView = identificationDocumentViewService.getIdentificationDocumentView(identificationDocumentId);
            RegisterEntry registerEntry = registerEntryViewService.findByRegisterId(identificationDocumentView.getRegisterId()).getData();

            checkHash(registerEntry, identificationDocumentView.getSalt(), hash);


            // here we have to check all documents which have a license to find a valid license
            Optional<FishingLicense> validFishingLicense = findValidLicense(registerEntry.getIdentificationDocuments(), federalState);

            createProtocolEntry(registerEntry.getRegisterId().toString());

            return buildValidationResponse(registerEntry, validFishingLicense, federalState);
        } catch (IllegalStateException e) {
            createProtocolEntry(null);
            throw e;
        }
    }

    private void createProtocolEntry(String registerEntryId) {
        inspectorProtocolService.createProtocolEntry(
                userDetailsService.getUserId().orElse(null),
                registerEntryId,
                userDetailsService.getFederalState(),
                LocalDate.now().atStartOfDay()
        );
    }

    private FishingLicenseValidationResult buildValidationResponse(RegisterEntry registerEntry, Optional<FishingLicense> validLicense, String federalState) {
        boolean isLicenseValid = validLicense.isPresent();
        boolean isTaxValid = hasValidTax(registerEntry.getTaxes(), federalState);
        boolean isPersonBanned = isBanned(registerEntry.getBan());
        final Person person = registerEntry.getPerson();

        return new FishingLicenseValidationResult(
                validLicense.map(FishingLicense::getNumber).orElse(null),
                LicenseType.REGULAR, // Always regular
                getLicenseStatus(isPersonBanned, isLicenseValid),
                validLicense.isPresent() ? "" : getTenantSpecificLicenseNotFoundNote(federalState),
                isTaxValid,
                "", // Hardcoded empty string
                person.getTitle() != null ? person.getTitle() : "",  // if null set as empty string
                person.getFirstname(),
                person.getLastname(),
                person.getBirthdate().toString()
        );
    }

    private String getTenantSpecificLicenseNotFoundNote(String federalState) {
        return tenantConfigurationService.getValue(FederalState.valueOf(federalState), LICENSE_NOT_FOUND_NOTE);
    }

    public Optional<FishingLicense> findValidLicense(List<IdentificationDocument> documents, String federalState) {
        for (IdentificationDocument identificationDocument : documents) {
            FishingLicense fishingLicense = identificationDocument.getFishingLicense();
            if (isLicenseValid(fishingLicense, federalState)) {
                return Optional.of(fishingLicense); // Found a valid license, return it
            }
        }
        return Optional.empty(); // No valid license found
    }

    private void checkHash(RegisterEntry registerEntry, String salt, String hash) {
        if (!hashMatches(registerEntry, salt, hash)) {
            throw new IllegalStateException("hash error");
        }
    }

    private boolean hasValidTax(List<Tax> taxes, String federalState) {
        if (!taxes.isEmpty()) {
            return taxes.stream().anyMatch(tax -> isTaxValid(tax, federalState));

        }
        return false;
    }

    private boolean isTaxValid(Tax tax, String federalState) {
        return tax.getFederalState().equals(federalState) &&
                tax.getValidFrom().isBefore(LocalDate.now()) &&
                (tax.getValidTo() == null || tax.getValidTo().isAfter(LocalDate.now()));
    }

    boolean hashMatches(RegisterEntry registerEntry, String salt, String hash) {
        String calculatedHash =
                hashingAdapter.calculateHash(registerEntry.getRegisterId(), registerEntry.getPerson(), salt);

        return calculatedHash.equals(hash);
    }

    private FishingLicenseValidationStatus getLicenseStatus(boolean isPersonBanned, boolean isLicenseValid) {
        if (isPersonBanned) {
            return FishingLicenseValidationStatus.BANNED;
        } else if (isLicenseValid) {
            return FishingLicenseValidationStatus.VALID;
        } else {
            return FishingLicenseValidationStatus.INVALID;
        }
    }

    public boolean isBanned(Ban ban) {
        // Check if the license has a Ban (Sperre) start Date
        if (ban != null && ban.getFrom() != null) {
            return ban.isCurrent();
        }
        // If there is no Ban start Date, the License has never been Banned, so it is
        // not banned
        return false;
    }

    private boolean isLicenseValid(FishingLicense fishingLicense, String targetFederalState) {
        if (fishingLicense == null) {
            return false; // null means invalid
        }
        // Return true if the license is not expired
        return isLicenseCurrentlyValidInFederalState(targetFederalState, fishingLicense);
    }

    public boolean isLicenseCurrentlyValidInFederalState(String federalState, FishingLicense fishingLicense) {
        if (REGULAR.equals(fishingLicense.getType())) {
            // the regular license is valid in all federal states and through the taxes payed it is valid in the given state,
            // this is checked here: ValidateFishingLicenseService.validateRegister
            return isLicenseCurrent(fishingLicense);
        } else {
            return federalState.equalsIgnoreCase(fishingLicense.getIssuingFederalState().toString()) && isLicenseCurrent(fishingLicense);
        }
    }

    private boolean isLicenseCurrent(FishingLicense fishingLicense) {
        return fishingLicense.getValidityPeriods().stream().anyMatch(
                validityPeriod ->
                        (LocalDate.now().isAfter(validityPeriod.getValidFrom()) || LocalDate.now().isEqual(validityPeriod.getValidFrom()))
                                &&
                                (validityPeriod.getValidTo() == null || LocalDate.now().isBefore(validityPeriod.getValidTo()) || LocalDate.now().isEqual(validityPeriod.getValidTo()))
        );
    }

}
