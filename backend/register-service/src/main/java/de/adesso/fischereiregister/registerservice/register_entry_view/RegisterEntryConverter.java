package de.adesso.fischereiregister.registerservice.register_entry_view;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Converter
@Component
public class RegisterEntryConverter implements AttributeConverter<RegisterEntry, String> {
	private final ObjectMapper objectMapper;

	@Autowired
	public RegisterEntryConverter(ObjectMapper mapper) {

		this.objectMapper = mapper;
	}

	@Override
	public String convertToDatabaseColumn(RegisterEntry attribute) throws RuntimeException {
		try {
			return objectMapper.writeValueAsString(attribute);
		} catch (JsonProcessingException e) {
			throw new RuntimeException("Failed to serialize RegisterEntry to JSON", e);
		}
	}

	@Override
	public RegisterEntry convertToEntityAttribute(String dbData) throws RuntimeException {
		try {
			return objectMapper.readValue(dbData, RegisterEntry.class);
		} catch (JsonProcessingException e) {
			throw new RuntimeException("Failed to deserialize JSON to RegisterEntry", e);
		}
	}

}
