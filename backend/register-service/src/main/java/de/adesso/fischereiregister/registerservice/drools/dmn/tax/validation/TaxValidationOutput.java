package de.adesso.fischereiregister.registerservice.drools.dmn.tax.validation;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;

import java.util.Map;

@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
public class TaxValidationOutput {

    private String errorMessage;

    public TaxValidationOutput(Map<String, Object> result) {
        this.errorMessage = (String) result.get("Fehlermeldung"); // Abgaben Regeln
    }

}