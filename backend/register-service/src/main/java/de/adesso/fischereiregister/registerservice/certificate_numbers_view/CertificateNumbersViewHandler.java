package de.adesso.fischereiregister.registerservice.certificate_numbers_view;

import de.adesso.fischereiregister.core.events.QualificationsProofCreatedEvent;
import jakarta.transaction.Transactional;
import lombok.AllArgsConstructor;
import org.axonframework.eventhandling.EventHandler;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class CertificateNumbersViewHandler {

	CertificateNumbersViewService certificateNumbersViewService;

	@EventHandler
	@Transactional
	void on(QualificationsProofCreatedEvent event)  {
		certificateNumbersViewService.createCertificateNumbersView(event.registerEntryId(), event.qualificationsProof().getFishingCertificateId(), event.qualificationsProof().getType());
	}
}
