package de.adesso.fischereiregister.registerservice.mail;

import de.adesso.fischereiregister.registerservice.fishing_license_export.model.RenderedContent;

import java.util.List;

public interface MailService {

	/**
	 * Sends an email with an attachment.
	 * Attachment should not be null.
	 */
	void sendMail(String to, String from, String subject, String text, List<RenderedContent> attachments);

}
