package de.adesso.fischereiregister.registerservice.identification_document_view;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface IdentificationDocumentViewRepository extends CrudRepository<IdentificationDocumentView, String> {

    Optional<IdentificationDocumentView> findByLicenseNumber(String licenseId);

    List<IdentificationDocumentView> findAllByLicenseNumber(String licenseId);

    @Query("SELECT i.licenseNumber FROM IdentificationDocumentView i")
    List<String> findAllNumbers();

    @Query("SELECT i.identificationDocumentId FROM IdentificationDocumentView i")
    List<String> findAllDocumentNumbers();

    Optional<IdentificationDocumentView> findByIdentificationDocumentId(String identificationDocumentId);

    IdentificationDocumentView[] findByRegisterId(UUID registerEntryId);
}
