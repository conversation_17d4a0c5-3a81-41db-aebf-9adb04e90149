package de.adesso.fischereiregister.registerservice.fishing_license;

import de.adesso.fischereiregister.core.commands.RejectLimitedLicenseApplicationCommand;
import de.adesso.fischereiregister.core.model.user.UserDetails;
import de.adesso.fischereiregister.registerservice.security.UserDetailsService;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.axonframework.commandhandling.gateway.CommandGateway;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.UUID;

@RestController
@RequestMapping
@Slf4j
@AllArgsConstructor
public class LimitedLicenseApplicationController implements api.LicenseApplicationsApi {

    private final UserDetailsService userDetailsService;
    private final CommandGateway commandGateway;


    @Override
    @SneakyThrows
    public ResponseEntity<?> limitedLicenseApplicationControllerRejectLimitedLicenseApplication(String registerEntryId) {
        log.info("Rejecting limited license application for register entry ID: {}", registerEntryId);

        final UserDetails userDetails = userDetailsService.getUserDetails()
                .orElseThrow(() -> {
                    log.error("Failed to retrieve user details: User details are missing or corrupted.");
                    return new IllegalStateException("The User Auth Information is missing or corrupted");
                });

        final RejectLimitedLicenseApplicationCommand command = new RejectLimitedLicenseApplicationCommand(
                UUID.fromString(registerEntryId),
                userDetails
        );

        try {
            commandGateway.send(command).get();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Reject Limited license application thread interrupted for RegisterID {}", command.registerEntryId(), e);
            throw new IllegalStateException("InterruptedException for registerEntryId:" + command.registerEntryId(), e);
        } catch (RuntimeException e) {
            Throwable cause = e.getCause();

            log.error("Failed to reject limited license application: {}", e.getMessage(), e);

            if (cause instanceof Exception causeAsException) {
                throw causeAsException;
            } else {
                throw e;
            }
        }

        return ResponseEntity.ok().build();
    }
}
