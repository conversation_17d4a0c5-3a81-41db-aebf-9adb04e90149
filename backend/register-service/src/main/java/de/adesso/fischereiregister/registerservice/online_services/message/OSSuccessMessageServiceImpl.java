package de.adesso.fischereiregister.registerservice.online_services.message;

import com.fasterxml.jackson.core.JsonProcessingException;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.IdentificationDocumentType;
import de.adesso.fischereiregister.message.service.OSInboxService;
import de.adesso.fischereiregister.message.service.model.OSAttachement;
import de.adesso.fischereiregister.message.service.model.OSMessage;
import de.adesso.fischereiregister.message.service.model.OSMessageWithAttachments;
import de.adesso.fischereiregister.registerservice.fishing_license_export.FishingLicenseExportService;
import de.adesso.fischereiregister.registerservice.fishing_license_export.model.RenderedContent;
import de.adesso.fischereiregister.registerservice.online_services.message.enums.OSMessageTemplate;
import lombok.AllArgsConstructor;
import lombok.Generated;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static de.adesso.fischereiregister.registerservice.online_services.message.enums.OSMessageTemplate.FISHING_TAX_CREATED_OS;
import static de.adesso.fischereiregister.registerservice.online_services.message.enums.OSMessageTemplate.REGULAR_FISHING_LICENSE_CREATED_OS;
import static de.adesso.fischereiregister.registerservice.online_services.message.enums.OSMessageTemplate.REGULAR_FISHING_LICENSE_REPLACED_OS;
import static de.adesso.fischereiregister.registerservice.online_services.message.enums.OSMessageTemplate.VACATION_FISHING_LICENSE_CREATED_OS;
import static de.adesso.fischereiregister.registerservice.online_services.message.enums.OSMessageTemplate.VACATION_FISHING_LICENSE_EXTENDED_OS;

@Service
@Slf4j
@AllArgsConstructor
@Generated
public class OSSuccessMessageServiceImpl implements OSSuccessMessageService {

    private final FishingLicenseExportService exportService;
    private final OSInboxService osInboxService;
    private final OSMessageTemplateResolutionService osMessageTemplateResolutionService;

    @Override
    public void handleRegularLicenseCreationSuccess(
            String inboxReference,
            FederalState federalState,
            Person person,
            UUID registerEntryId,
            String salt,
            List<IdentificationDocument> identificationDocumentList) throws JsonProcessingException {
        final OSMessageWithAttachments messageWithAttachments = createOsMessageWithAttachments(federalState, person, REGULAR_FISHING_LICENSE_CREATED_OS);
        final List<OSAttachement> pdfs = processOsAttachments(person, registerEntryId, salt, identificationDocumentList);

        osInboxService.sendMessageWithAttachments(inboxReference, messageWithAttachments,
                pdfs);
        log.info("OD license create message success send");
    }

    @Override
    public void handleVacationLicenseCreationSuccess(
            String inboxReference,
            FederalState federalState,
            Person person,
            UUID registerEntryId,
            String salt,
            List<IdentificationDocument> identificationDocumentList) throws JsonProcessingException {
        final OSMessageWithAttachments messageWithAttachments = createOsMessageWithAttachments(federalState, person, VACATION_FISHING_LICENSE_CREATED_OS);
        final List<OSAttachement> pdfs = processOsAttachments(person, registerEntryId, salt, removeTaxDocuments(identificationDocumentList));

        osInboxService.sendMessageWithAttachments(inboxReference, messageWithAttachments,
                pdfs);
        log.info("OD vacation message success send");
    }

    private OSMessageWithAttachments createOsMessageWithAttachments(
            FederalState federalState,
            Person person,
            OSMessageTemplate osMessageTemplate) {
        String subject = osMessageTemplateResolutionService.getSubject(federalState, osMessageTemplate);
        String body = osMessageTemplateResolutionService.getText(federalState, osMessageTemplate, person);
        String displayName = osMessageTemplateResolutionService.getDisplayName(federalState, osMessageTemplate);

        return new OSMessageWithAttachments(subject, displayName, body);
    }

    @Override
    public void handleLicenseReplacementSuccess(
            String inboxReference,
            Person person,
            UUID registerEntryId,
            FederalState federalState,
            String salt,
            List<IdentificationDocument> identificationDocumentList) throws JsonProcessingException {
        final OSMessageWithAttachments messageWithAttachments = createOsMessageWithAttachments(federalState, person, REGULAR_FISHING_LICENSE_REPLACED_OS);
        final List<OSAttachement> pdfs = processOsAttachments(person, registerEntryId, salt, identificationDocumentList);

        osInboxService.sendMessageWithAttachments(inboxReference, messageWithAttachments,
                pdfs);
        log.info("OD replace card message success send");
    }

    @Override
    public void handleTaxPaymentSuccess(String inboxReference, Person person, UUID registerEntryId, String salt, List<IdentificationDocument> identificationDocumentList) throws JsonProcessingException {
        FederalState federalState = identificationDocumentList.stream()
                .filter(doc -> doc.getTax() != null)
                .map(doc -> FederalState.valueOf(doc.getTax().getFederalState()))
                .findFirst().orElseThrow();

        final OSMessageWithAttachments messageWithAttachments = createOsMessageWithAttachments(federalState, person, FISHING_TAX_CREATED_OS);
        final List<OSAttachement> pdfs = processOsAttachments(person, registerEntryId, salt, identificationDocumentList);

        osInboxService.sendMessageWithAttachments(inboxReference, messageWithAttachments,
                pdfs);
        log.info("OD tax message success send");
    }

    @Override
    public void handleFishingLicenseExtendedSuccess(String inboxReference, Person person, UUID registerEntryId, String salt, List<IdentificationDocument> identificationDocuments) throws JsonProcessingException {
        String federalState = identificationDocuments.stream()
                .filter(doc -> doc.getTax() != null)
                .map(doc -> doc.getTax().getFederalState())
                .findFirst().orElseThrow();


        String subject = osMessageTemplateResolutionService.getSubject(FederalState.valueOf(federalState), VACATION_FISHING_LICENSE_EXTENDED_OS);
        String body = osMessageTemplateResolutionService.getText(FederalState.valueOf(federalState), VACATION_FISHING_LICENSE_EXTENDED_OS, person);
        String displayName = osMessageTemplateResolutionService.getDisplayName(FederalState.valueOf(federalState), VACATION_FISHING_LICENSE_EXTENDED_OS);

        final OSMessageWithAttachments messageWithAttachments = new OSMessageWithAttachments(subject, displayName, body);
        final List<OSAttachement> pdfs = processOsAttachments(person, registerEntryId, salt, removeTaxDocuments(identificationDocuments));

        osInboxService.sendMessageWithAttachments(inboxReference, messageWithAttachments,
                pdfs);
        log.info("OD extend vacation license success send");
    }

    @Override
    public void handleLimitedLinseApplicationPending(String inboxReference, FederalState federalState, Person person, UUID registerEntryId) {
        String subject = osMessageTemplateResolutionService.getSubject(federalState, OSMessageTemplate.LIMITED_FISHING_LICENSE_PENDING_OS);
        String body = osMessageTemplateResolutionService.getText(federalState, OSMessageTemplate.LIMITED_FISHING_LICENSE_PENDING_OS, person);
        String displayName = osMessageTemplateResolutionService.getDisplayName(federalState, OSMessageTemplate.LIMITED_FISHING_LICENSE_PENDING_OS);

        final OSMessage message = new OSMessage(subject, displayName, body);
        osInboxService.sendMessage(inboxReference, message);
        log.info("OD limited license application pending message success send");
    }

    @Override
    public void handleLimitedLicenseApplicationRejected(String inboxReference, FederalState federalState, Person person, UUID registerEntryId) {
        String subject = osMessageTemplateResolutionService.getSubject(federalState, OSMessageTemplate.LIMITED_FISHING_LICENSE_REJECTED);
        String body = osMessageTemplateResolutionService.getText(federalState, OSMessageTemplate.LIMITED_FISHING_LICENSE_REJECTED, person);
        String displayName = osMessageTemplateResolutionService.getDisplayName(federalState, OSMessageTemplate.LIMITED_FISHING_LICENSE_REJECTED);

        final OSMessage message = new OSMessage(subject, displayName, body);
        osInboxService.sendMessage(inboxReference, message);
        log.info("OD limited license application rejected message success send");
    }

    @Override
    public void handleLimitedLicenseCreated(
            String inboxReference,
            FederalState federalState,
            Person person,
            UUID registerEntryId,
            String salt,
            List<IdentificationDocument> identificationDocuments) throws JsonProcessingException {
        final OSMessageWithAttachments messageWithAttachments = createOsMessageWithAttachments(federalState, person, OSMessageTemplate.LIMITED_FISHING_LICENSE_CREATED_OS);
        final List<OSAttachement> pdfs = processOsAttachments(person, registerEntryId, salt, identificationDocuments);

        osInboxService.sendMessageWithAttachments(inboxReference, messageWithAttachments,
                pdfs);
        log.info("OD limited license created message success send");
    }

    private List<OSAttachement> processOsAttachments(Person person, UUID registerEntryId, String salt, List<IdentificationDocument> identificationDocumentList) {
        final List<OSAttachement> pdfs = new ArrayList<>();
        pdfs.addAll(identificationDocumentList.stream()
                .filter(doc -> doc.getTax() != null)
                .map(doc -> {
                    RenderedContent renderedContent = exportService.exportFishingTaxDocument(registerEntryId, salt, person, doc);
                    String name = renderedContent.getFullFilename() != null && !renderedContent.getFullFilename().isBlank() ? renderedContent.getFullFilename() : doc.getDocumentId() + renderedContent.type().getExtension();
                    byte[] content = renderedContent.content();
                    return new OSAttachement(name, content);
                })
                .toList());
        pdfs.addAll(identificationDocumentList.stream()
                .filter(doc -> doc.getFishingLicense() != null
                        && doc.getType() == IdentificationDocumentType.PDF)
                .map(doc -> {
                    RenderedContent renderedContent = exportService.exportFishingLicense(registerEntryId, salt, person, doc);
                    String name = renderedContent.getFullFilename() != null && !renderedContent.getFullFilename().isBlank() ? renderedContent.getFullFilename() : doc.getDocumentId() + renderedContent.type().getExtension();
                    byte[] content = renderedContent.content();
                    return new OSAttachement(name, content);
                })
                .toList());
        return pdfs;
    }

    private List<IdentificationDocument> removeTaxDocuments(List<IdentificationDocument> identificationDocumentList) {
        return identificationDocumentList.stream()
                .filter(doc -> doc.getTax() == null)
                .toList();
    }
}
