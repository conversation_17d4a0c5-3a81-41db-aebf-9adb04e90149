package de.adesso.fischereiregister.registerservice.online_services.message;

import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.utils.PersonUtils;
import de.adesso.fischereiregister.registerservice.online_services.message.enums.OSMessageTemplate;
import de.adesso.fischereiregister.registerservice.tenant.TenantConfigurationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class OSMessageTemplateResolutionServiceImpl implements OSMessageTemplateResolutionService {

    private static final String DEFAULT_CITIZEN_NAME_KEY = "os_message_template.default.citizen_name";
    private static final String CITIZEN_FULLNAME_TOKEN = "{{citizenFullname}}";
    private static final String HTML_LINEBREAK = "<br>";
    private static final String LINEFEED_CHAR = "\n";

    private final TenantConfigurationService tenantConfigurationService;

    @Override
    public String getSubject(FederalState federalState, OSMessageTemplate osMessageTemplate) {
        final String templateKey = osMessageTemplate.getSubjectKey();
        return tenantConfigurationService.getValue(federalState, templateKey);
    }

    @Override
    public String getText(FederalState federalState, OSMessageTemplate osMessageTemplate, Person person) {
        final String osMessageTemplateKey = osMessageTemplate.getTextKey();

        final String citizenName;
        if (person != null) {
            citizenName = PersonUtils.getFullName(person);
        } else {
            log.warn("Person is null while generating mail text for state '{}', using default citizen name.", federalState);
            citizenName = tenantConfigurationService.getValue(federalState, DEFAULT_CITIZEN_NAME_KEY);
        }

        final String templateText = tenantConfigurationService.getValue(federalState, osMessageTemplateKey);
        final String replacedText = templateText.replace(CITIZEN_FULLNAME_TOKEN, citizenName);

        return replaceHtmlLineBreaks(replacedText);
    }

    @Override
    public String getDisplayName(FederalState federalState, OSMessageTemplate osMessageTemplate) {
        final String templateKey = osMessageTemplate.getDisplayNameKey();
        return tenantConfigurationService.getValue(federalState, templateKey);
    }

    private String replaceHtmlLineBreaks(String text) {
        return text.replace(HTML_LINEBREAK, LINEFEED_CHAR);
    }
}
