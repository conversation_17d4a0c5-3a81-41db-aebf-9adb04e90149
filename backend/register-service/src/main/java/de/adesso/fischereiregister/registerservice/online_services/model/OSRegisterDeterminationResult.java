package de.adesso.fischereiregister.registerservice.online_services.model;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.UUID;

@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public class OSRegisterDeterminationResult {
    private final UUID registerEntryId;
    private final OSRequestStatus osStatus;

    // Static factory method for successful case
    public static OSRegisterDeterminationResult success(UUID registerEntryId) {
        if (registerEntryId == null) {
            throw new IllegalArgumentException("Register entry ID cannot be null for a successful result");
        }
        return new OSRegisterDeterminationResult(registerEntryId, null);
    }

    public static OSRegisterDeterminationResult successWithStatus(UUID registerEntryId, OSRequestStatus status) {
        if (registerEntryId == null) {
            throw new IllegalArgumentException("Register entry ID cannot be null for a successful result");
        }
        return new OSRegisterDeterminationResult(registerEntryId, status);
    }


    // Static factory method for failure case
    public static OSRegisterDeterminationResult status(OSRequestStatus osStatus) {
        if (osStatus == null) {
            throw new IllegalArgumentException("Failure type cannot be null for a failure result");
        }
        return new OSRegisterDeterminationResult(null, osStatus);
    }

    public UUID getRegisterEntryId() {
        return registerEntryId;
    }

    public OSRequestStatus getOsStatus() {
        return osStatus;
    }

    public boolean isSuccessful() {
        return registerEntryId != null;
    }

}