package de.adesso.fischereiregister.registerservice.certificate_numbers_view;

import de.adesso.fischereiregister.core.model.type.QualificationsProofType;
import de.adesso.fischereiregister.core.ports.FishingCertificateNumberService;
import de.adesso.fischereiregister.registerservice.utils.NumberGenerationUtils;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.Set;
import java.util.UUID;


@Service
@Profile("!dev & !localdev & !test & !stage")
public class CertificateNumbersViewServiceImpl implements CertificateNumbersViewService, FishingCertificateNumberService {

    private final CertificateNumbersViewRepository repository;

    public CertificateNumbersViewServiceImpl(CertificateNumbersViewRepository repository) {
        this.repository = repository;
    }

	@Override
	public void createCertificateNumbersView(UUID registerEntryId, String fishingCertificateId, QualificationsProofType type) {
		if (fishingCertificateId!= null
				&& fishingCertificateId.length() > 0
				&& type.equals(QualificationsProofType.CERTIFICATE)) {

            CertificateNumbersView view = new CertificateNumbersView();
            view.setRegisterEntryId(registerEntryId);
            view.setCertificateNumber(fishingCertificateId);

			repository.save(view);
		}

	}

    @Override
    public String createNewAvailableFishingCertificateNumber(UUID registerEntryId) {
        return this.createNewAvailableFishingCertificateNumber();
    }

    public String createNewAvailableFishingCertificateNumber() {
        Set<String> existingNumbers = new HashSet<>(repository.findAllNumbers());
        String newAvailableNumber;

        do {
            newAvailableNumber = NumberGenerationUtils.generateCertificateId();
        } while (existingNumbers.contains(newAvailableNumber));

        return newAvailableNumber;
    }
}
