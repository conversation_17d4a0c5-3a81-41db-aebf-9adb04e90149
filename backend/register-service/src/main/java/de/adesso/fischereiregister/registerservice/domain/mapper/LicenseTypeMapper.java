package de.adesso.fischereiregister.registerservice.domain.mapper;

import de.adesso.fischereiregister.core.model.type.LicenseType;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ValueMapping;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "default")
public interface LicenseTypeMapper {

    LicenseTypeMapper INSTANCE = Mappers.getMapper(LicenseTypeMapper.class);

    LicenseType toLicenseType(org.openapitools.model.LicenseType licenseType);

    @ValueMapping(target = MappingConstants.THROW_EXCEPTION, source = "NONE")
    org.openapitools.model.LicenseType toApiLicenseType(LicenseType licenseType);

}
