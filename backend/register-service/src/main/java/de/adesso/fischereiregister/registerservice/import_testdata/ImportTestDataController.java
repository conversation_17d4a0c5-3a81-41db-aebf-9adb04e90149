package de.adesso.fischereiregister.registerservice.import_testdata;

import de.adesso.fischereiregister.core.exceptions.AggregateValidationException;
import de.adesso.fischereiregister.registerservice.validation.ValidationResponseDto;
import org.springframework.context.annotation.Profile;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.concurrent.ExecutionException;


@RestController
@RequestMapping("/import-test-data")
@Profile({"dev", "localdev", "test", "stage"})
public class ImportTestDataController {

    private final ImportTestDataService importTestDataService;

    public ImportTestDataController(ImportTestDataService importTestDataService) {
        this.importTestDataService = importTestDataService;
    }

    @PostMapping()
    public ResponseEntity<?> uploadTestData(@RequestParam("file") MultipartFile file) {
        var responseDto = new ValidationResponseDto();

        if (file == null || file.isEmpty()) {
            responseDto.addErrorNote("OSFile is missing or empty. Please upload a valid CSV file.");
            return ResponseEntity.badRequest().body(responseDto);
        }

        String contentType = file.getContentType();
        if (contentType == null || !contentType.equals("text/csv")) {
            responseDto.addErrorNote("Invalid file format. Only CSV files are supported.");
            return ResponseEntity.status(HttpStatus.UNSUPPORTED_MEDIA_TYPE).body(responseDto);
        }

        try {
            importTestDataService.importFromUploadedFile(file.getInputStream());
            return ResponseEntity.ok().build();
        } catch (IOException e) {
            responseDto.addErrorNote("Failed to read the uploaded file: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(responseDto);
        } catch (IllegalArgumentException e) {
            responseDto.addErrorNote("Invalid data in the CSV file: " + e.getMessage());
            return ResponseEntity.badRequest().body(responseDto);
        } catch (IllegalStateException e) {
            responseDto.addErrorNote("Import functionality is currently disabled. Please check the service configuration: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(responseDto);
        } catch (ExecutionException e) {
            responseDto.addErrorNote("An unexpected error occurred during execution: " + e.getMessage());
            if (e.getCause() instanceof ExecutionException && e.getCause().getCause() instanceof AggregateValidationException) {
                ((AggregateValidationException) e.getCause().getCause()).getValidationResult()
                        .getErrorNotes()
                        .forEach(responseDto::addErrorNote);
            }

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(responseDto);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt(); // Re-interrupt the thread
            responseDto.addErrorNote("The operation was interrupted: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(responseDto);
        } catch (RuntimeException e) {
            responseDto.addErrorNote("An unexpected error occurred: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(responseDto);
        }
    }
}

