package de.adesso.fischereiregister.registerservice.admin;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import org.axonframework.eventsourcing.eventstore.EventStore;
import org.springframework.context.annotation.Profile;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@AllArgsConstructor
@RestController
@RequestMapping("/admin")
@Profile({"dev", "localdev", "test", "stage"})
public class AdminController {

    private final AdminService adminService;
    private final ObjectMapper objectMapper;

    private final EventStore eventStore;

    @DeleteMapping("/truncate-all")
    public ResponseEntity<Void> truncateAll() {
        try {
            adminService.truncateAll();

            return ResponseEntity.ok().build();
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/event-stream/{registerEntryId}")
    public ResponseEntity<?> getEventStream(@PathVariable("registerEntryId") String registerEntryId) throws Exception {
        final List<EventDataDto> result = eventStore.readEvents(registerEntryId)
                .asStream()
                .map(event -> EventDataDto.builder()
                        .payloadType(event.getPayloadType())
                        .payload(event.getPayload())
                        .metaData(event.getMetaData())
                        .aggregateIdentifier(event.getAggregateIdentifier())
                        .timestamp(event.getTimestamp())
                        .build())
                .toList();

        return ResponseEntity.ok().body(objectMapper.writeValueAsString(result));
    }
}