package de.adesso.fischereiregister.registerservice.statistics.bans_statistics;

import de.adesso.fischereiregister.view.ban.services.BanViewService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

@Service
@Slf4j
@AllArgsConstructor
public class BansStatisticsServiceImpl implements BansStatisticsService {

    private final BanViewService banViewService;

    @Override
    public List<BansStatistics> getStatisticsByFederalStateAndYears(String federalState, List<Integer> years) {
        try {
            log.debug("Fetching ban statistics for federal state: {} and years: {}", federalState, years);

            List<BansStatistics> result = new ArrayList<>();

            for (Integer year : years) {
                // Get counts directly from BanViewService for database-level operations
                Integer issuedCount = banViewService.getIssuedAmountByFederalStateAndYear(federalState, year);
                Integer startedCount = banViewService.getStartedAmountByFederalStateAndYear(federalState, year);
                Integer expiredCount = banViewService.getExpiredAmountByFederalStateAndYear(federalState, year);

                // Create data entry with issued, started, and expired counts
                BansStatisticsData data = new BansStatisticsData(issuedCount, startedCount, expiredCount);

                // Create BansStatistics with the year and data entry
                BansStatistics bansStatistics = new BansStatistics(year, data);
                result.add(bansStatistics);
            }

            // Sort the result by year in descending order
            result.sort(Comparator.comparingInt(BansStatistics::year).reversed());

            log.debug("Successfully fetched ban statistics for federal state: {} and years: {}, result size: {}",
                     federalState, years, result.size());

            return result;
        } catch (Exception e) {
            log.error("Error fetching ban statistics for federal state: {} and years: {}: {}",
                     federalState, years, e.getMessage(), e);
            throw new RuntimeException("Failed to fetch ban statistics", e);
        }
    }

    @Override
    public List<BansStatistics> getStatisticsByYears(List<Integer> years) {
        try {
            log.debug("Fetching ban statistics for years: {}", years);

            List<BansStatistics> result = new ArrayList<>();

            for (Integer year : years) {
                // Get counts directly from BanViewService for database-level operations
                Integer issuedCount = banViewService.getIssuedAmountByYear(year);
                Integer startedCount = banViewService.getStartedAmountByYear(year);
                Integer expiredCount = banViewService.getExpiredAmountByYear(year);

                // Create data entry with issued, started, and expired counts
                BansStatisticsData data = new BansStatisticsData(issuedCount, startedCount, expiredCount);

                // Create BansStatistics with the year and data entry
                BansStatistics bansStatistics = new BansStatistics(year, data);
                result.add(bansStatistics);
            }

            // Sort the result by year in descending order
            result.sort(Comparator.comparingInt(BansStatistics::year).reversed());

            log.debug("Successfully fetched ban statistics for years: {}, result size: {}", years, result.size());

            return result;
        } catch (Exception e) {
            log.error("Error fetching ban statistics for years: {}: {}", years, e.getMessage(), e);
            throw new RuntimeException("Failed to fetch ban statistics", e);
        }
    }

    @Override
    public List<Integer> getAvailableYears() {
        try {
            log.debug("Fetching available years for ban statistics");

            List<Integer> availableYears = banViewService.getAvailableYears();

            log.debug("Successfully fetched available years for ban statistics: {}", availableYears);

            return availableYears;
        } catch (Exception e) {
            log.error("Error fetching available years for ban statistics: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to fetch available years", e);
        }
    }
}
