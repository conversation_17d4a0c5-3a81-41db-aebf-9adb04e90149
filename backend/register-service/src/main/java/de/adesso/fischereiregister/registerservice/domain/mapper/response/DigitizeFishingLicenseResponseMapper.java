package de.adesso.fischereiregister.registerservice.domain.mapper.response;

import de.adesso.fischereiregister.core.commands.results.DigitizeRegularLicenseCommandResult;
import de.adesso.fischereiregister.registerservice.domain.mapper.DocumentMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.FishingLicenseMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.JurisdictionMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.PersonMapper;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.openapitools.model.DigitizeFishingLicenseResponse;

@Mapper(componentModel = "default", uses = {DocumentMapper.class, PersonMapper.class, JurisdictionMapper.class, FishingLicenseMapper.class})
public interface DigitizeFishingLicenseResponseMapper {
    DigitizeFishingLicenseResponseMapper INSTANCE = Mappers.getMapper(DigitizeFishingLicenseResponseMapper.class);

    DigitizeFishingLicenseResponse toResponse(DigitizeRegularLicenseCommandResult commandResult);

}
