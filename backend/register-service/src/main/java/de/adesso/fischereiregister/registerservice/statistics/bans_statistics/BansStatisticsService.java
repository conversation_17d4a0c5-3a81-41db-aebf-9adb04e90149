package de.adesso.fischereiregister.registerservice.statistics.bans_statistics;

import java.util.List;

public interface BansStatisticsService {

    /**
     * gets the bans statistics for a given federal state and years
     *
     * @param federalState The federal state for which the statistics are retrieved.
     * @param years        The list of years for which the statistics are requested.
     * @return A list of {@link BansStatistics} objects containing the requested statistics.
     */
    List<BansStatistics> getStatisticsByFederalStateAndYears(String federalState, List<Integer> years);

    /**
     * gets the bans statistics for a given years
     *
     * @param years The list of years for which the statistics are requested.
     * @return A list of {@link BansStatistics} objects containing the requested statistics.
     */
    List<BansStatistics> getStatisticsByYears(List<Integer> years);

    /**
     * gets the available years for bans statistics
     *
     * @return A list of years for which bans statistics are available.
     */
    List<Integer> getAvailableYears();

}
