package de.adesso.fischereiregister.registerservice.domain.mapper;

import de.adesso.fischereiregister.core.model.QualificationsProof;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper()
public interface QualificationsProofMapper {

    QualificationsProofMapper INSTANCE = Mappers.getMapper(QualificationsProofMapper.class);

    @Mapping(target = "examinerId", ignore = true)
    QualificationsProof toQualificationsProof(org.openapitools.model.QualificationsProof dto);

    List<QualificationsProof> toQualificationsProofs(List<org.openapitools.model.QualificationsProof> dto);

}
