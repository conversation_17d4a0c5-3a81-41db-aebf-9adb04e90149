package de.adesso.fischereiregister.registerservice.utils;

import de.adesso.fischereiregister.core.model.type.FederalState;

import java.security.SecureRandom;

public class NumberGenerationUtils {
    private static final SecureRandom secureRandom = new SecureRandom();

    private static final String CERTIFICATE_PREFIX = "ZF";

    private NumberGenerationUtils() {
    }

    public static String generateLicenseNumber(FederalState federalState) {
        return generateNumberWithVerification(federalState.toString());
    }

    public static String generateCertificateId() {
        return generateNumberWithVerification(CERTIFICATE_PREFIX);
    }

    private static String generateNumberWithVerification(String prefix) {
        final long randomNumber = secureRandom.nextLong(1000000000000L);
        final long verificationDigits = 98 - ((randomNumber * 100) % 97);

        return String.format(
                "%s%012d%02d",
                prefix,
                randomNumber,
                verificationDigits);
    }

    /**
     * Formats 16 digit/letter string into a string of the format
     * "XXXX-XXXX-XXXX-XXXX"
     *
     * @param number 16 digit/letter identification number
     * @return formatted number of the format "XXXX-XXXX-XXXX-XXXX"
     */
    public static String formatIdentificationNumber(String number) {
        return number.substring(0, 4) + "-" +
                number.substring(4, 8) + "-" +
                number.substring(8, 12) + "-" +
                number.substring(12, 16);
    }
}
