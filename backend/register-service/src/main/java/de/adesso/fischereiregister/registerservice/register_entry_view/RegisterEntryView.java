package de.adesso.fischereiregister.registerservice.register_entry_view;

import java.util.UUID;

import de.adesso.fischereiregister.core.model.RegisterEntry;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Lob;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table
@Getter
@Setter
public class RegisterEntryView {

	@Id
	private UUID registerId;

	/**
	 * Complete mapping of the RegisterEntry as a JSON blob.
	 */
	@Lob
	@Column(columnDefinition = "TEXT")
	@Convert(converter = RegisterEntryConverter.class)
	private RegisterEntry data;

}
