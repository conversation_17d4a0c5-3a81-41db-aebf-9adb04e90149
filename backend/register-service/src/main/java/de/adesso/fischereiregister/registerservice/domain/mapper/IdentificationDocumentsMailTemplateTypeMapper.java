package de.adesso.fischereiregister.registerservice.domain.mapper;

import de.adesso.fischereiregister.registerservice.mail.enums.MailTemplate;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper()
public interface IdentificationDocumentsMailTemplateTypeMapper {
    IdentificationDocumentsMailTemplateTypeMapper INSTANCE = Mappers.getMapper(IdentificationDocumentsMailTemplateTypeMapper.class);

    MailTemplate toIdentificationDocumentMailTemplate(org.openapitools.model.IdentificationDocumentsMailTemplateType identificationDocumentMailTemplate);
}
