package de.adesso.fischereiregister.registerservice.tenant.port;

import de.adesso.fischereiregister.core.exceptions.RulesProcessingException;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.registerservice.drools.dmn.license.configuration.output.LicenseInformationOutput;
import de.adesso.fischereiregister.registerservice.drools.dmn.tax.configuration.TaxInformationOutput;

import java.util.List;

/**
 * Interface for retriving configuration information for license fee prices (and durations and issued dates) and tax prices which
 * are tenant specific.
 * The returned information is used for configuring the frontend gui controlls for
 * fees and taxes.
 */
public interface TenantRulesConfigurationPort {

    /**
     * Retrieves GUI configuration information for License fee prices and durations and issue dates for the given federal state.
     * This information is used for configuring the frontend GUI controls for license fees.
     *
     * @param federalState the federal state
     * @return a list of license information outputs
     * @throws RulesProcessingException if an error occurs during rules processing
     */
    List<LicenseInformationOutput> getLicenseFeeInformation(FederalState federalState) throws RulesProcessingException;

    /**
     * Retrieves GUI configuration information for tax prices for the given federal state.
     * This information is used for configuring the frontend GUI controls for taxes.
     *
     * @param federalState the federal state
     * @param officeFeeAlreadyPayed whether the office fee is already paid or not
     * @return a list of tax information outputs
     * @throws RulesProcessingException if an error occurs during rules processing
     */
    List<TaxInformationOutput> getTaxPriceInformation(FederalState federalState, boolean officeFeeAlreadyPayed) throws RulesProcessingException;
}