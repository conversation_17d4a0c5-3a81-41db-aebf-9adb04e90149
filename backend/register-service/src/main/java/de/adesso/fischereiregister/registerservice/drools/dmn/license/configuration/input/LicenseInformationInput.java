package de.adesso.fischereiregister.registerservice.drools.dmn.license.configuration.input;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import de.adesso.fischereiregister.registerservice.drools.dmn.model.DmnProcessingType;
import lombok.Getter;

import java.math.BigDecimal;
import java.time.LocalDate;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class LicenseInformationInput {
    private DmnLicenseType dmnLicenseType; //  DMN Table input:  Verarbeitungstyp (enum/ string)
    private LocalDate legislativeValidityDate; // DMN Table input: Gesetzliche Gültigkeit (date)
    private DmnProcessingType dmnProcessingType; // DMNTable input: Verarbeitungstyp (Enum/ string)
    private BigDecimal currentMonth;
    private BigDecimal currentYear;

    public LicenseInformationInput() {
    }

    public LicenseInformationInput(DmnLicenseType dmnLicenseType,
                                   LocalDate legislativeValidityDate,
                                   DmnProcessingType dmnProcessingType,
                                   BigDecimal currentMonth,
                                   BigDecimal currentYear) {
        this.dmnLicenseType = dmnLicenseType;
        this.legislativeValidityDate = legislativeValidityDate;
        this.dmnProcessingType = dmnProcessingType;
        this.currentMonth = currentMonth;
        this.currentYear = currentYear;
    }

    public LicenseInformationInput(DmnLicenseType dmnLicenseType,
                                   LocalDate legislativeValidityDateWhichIsCurrentDate,
                                   DmnProcessingType dmnProcessingType) {

        this.dmnLicenseType = dmnLicenseType;
        this.legislativeValidityDate = legislativeValidityDateWhichIsCurrentDate;
        this.dmnProcessingType = dmnProcessingType;
        this.currentMonth = convertToMonth(legislativeValidityDateWhichIsCurrentDate);
        this.currentYear = convertToYear(legislativeValidityDateWhichIsCurrentDate);
    }

    private BigDecimal convertToMonth(LocalDate localDate) {
        return BigDecimal.valueOf(localDate.getMonthValue());
    }

    private BigDecimal convertToYear(LocalDate localDate) {
        return BigDecimal.valueOf(localDate.getYear());
    }
}
