package de.adesso.fischereiregister.registerservice.mail;

import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.registerservice.mail.enums.MailTemplate;

/**
 * Service interface for resolving mail templates such as subject, text.
 *
 * The templates can be used for sending emails,
 * and are resolved based on the tenant (federal state) and the specific type of mail.
 */
public interface MailTemplateResolutionService {
    /**
     * Resolves the subject line for a mail
     * based on the given federal state (tenant) and message template type.
     *
     * @param federalState the federal state (tenant) for which the subject should be resolved
     * @param mailTemplate the type of mail to resolve
     * @return the resolved subject
     */
    String getSubject(FederalState federalState, MailTemplate mailTemplate);

    /**
     * Resolves the mail body text for a given federal state, mail template,
     * and optional person. If a person is provided, their full name is inserted
     * where the placeholder (e.g., {{citizenFullname}}) appears in the template.
     * Otherwise a default token will be used for example "Bürger".
     *
     * @param federalState the federal state (tenant) for which the text is resolved
     * @param mailTemplate the type of mail template to resolve
     * @param person the person whose full name (if not null) should be inserted into the mail
     * @return the resolved mail text with the citizen's name, or the template text if person is null
     */
    String getText(FederalState federalState, MailTemplate mailTemplate, Person person);
}
