package de.adesso.fischereiregister.registerservice.domain.mapper;

import de.adesso.fischereiregister.core.model.consent.ConsentInfo;
import de.adesso.fischereiregister.core.model.consent.TaxConsentInfo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface TaxConsentInfoMapper {
    TaxConsentInfoMapper INSTANCE = Mappers.getMapper(TaxConsentInfoMapper.class);

    TaxConsentInfo toTaxConsentInfo(org.openapitools.model.TaxConsentInfo apiModelTaxConsentInfo);

    TaxConsentInfo toTaxConsentInfo(ConsentInfo consentInfo);
}
