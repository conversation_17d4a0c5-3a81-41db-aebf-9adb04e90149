package de.adesso.fischereiregister.registerservice.security;

import org.springframework.boot.context.properties.ConfigurationProperties;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ConfigurationProperties(prefix = "security")
public class SecurityProperties {
	
	private String groups;
	private String realmAccess;
	private String rolesClaim;
	private String realmAccessClaim;
	private String resourceAccessClaim;
	private String prefixRealmRole;
	private String prefixResourceRole;
	
	public SecurityProperties() {
		super();
	}
	
}
