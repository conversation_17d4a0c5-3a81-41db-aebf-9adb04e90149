package de.adesso.fischereiregister.registerservice.security;

import de.adesso.fischereiregister.core.model.user.UserDetails;

import java.util.Optional;

public interface UserDetailsService {

    String getFederalState();

	Optional<UserDetails> getUserDetails();

	Optional<String> getUserId();

    Optional<String> getCertificationIssuer();

    Optional<String> getOffice();

    Optional<String> getParsedOfficeAddress();
}