package de.adesso.fischereiregister.registerservice.online_services.message;

import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.registerservice.online_services.message.enums.OSMessageTemplate;
import de.adesso.fischereiregister.registerservice.online_services.model.OSRequestStatus;

public interface OSFailureMessageService {
    void handleFailure(OSRequestStatus osStatus, String inboxReference, String licenseNumber, Person person, FederalState federalState, OSMessageTemplate osMessageTemplate);
}
