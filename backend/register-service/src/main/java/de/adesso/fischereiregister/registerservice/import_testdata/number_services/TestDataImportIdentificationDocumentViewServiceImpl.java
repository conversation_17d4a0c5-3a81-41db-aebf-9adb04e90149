package de.adesso.fischereiregister.registerservice.import_testdata.number_services;

import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.registerservice.identification_document_view.IdentificationDocumentViewRepository;
import de.adesso.fischereiregister.registerservice.identification_document_view.IdentificationDocumentViewServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.UUID;

@Slf4j
@Service
@Profile({"dev", "localdev", "test", "stage"})
public class TestDataImportIdentificationDocumentViewServiceImpl extends IdentificationDocumentViewServiceImpl {

    private final HashMap<UUID, String> fishingLicenseNumbers = new HashMap<>();

    public TestDataImportIdentificationDocumentViewServiceImpl(IdentificationDocumentViewRepository identificationDocumentViewRepository) {
        super(identificationDocumentViewRepository);

        log.info("TestDataImportIdentificationDocumentViewServiceImpl loaded. Id's will be read from csv data if possible.");
    }

    /**
     * Adds a single fishing license number to the queue.
     */
    public void addFishingLicenseNumber(UUID registerEntryId, String newNumber) {
        if (newNumber != null && !newNumber.isEmpty()) {
            this.fishingLicenseNumbers.put(registerEntryId, newNumber);
        }
    }

    @Override
    public String createNewAvailableFishingLicenseNumber(UUID registerEntryId, FederalState federalState) {

        if (this.fishingLicenseNumbers.containsKey(registerEntryId)) {
            log.info("Found license number for registerEntryId {}, will output the specified license number and not generate a new one. Loaded license numbers: {}", registerEntryId, this.fishingLicenseNumbers.size());
            return this.fishingLicenseNumbers.remove(registerEntryId);
        } else {
            log.info("No license number found for registerEntryId {}, will generate a new license number.", registerEntryId);
            return super.createNewAvailableFishingLicenseNumber(federalState);
        }
    }

}
