package de.adesso.fischereiregister.registerservice.s3;

import lombok.AllArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;

@RestController
@AllArgsConstructor
public class S3Controller implements api.S3StorageApi {

    private final S3Service s3Service;

    @Override
    public ResponseEntity<byte[]> s3ControllerGet(String objectPath) {
        String fileKey = URLDecoder.decode(objectPath, StandardCharsets.UTF_8);
        S3ObjectResponse response = s3Service.downloadFile(fileKey);
        HttpHeaders headers = new HttpHeaders();
        String fileName = fileKey.replace("/", "_");
        headers.setContentDispositionFormData("attachment", fileName);
        if (response.getContentType() != null) {
            headers.setContentType(MediaType.parseMediaType(response.getContentType()));
        }
        return ResponseEntity
                .ok()
                .headers(headers)
                .body(response.getContent());
    }
}


