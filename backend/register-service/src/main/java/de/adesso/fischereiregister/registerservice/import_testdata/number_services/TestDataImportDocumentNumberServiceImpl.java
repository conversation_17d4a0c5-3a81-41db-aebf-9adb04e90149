package de.adesso.fischereiregister.registerservice.import_testdata.number_services;

import de.adesso.fischereiregister.core.model.type.IdentificationDocumentType;
import de.adesso.fischereiregister.registerservice.identification_document_view.DocumentNumberServiceImpl;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.UUID;

@Service
@Profile({"dev", "localdev", "test", "stage"})
public class TestDataImportDocumentNumberServiceImpl extends DocumentNumberServiceImpl {
    private final HashMap<UUID, String> documentNumbers = new HashMap<>();

    public void addDocumentNumber(UUID registerEntryId, String newNumber) {
        if(newNumber != null && !newNumber.isEmpty()) {
            this.documentNumbers.put(registerEntryId, newNumber);
        }
    }

    @Override
    public String createNewDocumentNumber(UUID registerEntryId, IdentificationDocumentType documentType) {
        if(this.documentNumbers.containsKey(registerEntryId)
                && documentType.equals(IdentificationDocumentType.CARD)) {
            return this.documentNumbers.remove(registerEntryId);
        }else{
            return createNewDocumentNumber();
        }
    }

}
