package de.adesso.fischereiregister.registerservice.drools.dmn.model;

import com.fasterxml.jackson.annotation.JsonValue;

public enum DmnProcessingType {
    ANALOG("ANALOG"),
    DIGITAL("DIGITAL");

    private String value;

    DmnProcessingType(String value) {
        this.value = value;
    }

    @JsonValue
    public String getValue() {
        return this.value;
    }

    @Override
    public String toString() {
        return String.valueOf(this.value);
    }
}
