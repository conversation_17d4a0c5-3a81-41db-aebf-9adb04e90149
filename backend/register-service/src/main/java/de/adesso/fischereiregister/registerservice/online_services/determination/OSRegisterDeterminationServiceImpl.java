package de.adesso.fischereiregister.registerservice.online_services.determination;

import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.registerservice.online_services.model.OSRegisterDeterminationResult;
import de.adesso.fischereiregister.registerservice.online_services.model.OSRequestStatus;
import de.adesso.fischereiregister.view.register_entry_search.RegisterEntrySearchViewService;
import lombok.AllArgsConstructor;
import lombok.Generated;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
@AllArgsConstructor
@Generated
public class OSRegisterDeterminationServiceImpl implements OSRegisterDeterminationService {

    private final RegisterEntrySearchViewService registerEntrySearchViewService;

    @Override
    public OSRegisterDeterminationResult determineRegisterEntryId(String identificationNumber, Person person) {
        final Optional<UUID> registerEntryId = Optional.ofNullable(identificationNumber)
                .flatMap(registerEntrySearchViewService::findRegisterEntryIdByIdentificationNumber);

        if (registerEntryId.isPresent()) {
            // If a register entry is found for identification number, return it as the result
            return OSRegisterDeterminationResult.success(registerEntryId.get());
        }

        // If no identificationNumber is provided or no register entry ID is found for the identificationNumber, search by person details
        final List<UUID> ids = registerEntrySearchViewService.findRegisterEntryIdsByPersonDetails(person);

        if (ids.isEmpty()) {
            return OSRegisterDeterminationResult.status(OSRequestStatus.PERSON_NOT_FOUND);
        } else if (ids.size() > 1) {
            return OSRegisterDeterminationResult.status(OSRequestStatus.MULTIPLE_PERSONS_FOUND);
        }
        // If exactly one register entry is found for the request person data, return it as the result
        return OSRegisterDeterminationResult.success(ids.get(0));
    }

    @Override
    public OSRegisterDeterminationResult determineOrCreateRegisterEntryId(Person person) {
        final List<UUID> ids = registerEntrySearchViewService.findRegisterEntryIdsByPersonDetails(person);

        if (ids.isEmpty()) {
            // here we return a new UUID because in this case we will want to create a new register entry
            return OSRegisterDeterminationResult.successWithStatus(UUID.randomUUID(), OSRequestStatus.PERSON_NOT_FOUND_CREATE_NEW_REGISTER_ENTRY); // OSStatus.PERSON_NOT_FOUND
        } else if (ids.size() > 1) {
            // here we return a new UUID because in this case we will want to create a new register entry
            return OSRegisterDeterminationResult.successWithStatus(UUID.randomUUID(), OSRequestStatus.MULTIPLE_PERSONS_FOUND_CREATE_NEW_REGISTER_ENTRY); // OSStatus.MULTIPLE_PERSONS_FOUND
        }
        return OSRegisterDeterminationResult.success(ids.get(0));
    }

    @Override
    public OSRegisterDeterminationResult determineRegisterEntryId(String identificationNumber) {
        final Optional<UUID> registerEntryId = Optional.ofNullable(identificationNumber)
                .flatMap(registerEntrySearchViewService::findRegisterEntryIdByIdentificationNumber);

        if (registerEntryId.isPresent()) {
            // If a register entry is found for identification number, return it as the result
            return OSRegisterDeterminationResult.success(registerEntryId.get());
        }else{
            return OSRegisterDeterminationResult.status(OSRequestStatus.REGISTER_NOT_FOUND);
        }
    }


}
