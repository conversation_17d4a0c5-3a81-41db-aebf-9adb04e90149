package de.adesso.fischereiregister.registerservice.drools.dmn;

import org.kie.api.KieServices;
import org.kie.api.builder.KieBuilder;
import org.kie.api.builder.KieFileSystem;
import org.kie.api.builder.KieModule;
import org.kie.api.runtime.KieContainer;
import org.kie.api.runtime.KieSession;
import org.kie.internal.io.ResourceFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.InputStream;

@Configuration
public class DmnRuleEngineConfig {

    private static final String RULES_PATH = "config/dmn";

    private static final String[] RULE_FILES = {
            "default.dmn",
            "SH.dmn",
            "BE.dmn",
            "NW.dmn"
  };

    private static final KieServices kieServices = KieServices.Factory.get();

    @Bean(name="dmn-kie-container")
    public KieContainer kieContainer() throws SecurityException {

        ClassLoader classLoader = DmnRuleEngineConfig.class.getClassLoader();
        KieFileSystem kieFileSystem = kieServices.newKieFileSystem();

        for (String file : RULE_FILES) {
            InputStream inputStream = classLoader.getResourceAsStream(RULES_PATH + "/" + file);
            kieFileSystem.write(ResourceFactory.newInputStreamResource(inputStream).setSourcePath("src/main/resources" + RULES_PATH + "/" + file));
        }

        KieBuilder kb = kieServices.newKieBuilder(kieFileSystem);
        kb.buildAll();
        KieModule kieModule = kb.getKieModule();
        return kieServices.newKieContainer(kieModule.getReleaseId());
    }


    @Bean(name="dmn-kie-session")
    public KieSession kieSession() {
        return kieContainer().newKieSession();
    }

}
