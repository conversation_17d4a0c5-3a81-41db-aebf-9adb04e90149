package de.adesso.fischereiregister.registerservice.destatis_country.data;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

public class DataRowDeserializer extends JsonDeserializer<List<CountryDataRow>> {

    @Override
    public List<CountryDataRow> deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        ObjectMapper mapper = (ObjectMapper) p.getCodec();
        List<List<String>> rawRows = mapper.readValue(p, new TypeReference<>() {});

        // Map each List<String> to a CountryDataRow
        return rawRows.stream().map(row -> {
            CountryDataRow dataRow = new CountryDataRow();
            dataRow.setId(row.get(0));
            dataRow.setName(row.get(1));
            dataRow.setAdjective(row.get(2));
            dataRow.setShortName(row.get(3));
            dataRow.setFullName(row.get(4));
            dataRow.setExistenceFrom(row.get(5));
            dataRow.setExistenceTo(row.get(6));
            dataRow.setIsoAlpha3(row.get(7));
            dataRow.setIsoAlpha2(row.get(8));
            dataRow.setContinent(row.get(9));
            dataRow.setNote(row.get(10));
            return dataRow;
        }).collect(Collectors.toList());
    }
}
