package de.adesso.fischereiregister.registerservice.drools.dmn.adapter;

import de.adesso.fischereiregister.core.exceptions.RulesProcessingException;
import de.adesso.fischereiregister.core.model.Fee;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.ports.TenantRulesValidationPort;
import de.adesso.fischereiregister.core.validation.ValidationResult;
import de.adesso.fischereiregister.registerservice.drools.dmn.DmnDroolsService;
import de.adesso.fischereiregister.registerservice.drools.dmn.license.configuration.input.LicenseInformationInput;
import de.adesso.fischereiregister.registerservice.drools.dmn.license.configuration.output.LicenseInformationOutput;
import de.adesso.fischereiregister.registerservice.drools.dmn.license.validation.input.LicenseValidationInput;
import de.adesso.fischereiregister.registerservice.drools.dmn.license.validation.output.LicenseValidationOutput;
import de.adesso.fischereiregister.registerservice.drools.dmn.mapper.DmnLicenseTypeMapper;
import de.adesso.fischereiregister.registerservice.drools.dmn.mapper.DmnProcessingTypeMapper;
import de.adesso.fischereiregister.registerservice.drools.dmn.qualifications_proof.QualificationsProofValidationInput;
import de.adesso.fischereiregister.registerservice.drools.dmn.qualifications_proof.QualificationsProofValidationOutput;
import de.adesso.fischereiregister.registerservice.drools.dmn.tax.configuration.TaxInformationInput;
import de.adesso.fischereiregister.registerservice.drools.dmn.tax.configuration.TaxInformationOutput;
import de.adesso.fischereiregister.registerservice.drools.dmn.tax.validation.TaxValidationInput;
import de.adesso.fischereiregister.registerservice.drools.dmn.tax.validation.TaxValidationOutput;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Service
@AllArgsConstructor
public class TenantRulesValidationAdapter implements TenantRulesValidationPort {

    private final DmnDroolsService dmnDroolsService;

    @Override
    public void validateUsingFishingLicenseRules(FederalState federalState, Person person, LicenseType licenseType, ValidationResult validationResult) throws RulesProcessingException {
        LicenseValidationInput input = new LicenseValidationInput(person.getBirthdate().getAge(), DmnLicenseTypeMapper.INSTANCE.toDmnLicenseType(licenseType), LocalDate.now());
        List<LicenseValidationOutput> outputs = dmnDroolsService.evaluateLicenseValidationRules(input, federalState);
        if (!outputs.isEmpty()) {
            outputs.forEach(output -> validationResult.addErrorNote(output.getErrorMessage()));
        }
    }

    @Override
    public void validateUsingQualificationProofRules(FederalState federalState, Person person, ValidationResult validationResult) throws RulesProcessingException {
        QualificationsProofValidationInput input = new QualificationsProofValidationInput(LocalDate.now(), person.getBirthdate().getAge());
        List<QualificationsProofValidationOutput> outputs = dmnDroolsService.evaluateQualificationValidationRules(input, federalState);
        if (!outputs.isEmpty()) {
            outputs.forEach(output -> validationResult.addErrorNote(output.getException()));
        }
    }

    @Override
    public void validateUsingFishingTaxRules(FederalState federalState, Person person, List<Tax> taxes, ValidationResult validationResult) throws RulesProcessingException {
        for (Tax tax : taxes) {
            TaxValidationInput input = new TaxValidationInput(person.getBirthdate().getAge(), tax.getValidTo().getYear(), LocalDate.now());
            List<TaxValidationOutput> outputs = dmnDroolsService.evaluateTaxValidationRules(input, federalState);
            if (!outputs.isEmpty()) {
                outputs.forEach(output -> validationResult.addErrorNote(output.getErrorMessage()));
            }
        }
    }

    @Override
    public void validateTaxes(FederalState federalState, List<Tax> taxes, boolean officeFeeAlreadyPayed, ValidationResult validationResult) throws RulesProcessingException {
        for (Tax tax : taxes) {
            int years = countYears(tax.getValidFrom(), tax.getValidTo());
            TaxInformationInput input = new TaxInformationInput(LocalDate.now(), BigDecimal.valueOf(years), DmnProcessingTypeMapper.INSTANCE.toDmnProcessingType(tax.getPaymentInfo().getType()), officeFeeAlreadyPayed);
            List<TaxInformationOutput> outputs = dmnDroolsService.evaluateTaxRules(input, federalState);

            if (outputs.size() != 1) {
                validationResult.addErrorNote("No results found for tax information, searched for years: " + years);

            } else if (outputs.get(0).getSummOfTaxAmount().doubleValue() != tax.getPaymentInfo().getAmount()) {

                outputs.forEach(output -> validationResult.addErrorNote("Tax amount is not correct, expected Value: " + outputs.get(0).getSummOfTaxAmount().doubleValue()));
            }
        }
    }

    public static int countYears(LocalDate from, LocalDate to) {
        return (to.getYear() - from.getYear()) + 1;
    }

    @Override
    public void validateFeesForFishingLicense(FederalState federalState, LicenseType licenseType, List<Fee> fees, ValidationResult validationResult) throws RulesProcessingException {
        for (Fee fee : fees) {
            LicenseInformationInput licenseInformationInput = new LicenseInformationInput(DmnLicenseTypeMapper.INSTANCE.toDmnLicenseType(licenseType),
                    LocalDate.now(),
                    DmnProcessingTypeMapper.INSTANCE.toDmnProcessingType(fee.getPaymentInfo().getType()));

            final List<LicenseInformationOutput> outputs = dmnDroolsService.evaluateLicenseRules(
                    licenseInformationInput, federalState);

            if (outputs.size() != 1) {
                validationResult.addErrorNote("No results found for fee information");

            } else if (outputs.get(0).getFeeAmount() != fee.getPaymentInfo().getAmount().intValue()) {

                outputs.forEach(output -> validationResult.addErrorNote("Fee amount is not correct, expected Value: " + outputs.get(0).getFeeAmount()));
            }
        }
    }
}
