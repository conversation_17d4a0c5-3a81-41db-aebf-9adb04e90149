package de.adesso.fischereiregister.registerservice.domain.mapper;

import de.adesso.fischereiregister.core.model.consent.JurisdictionConsentInfo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "default")
public interface JurisdictionConsentInfoMapper {

    JurisdictionConsentInfoMapper INSTANCE = Mappers.getMapper(JurisdictionConsentInfoMapper.class);


    JurisdictionConsentInfo toJurisdictionConsentInfo(org.openapitools.model.JurisdictionConsentInfo dto);
}
