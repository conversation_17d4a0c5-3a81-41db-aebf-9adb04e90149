package de.adesso.fischereiregister.registerservice.drools.dmn.tax.configuration;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import de.adesso.fischereiregister.registerservice.drools.dmn.model.DmnProcessingType;
import lombok.Getter;

import java.math.BigDecimal;
import java.time.LocalDate;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class TaxInformationInput {
    private LocalDate legislativeValidityDate; // DMN Table input: Gesetzliche Gültigkeit (date)
    /**
     * Inputs: Years are an array in the DMN table. We have cases where the years are not set when querying the rule,
     * and in such cases, 0 is set to ensure the correct rules are found.
     * For the same reason, the year is also an output.
     */
    private BigDecimal years; // Jahre
    private DmnProcessingType dmnProcessingType; // DMNTable input: Verarbeitungstyp (Enum/ string)
    private String officeFeeAlreadyPayed; // DMNTable input: mit Gebühr

    public TaxInformationInput(LocalDate legislativeValidityDate,
                               BigDecimal years,
                               DmnProcessingType dmnProcessingType,
                               boolean officeFeeAlreadyPayed) {
        this.legislativeValidityDate = legislativeValidityDate;
        this.years = years;
        this.dmnProcessingType = dmnProcessingType;
        this.officeFeeAlreadyPayed = officeFeeAlreadyPayed ? "ja" : "nein";
    }
}
