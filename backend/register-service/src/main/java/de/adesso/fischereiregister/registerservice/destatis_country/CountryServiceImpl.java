package de.adesso.fischereiregister.registerservice.destatis_country;

import com.fasterxml.jackson.databind.ObjectMapper;
import de.adesso.fischereiregister.registerservice.destatis_country.data.CountryDataRow;
import de.adesso.fischereiregister.registerservice.destatis_country.data.DESTATISCountryFile;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;

@Component
public class CountryServiceImpl implements CountryApplicationService {

    private static final String NATIONALITY_CONFIG_FILE = "config/nationality/DESTATIS-Nationality.json";
    ObjectMapper objectMapper = new ObjectMapper();
    private final DESTATISCountryFile nationalityData;
    private final List<CountryDataRow> dataRows;

    public CountryServiceImpl() {
        nationalityData = loadNationalityFile();
        dataRows = nationalityData.getDaten().stream().filter(CountryDataRow::isValid).toList();
    }

    @Override
    public List<CountryDataRow> getAllCountryInformationData() {
        return dataRows;
    }

    @Override
    public boolean isNationalityValid(String nationalitySearchValue) {
        for (CountryDataRow row : nationalityData.getDaten()) {
            if (row.matchesAdjective(nationalitySearchValue)) {
                return true; // Match found
            }
        }
        return false; // value does not match any nationalities from the list.
    }

    public DESTATISCountryFile loadNationalityFile() {
        InputStream nationalityStream = CountryServiceImpl.class.getClassLoader().getResourceAsStream(NATIONALITY_CONFIG_FILE);

        try {
            return  parseNationalityFile(nationalityStream);

        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private DESTATISCountryFile parseNationalityFile(InputStream nationalityStream) throws IOException {
        return objectMapper.readValue(
                nationalityStream,
                DESTATISCountryFile.class
        );
    }
}
