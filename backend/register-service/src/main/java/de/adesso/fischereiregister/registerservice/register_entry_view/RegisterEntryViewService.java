package de.adesso.fischereiregister.registerservice.register_entry_view;

import de.adesso.fischereiregister.core.exceptions.LicenseNotFoundException;
import de.adesso.fischereiregister.core.model.Fee;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.ValidityPeriod;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.user.UserRole;
import jakarta.transaction.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface RegisterEntryViewService {
    /**
     * Should be used with care. Will force a truncate of the complete table and only be used in cases where a reset is mandatory.
     */
    void truncateView();

    /**
     * Find a register entry by id.
     * WARNING: This method does not filter out sensitive information. If you do not want unauthorized users to see sensitive information, use {@link #findByRegisterId(UUID, Collection)} instead.
     *
     * @param registerEntryId the id of the register entry
     * @return the register entry view
     */
    RegisterEntryView findByRegisterId(UUID registerEntryId);

    @Transactional
    Optional<RegisterEntryView> findOptionalByRegisterId(UUID registerEntryId);

    /**
     * Find a register entry by id and filter out sensitive information based on the requesting user role.
     *
     * @param registerEntryId the id of the register entry
     * @param userRoles       the roles of the user requesting the register entry
     * @return the register entry view with sensitive information filtered out
     */
    RegisterEntryView findByRegisterId(UUID registerEntryId, Collection<UserRole> userRoles);

    void reorderCardAndUpdateRegisterEntry(UUID registerEntryId,
                                           Person person,
                                           List<Fee> fees,
                                           List<Tax> taxes,
                                           List<IdentificationDocument> identificationDocuments,
                                           String fishingLicenseNumber, // Read only, to change license info
                                           FederalState issuingFederalState
    );

    void deleteBan(UUID registerEntryId);

    void createOrUpdateRegisterEntryView(RegisterEntryViewServiceParameters parameters);

    void updateRegisterEntryView(RegisterEntryViewServiceParameters parameters);

    void createRegisterEntryView(RegisterEntryViewServiceParameters parameters);

    void extendFishingLicense(UUID registerEntryId, String licenseNumber, ValidityPeriod validityPeriod) throws LicenseNotFoundException;

    void rejectLimitedLicenseApplication(UUID registerEntryId, UUID limitedLicenseApplicationId);

    /**
     * Will remove the limited license application from the register entry view and set the status to ACCEPTED.
     */
    void acceptLimitedLicenseApplication(UUID registerEntryId);
}
