package de.adesso.fischereiregister.registerservice.drools.dmn.qualifications_proof;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;

import java.util.Map;

@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
public class QualificationsProofValidationOutput {
    private String exception;

    public QualificationsProofValidationOutput(Map<String, Object> result) {
        this.exception = (String) result.get("Fehlermeldung"); // Fehlermeldung
    }
}
