package de.adesso.fischereiregister.registerservice.domain.mapper;

import de.adesso.fischereiregister.registerservice.statistics.licenses_statistics.LicensesStatistics;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "default")
public interface LicensesStatisticsMapper {

    LicensesStatisticsMapper INSTANCE = Mappers.getMapper(LicensesStatisticsMapper.class);

    List<org.openapitools.model.LicensesStatistics> licensesStatisticsListToApiLicensesStatisticsList(List<LicensesStatistics> licensesStatisticsList);
}
