package de.adesso.fischereiregister.registerservice.tenant;

import api.NamingsApi;
import com.fasterxml.jackson.databind.node.ObjectNode;
import de.adesso.fischereiregister.core.model.type.FederalState;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping()
@AllArgsConstructor
@Slf4j
public class NamingsController implements NamingsApi {

    private final TenantConfigurationService tenantConfigurationService;

    private static final String NAMINGS_TRANSLATIONS_NAMESPACE = "namings";

    @Override
    public ResponseEntity<?> namingsControllerGetAll() {
        final Map<FederalState, ObjectNode> namings = tenantConfigurationService.getTenantConfigurationsForNamespace(NAMINGS_TRANSLATIONS_NAMESPACE);

        return ResponseEntity.ok(namings);
    }
}
