package de.adesso.fischereiregister.registerservice.import_testdata;

import de.adesso.fischereiregister.core.model.Address;
import de.adesso.fischereiregister.core.model.Birthdate;
import de.adesso.fischereiregister.core.model.Fee;
import de.adesso.fischereiregister.core.model.LimitedLicenseApproval;
import de.adesso.fischereiregister.core.model.PaymentInfo;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.QualificationsProof;
import de.adesso.fischereiregister.core.model.SigningEmployee;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.ValidityPeriod;
import de.adesso.fischereiregister.core.model.consent.ConsentInfo;
import de.adesso.fischereiregister.core.model.consent.JurisdictionConsentInfo;
import de.adesso.fischereiregister.core.model.consent.LimitedLicenseConsentInfo;
import de.adesso.fischereiregister.core.model.consent.TaxConsentInfo;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.model.type.PaymentType;
import de.adesso.fischereiregister.core.model.type.QualificationsProofType;
import de.adesso.fischereiregister.core.model.user.UserDetails;
import de.adesso.fischereiregister.core.model.user.UserRole;
import de.adesso.fischereiregister.registerservice.tenant.port.TenantRulesConfigurationPort;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.csv.CSVRecord;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;

import static de.adesso.fischereiregister.utils.DateUtils.parseGermanDate;

/**
 * Service for building object from the domain model out of a csv data row.
 */
@Component
@AllArgsConstructor
@Profile({"dev", "localdev", "test", "stage"})
public class CsvDomainCreationService {

    private static final String PARSED_OFFICE_ADDRESS = "Test data import, no real Order here so no real Office Address.";
    private static final String USER_ID = "SYSTEM";

    private final TenantRulesConfigurationPort tenantConfigurationService;


    public Person buildPerson(CSVRecord csvRecord) {
        final Address address = new Address();
        address.setStreet(csvRecord.get(HeaderConstants.STREET));
        address.setStreetNumber(csvRecord.get(HeaderConstants.STREET_NUMBER));
        address.setPostcode(csvRecord.get(HeaderConstants.POST_CODE));
        address.setCity(csvRecord.get(HeaderConstants.CITY));

        final Person person = new Person();
        final String title = normalizeField(csvRecord.get(HeaderConstants.TITLE));
        person.setTitle(title);
        person.setFirstname(csvRecord.get(HeaderConstants.FIRST_NAME));
        person.setLastname(csvRecord.get(HeaderConstants.LAST_NAME));
        person.setBirthname(csvRecord.get(HeaderConstants.BIRTHNAME));

        final String dateOfBirth = csvRecord.get(HeaderConstants.DATE_OF_BIRTH);
        person.setBirthdate(Birthdate.parse(dateOfBirth));

        final String nationality = normalizeField(csvRecord.get(HeaderConstants.NATIONALITY));
        person.setNationality(nationality);
        person.setBirthplace(csvRecord.get(HeaderConstants.BIRTHPLACE));
        person.setAddress(address);

        return person;
    }

    public List<Tax> buildTaxes(CSVRecord csvRecord, boolean officeFeeAlreadyPayed) {
        final String fishingTaxFrom = csvRecord.get(HeaderConstants.FISHINGTAX_FROM);
        final String fishingTaxUntil = csvRecord.get(HeaderConstants.FISHINGTAX_UNTIL);
        if (fishingTaxFrom != null && !fishingTaxFrom.equalsIgnoreCase("null")) {
            final Tax tax = new Tax();
            final PaymentInfo taxPaymentItem = new PaymentInfo();
            final String federalState = csvRecord.get(HeaderConstants.FEDERAL_STATE);

            final double taxPrice = getTaxPrice(FederalState.valueOf(federalState), officeFeeAlreadyPayed);
            taxPaymentItem.setAmount(taxPrice);

            taxPaymentItem.setType(PaymentType.CARD);
            tax.setValidFrom(parseGermanDate(fishingTaxFrom));
            tax.setValidTo(parseGermanDate(fishingTaxUntil));
            tax.setFederalState(csvRecord.get(HeaderConstants.FEDERAL_STATE));
            tax.setPaymentInfo(taxPaymentItem);
            return List.of(tax);
        }
        return List.of();
    }

    public List<Fee> buildFees(CSVRecord csvRecord) {
        final String fishingFeeFrom = csvRecord.get(HeaderConstants.FISHINGFEE_FROM);
        final String fishingFeeUntil = csvRecord.get(HeaderConstants.FISHINGFEE_TO);
        if (fishingFeeFrom != null && !fishingFeeFrom.equalsIgnoreCase("null")) {
            final PaymentInfo feePaymentItem = new PaymentInfo();
            final LicenseType licenseType = buildLicenseType(csvRecord);
            final double feePrice = getFeePrice(FederalState.valueOf(csvRecord.get(HeaderConstants.FEDERAL_STATE)), licenseType);

            feePaymentItem.setAmount(feePrice);
            feePaymentItem.setType(PaymentType.CASH);

            final Fee fee = new Fee();
            fee.setValidFrom(parseGermanDate(fishingFeeFrom));
            fee.setValidTo(parseGermanDate(fishingFeeUntil));
            fee.setFederalState(csvRecord.get(HeaderConstants.FEDERAL_STATE));
            fee.setPaymentInfo(feePaymentItem);
            return List.of(fee);
        }
        return List.of();
    }

    public ValidityPeriod buildValidityPeriod(CSVRecord csvRecord, LocalDate validFromOverride) {
        final ValidityPeriod validityPeriod = new ValidityPeriod();

        if (validFromOverride != null) {
            validityPeriod.setValidFrom(validFromOverride);
        } else {
            validityPeriod.setValidFrom(parseGermanDate(csvRecord.get(HeaderConstants.FISHINGFEE_FROM)));
        }
        validityPeriod.setValidTo(parseGermanDate(csvRecord.get(HeaderConstants.FISHINGFEE_TO)));

        return validityPeriod;
    }

    public ConsentInfo buildConsentInfo() {
        final ConsentInfo consentInfo = new ConsentInfo();
        consentInfo.setGdprAccepted(true);
        consentInfo.setSelfDisclosureAccepted(true);
        consentInfo.setSubmittedByThirdParty(true);

        return consentInfo;
    }

    public TaxConsentInfo buildTaxConsentInfo() {
        final TaxConsentInfo consentInfo = new TaxConsentInfo();
        consentInfo.setGdprAccepted(true);
        consentInfo.setSubmittedByThirdParty(true);

        return consentInfo;
    }

    public JurisdictionConsentInfo buildJurisdictionConsentInfo() {
        JurisdictionConsentInfo jurisdictionConsentInfo = new JurisdictionConsentInfo();

        jurisdictionConsentInfo.setProofOfMoveVerified(true);
        jurisdictionConsentInfo.setGdprAccepted(true);
        jurisdictionConsentInfo.setSubmittedByThirdParty(false);
        jurisdictionConsentInfo.setSelfDisclosureAccepted(true);

        return jurisdictionConsentInfo;
    }

    public LimitedLicenseConsentInfo buildLimitedLicenseConsentInfo() {
        LimitedLicenseConsentInfo limitedLicenseConsentInfo = new LimitedLicenseConsentInfo();

        limitedLicenseConsentInfo.setDisablityCertificateVerified(true);
        limitedLicenseConsentInfo.setGdprAccepted(true);
        limitedLicenseConsentInfo.setSelfDisclosureAccepted(true);
        limitedLicenseConsentInfo.setSubmittedByThirdParty(true);

        return limitedLicenseConsentInfo;
    }

    public QualificationsProof buildCertificate(CSVRecord csvRecord) {
        // Parse and prepare data for the constructor
        final String fishingCertificateId = csvRecord.get(HeaderConstants.CERTIFICATE_ID).replace(Character.toString('-'), "");
        final String federalState = csvRecord.get(HeaderConstants.FEDERAL_STATE);
        final LocalDate passedOn = parseGermanDate(csvRecord.get(HeaderConstants.EXAMINATION_PASSED_ON));
        final String issuedBy = csvRecord.get(HeaderConstants.ISSUED_BY);

        // Use the constructor for creating the QualificationsProof object
        return new QualificationsProof(
                USER_ID, // Examiner ID
                fishingCertificateId, // Fishing Certificate ID
                issuedBy, // Issued By
                QualificationsProofType.CERTIFICATE, // Type
                federalState, // Federal State
                passedOn // Passed On
        );
    }

    public UserDetails buildUserDetails(CSVRecord csvRecord) {
        final String federalState = csvRecord.get(HeaderConstants.FEDERAL_STATE);
        final String issuer = csvRecord.get(HeaderConstants.ISSUED_BY);
        return new UserDetails(USER_ID, federalState, issuer, PARSED_OFFICE_ADDRESS, issuer, UserRole.ALL_ROLES);
    }

    public LicenseType buildLicenseType(CSVRecord csvRecord) {
        if (csvRecord.get(HeaderConstants.REGULAR_FL) != null && csvRecord.get(HeaderConstants.REGULAR_FL).equalsIgnoreCase("true")) {
            return LicenseType.REGULAR;
        }
        if (csvRecord.get(HeaderConstants.VACATION_FL) != null && csvRecord.get(HeaderConstants.VACATION_FL).equalsIgnoreCase("true")) {
            return LicenseType.VACATION;
        }
        if (csvRecord.get(HeaderConstants.SPECIAL_FL) != null && csvRecord.get(HeaderConstants.SPECIAL_FL).equalsIgnoreCase("true")) {
            return LicenseType.LIMITED;
        }

        throw new IllegalArgumentException("For this event a license type must be specified but no license type was set to true.");
    }


    @SneakyThrows
    private double getTaxPrice(FederalState federalState, boolean officeFeeAlreadyPayed) {
        return tenantConfigurationService.getTaxPriceInformation(federalState, officeFeeAlreadyPayed).stream()
                .filter(info -> info.getDuration() == 1)
                .findAny()
                .orElseThrow(() -> new IllegalStateException("Tax price not found for federal state: " + federalState))
                .getSummOfTaxAmount()
                .doubleValue();
    }

    @SneakyThrows
    private double getFeePrice(FederalState federalState, LicenseType licenseType) {
        return tenantConfigurationService.getLicenseFeeInformation(federalState)
                .stream()
                .filter(info -> info.getLicenseType() == licenseType)
                .findAny()
                .orElseThrow(() -> new IllegalStateException("Fee price not found for federal state: " + federalState))
                .getFeeAmount();
    }

    private String normalizeField(String field) {
        if (field == null || field.trim().isEmpty() || field.trim().equalsIgnoreCase("null")) {
            return null;
        }

        return field;
    }

    public LimitedLicenseApproval buildLimitedLicenseApproval() {
        LimitedLicenseApproval limitedLicenseApproval = new LimitedLicenseApproval();

        limitedLicenseApproval.setCreatedAt(LocalDate.now());
        limitedLicenseApproval.setFileNumber("Testdata import file");
        limitedLicenseApproval.setJustificationForLimitedDurationNotice("Diese Daten wurden durch den Testdatenimport geniert");
        limitedLicenseApproval.setCashRegisterSign("TESTDATA-CASHSIGN");

        final SigningEmployee signingEmployee = buildSigningEmployee();
        limitedLicenseApproval.setSigningEmployee(signingEmployee);

        return limitedLicenseApproval;
    }

    private SigningEmployee buildSigningEmployee() {
        SigningEmployee signingEmployee = new SigningEmployee();

        signingEmployee.setEmail("<EMAIL>");
        signingEmployee.setPersonalSign("TEST DATA IMPORTEUR");
        signingEmployee.setName("Testdaten Importeur");
        signingEmployee.setPhone("000000000000");

        return signingEmployee;
    }
}
