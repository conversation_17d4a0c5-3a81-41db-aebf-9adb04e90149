package de.adesso.fischereiregister.registerservice.domain.mapper.response;

import de.adesso.fischereiregister.card_orders.persistence.CardOrder;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.openapitools.model.CardOrderResponse;

import java.util.List;

@Mapper
public interface CardOrderResponseMapper {
    CardOrderResponseMapper INSTANCE = Mappers.getMapper(CardOrderResponseMapper.class);

    CardOrderResponse toCardOrderResponse(CardOrder cardOrder);

    List<CardOrderResponse> toCardOrderResponseList(List<CardOrder> cardOrders);
}
