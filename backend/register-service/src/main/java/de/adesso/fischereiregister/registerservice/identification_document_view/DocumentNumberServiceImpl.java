package de.adesso.fischereiregister.registerservice.identification_document_view;

import de.adesso.fischereiregister.core.model.type.IdentificationDocumentType;
import de.adesso.fischereiregister.core.ports.DocumentNumberService;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import java.util.UUID;

@Service
@Profile("!dev & !localdev & !test & !stage")
public class DocumentNumberServiceImpl implements DocumentNumberService {

    @Override
    public String createNewDocumentNumber(UUID registerEntryId, IdentificationDocumentType documentType) {
        return this.createNewDocumentNumber();
    }

    public String createNewDocumentNumber() {
        return UUID.randomUUID().toString();
    }
}
