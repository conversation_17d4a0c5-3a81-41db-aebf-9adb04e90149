package de.adesso.fischereiregister.registerservice.admin;

import de.adesso.fischereiregister.card_orders.persistence.CardOrderRepository;
import de.adesso.fischereiregister.registerservice.certificate_numbers_view.CertificateNumbersViewRepository;
import de.adesso.fischereiregister.registerservice.identification_document_view.IdentificationDocumentViewRepository;
import de.adesso.fischereiregister.registerservice.import_testdata.repository.DomainEventEntryRepository;
import de.adesso.fischereiregister.registerservice.import_testdata.repository.TokenEntryRepository;
import de.adesso.fischereiregister.view.register_entry_search.RegisterEntrySearchViewRepository;
import de.adesso.fischereiregister.registerservice.register_entry_view.RegisterEntryViewRepository;
import de.adesso.fischereiregister.view.ban_expiration.persistance.BanExpirationViewRepository;
import de.adesso.fischereiregister.view.licenses_statistics.persistance.LicensesStatisticsViewRepository;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

@AllArgsConstructor
@Component
@Slf4j
@Profile({"dev", "localdev", "test", "stage"})
public class AdminService {
    private final RegisterEntrySearchViewRepository registerEntrySearchViewRepository;
    private final RegisterEntryViewRepository registerEntryViewRepository;
    private final IdentificationDocumentViewRepository identificationDocumentViewRepository;
    private final CertificateNumbersViewRepository certificateNumbersViewRepository;

    private final BanExpirationViewRepository banExpirationReportViewRepository;
    private final LicensesStatisticsViewRepository licenseStatisticsViewRepository;

    private final CardOrderRepository cardOrderRepository;

    private final DomainEventEntryRepository domainEventEntryRepository;
    private final TokenEntryRepository tokenEntryRepository;
    private final JdbcTemplate jdbcTemplate;

    public void truncateAll() {
        log.info("Truncating all tables and resetting all sequences...");

        log.info("Starting truncating all tables...");
        truncateAllTables();
        log.info("... truncating all tables completed");

        log.info("Starting resetting all sequences...");
        resetAllSequences();
        log.info("... resetting all sequences completed");

        log.info("... truncating all tables and resetting all sequences completed");
    }

    private void truncateAllTables() {
        // PostgreSQL query to get all table names
        String tableNameQuery = """
                SELECT table_name 
                    FROM information_schema.tables 
                    WHERE table_schema <> 'pg_catalog'
                          AND table_schema <> 'information_schema'
                          AND table_schema !~ '^pg_toast'
                          AND table_name <> 'databasechangelog'
                          AND table_name <> 'databasechangeloglock'
                """;

        jdbcTemplate.query(tableNameQuery, (rs, rowNum) -> {
            String tableName = rs.getString("table_name");
            log.debug("Truncating table: {}", tableName);
            jdbcTemplate.execute("TRUNCATE TABLE " + tableName + " RESTART IDENTITY CASCADE");
            return tableName;
        });
    }

    /**
     * Resets all sequences in the database to 1.
     * This method queries the database for all sequences and resets each one.
     */
    private void resetAllSequences() {
        // PostgreSQL query to get all sequences
        String sequencesQuery = """
                SELECT sequence_name
                FROM information_schema.sequences
                WHERE sequence_schema = 'public'
                """;

        // Execute the query and process each sequence
        jdbcTemplate.query(sequencesQuery, (rs, rowNum) -> {
            String sequenceName = rs.getString("sequence_name");
            log.debug("Resetting sequence: {}", sequenceName);
            jdbcTemplate.execute("ALTER SEQUENCE " + sequenceName + " RESTART WITH 1");
            return sequenceName;
        });
    }
}
