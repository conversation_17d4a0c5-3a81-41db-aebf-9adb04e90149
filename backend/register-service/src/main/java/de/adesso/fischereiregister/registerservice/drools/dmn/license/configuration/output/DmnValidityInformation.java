package de.adesso.fischereiregister.registerservice.drools.dmn.license.configuration.output;

import com.fasterxml.jackson.annotation.JsonValue;

/**
 * Bestimmt welche Werte sinvollerweise im Output Feld: Gültigkeitsoptionen eingetragen werden können.
 * für LEBENSLANG: bleibt es lehr oder -
 * für TAG sind gültige Werte: 14, 28, 30
 * für VOLLES_JAHR: 1, 2, 3, 4
 * für VOLLER_MONAT: 1, 2,...
*/
public enum DmnValidityInformation {
    LIFE_LONG("LEBENSLANG"),
    DAY("TAG"), //
    FULL_YEAR("VOLLES_JAHR"),
    FULL_MONTH("VOLLER_MONAT"),
    NOT_SET("");

    private String value;

    DmnValidityInformation(String value) {
        this.value = value;
    }

    @JsonValue
    public String getValue() {
        return this.value;
    }

    @Override
    public String toString() {
        return String.valueOf(this.value);
    }

    public static DmnValidityInformation fromValue(String value) {
        for (DmnValidityInformation info : DmnValidityInformation.values()) {
            if (info.value.equals(value)) {
                return info;
            }
        }
        if(value == null || value.isEmpty()) {
            return NOT_SET;
        }
        throw new IllegalStateException("DmnValidityInformation: Unknown value: " + value);
    }
}
