package de.adesso.fischereiregister.registerservice.tenant;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import de.adesso.fischereiregister.core.model.type.FederalState;

import java.util.Map;

/**
 * Service for retrieving configuration data specific to a federal state.
 * Provides access to settings and text variations that may differ per federal state (tenant).
 */
public interface TenantConfigurationService {

    /**
     * Retrieves the full configuration for a given federal state (tenant).
     *
     * @param federalState The federal state (tenant) for which to retrieve the configuration.
     * @return A {@link JsonNode} containing the configuration data.
     */
    ObjectNode getTenantConfiguration(FederalState federalState);

    /**
     * Returns all tenant Configurations for a given namespace.
     */
    Map<FederalState, ObjectNode> getTenantConfigurationsForNamespace(String namespace);

    /**
     * Retrieves a specific configuration value for the given federal state (tenant).
     *
     * <p>Usage Example:</p>
     * <pre>{@code
     * String value = getValue(FederalState.SH, "login.button");
     * System.out.println(value); // Output: "Schleswig-Holstein-ButtonValue" or "login.button" if not found
     * }</pre>
     *
     * @param federalState The federal state (tenant) for which to retrieve the value.
     * @param key          The key for the desired translation value.
     * @return The tenant configuration value as a {@link String}, or the key itself if not found.
     * @throws IllegalArgumentException if the federal state is null or has no configuration.
     */
    String getValue(FederalState federalState, String key);

}
