package de.adesso.fischereiregister.registerservice.register_entry_view;

import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.UUID;

@Repository
public interface RegisterEntryViewRepository extends CrudRepository<RegisterEntryView, UUID> {
	
    Optional<RegisterEntryView> findByRegisterId(UUID id);

}
