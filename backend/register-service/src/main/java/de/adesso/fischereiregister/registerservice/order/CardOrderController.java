package de.adesso.fischereiregister.registerservice.order;

import api.CardOrderApi;
import de.adesso.fischereiregister.card_orders.persistence.CardOrder;
import de.adesso.fischereiregister.card_orders.persistence.OrderStatus;
import de.adesso.fischereiregister.card_orders.services.CardOrderService;
import de.adesso.fischereiregister.core.commands.OrderReplacementCardCommand;
import de.adesso.fischereiregister.core.commands.results.LicenseCommandResult;
import de.adesso.fischereiregister.core.exceptions.CommandResultMismatchException;
import de.adesso.fischereiregister.core.model.Fee;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.consent.ConsentInfo;
import de.adesso.fischereiregister.core.model.user.UserDetails;
import de.adesso.fischereiregister.registerservice.domain.mapper.ConsentInfoMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.FeeMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.PersonMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.TaxMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.response.CardOrderResponseMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.response.OrderCheckCardResponseMapper;
import de.adesso.fischereiregister.registerservice.register_entry_view.RegisterEntryViewService;
import de.adesso.fischereiregister.registerservice.security.UserDetailsService;
import de.adesso.fischereiregister.utils.HashUtils;
import jakarta.persistence.EntityNotFoundException;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.axonframework.commandhandling.gateway.CommandGateway;
import org.openapitools.model.CardOrderResponse;
import org.openapitools.model.CardOrderStatus;
import org.openapitools.model.CardOrderStatusRequest;
import org.openapitools.model.OrderCheckCardRequest;
import org.openapitools.model.OrderCheckCardResponse;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ExecutionException;

@Slf4j
@Controller
@AllArgsConstructor
public class CardOrderController implements CardOrderApi {

    private final UserDetailsService userDetailsService;
    private final CommandGateway commandGateway;
    private final CardOrderService cardOrderService;
    private final RegisterEntryViewService registerEntryViewService;

    @SneakyThrows
    @Override
    public ResponseEntity<?> fishingLicenseOrdersControllerOrder(String registerEntryId,
                                                                 String licenseId,
                                                                 OrderCheckCardRequest request) {
        final UUID registerEntryIdAsUUID = UUID.fromString(registerEntryId);
        final Person person = PersonMapper.INSTANCE.toPerson(request.getPerson());
        final List<Tax> taxes = TaxMapper.INSTANCE.toTaxes(request.getTaxes());
        final List<Fee> fees = FeeMapper.INSTANCE.toFees(request.getFees());
        final ConsentInfo consentInfo = ConsentInfoMapper.INSTANCE.toConsentInfo(request.getConsentInfo());


        final UserDetails userDetails = userDetailsService.getUserDetails()
                .orElseThrow(() -> {
                    log.error("Failed to retrieve user details: User details are missing or corrupted.");
                    return new IllegalStateException("The User Auth Information is missing or corrupted");
                });

        final OrderReplacementCardCommand command = new OrderReplacementCardCommand(registerEntryIdAsUUID,
                licenseId,
                person,
                HashUtils.gensalt(),
                fees,
                taxes,
                consentInfo,
                userDetails
        );

        try {
            final Object result = commandGateway.send(command).get();
            if (result instanceof LicenseCommandResult commandResult) {
                OrderCheckCardResponse response = OrderCheckCardResponseMapper.INSTANCE.toResponse(commandResult);
                final URI location = UriComponentsBuilder.fromPath("/api/orders/{orderId}")
                        .buildAndExpand("0") // this is to be corrected as well
                        .toUri();

                return ResponseEntity.created(location).body(response);
            } else {
                throw new CommandResultMismatchException(LicenseCommandResult.class, result.getClass());
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("ReplacementCardOrderServiceImpl thread interrupted for RegisterID {}", command.registerId(), e);
            throw new IllegalStateException("InterruptedException for registerEntryId:" + command.registerId(), e);
        } catch (ExecutionException e) {
            Throwable cause = e.getCause();
            log.error("ReplacementCardOrderServiceImpl thread ExecutionException for RegisterID {}", command.registerId(),
                    e);
            if (cause != null) {
                throw cause;
            } else {
                throw e;
            }
        }
    }

    @Override
    public ResponseEntity<?> fishingLicenseOrdersControllerGet(String registerEntryId, String licenseNumber) throws EntityNotFoundException {
        final UUID registerEntryIdParsed = UUID.fromString(registerEntryId);

        final var registerEntryView = registerEntryViewService.findByRegisterId(registerEntryIdParsed);

        if (registerEntryView == null) {
            throw new EntityNotFoundException("Register Entry not found");
        }

        final FishingLicense fishingLicense = registerEntryView.getData().getFishingLicenses().stream()
                .filter(license -> license.getNumber().equals(licenseNumber))
                .findFirst()
                .orElse(null);

        if (fishingLicense == null) {
            throw new EntityNotFoundException("Fishing License not found");
        }

        final List<CardOrder> orders = cardOrderService.findOrdersFor(licenseNumber);

        final List<CardOrderResponse> response = CardOrderResponseMapper.INSTANCE.toCardOrderResponseList(orders);

        return ResponseEntity.ok(response);
    }

    @Override
    public ResponseEntity<?> ordersControllerGetOrders(String orderId) throws EntityNotFoundException {
        final CardOrder order = cardOrderService.getCardOrder(UUID.fromString(orderId));

        final CardOrderResponse response = CardOrderResponseMapper.INSTANCE.toCardOrderResponse(order);

        return ResponseEntity.ok(response);
    }

    @Override
    public ResponseEntity<?> ordersControllerUpdateOrderStatus(String orderId, CardOrderStatusRequest cardOrderStatusRequest) throws EntityNotFoundException {

        // map enum value
        final CardOrderStatus openApiOrderStatus = cardOrderStatusRequest.getStatus();
        final OrderStatus orderStatus = OrderStatus.valueOf(openApiOrderStatus.name());
        final String statusNote = cardOrderStatusRequest.getStatusNote();

        cardOrderService.updateCardOrderStatus(UUID.fromString(orderId), orderStatus, statusNote);

        return ResponseEntity.ok().build();
    }
}
