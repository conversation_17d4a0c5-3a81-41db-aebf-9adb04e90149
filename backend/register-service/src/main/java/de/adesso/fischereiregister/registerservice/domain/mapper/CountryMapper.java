package de.adesso.fischereiregister.registerservice.domain.mapper;

import de.adesso.fischereiregister.registerservice.destatis_country.data.CountryDataRow;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import org.openapitools.model.Nationality;

import java.util.List;

@Mapper
public interface CountryMapper {
    CountryMapper INSTANCE = Mappers.getMapper(CountryMapper.class);

    @Mapping(target = "nationalityCode", source = "isoAlpha2")
    @Mapping(target = "name", source = "adjective")
    Nationality toNationality(CountryDataRow countryDataRow);

    List<Nationality> toNationalities(List<CountryDataRow> countryDataRows);
}
