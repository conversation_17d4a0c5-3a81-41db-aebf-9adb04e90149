package de.adesso.fischereiregister.registerservice.jurisdiction;

import api.JurisdictionApi;
import de.adesso.fischereiregister.core.commands.MoveJurisdictionCommand;
import de.adesso.fischereiregister.core.commands.results.JurisdictionCommandResult;
import de.adesso.fischereiregister.core.exceptions.CommandResultMismatchException;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.consent.JurisdictionConsentInfo;
import de.adesso.fischereiregister.core.model.user.UserDetails;
import de.adesso.fischereiregister.registerservice.domain.mapper.JurisdictionConsentInfoMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.TaxMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.response.JurisdictionResponseMapper;
import de.adesso.fischereiregister.registerservice.security.UserDetailsService;
import de.adesso.fischereiregister.utils.HashUtils;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.axonframework.commandhandling.gateway.CommandGateway;
import org.openapitools.model.JurisdictionResponse;
import org.openapitools.model.UpdateJurisdictionRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.UUID;
import java.util.concurrent.ExecutionException;

@RestController
@AllArgsConstructor
@Slf4j
public class JurisdictionController implements JurisdictionApi {

    private final UserDetailsService userDetailsService;
    private final CommandGateway commandGateway;


    @SneakyThrows
    @Override
    public ResponseEntity<JurisdictionResponse> jurisdictionControllerUpdate(String registerEntryId, UpdateJurisdictionRequest request) {

        final JurisdictionConsentInfo jurisdictionConsentInfo = JurisdictionConsentInfoMapper.INSTANCE.toJurisdictionConsentInfo(request.getConsentInfo());
        final List<Tax> taxes = TaxMapper.INSTANCE.toTaxes(request.getTaxes());

        final UserDetails userDetails = userDetailsService.getUserDetails()
                .orElseThrow(() -> {
                    log.error("Failed to retrieve user details: User details are missing or corrupted.");
                    return new IllegalStateException("The User Auth Information is missing or corrupted");
                });

        final MoveJurisdictionCommand command = new MoveJurisdictionCommand(
                UUID.fromString(registerEntryId),
                jurisdictionConsentInfo,
                HashUtils.gensalt(),
                taxes,
                userDetails
        );

        try {
            final Object result = commandGateway.send(command).get();

            if (result instanceof JurisdictionCommandResult commandResult) {
                final JurisdictionResponse response = JurisdictionResponseMapper.INSTANCE.toResponse(commandResult);

                return ResponseEntity.ok().body(response);
            } else {
                throw new CommandResultMismatchException(JurisdictionCommandResult.class, result.getClass());
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error(" '/register-entries/{registerEntryId}/jurisdiction:move' thread interrupted for RegisterID {}", command.registerId(), e);
            throw new IllegalStateException("InterruptedException for registerId:" + command.registerId(), e);
        } catch (ExecutionException e) {
            Throwable cause = e.getCause();
            log.error(" '/register-entries/{registerEntryId}/jurisdiction:move' thread ExecutionException for RegisterID {}", command.registerId(),
                    e);
            if (cause != null) {
                throw cause;  // Unwrapped cause
            } else {
                throw e;  // Original exception
            }
        }

    }

}
