package de.adesso.fischereiregister.registerservice.protocol;

import de.adesso.fischereiregister.core.model.Person;
import lombok.Generated;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Generated
@Slf4j
public class ProtocolServiceImpl implements ProtocolService {
    @Override
    public void protocolFailedMessage(String identificationNumber, Person person) {
        // beware: person can be null
        // beware: identificationNumber can be null
        if (identificationNumber == null) {
            log.info("Protocol failed message. ");
            return;
        }

        if (person == null) {
            log.info("Protocol failed message. Identification number: {}, person: null", identificationNumber);
            return;
        }

        log.info("Protocol failed message. Identification number: {}, person: {}", identificationNumber, person.getFirstname() + person.getLastname());
    }

}
