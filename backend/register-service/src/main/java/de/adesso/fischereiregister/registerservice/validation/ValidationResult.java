package de.adesso.fischereiregister.registerservice.validation;


import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Getter
public class ValidationResult {

    private UUID validationResultId = UUID.randomUUID();

    private List<String> errorNotes = new ArrayList<>();

    public ValidationResult(List<String> errorNotes) {
        this.errorNotes = errorNotes;
    }

    public boolean hasErrors() {
        return !errorNotes.isEmpty();
    }

}