package de.adesso.fischereiregister.registerservice.domain.mapper;

import de.adesso.fischereiregister.registerservice.statistics.certifications_statistics.CertificationsStatistics;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "default")
public interface CertificationsStatisticsMapper {

    CertificationsStatisticsMapper INSTANCE = Mappers.getMapper(CertificationsStatisticsMapper.class);

    List<org.openapitools.model.CertificationsStatistics> certificationsStatisticsListToApiCertificationsStatisticsList(List<CertificationsStatistics> certificationsStatisticsList);
}
