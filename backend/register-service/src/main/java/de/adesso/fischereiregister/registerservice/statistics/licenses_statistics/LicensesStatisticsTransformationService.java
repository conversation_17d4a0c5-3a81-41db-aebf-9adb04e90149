package de.adesso.fischereiregister.registerservice.statistics.licenses_statistics;

import de.adesso.fischereiregister.view.licenses_statistics.persistance.LicensesStatisticsView;

import java.util.List;

/**
 * Transforms {@link LicensesStatisticsView} objects into {@link LicensesStatistics} objects, grouped by year.
 */
public interface LicensesStatisticsTransformationService {

    /**
     * Converts a list of {@link LicensesStatisticsView} objects into a list of {@link LicensesStatistics} objects.
     * Returns entries for all requested years, filling missing data with zeroes.
     *
     * @param statisticsViews The list of raw statistical data to transform.
     * @param yearsToQuery The complete list of years that should be included in the response.
     * @return A list of transformed {@link LicensesStatistics} objects for all requested years.
     */
    List<LicensesStatistics> transformToLicensesStatistics(List<LicensesStatisticsView> statisticsViews, List<Integer> yearsToQuery);
}