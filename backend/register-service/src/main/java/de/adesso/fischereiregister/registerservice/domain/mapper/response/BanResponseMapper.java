package de.adesso.fischereiregister.registerservice.domain.mapper.response;

import de.adesso.fischereiregister.core.commands.results.BanCommandResult;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.openapitools.model.CreateBanResponse;

@Mapper(componentModel = "default")
public interface BanResponseMapper {

    BanResponseMapper INSTANCE = Mappers.getMapper(BanResponseMapper.class);

    CreateBanResponse toResponse(BanCommandResult commandResult);

}
