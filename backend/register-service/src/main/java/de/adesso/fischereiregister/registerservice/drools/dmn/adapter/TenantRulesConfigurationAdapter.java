package de.adesso.fischereiregister.registerservice.drools.dmn.adapter;

import de.adesso.fischereiregister.core.exceptions.RulesProcessingException;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.registerservice.drools.dmn.DmnDroolsService;
import de.adesso.fischereiregister.registerservice.drools.dmn.license.configuration.input.DmnLicenseType;
import de.adesso.fischereiregister.registerservice.drools.dmn.license.configuration.output.LicenseInformationOutput;
import de.adesso.fischereiregister.registerservice.drools.dmn.model.DmnProcessingType;
import de.adesso.fischereiregister.registerservice.drools.dmn.tax.configuration.TaxInformationOutput;
import de.adesso.fischereiregister.registerservice.tenant.port.TenantRulesConfigurationPort;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@AllArgsConstructor
public class TenantRulesConfigurationAdapter implements TenantRulesConfigurationPort {

    private final DmnDroolsService dmnDroolsService;

    @Override
    public List<LicenseInformationOutput> getLicenseFeeInformation(FederalState federalState) throws RulesProcessingException {
        List<LicenseInformationOutput> results = new ArrayList<>();

        results.addAll(dmnDroolsService.getLicenseInformation(federalState, DmnLicenseType.VACATION, DmnProcessingType.ANALOG));
        results.addAll(dmnDroolsService.getLicenseInformation(federalState, DmnLicenseType.REGULAR, DmnProcessingType.ANALOG));
        results.addAll(dmnDroolsService.getLicenseInformation(federalState, DmnLicenseType.LIMITED, DmnProcessingType.ANALOG));

        return results;
    }

    @Override
    public List<TaxInformationOutput> getTaxPriceInformation(FederalState federalState, boolean officeFeeAlreadyPayed) throws RulesProcessingException {
        return dmnDroolsService.getTaxInformation(federalState, officeFeeAlreadyPayed);
    }


}
