package de.adesso.fischereiregister.registerservice.protocol;

import de.adesso.fischereiregister.core.model.Person;

public interface ProtocolService {
    /**
     * This will create a protocol that for the given person and identification number an Online Service request failed.
     *
     * @param identificationNumber can be null
     * @param person             can be null
     */
    void protocolFailedMessage(String identificationNumber, Person person);
}
