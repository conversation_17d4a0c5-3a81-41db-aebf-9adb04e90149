package de.adesso.fischereiregister.registerservice.personal_data;

import de.adesso.fischereiregister.core.commands.ChangePersonalDataCommand;
import de.adesso.fischereiregister.core.commands.CreatePersonCommand;
import de.adesso.fischereiregister.core.commands.results.ChangePersonCommandResult;
import de.adesso.fischereiregister.core.commands.results.PersonCommandResult;
import de.adesso.fischereiregister.core.exceptions.CommandResultMismatchException;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.consent.ConsentInfo;
import de.adesso.fischereiregister.core.model.consent.TaxConsentInfo;
import de.adesso.fischereiregister.core.model.user.UserDetails;
import de.adesso.fischereiregister.registerservice.domain.mapper.ConsentInfoMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.PersonMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.TaxConsentInfoMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.TaxMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.response.ChangePersonalDataResponseMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.response.CreatePersonResponseMapper;
import de.adesso.fischereiregister.registerservice.security.UserDetailsService;
import de.adesso.fischereiregister.utils.HashUtils;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.axonframework.commandhandling.gateway.CommandGateway;
import org.openapitools.model.ChangePersonalDataResponse;
import org.openapitools.model.CreatePersonRequest;
import org.openapitools.model.CreatePersonResponse;
import org.openapitools.model.UpdatePersonRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.net.URI;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ExecutionException;

@Slf4j
@RestController
@RequestMapping
@AllArgsConstructor
public class PersonController implements api.PersonApi {

    private final CommandGateway commandGateway;
    private final UserDetailsService userDetailsService;

    @SneakyThrows
    @Override
    @PostMapping(path = "/register-entries/person")
    public ResponseEntity<?> personControllerCreate(CreatePersonRequest request) {
        final Person person = PersonMapper.INSTANCE.toPerson(request.getPerson());
        final List<Tax> taxes = TaxMapper.INSTANCE.toTaxes(request.getTaxes());
        final List<Tax> payedTaxes = TaxMapper.INSTANCE.toTaxes(request.getPayedTaxes());
        final TaxConsentInfo consentInfo = TaxConsentInfoMapper.INSTANCE.toTaxConsentInfo(request.getConsentInfo());

        final UserDetails userDetails = userDetailsService.getUserDetails()
                .orElseThrow(() -> {
                    log.error("Failed to retrieve user details: User details are missing or corrupted.");
                    return new IllegalStateException("The User Auth Information is missing or corrupted");
                });

        final CreatePersonCommand command = new CreatePersonCommand(
                UUID.randomUUID(),
                person,
                taxes,
                payedTaxes,
                HashUtils.gensalt(),
                consentInfo,
                userDetails);

        try {

            final Object result = commandGateway.send(command).get();

            if (result instanceof PersonCommandResult commandResult) {
                final CreatePersonResponse response = CreatePersonResponseMapper.INSTANCE.toResponse(commandResult);
                final URI location = URI.create("/api/register-entries/" + command.registerId());

                return ResponseEntity.created(location).body(response);
            } else {
                throw new CommandResultMismatchException(PersonCommandResult.class, result.getClass());
            }

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Thread interrupted while waiting for CreatePersonCommand with ID: {} to be processed. Interrupt status reset.",
                    command.registerId(), e);
            throw new IllegalStateException("Thread interrupted during command processing", e);
        } catch (ExecutionException e) {
            Throwable cause = e.getCause();

            log.error("ExecutionException occurred while processing CreatePersonCommand with ID: {}. Cause: {}",
                    command.registerId(), cause != null ? cause.getMessage() : "Unknown", e);

            if (cause != null) {
                throw cause;  // Unwrapped cause
            } else {
                throw e;  // Original exception
            }
        }
    }

    @SneakyThrows
    @Override
    public ResponseEntity<?> personControllerUpdate(String registerEntryId, UpdatePersonRequest request) {
        final Person person = PersonMapper.INSTANCE.toPerson(request.getPerson());
        final List<Tax> taxes = TaxMapper.INSTANCE.toTaxes(request.getTaxes());
        final ConsentInfo consentInfo = ConsentInfoMapper.INSTANCE.toConsentInfo(request.getConsentInfo());

        final UserDetails userDetails = userDetailsService.getUserDetails()
                .orElseThrow(() -> {
                    log.error("Failed to retrieve user details: User details are missing or corrupted.");
                    return new IllegalStateException("The User Auth Information is missing or corrupted");
                });

        final ChangePersonalDataCommand command = new ChangePersonalDataCommand(
                UUID.fromString(registerEntryId),
                person,
                taxes,
                consentInfo,
                HashUtils.gensalt(),
                userDetails
        );

        try {
            final Object result = commandGateway.send(command).get();

            if (result instanceof ChangePersonCommandResult commandResult) {
                final ChangePersonalDataResponse response = ChangePersonalDataResponseMapper.INSTANCE.toResponse(commandResult);

                return ResponseEntity.ok().body(response);
            } else {
                throw new CommandResultMismatchException(ChangePersonCommandResult.class, result.getClass());
            }

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Failed to change personal data", e);
            throw new IllegalStateException(e);
        } catch (ExecutionException e) {
            Throwable cause = e.getCause();
            log.error("Failed to change personal data", e);
            if (cause != null) {
                throw cause;  // Unwrapped cause
            } else {
                throw e;  // Original exception
            }
        }
    }


}
