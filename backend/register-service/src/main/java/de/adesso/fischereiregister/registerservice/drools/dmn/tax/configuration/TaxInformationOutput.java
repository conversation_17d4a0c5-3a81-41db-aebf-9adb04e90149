package de.adesso.fischereiregister.registerservice.drools.dmn.tax.configuration;

import lombok.Getter;

import java.math.BigDecimal;
import java.util.Map;

@Getter
public class TaxInformationOutput {

    public static final String NETTOBETRAG_ABGABE = "Nettobetrag Abgabe";
    public static final String VERWALTUNGSGEBUEHR = "Verwaltungsgebühr";
    public static final String GEBUEHR_BEHOERDENGANG = "Gebühr Behördengang";
    public static final String JAHRE = "Jahre";

    /*
    In Germany a Tax (Abgabe) can be put togehter/ contain fees which is the case here.
    The sum of the Tax Amount (summOfTaxAmount) contains netTaxAmount and two fees (officeFee and generalFee)
     */
    private BigDecimal netTaxAmount; // Output: Nettobetrag Abgabe
    private BigDecimal administrativeFee; // Output: Verwaltungsgebühr
    private BigDecimal officeFee; // Output: Gebühr Behördengang
    private int duration;
    private BigDecimal summOfTaxAmount;

    public TaxInformationOutput(Map<String, Object> result) {
        this.netTaxAmount = getBigDecimalOrDefault(result.get(NETTOBETRAG_ABGABE));
        this.administrativeFee = getBigDecimalOrDefault(result.get(VERWALTUNGSGEBUEHR));
        this.officeFee = getBigDecimalOrDefault(result.get(GEBUEHR_BEHOERDENGANG));
        this.duration = getIntOrDefault(result.get(JAHRE));
        this.summOfTaxAmount = netTaxAmount.add(administrativeFee).add(officeFee);

    }

    private BigDecimal getBigDecimalOrDefault(Object value) {
        return value instanceof BigDecimal ? (BigDecimal) value : BigDecimal.ZERO;
    }

    private int getIntOrDefault(Object value) {
        return value instanceof BigDecimal ? ((BigDecimal) value).intValue() : 0;
    }

}
