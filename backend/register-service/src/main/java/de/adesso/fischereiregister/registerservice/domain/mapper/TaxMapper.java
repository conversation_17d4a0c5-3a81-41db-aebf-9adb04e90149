package de.adesso.fischereiregister.registerservice.domain.mapper;

import de.adesso.fischereiregister.core.model.Tax;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper()
public interface TaxMapper {

    TaxMapper INSTANCE = Mappers.getMapper(TaxMapper.class);

    @Mapping(target = "taxId", expression = "java(java.util.UUID.randomUUID().toString())")
    @Mapping(target = "federalState", expression = "java(dto.getFederalState().getValue())")
    Tax toTax(org.openapitools.model.Tax dto);

    @Mapping(target = "taxId", expression = "java(java.util.UUID.randomUUID().toString())")
    List<Tax> toTaxes(List<org.openapitools.model.Tax> dtos);
}
