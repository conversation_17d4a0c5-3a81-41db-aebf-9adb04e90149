package de.adesso.fischereiregister.registerservice.import_testdata.repository;

import jakarta.transaction.Transactional;
import org.axonframework.eventsourcing.eventstore.jpa.DomainEventEntry;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DomainEventEntryRepository extends JpaRepository<DomainEventEntry, Integer> {

    @Transactional
    List<DomainEventEntry> findByAggregateIdentifier(String aggregateIdentifier);

}
