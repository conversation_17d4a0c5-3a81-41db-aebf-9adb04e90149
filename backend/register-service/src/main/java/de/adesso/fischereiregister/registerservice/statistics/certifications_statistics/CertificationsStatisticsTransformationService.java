package de.adesso.fischereiregister.registerservice.statistics.certifications_statistics;

import de.adesso.fischereiregister.view.certifications_statistics.persistence.CertificationsStatisticsView;

import java.util.List;

/**
 * Service for transforming certification statistics view objects into domain model objects.
 */
public interface CertificationsStatisticsTransformationService {
    /**
     * Transforms a list of CertificationsStatisticsView objects into a list of CertificationsStatistics objects.
     * Returns entries for all requested years, filling missing data with zeroes.
     * All years will share the same set of offices (issuers) extracted from the provided views.
     *
     * @param statisticsViews The list of CertificationsStatisticsView objects to transform.
     * @param yearsToQuery The complete list of years that should be included in the response.
     * @return A list of CertificationsStatistics objects for all requested years.
     */
    List<CertificationsStatistics> transformToCertificationsStatistics(List<CertificationsStatisticsView> statisticsViews, List<Integer> yearsToQuery);
}
