package de.adesso.fischereiregister.registerservice.domain.mapper.dmn;

import de.adesso.fischereiregister.registerservice.domain.mapper.LicenseTypeMapper;
import de.adesso.fischereiregister.registerservice.drools.dmn.license.configuration.output.LicenseInformationOutput;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "default", uses = {LicenseTypeMapper.class})
public interface LicenseInformationResponseMapper {

    LicenseInformationResponseMapper INSTANCE = Mappers.getMapper(LicenseInformationResponseMapper.class);

    @Mapping(target = "isExtendable", source = "extendable")
    @Mapping(target = "isAvailable", source = "available")
    @Mapping(target = "isTimeLimitable", source = "timeLimitable")
    org.openapitools.model.LicenseInformation toResponse(LicenseInformationOutput dmnResult);

    List<org.openapitools.model.LicenseInformation> toResponse(List<LicenseInformationOutput> dmnResult);

}
