package de.adesso.fischereiregister.registerservice.online_services.eventhandling;

import com.fasterxml.jackson.core.JsonProcessingException;
import de.adesso.fischereiregister.core.events.FishingTaxPayedEvent;
import de.adesso.fischereiregister.core.events.LicenseExtendedEvent;
import de.adesso.fischereiregister.core.events.LimitedLicenseApplicationCreatedEvent;
import de.adesso.fischereiregister.core.events.LimitedLicenseApplicationRejectedEvent;
import de.adesso.fischereiregister.core.events.LimitedLicenseCreatedEvent;
import de.adesso.fischereiregister.core.events.PersonCreatedEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseCreatedEvent;
import de.adesso.fischereiregister.core.events.ReplacementCardOrderedEvent;
import de.adesso.fischereiregister.core.events.VacationLicenseCreatedEvent;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.registerservice.online_services.message.OSSuccessMessageService;
import lombok.AllArgsConstructor;
import org.axonframework.eventhandling.EventHandler;
import org.axonframework.eventhandling.ReplayStatus;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class MessageServiceHandler {

    private final OSSuccessMessageService osSuccessMessageService;

    @EventHandler()
    public void on(ReplacementCardOrderedEvent event, ReplayStatus replayStatus) throws JsonProcessingException {
        // Check if we are in a replay
        if (replayStatus.isReplay()) {
            // Skip sending the message if this is a replay
            return;
        }

        if (event.inboxReference() != null && !event.inboxReference().isBlank()) {
            osSuccessMessageService.handleLicenseReplacementSuccess(event.inboxReference(), event.person(), event.registerId(), event.federalState(), event.salt(), event.identificationDocuments());
        }
    }

    @EventHandler()
    public void on(RegularLicenseCreatedEvent event, ReplayStatus replayStatus) throws JsonProcessingException {
        // Check if we are in a replay
        if (replayStatus.isReplay()) {
            // Skip sending the message if this is a replay
            return;
        }

        FederalState federalState = FederalState.valueOf(event.jurisdiction().getFederalState());
        if (event.inboxReference() != null && !event.inboxReference().isBlank()) {
            osSuccessMessageService.handleRegularLicenseCreationSuccess(event.inboxReference(), federalState, event.person(), event.registerId(), event.salt(), event.identificationDocuments());
        }
    }

    @EventHandler()
    public void on(PersonCreatedEvent event, ReplayStatus replayStatus) throws JsonProcessingException {
        // Check if we are in a replay
        if (replayStatus.isReplay()) {
            // Skip sending the message if this is a replay
            return;
        }


        final FederalState federalState = FederalState.valueOf(event.federalState());
        if (event.inboxReference() != null && !event.inboxReference().isBlank()) {
            osSuccessMessageService.handleRegularLicenseCreationSuccess(event.inboxReference(), federalState, event.person(), event.registerId(), event.salt(), event.identificationDocuments());
        }
    }

    @EventHandler()
    public void on(FishingTaxPayedEvent event, ReplayStatus replayStatus) throws JsonProcessingException {
        // Check if we are in a replay
        if (replayStatus.isReplay()) {
            // Skip sending the message if this is a replay
            return;
        }

        if (event.inboxReference() != null && !event.inboxReference().isBlank()) {
            osSuccessMessageService.handleTaxPaymentSuccess(event.inboxReference(), event.person(), event.registerId(), event.salt(), event.identificationDocuments());
        }
    }

    @EventHandler()
    public void on(VacationLicenseCreatedEvent event, ReplayStatus replayStatus) throws JsonProcessingException {
        // Check if we are in a replay
        if (replayStatus.isReplay()) {
            // Skip sending the message if this is a replay
            return;
        }

        if (event.inboxReference() != null && !event.inboxReference().isBlank()) {
            FederalState federalState = event.fishingLicense().getIssuingFederalState();

            osSuccessMessageService.handleVacationLicenseCreationSuccess(
                    event.inboxReference(),
                    federalState,
                    event.person(),
                    event.registerEntryId(),
                    event.salt(),
                    event.identificationDocuments());
        }
    }

    @EventHandler()
    public void on(LimitedLicenseApplicationCreatedEvent event, ReplayStatus replayStatus) throws JsonProcessingException {
        // Check if we are in a replay
        if (replayStatus.isReplay()) {
            // Skip sending the message if this is a replay
            return;
        }

        if (event.inboxReference() != null && !event.inboxReference().isBlank()) {
            FederalState federalState = event.limitedLicenseApplication().getFederalState();

            osSuccessMessageService.handleLimitedLinseApplicationPending(
                    event.inboxReference(),
                    federalState,
                    event.person(),
                    event.registerEntryId());
        }
    }

    @EventHandler()
    public void on(LimitedLicenseCreatedEvent event, ReplayStatus replayStatus) throws JsonProcessingException {
        // Check if we are in a replay
        if (replayStatus.isReplay()) {
            // Skip sending the message if this is a replay
            return;
        }

        if (event.inboxReference() != null && !event.inboxReference().isBlank()) {
            osSuccessMessageService.handleLimitedLicenseCreated(
                    event.inboxReference(),
                    event.fishingLicense().getIssuingFederalState(),
                    event.person(),
                    event.registerId(),
                    event.salt(),
                    event.identificationDocuments());
        }
    }

    @EventHandler
    public void on(LimitedLicenseApplicationRejectedEvent event, ReplayStatus replayStatus) throws JsonProcessingException {
        // Check if we are in a replay
        if (replayStatus.isReplay()) {
            // Skip sending the message if this is a replay
            return;
        }

        if (event.inboxReference() != null && !event.inboxReference().isBlank()) {
            FederalState federalState = event.limitedLicenseApplication().getFederalState();

            osSuccessMessageService.handleLimitedLicenseApplicationRejected(
                    event.inboxReference(),
                    federalState,
                    event.person(),
                    event.registerEntryId());
        }
    }

    @EventHandler()
    public void on(LicenseExtendedEvent event, ReplayStatus replayStatus) throws JsonProcessingException {
        // Check if we are in a replay
        if (replayStatus.isReplay()) {
            // Skip sending the message if this is a replay
            return;
        }

        if (event.inboxReference() != null && !event.inboxReference().isBlank()) {
            osSuccessMessageService.handleFishingLicenseExtendedSuccess(event.inboxReference(), event.person(), event.registerEntryId(), event.salt(), event.identificationDocuments());
        }
    }

}
