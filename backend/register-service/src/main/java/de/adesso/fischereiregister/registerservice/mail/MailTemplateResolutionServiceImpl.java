package de.adesso.fischereiregister.registerservice.mail;

import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.utils.PersonUtils;
import de.adesso.fischereiregister.registerservice.mail.enums.MailTemplate;
import de.adesso.fischereiregister.registerservice.tenant.TenantConfigurationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class MailTemplateResolutionServiceImpl implements MailTemplateResolutionService {

    private static final String DEFAULT_CITIZEN_NAME_KEY = "mail_template.default.citizen_name";
    private static final String CITIZEN_FULLNAME_TOKEN = "{{citizenFullname}}";
    private static final String HTML_LINEBREAK = "<br>";
    private static final String LINEFEED_CHAR = "\n";

    private final TenantConfigurationService tenantConfigurationService;

    @Override
    public String getSubject(FederalState federalState, MailTemplate mailTemplate) {
        final String templateKey = mailTemplate.getSubjectKey();
        return tenantConfigurationService.getValue(federalState, templateKey);
    }

    @Override
    public String getText(FederalState federalState, MailTemplate mailTemplate, Person person) {
        final String textTemplateKey = mailTemplate.getTextKey();

        final String citizenName;
        if (person != null) {
            citizenName = PersonUtils.getFullName(person);
        } else {
            log.warn("Person is null while generating mail text for state '{}', using default citizen name.", federalState);
            citizenName = tenantConfigurationService.getValue(federalState, DEFAULT_CITIZEN_NAME_KEY);
        }

        final String templateText = tenantConfigurationService.getValue(federalState, textTemplateKey);
        final String replacedText = templateText.replace(CITIZEN_FULLNAME_TOKEN, citizenName);

        return replaceHtmlLineBreaks(replacedText);
    }

    private String replaceHtmlLineBreaks(String text) {
        return text.replace(HTML_LINEBREAK, LINEFEED_CHAR);
    }

}
