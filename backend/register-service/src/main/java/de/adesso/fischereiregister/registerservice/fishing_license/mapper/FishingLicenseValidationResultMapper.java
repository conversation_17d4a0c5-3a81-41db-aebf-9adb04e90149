package de.adesso.fischereiregister.registerservice.fishing_license.mapper;

import de.adesso.fischereiregister.registerservice.domain.mapper.LicenseTypeMapper;
import de.adesso.fischereiregister.registerservice.validate_fishing_license.model.FishingLicenseValidationResult;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "default", uses= {LicenseTypeMapper.class})
public interface FishingLicenseValidationResultMapper {

    FishingLicenseValidationResultMapper INSTANCE = Mappers.getMapper(FishingLicenseValidationResultMapper.class);

    org.openapitools.model.ValidationResponse toResponse(
            FishingLicenseValidationResult fishingLicenseValidationResult);

}
