package de.adesso.fischereiregister.registerservice.identification_document_view;

import java.util.UUID;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table
@Getter
@Setter
public class IdentificationDocumentView {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id; // technical ID

    @Column(nullable = false)
    private UUID registerId;

    @Column(unique = true, nullable = true)
    private String licenseNumber;

    @Column
    private String identificationDocumentId;

    @Column
    private String salt;

    @Column
    private String documentType;
    
}
