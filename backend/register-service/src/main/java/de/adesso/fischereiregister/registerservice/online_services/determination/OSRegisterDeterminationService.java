package de.adesso.fischereiregister.registerservice.online_services.determination;

import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.registerservice.online_services.model.OSRegisterDeterminationResult;

/**
 * Service interface for determining a register entry ID based on provided person details.
 * This service facilitates the search for a register entry using a combination of an
 * identification number and person details.
 *
 * <p>The result of the operation can include:
 * <ul>
 *   <li>A successful determination of the register entry ID.</li>
 *   <li>An error result if no matching person is found.</li>
 *   <li>An error result if multiple persons are found with the same provided details,
 *       indicating ambiguity in the data.</li>
 * </ul>
 * </p>
 */
public interface OSRegisterDeterminationService {

    /**
     * Determines the register entry ID for a given person based on their identification number
     * and personal details.
     *
     * @param identificationNumber the identification number of the person, it can be a licenseNumber or a CertificateNumber
     * @param person the {@link Person} object containing the person's details for use in the search.
     * @return an {@link OSRegisterDeterminationResult} containing the outcome of the search,
     *         which may include a register entry ID, or an error result in case of no match or ambiguity.
     */
    OSRegisterDeterminationResult determineRegisterEntryId(String identificationNumber, Person person);

    /**
     * Determines the register entry ID for a given person based on their personal details.
     * Here if we do not find a person or we do find a lot of persons we will return a new UUID because we want to create a new register entry.
     *
     * @param person
     * @return
     */
    OSRegisterDeterminationResult determineOrCreateRegisterEntryId(Person person);

    /**
     * Determines the register entry ID for a given person based on their personal details.
     *
     * @param identificationNumber
     * @return
     */
    OSRegisterDeterminationResult determineRegisterEntryId(String identificationNumber);
}
