package de.adesso.fischereiregister.registerservice.destatis_country.data;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class CountryMetadata {
    private String kennung;
    private String kennungInhalt;
    private String version;
    private List<CountryNameValue> nameKurz;
    private List<CountryNameValue> nameLang;
    private String nameTechnisch;
    private List<CountryNameValue> herausgebernameLang;
    private List<CountryNameValue> herausgebernameKurz;
    private List<CountryNameValue> beschreibung;
    private List<CountryNameValue> versionBeschreibung;
    private List<CountryNameValue> aenderungZurVorversion;
    private String handbuchVersion;
    private boolean xoevHandbuch;
    private String gueltigAb;
    private List<String> bezugsorte;
}
