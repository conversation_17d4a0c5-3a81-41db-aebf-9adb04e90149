package de.adesso.fischereiregister.registerservice.certificate_numbers_view;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

@Repository
public interface CertificateNumbersViewRepository extends CrudRepository<CertificateNumbersView, String> {

    @Query("SELECT p.certificateNumber FROM CertificateNumbersView p")
    List<String> findAllNumbers();

    CertificateNumbersView findByRegisterEntryId(UUID registerEntryId);

}
