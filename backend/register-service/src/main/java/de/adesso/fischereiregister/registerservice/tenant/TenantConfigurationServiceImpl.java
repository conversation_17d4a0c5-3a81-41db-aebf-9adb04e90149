package de.adesso.fischereiregister.registerservice.tenant;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import de.adesso.fischereiregister.core.model.type.FederalState;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.yaml.snakeyaml.DumperOptions;
import org.yaml.snakeyaml.Yaml;

import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.EnumMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TenantConfigurationServiceImpl implements TenantConfigurationService {

    private static final String DEFAULT_CONFIG_FILE = "config/tenant/default.yml";
    private static final String CONFIG_PATTERN = "config/tenant/?.yml";

    private final Map<FederalState, ObjectNode> tenantConfigurations = new EnumMap<>(FederalState.class);

    public TenantConfigurationServiceImpl() {
        loadAllTenantConfigurations();
    }

    @Override
    public ObjectNode getTenantConfiguration(FederalState federalState) {
        ObjectNode config = tenantConfigurations.get(federalState);
        if (config == null) {
            log.warn("No tenant configuration found for federal state: {}", federalState);
        }
        return config;
    }

    @Override
    public Map<FederalState, ObjectNode> getTenantConfigurationsForNamespace(String namespace) {
        FederalState[] federalStates = FederalState.values();

        return Arrays.stream(federalStates)
                .filter(this.tenantConfigurations::containsKey)
                .collect(Collectors.toMap(
                        federalState -> federalState,
                        federalState -> (ObjectNode) this.tenantConfigurations.get(federalState).findValue(namespace)
                ));
    }

    @Override
    public String getValue(FederalState federalState, String key) {
        ObjectNode rootNode = tenantConfigurations.get(federalState);
        if (rootNode == null) {
            log.warn("tried getting value: {} for federal state: {}, but No tenant configuration was found", key, federalState);
            throw new IllegalArgumentException("No Tenant configuration found for state: " + federalState);
        }

        String[] pathSegments = key.split("\\.");
        JsonNode currentNode = rootNode;
        for (String segment : pathSegments) {
            currentNode = currentNode.path(segment);
            if (currentNode.isMissingNode() || currentNode.isNull()) {
                log.warn("Configuration key not found: {} for federal state: {}", key, federalState);
                return key;
            }
        }

        return currentNode.asText();
    }

    private void loadAllTenantConfigurations() {
        try {
            for (FederalState federalState : FederalState.values()) {
                final ObjectNode tenantConfiguration = this.loadTenantConfiguration(federalState);
                tenantConfigurations.put(federalState, tenantConfiguration);
            }
        } catch (IOException e) {
            log.error("Failed to load tenant configurations", e);
            throw new RuntimeException(e);
        }
    }

    private ObjectNode loadTenantConfiguration(FederalState federalState) throws IOException {
        final ClassLoader classLoader = TenantConfigurationService.class.getClassLoader();

        final InputStream defaultStream = classLoader.getResourceAsStream(DEFAULT_CONFIG_FILE);
        final InputStream tenantStream = classLoader.getResourceAsStream(CONFIG_PATTERN.replace("?", federalState.toString().toLowerCase()));

        final ObjectNode jsonNode = processFiles(tenantStream, defaultStream);

        if (tenantStream != null) {
            tenantStream.close();
        }
        if (defaultStream != null) {
            defaultStream.close();
        }

        return jsonNode;
    }

    /**
     * Update of tenant Key/Values using default Key/Values:
     * <p>
     * If a key is missing from the tenant configuration, the default value
     * (fallback) is loaded from default.yaml<br>
     * <br>
     * <p>
     * Recursive method which transverses the default OSFile KeyValues. <br>
     * If the tenant file does not contain a key the key/value from the default file
     * will be added. <br>
     * <br>
     * If a key is present in the default OSFile and also in the tenant file NO update
     * of the key/value value (tenant first) will be done. <br>
     * <br>
     * If a key is present in the tenant file but not in the default file NO action
     * will be taken.
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> update(Map<String, Object> tenantKeyValue, Map<String, Object> defaultKeyValue) {
        defaultKeyValue.forEach((key, value) -> {
            if (value instanceof LinkedHashMap) {
                LinkedHashMap<String, Object> newMapValue = (LinkedHashMap<String, Object>) value;
                LinkedHashMap<String, Object> existingMapValue = (LinkedHashMap<String, Object>) tenantKeyValue
                        .getOrDefault(key, new LinkedHashMap<String, Object>());
                if (!tenantKeyValue.containsKey(key)) {
                    tenantKeyValue.put(key, value);
                }
                update(existingMapValue, newMapValue);
            } else {
                if (!tenantKeyValue.containsKey(key)) {
                    tenantKeyValue.put(key, value);
                }
            }
        });

        return tenantKeyValue;
    }

    /**
     * Reads tenant and default file and makes further processing.
     */
    private ObjectNode processFiles(InputStream tenantFile, InputStream defaultFile) {
        // formating options for yaml files
        final DumperOptions options = new DumperOptions();
        options.setDefaultFlowStyle(DumperOptions.FlowStyle.BLOCK);
        options.setPrettyFlow(true);
        final Yaml yaml = new Yaml(options);

        Map<String, Object> resultKeyValue;

        Map<String, Object> defaultKeyValue = yaml.load(defaultFile);
        if (tenantFile != null) {
            Map<String, Object> tenantKeyValue = yaml.load(tenantFile);
            // this update is only needed if a tenant configuration exists
            // but this should be the case 99% of times
            resultKeyValue = update(tenantKeyValue, defaultKeyValue);
        } else {
            resultKeyValue = defaultKeyValue;
        }

        return new ObjectMapper().valueToTree(resultKeyValue);
    }
}
