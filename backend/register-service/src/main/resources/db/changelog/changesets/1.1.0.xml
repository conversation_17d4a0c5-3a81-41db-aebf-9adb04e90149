<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">


    <changeSet id="1.1.0" author="paul.lindt">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="register_entry_search_view" />
            <columnExists tableName="register_entry_search_view" columnName="birthdate" />
        </preConditions>
        <modifyDataType tableName="register_entry_search_view" columnName="birthdate" newDataType="VARCHAR(10)"/>

    </changeSet>

</databaseChangeLog>