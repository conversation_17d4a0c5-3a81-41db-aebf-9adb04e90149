<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

	<!-- ChangeSet for CitizenOverviewView Table -->
	<changeSet id="1.0.4-1" author="madalina-iuliana.gheorghe">
		<preConditions onFail="MARK_RAN">
			<tableExists tableName="citizen_overview_view" />
		</preConditions>
		<renameTable oldTableName="citizen_overview_view"
			newTableName="register_entry_search_view" />
	</changeSet>

	<!-- ChangeSet for CitizenDetailView Table -->
	<changeSet id="1.0.4-2" author="madalina-iuliana.gheorghe">
		<preConditions onFail="MARK_RAN">
			<tableExists tableName="citizen_detail_view" />
		</preConditions>
		<renameTable oldTableName="citizen_detail_view"
			newTableName="register_entry_view" />
	</changeSet>

	<!-- ChangeSet for preliminary_register_entry_view Table -->
	<changeSet id="1.0.4-3" author="madalina-iuliana.gheorghe">
		<preConditions onFail="MARK_RAN">
			<tableExists tableName="examiner_view" />
		</preConditions>
		<renameTable oldTableName="examiner_view"
			newTableName="preliminary_register_entry_view" />
	</changeSet>

	<!-- ChangeSet for IdentificationDocumentView Table -->
	<changeSet id="1.0.4-4" author="madalina-iuliana.gheorghe">
		<preConditions onFail="MARK_RAN">
			<not>
				<tableExists tableName="identification_document_view" />
			</not>
		</preConditions>
		<createTable tableName="identification_document_view">
			<column name="id" type="BIGINT" autoIncrement="true">
				<constraints primaryKey="true" nullable="false" />
			</column>
			<column name="register_id" type="uuid">
				<constraints nullable="false" />
			</column>
			<column name="identification_document_id" type="VARCHAR(255)">
				<constraints nullable="false" />
			</column>
			<column name="salt" type="VARCHAR(29)">
				<constraints nullable="false" />
			</column>
			<column name="license_number" type="VARCHAR(255)">
				<constraints nullable="true" />
			</column>
		</createTable>
	</changeSet>

	<!-- Delete payment view -->
	<changeSet id="1.0.4-5" author="bjarne.wittlieb">
		<preConditions onFail="MARK_RAN">
			<tableExists tableName="payment_view" />
		</preConditions>
		<dropTable tableName="payment_view" />
	</changeSet>

	<!-- Delete inspector view -->
	<changeSet id="1.0.4-6" author="bjarne.wittlieb">
		<preConditions onFail="MARK_RAN">
			<tableExists tableName="inspector_view" />
		</preConditions>
		<dropTable tableName="inspector_view" />
	</changeSet>

	<!-- Delete fishing_license_view -->
	<changeSet id="1.0.4-7" author="bjarne.wittlieb">
		<preConditions onFail="MARK_RAN">
			<tableExists tableName="fishing_license_view" />
		</preConditions>
		<dropTable tableName="fishing_license_view" />
	</changeSet>

</databaseChangeLog>