<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

	<!-- ChangeSet for IdentificationDocumentView Table -->
	<changeSet id="1.0.6-1" author="madalina-iuliana.gheorghe">
		<preConditions onFail="MARK_RAN">
			<tableExists tableName="identification_document_view" />
		</preConditions>
		<addColumn tableName="identification_document_view">
			<column name="document_type" type="VARCHAR(25)">
				<constraints nullable="true" />
			</column>
		</addColumn>
	</changeSet>

</databaseChangeLog>