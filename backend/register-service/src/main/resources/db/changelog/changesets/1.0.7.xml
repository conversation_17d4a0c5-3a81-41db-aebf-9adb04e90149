<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="1.0.7" author="christopher.werl">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="register_entry_search_view" />
            <columnExists tableName="register_entry_search_view" columnName="firstname" />
            <columnExists tableName="register_entry_search_view" columnName="lastname" />
            <columnExists tableName="register_entry_search_view" columnName="person_birthname" />
            <not>
                <columnExists tableName="register_entry_search_view" columnName="normalized_name" />
            </not>
        </preConditions>
        <addColumn tableName="register_entry_search_view">
            <column name="normalized_name" type="varchar(255)" />
        </addColumn>
        <renameColumn tableName="register_entry_search_view" oldColumnName="birthplace"
                      newColumnName="normalized_birthplace" />
        <dropColumn tableName="register_entry_search_view">
            <column name="firstname" />
            <column name="lastname" />
            <column name="person_birthname" />
        </dropColumn>
    </changeSet>

</databaseChangeLog>