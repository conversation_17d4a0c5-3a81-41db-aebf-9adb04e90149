<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

	<changeSet id="1.0.5" author="christopher.werl">
		<preConditions onFail="MARK_RAN">
			<tableExists tableName="register_entry_search_view"/>
			<columnExists tableName="register_entry_search_view" columnName="person_birthdate"/>
			<columnExists tableName="register_entry_search_view" columnName="person_firstname"/>
			<columnExists tableName="register_entry_search_view" columnName="person_lastname"/>
			<not>
				<columnExists tableName="register_entry_search_view" columnName="birthplace"/>
				<columnExists tableName="register_entry_search_view" columnName="identification_number"/>
			</not>
		</preConditions>
		<renameColumn tableName="register_entry_search_view" oldColumnName="person_birthdate" newColumnName="birthdate"/>
		<renameColumn tableName="register_entry_search_view" oldColumnName="person_firstname" newColumnName="firstname"/>
		<renameColumn tableName="register_entry_search_view" oldColumnName="person_lastname" newColumnName="lastname"/>
		<addColumn tableName="register_entry_search_view">
			<column name="birthplace" type="varchar(255)"/>
			<column name="identification_number" type="varchar(255)"/>
		</addColumn>
	</changeSet>

</databaseChangeLog>
