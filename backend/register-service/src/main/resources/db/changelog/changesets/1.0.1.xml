<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <!-- ChangeSet for InspectorView Table -->
    <changeSet id="1.0.1-1" author="alexis.ortiz">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="inspector_view"/>
            </not>
        </preConditions>
        <createTable tableName="inspector_view">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="register_id" type="uuid">
                <constraints nullable="false"  unique="true"/>
            </column>
            <column name="firstname" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="lastname" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="birthname" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
            <column name="birthplace" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
            <column name="birthdate" type="datetime(6)">
                <constraints nullable="false"/>
            </column>
            <column name="banned_from" type="datetime(6)">
                <constraints nullable="true"/>
            </column>
            <column name="banned_to" type="datetime(6)">
                <constraints nullable="true"/>
            </column>
            <column name="salt" type="VARCHAR(29)">
                <constraints nullable="false"/>
            </column>
            <column name="data" type="mediumtext">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <!-- Create index on register_id -->
        <createIndex indexName="idx_inspector_view_register_id" tableName="inspector_view">
            <column name="register_id"/>
        </createIndex>
    </changeSet>

    <!-- ChangeSet for PaymentView Table -->
    <changeSet id="1.0.1-2" author="alexis.ortiz">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="payment_view"/>
            </not>
        </preConditions>
        <createTable tableName="payment_view">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="fishing_license_id" type="uuid">
                <constraints nullable="false"/>
            </column>
            <column name="register_id" type="uuid">
                <constraints nullable="false"/>
            </column>
            <column name="type" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="federal_state" type="VARCHAR(2)">
                <constraints nullable="false"/>
            </column>
            <column name="valid_from" type="datetime(6)">
                <constraints nullable="false"/>
            </column>
            <column name="valid_to" type="datetime(6)">
                <constraints nullable="true"/>
            </column>
            <column name="data" type="mediumtext">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <!-- Create index on register_id and federal_state -->
        <createIndex indexName="idx_payment_view_register_id_federal_state"
                     tableName="payment_view">
            <column name="register_id"/>
            <column name="federal_state"/>
        </createIndex>
    </changeSet>

    <!-- ChangeSet for FishingLicenseView Table -->
    <changeSet id="1.0.1-3" author="alexis.ortiz">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="fishing_license_view"/>
            </not>
        </preConditions>
        <createTable tableName="fishing_license_view">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="fishing_license_id" type="uuid">
                <constraints nullable="false"/>
            </column>
            <column name="register_id" type="uuid">
                <constraints nullable="false"/>
            </column>
            <column name="type" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="valid_from" type="datetime(6)">
                <constraints nullable="false"/>
            </column>
            <column name="valid_to" type="datetime(6)">
                <constraints nullable="true"/>
            </column>
            <column name="data" type="mediumtext">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <!-- Create index on register_id -->
        <createIndex indexName="idx_fishing_license_view_register_id" tableName="fishing_license_view">
            <column name="register_id"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>
