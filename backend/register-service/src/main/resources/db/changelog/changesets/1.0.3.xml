<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <!-- ChangeSet for CitizenDetailView Table -->
    <changeSet id="1.0.3-1" author="madalina-iuliana.gheorghe">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="citizen_detail_view"/>
            </not>
        </preConditions>
        <createTable tableName="citizen_detail_view">
			<column name="register_id" type="uuid">
				<constraints primaryKey="true" nullable="false" />
			</column>
			<column name="data" type="mediumtext">
				<constraints nullable="true" />
			</column>
		</createTable>
    </changeSet>

    <!-- ChangeSet for ExaminerView Table -->
    <changeSet id="1.0.3-2" author="madalina-iuliana.gheorghe">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="examiner_view"/>
            </not>
        </preConditions>
        <createTable tableName="examiner_view">
			<column name="register_id" type="uuid">
				<constraints primaryKey="true" nullable="false" />
			</column>
			<column name="examiner" type="VARCHAR(255)">
				<constraints primaryKey="false" nullable="false" />
			</column>
			<column name="data" type="mediumtext">
				<constraints nullable="true" />
			</column>
        </createTable>
    </changeSet>

    <!-- ChangeSet for Updating FishingLicenseView Table -->
    <changeSet id="1.0.3-1" author="alexis.ortiz">
        <preConditions onFail="MARK_RAN">
            <and>
                <not>
                    <columnExists tableName="fishing_license_view" columnName="number"/>
                </not>
                <columnExists tableName="fishing_license_view" columnName="fishing_license_id"/>
            </and>
        </preConditions>

        <!-- Add 'number' column without unique constraint -->
        <addColumn tableName="fishing_license_view">
            <column name="number" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
        </addColumn>

        <!-- Update 'number' column with values from 'register_id' -->
        <update tableName="fishing_license_view">
            <column name="number" value="register_id"/>
        </update>

        <!-- Add unique constraint to 'number' column -->
        <addUniqueConstraint columnNames="number" tableName="fishing_license_view" constraintName="unique_number"/>

        <!-- Drop 'fishing_license_id' column -->
        <dropColumn columnName="fishing_license_id" tableName="fishing_license_view"/>
    </changeSet>

    <!-- ChangeSet for Updating PaymentView Table -->
    <changeSet id="1.0.3-2" author="alexis.ortiz">
        <preConditions onFail="MARK_RAN">
            <and>
                <not>
                    <columnExists tableName="payment_view" columnName="fishing_license_number"/>
                </not>
                <columnExists tableName="payment_view" columnName="fishing_license_id"/>
            </and>
        </preConditions>

        <!-- Add 'fishingLicenseNumber' column -->
        <addColumn tableName="payment_view">
            <column name="fishing_license_number" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
        </addColumn>

        <!-- Remove 'fishingLicenseId' column -->
        <dropColumn columnName="fishing_license_id" tableName="payment_view"/>
    </changeSet>

    <!-- ChangeSet for Updating InspectorView Table -->
    <changeSet id="1.0.3-3" author="alexis.ortiz">
        <preConditions onFail="MARK_RAN">
            <and>
                <columnExists tableName="inspector_view" columnName="birthname"/>
                <columnExists tableName="inspector_view" columnName="birthplace"/>
            </and>
        </preConditions>

        <!-- Add 'Title' column -->
        <addColumn tableName="inspector_view">
            <column name="title" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <!-- Remove 'birthname' column -->
        <dropColumn columnName="birthname" tableName="inspector_view"/>

        <!-- Remove 'birthplace' column -->
        <dropColumn columnName="birthplace" tableName="inspector_view"/>
    </changeSet>
</databaseChangeLog>
