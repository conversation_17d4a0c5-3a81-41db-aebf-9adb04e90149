<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="1.0.9" author="madalina-iuliana.gheorghe">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="preliminary_register_entry_view" />
        </preConditions>
        <dropTable tableName="preliminary_register_entry_view"/>
        <createTable tableName="certificate_numbers_view">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false" />
            </column>
            <column name="register_entry_id" type="uuid">
                <constraints nullable="false" />
            </column>
            <column name="certificate_number" type="VARCHAR(255)">
                <constraints nullable="false" />
            </column>
        </createTable>

    </changeSet>
</databaseChangeLog>