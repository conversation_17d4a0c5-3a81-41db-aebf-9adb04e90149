<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.10.xsd">

	<changeSet id="1.0.0-1" author="admin">

		<createSequence cycle="false" incrementBy="50"
			sequenceName="domain_event_entry_seq" />

		<createTable tableName="citizen_overview_view">
			<column name="register_id" type="VARCHAR(255)">
				<constraints primaryKey="true" nullable="false" />
			</column>
			<column name="data" type="mediumtext">
				<constraints nullable="true" />
			</column>
			<column name="person_birthdate" type="datetime(6)">
				<constraints nullable="true" />
			</column>
			<column name="person_birthname" type="varchar(255)">
				<constraints nullable="true" />
			</column>
			<column name="person_firstname" type="VARCHAR(255)">
				<constraints nullable="true" />
			</column>
			<column name="person_lastname" type="VARCHAR(255)">
				<constraints nullable="true" />
			</column>
		</createTable>

		<createTable tableName="domain_event_entry">
			<column name="global_index" type="bigint(20)">
				<constraints primaryKey="true" nullable="false" />
			</column>
			<column name="event_identifier" type="VARCHAR(255)">
				<constraints nullable="false" />
			</column>
			<column name="meta_data" type="mediumtext">
				<constraints nullable="true" />
			</column>
			<column name="payload" type="mediumtext">
				<constraints nullable="false" />
			</column>
			<column name="payload_revision" type="varchar(255)">
				<constraints nullable="true" />
			</column>
			<column name="payload_type" type="varchar(255)">
				<constraints nullable="true" />
			</column>
			<column name="time_stamp" type="varchar(255)">
				<constraints nullable="true" />
			</column>
			<column name="aggregate_identifier" type="varchar(255)">
				<constraints nullable="true" />
			</column>
			<column name="sequence_number" type="bigint(20)">
				<constraints nullable="true" />
			</column>
			<column name="type" type="varchar(255)">
				<constraints nullable="true" />
			</column>
		</createTable>

		<createTable tableName="snapshot_event_entry">
			<column name="aggregate_identifier" type="VARCHAR(255)">
				<constraints primaryKey="true" nullable="false" />
			</column>
			<column name="sequence_number" type="bigint(20)">
				<constraints primaryKey="true" nullable="false" />
			</column>
			<column name="type" type="VARCHAR(255)">
				<constraints primaryKey="true" nullable="false" />
			</column>
			<column name="event_identifier" type="VARCHAR(255)">
				<constraints nullable="false" />
			</column>
			<column name="meta_data" type="mediumtext">
				<constraints nullable="false" />
			</column>
			<column name="payload" type="mediumtext">
				<constraints nullable="false" />
			</column>
			<column name="payload_revision" type="varchar(255)">
				<constraints nullable="true" />
			</column>
			<column name="payload_type" type="varchar(255)">
				<constraints nullable="false" />
			</column>
			<column name="time_stamp" type="VARCHAR(255)">
				<constraints nullable="false" />
			</column>
		</createTable>

		<createTable tableName="token_entry">
			<column name="processor_name" type="VARCHAR(255)">
				<constraints primaryKey="true" nullable="false" />
			</column>
			<column name="segment" type="int(11)">
				<constraints primaryKey="true" nullable="false" />
			</column>
			<column name="owner" type="VARCHAR(255)">
				<constraints nullable="true" />
			</column>
			<column name="timestamp" type="VARCHAR(255)">
				<constraints nullable="false" />
			</column>
			<column name="token" type="mediumtext">
				<constraints nullable="true" />
			</column>
			<column name="token_type" type="varchar(255)">
				<constraints nullable="true" />
			</column>
		</createTable>

		<addUniqueConstraint
			tableName="domain_event_entry"
			columnNames="aggregate_identifier, sequence_number"
			constraintName="UNIQUE_KEY_1" />
		<addUniqueConstraint
			tableName="domain_event_entry"
			columnNames="event_identifier"
			constraintName="UNIQUE_KEY_2" />
		<addUniqueConstraint
			tableName="snapshot_event_entry"
			columnNames="event_identifier"
			constraintName="UNIQUE_KEY_3" />

	</changeSet>

</databaseChangeLog>