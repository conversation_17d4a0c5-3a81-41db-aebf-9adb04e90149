<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="1.1.1" author="alexis.ortiz">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="register_entry_search_view" />
            <not>
                <columnExists tableName="register_entry_search_view" columnName="title" />
            </not>
            <not>
                <columnExists tableName="register_entry_search_view" columnName="firstname" />
            </not>
            <not>
                <columnExists tableName="register_entry_search_view" columnName="lastname" />
            </not>
            <not>
                <columnExists tableName="register_entry_search_view" columnName="birthname" />
            </not>
            <not>
                <columnExists tableName="register_entry_search_view" columnName="birthplace" />
            </not>
            <not>
                <columnExists tableName="register_entry_search_view" columnName="nationality" />
            </not>
        </preConditions>

        <addColumn tableName="register_entry_search_view">
            <column name="title" type="varchar(255)" />
            <column name="firstname" type="varchar(255)" />
            <column name="lastname" type="varchar(255)" />
            <column name="birthname" type="varchar(255)" />
            <column name="birthplace" type="varchar(255)" />
            <column name="nationality" type="varchar(255)" />
        </addColumn>
    </changeSet>

</databaseChangeLog>