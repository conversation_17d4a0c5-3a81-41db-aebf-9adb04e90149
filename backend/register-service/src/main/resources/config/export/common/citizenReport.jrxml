<!-- Created with Jaspersoft Studio version 7.0.0.final using JasperReports Library version 7.0.0-b478feaa9aab4375eba71de77b4ca138ad2f62aa  -->
<jasperReport name="citizens" language="java" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="8b569b21-d605-4652-a54b-3e3479dc8334">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<parameter name="registerEntry" class="de.adesso.fischereiregister.core.model.RegisterEntry"/>

	<background height="55" splitType="Stretch"/>
	<title height="33" splitType="Stretch"/>
	<detail>
		<band height="427" splitType="Stretch">
			<element kind="textField" uuid="a98510f6-d7d4-4117-8950-be4e88e892ea" x="100" y="0" width="450" height="20">
				<expression><![CDATA[$P{registerEntry}.getPerson().getFirstname()]]></expression>
			</element>
			<element kind="staticText" uuid="7828148a-db18-49cd-bae8-5462e8de9259" x="0" y="0" width="100" height="20" bold="true">
				<text><![CDATA[Vorname]]></text>
			</element>
			<element kind="textField" uuid="2a22dddf-80cf-4867-9895-a1eae2e7dbc0" x="100" y="20" width="450" height="20">
				<expression><![CDATA[$P{registerEntry}.getPerson().getLastname()]]></expression>
			</element>
			<element kind="staticText" uuid="c9f11bb5-96b4-4e75-a4b6-48317d3ade39" x="0" y="20" width="100" height="20" bold="true">
				<text><![CDATA[Nachname]]></text>
			</element>
			<element kind="textField" uuid="21a989f2-53fd-46e3-a137-54699347558e" x="100" y="40" width="450" height="20">
				<expression><![CDATA[$P{registerEntry}.getPerson().getBirthname()]]></expression>
			</element>
			<element kind="staticText" uuid="71e6fe81-f764-45c5-89bc-cd668b121c2f" x="0" y="40" width="100" height="20" bold="true">
				<text><![CDATA[Geburtsname]]></text>
			</element>
			<element kind="textField" uuid="b79e62da-2779-4eee-8999-9872898493ed" x="100" y="80" width="450" height="20">
				<expression><![CDATA[$P{registerEntry}.getPerson().getBirthplace()]]></expression>
			</element>
			<element kind="staticText" uuid="61f07258-6dc7-4287-b698-a212b2303eb6" x="0" y="80" width="100" height="20" bold="true">
				<text><![CDATA[Geburtsort]]></text>
			</element>
			<element kind="textField" uuid="14c9df0d-e6a3-45d5-848a-6f830834cf03" x="100" y="60" width="450" height="20">
				<expression><![CDATA[$P{registerEntry}.getPerson().getBirthdate()]]></expression>
			</element>
			<element kind="staticText" uuid="e5a6b7f5-d0e8-43be-8f9a-8972f1de673f" x="0" y="60" width="100" height="20" bold="true">
				<text><![CDATA[Geburtsdatum]]></text>
			</element>
			<element kind="staticText" uuid="0a0884d4-9bd4-4f07-9e98-4131d9660bb7" x="0" y="120" width="100" height="60" bold="true">
				<text><![CDATA[Adresse]]></text>
			</element>
			<element kind="textField" uuid="84b459f5-7f1e-49f3-87d9-85901459802f" x="100" y="120" width="450" height="20">
				<expression><![CDATA[$P{registerEntry}.getPerson().getAddress().getStreet() + " " + $P{registerEntry}.getPerson().getAddress().getStreetNumber()]]></expression>
			</element>
			<element kind="textField" uuid="7bb76811-0c0b-4723-b2e2-673e352f368f" x="100" y="140" width="450" height="20">
				<expression><![CDATA[$P{registerEntry}.getPerson().getAddress().getPostcode() + " " + $P{registerEntry}.getPerson().getAddress().getCity()]]></expression>
			</element>
			<property name="com.jaspersoft.studio.layout"/>
		</band>
	</detail>
	<pageFooter height="16">
		<element kind="textField" uuid="e002bad0-87f9-4421-8568-2a4d9b9089c9" x="0" y="0" width="550" height="16" hTextAlign="Center">
			<expression><![CDATA[$V{PAGE_NUMBER}]]></expression>
		</element>
	</pageFooter>
</jasperReport>
