<!-- Created with Jaspersoft Studio version 7.0.1.final using JasperReports Library version 7.0.1-573496633c2b4074e32f433154b543003f7d2498  -->
<jasperReport name="digitalLimitedLicenseApproval" language="java" columnCount="1" pageWidth="595" pageHeight="842" orientation="Landscape" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="8b569b21-d605-4652-a54b-3e3479dc8334">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<property name="net.sf.jasperreports.export.pdfa.conformance" value="pdfa1a"/>
	<property name="net.sf.jasperreports.export.pdfa.icc.profile.path" value="config/export/common/sRGB_v4_ICC_preference.icc"/>
	<property name="net.sf.jasperreports.export.pdf.tagged" value="true"/>
	<property name="net.sf.jasperreports.export.pdf.tag.language" value="de-DE"/>
	<property name="net.sf.jasperreports.export.pdf.classic.document.language" value="de-DE"/>
	<property name="net.sf.jasperreports.export.pdf.display.metadata.title" value="true"/>
	<property name="net.sf.jasperreports.default.pdf.embedded" value="true"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="primaryText" default="true" mode="Transparent" forecolor="#000000" fontName="NotoSansLight" fontSize="10.0"/>
	<parameter name="limitedLicenseApproval" class="de.adesso.fischereiregister.core.model.LimitedLicenseApproval"/>
	<parameter name="formattedDateToday" class="java.lang.String"/>
	<parameter name="subject" class="java.lang.String"/>
	<parameter name="formattedLimitedLicenseApplicationDate" class="java.lang.String"/>
	<parameter name="subjectSecondPart" class="java.lang.String"/>
	<parameter name="salutation" class="java.lang.String"/>
	<parameter name="firstParagraph" class="java.lang.String"/>
	<parameter name="durationParagraph" class="java.lang.String"/>
	<parameter name="headerNotes" class="java.lang.String"/>
	<parameter name="textNotesBulletPoints" class="java.lang.String"/>
	<parameter name="headerFeesAndTaxes" class="java.lang.String"/>
	<parameter name="textFeesAndTaxes" class="java.lang.String"/>
	<parameter name="paymentAccount" class="java.lang.String"/>
	<parameter name="closing" class="java.lang.String"/>
	<parameter name="headerInstructionsOnLegalRemedies" class="java.lang.String"/>
	<parameter name="textInstructionsOnLegalRemedies" class="java.lang.String"/>
	<parameter name="footer" class="java.lang.String"/>
	<parameter name="officeAddress" class="java.lang.String"/>
	<parameter name="officePhone" class="java.lang.String"/>
	<parameter name="officeEmail" class="java.lang.String"/>
	<parameter name="nameEmployee" class="java.lang.String"/>
	<parameter name="deliveryAddress" class="java.lang.String"/>
	<background height="55" splitType="Stretch"/>
	<title splitType="Stretch"/>
	<pageHeader height="100">
		<element kind="image" uuid="2726210c-97bb-4e7b-a582-edab07d2a9b5" x="0" y="0" width="135" height="80">
			<expression><![CDATA[this.getClass().getClassLoader().getResourceAsStream("config/export/sh/state-logo.png")]]></expression>
			<hyperlinkTooltipExpression><![CDATA["Schleswig-Holstein der echte Norden"]]></hyperlinkTooltipExpression>
			<property name="net.sf.jasperreports.export.pdf.tag.figure" value="full"/>
			<property name="net.sf.jasperreports.accessibility.role" value="image"/>
		</element>
		<element kind="image" uuid="1eda496c-4841-436b-9aba-b429d816884c" stretchType="NoStretch" x="202" y="0" width="322" height="80">
			<expression><![CDATA[this.getClass().getClassLoader().getResourceAsStream("config/export/sh/state-logo-large.png")]]></expression>
			<hyperlinkTooltipExpression><![CDATA["Schleswig - Holstein Landesamt für Landwirftschaft und nachhaltige Landentwicklung"]]></hyperlinkTooltipExpression>
			<property name="net.sf.jasperreports.export.pdf.tag.figure" value="full"/>
			<property name="net.sf.jasperreports.accessibility.role" value="image"/>
		</element>
		<property name="net.sf.jasperreports.export.pdf.tag.h1" value="end"/>
	</pageHeader>
	<detail>
		<band height="590" splitType="Stretch">
			<element kind="frame" uuid="369f8187-ada7-4fba-9888-6d1a8ff507e3" x="20" y="10" width="524" height="220">
				<element kind="textField" uuid="95fcb02f-c82a-46fb-b181-e6f69e62d6c6" x="0" y="5" width="248" height="30" markup="html" fontSize="8.0" blankWhenNull="true">
					<expression><![CDATA[$P{officeAddress}]]></expression>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</element>
				<element kind="textField" uuid="54fa3b49-f3cb-45a0-b2e8-8a1a86cdda68" x="0" y="48" width="238" height="107" markup="html" fontSize="12.0" blankWhenNull="true">
					<expression><![CDATA[$P{deliveryAddress}]]></expression>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</element>
				<element kind="staticText" uuid="db6726cf-61ff-4a49-a1c2-19f1358c23c5" x="265" y="5" width="110" height="15" fontSize="9.5" bold="false">
					<text><![CDATA[Ihre Nachricht vom:]]></text>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<box>
						<pen lineColor="#000000"/>
					</box>
				</element>
				<element kind="textField" uuid="47e06589-a09a-4360-959d-d299110039da" positionType="Float" x="360" y="5" width="150" height="15" markup="html" fontSize="9.5" blankWhenNull="true">
					<expression><![CDATA[$P{formattedLimitedLicenseApplicationDate}]]></expression>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</element>
				<element kind="staticText" uuid="cbb9106e-c2d0-48a6-b2e4-f71a9a8e5c59" x="265" y="20" width="100" height="15" fontSize="9.5">
					<text><![CDATA[Mein Zeichen:]]></text>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</element>
				<element kind="textField" uuid="9776a286-3081-4d85-9f36-eafb6f5d8a1c" x="360" y="20" width="150" height="15" markup="html" fontSize="9.5" blankWhenNull="true">
					<expression><![CDATA[$P{limitedLicenseApproval}.getFileNumber() + " - " + $P{limitedLicenseApproval}.getSigningEmployee().getPersonalSign()]]></expression>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</element>
				<element kind="textField" uuid="0bde055d-8cb1-47b6-817e-a304888910fb" x="360" y="35" width="150" height="15" markup="html" fontSize="9.5" blankWhenNull="true">
					<expression><![CDATA[$P{nameEmployee}]]></expression>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</element>
				<element kind="staticText" uuid="a07f8225-064a-4f86-9e47-b8f508053d20" x="265" y="50" width="100" height="15" fontSize="9.5">
					<text><![CDATA[E-Mail:]]></text>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</element>
				<element kind="textField" uuid="976d7731-bf33-49a5-89a7-2f1718560b35" positionType="Float" x="360" y="50" width="150" height="15" markup="html" fontSize="9.5" blankWhenNull="true">
					<expression><![CDATA[$P{officeEmail}]]></expression>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</element>
				<element kind="staticText" uuid="ffd1f59c-5fe9-443d-8d5d-cf274d17ad19" x="265" y="65" width="100" height="15" fontSize="9.5">
					<text><![CDATA[Telefon:]]></text>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</element>
				<element kind="textField" uuid="096d3974-9b7e-4090-9b19-00cb8237c731" positionType="Float" x="360" y="65" width="149" height="15" markup="html" fontSize="9.5" blankWhenNull="true">
					<expression><![CDATA[$P{officePhone}]]></expression>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</element>
				<element kind="textField" uuid="*************-45d2-988e-25a27fcc4668" x="367" y="135" width="140" height="30" markup="html" fontSize="12.0" blankWhenNull="true">
					<expression><![CDATA[$P{formattedDateToday}]]></expression>
				</element>
				<element kind="textField" uuid="7e0fa093-47fa-4927-8f8f-aca2e5dc34d1" x="0" y="175" width="504" height="20" markup="html" fontSize="12.0" textAdjust="StretchHeight" blankWhenNull="true" bold="true">
					<expression><![CDATA[$P{subject}]]></expression>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</element>
				<element kind="textField" uuid="99f06511-0293-4f15-8a8b-9fb9325457ab" positionType="Float" x="0" y="201" width="504" height="19" markup="html" fontSize="12.0" textAdjust="StretchHeight" blankWhenNull="true">
					<expression><![CDATA[$P{subjectSecondPart}]]></expression>
				</element>
			</element>
			<element kind="textField" uuid="9952ab78-bd39-4935-b5f3-0d83d49f8d31" positionType="Float" x="20" y="260" width="464" height="20" markup="html" fontSize="12.0" textAdjust="StretchHeight" blankWhenNull="true">
				<expression><![CDATA[$P{salutation} + $P{firstParagraph} + $P{durationParagraph} + $P{headerNotes} + $P{textNotesBulletPoints} + $P{headerFeesAndTaxes} + $P{textFeesAndTaxes} + $P{paymentAccount} + $P{closing} + $P{nameEmployee} + $P{headerInstructionsOnLegalRemedies} + $P{textInstructionsOnLegalRemedies}]]></expression>
				<property name="net.sf.jasperreports.allow.element.overlap" value="false"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
			</element>
			<property name="com.jaspersoft.studio.layout"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</band>
	</detail>
	<pageFooter height="112">
		<element kind="textField" uuid="1057a334-130c-4033-82c6-22693515da3a" positionType="Float" x="0" y="10" width="458" height="73" markup="html" fontSize="8.0" textAdjust="StretchHeight" blankWhenNull="true">
			<expression><![CDATA[$P{footer}]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</element>
		<property name="com.jaspersoft.studio.unit.height" value="px"/>
	</pageFooter>
</jasperReport>
