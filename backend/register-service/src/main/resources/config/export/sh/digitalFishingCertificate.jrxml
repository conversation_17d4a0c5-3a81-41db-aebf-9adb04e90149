<!-- Created with Jaspersoft Studio version 7.0.1.final using JasperReports Library version 7.0.1-573496633c2b4074e32f433154b543003f7d2498  -->
<jasperReport name="digitalFishingLicense" language="java" pageWidth="842" pageHeight="595" columnWidth="555"
			  leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="8b569b21-d605-4652-a54b-3e3479dc8334">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<parameter name="feeOD" class="java.lang.String"/>
	<parameter name="feeOffice" class="java.lang.String"/>
	<parameter name="fishingCertificateId" class="java.lang.String"/>
	<parameter name="person" class="de.adesso.fischereiregister.core.model.Person"/>
	<background height="595" splitType="Stretch">
		<element kind="image" uuid="4666df0a-45e7-4d2d-a988-1c19be4fce94" x="0" y="0" width="842" height="595">
			<printWhenExpression><![CDATA[$V{PAGE_NUMBER} == 1]]></printWhenExpression>
			<expression><![CDATA[
                this.getClass().getClassLoader().getResourceAsStream("config/export/sh/assets/bg-certificate-page1.png")]]></expression>
		</element>
		<element kind="image" uuid="4666df0a-45e7-4d2d-a988-1c19be4fce94" x="0" y="0" width="842" height="595">
			<printWhenExpression><![CDATA[$V{PAGE_NUMBER} == 2]]></printWhenExpression>
			<expression><![CDATA[
                this.getClass().getClassLoader().getResourceAsStream("config/export/sh/assets/bg-certificate-page2.png")]]></expression>
		</element>
	</background>
	<title splitType="Stretch"/>
	<detail>
		<band height="595" splitType="Stretch">
			<element kind="textField" uuid="14c9df0d-e6a3-45d5-848a-6f830834cf03" x="155" y="441" width="48" height="14"
					 forecolor="#163BBF" fontName="NotoSansBold" fontSize="7.0">
				<expression><![CDATA[$P{feeOD} + " €"]]></expression>
			</element>
			<element kind="textField" uuid="b79e62da-2779-4eee-8999-9872898493ed" x="342" y="441" width="48" height="14"
					 forecolor="#163BBF" fontName="NotoSansBold" fontSize="7.0">
				<expression><![CDATA[$P{feeOffice} + " €"]]></expression>
			</element>
			<property name="com.jaspersoft.studio.layout"/>

			<element kind="textField" uuid="4ece6395-5c26-4a59-a088-fcf93617409e" x="515" y="311" width="50" height="23"
					 forecolor="#163BBF" fontName="NotoSansRegular" fontSize="16.0" hTextAlign="Center"
					 vTextAlign="Middle">
				<expression><![CDATA[$P{fishingCertificateId}.substring(0,4)]]></expression>
			</element>
			<element kind="textField" uuid="4ece6395-5c26-4a59-a088-fcf93617409e" x="576" y="311" width="50" height="23"
					 forecolor="#163BBF" fontName="NotoSansRegular" fontSize="16.0" hTextAlign="Center"
					 vTextAlign="Middle">
				<expression><![CDATA[$P{fishingCertificateId}.substring(4,8)]]></expression>
			</element>
			<element kind="textField" uuid="4ece6395-5c26-4a59-a088-fcf93617409e" x="637" y="311" width="50" height="23"
					 forecolor="#163BBF" fontName="NotoSansRegular" fontSize="16.0" hTextAlign="Center"
					 vTextAlign="Middle">
				<expression><![CDATA[$P{fishingCertificateId}.substring(8,12)]]></expression>
			</element>
			<element kind="textField" uuid="4ece6395-5c26-4a59-a088-fcf93617409e" x="697" y="311" width="50" height="23"
					 forecolor="#163BBF" fontName="NotoSansRegular" fontSize="16.0" hTextAlign="Center"
					 vTextAlign="Middle">
				<expression><![CDATA[$P{fishingCertificateId}.substring(12)]]></expression>
			</element>
			<element kind="textField" uuid="9214262d-750d-45df-9140-543d5c0d38a1" x="517" y="358" width="311"
					 height="14"
					 fontName="NotoSansRegular" fontSize="10.0">
				<expression><![CDATA[
				$P{person}.getTitle() == null || $P{person}.getTitle().isBlank() ?
					String.join(" ", $P{person}.getFirstname(), $P{person}.getLastname()) : String.join(" ", $P{person}.getTitle(), $P{person}.getFirstname(), $P{person}.getLastname())]]></expression>
			</element>
		</band>
		<band height="595" splitType="Stretch">
			<element kind="textField" uuid="14c9df0d-e6a3-45d5-848a-6f830834cf03" x="317" y="86" width="71" height="25"
					 fontName="NotoSansLight" fontSize="18.0" hTextAlign="Right">
				<expression><![CDATA[$P{feeOD} + " €"]]></expression>
			</element>
			<property name="com.jaspersoft.studio.layout"/>
			<element kind="textField" uuid="b79e62da-2779-4eee-8999-9872898493ed" x="738" y="86" width="71" height="25"
					 fontName="NotoSansLight" fontSize="18.0" hTextAlign="Right">
				<expression><![CDATA[$P{feeOffice} + " €"]]></expression>
			</element>
			<property name="com.jaspersoft.studio.layout"/>
		</band>
	</detail>
</jasperReport>
