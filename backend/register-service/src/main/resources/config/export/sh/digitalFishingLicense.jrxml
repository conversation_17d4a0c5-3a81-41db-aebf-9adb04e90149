<!-- Created with Jaspersoft Studio version 7.0.0.final using JasperReports Library version 7.0.0-b478feaa9aab4375eba71de77b4ca138ad2f62aa  -->
<jasperReport name="digitalFishingLicense" language="java" pageWidth="595" pageHeight="842" columnWidth="555"
              leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="8b569b21-d605-4652-a54b-3e3479dc8334">
    <property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
    <property name="net.sf.jasperreports.export.pdfa.conformance" value="pdfa1a"/>
    <property name="net.sf.jasperreports.export.pdfa.icc.profile.path"
              value="config/export/common/sRGB_v4_ICC_preference.icc"/>
    <property name="net.sf.jasperreports.export.pdf.tagged" value="true"/>
    <property name="net.sf.jasperreports.export.pdf.tag.language" value="de-DE"/>
    <property name="net.sf.jasperreports.export.pdf.classic.document.language" value="de-DE"/>
    <property name="net.sf.jasperreports.export.pdf.display.metadata.title" value="true"/>
    <property name="net.sf.jasperreports.default.pdf.embedded" value="true"/>
    <propertyExpression name="net.sf.jasperreports.export.pdf.metadata.title"><![CDATA[
        "Digitaler Fischereischein von " + $P{person}.getFirstname() + " " + $P{person}.getLastname()
    ]]></propertyExpression>
    <style name="primaryText" default="true" mode="Transparent" forecolor="#4D5A91" fontName="NotoSansLight"
           fontSize="10.0"/>
    <parameter name="licenseNumber" class="java.lang.String"/>
    <parameter name="documentId" class="java.lang.String"/>
    <parameter name="qrcode" class="java.lang.String"/>
    <parameter name="person" class="de.adesso.fischereiregister.core.model.Person"/>
    <parameter name="document" class="de.adesso.fischereiregister.core.model.IdentificationDocument"/>
    <background height="842" splitType="Stretch">
        <element kind="image" uuid="4666df0a-45e7-4d2d-a988-1c19be4fce94" x="0" y="0" width="595" height="842">
            <expression><![CDATA[
                this.getClass().getClassLoader().getResourceAsStream("config/export/sh/assets/bg-license.png")]]></expression>
            <hyperlinkTooltipExpression>
                <![CDATA[""]]></hyperlinkTooltipExpression>
        </element>
    </background>
    <title splitType="Stretch"/>
    <detail>
        <band height="842" splitType="Stretch">
            <element kind="image" uuid="1eda496c-4841-436b-9aba-b429d816884c" x="33" y="240" width="237" height="158"
                     scaleImage="FillFrame">
                <expression>
                    <![CDATA[this.getClass().getClassLoader().getResourceAsStream("config/export/common/transparent.png")]]></expression>
                <hyperlinkTooltipExpression>
                    <![CDATA["Digitaler Fischereischein aus Schleswig-Holstein"]]></hyperlinkTooltipExpression>
                <property name="com.jaspersoft.studio.unit.width" value="px"/>
                <property name="com.jaspersoft.studio.unit.height" value="px"/>
            </element>
            <element kind="image" uuid="8a879914-2877-4150-b2ea-480de5a1138c" x="92" y="24" width="114" height="114"
                     scaleImage="FillFrame">
                <expression>
                    <![CDATA[this.getClass().getClassLoader().getResourceAsStream("config/export/common/transparent.png")]]></expression>
                <hyperlinkTooltipExpression>
                    <![CDATA["QR-Code für das Scannen des Fischereischeins mit einer Kontroll-App. Bitte nicht knicken!"]]></hyperlinkTooltipExpression>
                <property name="com.jaspersoft.studio.unit.width" value="px"/>
                <property name="com.jaspersoft.studio.unit.height" value="px"/>
            </element>
            <element kind="image" uuid="8a879914-2877-4150-b2ea-480de5a1138c" x="32" y="166" width="70" height="6"
                     scaleImage="FillFrame">
                <expression>
                    <![CDATA[this.getClass().getClassLoader().getResourceAsStream("config/export/common/transparent.png")]]></expression>
                <hyperlinkTooltipExpression>
                    <![CDATA["Bitte falten Sie das Dokument zweimal mittig auf das Format DIN A6, um den aufgedruckten QR-Code nicht zu knicken."]]></hyperlinkTooltipExpression>
                <property name="com.jaspersoft.studio.unit.width" value="px"/>
                <property name="com.jaspersoft.studio.unit.height" value="px"/>
            </element>
            <element kind="image" uuid="8a879914-2877-4150-b2ea-480de5a1138c" x="39" y="507" width="162" height="31"
                     scaleImage="FillFrame">
                <expression>
                    <![CDATA[this.getClass().getClassLoader().getResourceAsStream("config/export/common/transparent.png")]]></expression>
                <hyperlinkTooltipExpression>
                    <![CDATA["Fischereischein Daten"]]></hyperlinkTooltipExpression>
                <property name="com.jaspersoft.studio.unit.width" value="px"/>
                <property name="com.jaspersoft.studio.unit.height" value="px"/>
            </element>

            <element kind="image" uuid="1eda496c-4841-436b-9aba-b429d816884c" x="33" y="598" width="70" height="6"
                     scaleImage="FillFrame">
                <expression>
                    <![CDATA[this.getClass().getClassLoader().getResourceAsStream("config/export/common/transparent.png")]]></expression>
                <hyperlinkTooltipExpression><![CDATA["Fischereischein-ID"]]></hyperlinkTooltipExpression>
                <property name="com.jaspersoft.studio.unit.width" value="px"/>
                <property name="com.jaspersoft.studio.unit.height" value="px"/>
            </element>
            <element kind="textField" uuid="82354380-202b-48c4-a6ce-29790800b130" x="33" y="608" width="28" height="14"
                     hTextAlign="Center" vTextAlign="Middle" style="primaryText">
                <expression><![CDATA[$P{licenseNumber}.substring(0,4)]]></expression>
            </element>
            <element kind="textField" uuid="8ab6d02c-ac4f-4608-aeed-a01dc32a2ab1" x="72" y="608" width="28" height="14"
                     hTextAlign="Center" vTextAlign="Middle" style="primaryText">
                <expression><![CDATA[$P{licenseNumber}.substring(4,8)]]></expression>
            </element>
            <element kind="textField" uuid="8040435d-07d2-4952-afba-1d1612773b46" x="111" y="608" width="28" height="14"
                     hTextAlign="Center" vTextAlign="Middle" style="primaryText">
                <expression><![CDATA[$P{licenseNumber}.substring(8,12)]]></expression>
            </element>
            <element kind="textField" uuid="70c642fb-a28b-44cd-b470-04801684e3ef" x="150" y="608" width="28" height="14"
                     hTextAlign="Center" vTextAlign="Middle" style="primaryText">
                <expression><![CDATA[$P{licenseNumber}.substring(12)]]></expression>
            </element>

            <element kind="image" uuid="00129392-c3d3-4388-af82-e9e61b0f71ba" x="32" y="637" width="70" height="6"
                     scaleImage="FillFrame">
                <expression>
                    <![CDATA[this.getClass().getClassLoader().getResourceAsStream("config/export/common/transparent.png")]]></expression>
                <hyperlinkTooltipExpression><![CDATA[
                    $P{person}.getTitle() != null && !$P{person}.getTitle().isBlank() ? "Titel" : "Titel: keine Angabe"]]></hyperlinkTooltipExpression>
                <property name="com.jaspersoft.studio.unit.width" value="px"/>
                <property name="com.jaspersoft.studio.unit.height" value="px"/>
            </element>
            <element kind="textField" uuid="9214262d-750d-45df-9140-543d5c0d38a1" x="32" y="647" width="248" height="23"
                     style="primaryText" textAdjust="ScaleFont">
                <expression><![CDATA[
                    $P{person}.getTitle() != null && !$P{person}.getTitle().isBlank() ? $P{person}.getTitle() : "-"]]></expression>
            </element>

            <element kind="image" uuid="028e0e68-ed89-45fa-aed7-711163dfdce1" x="32" y="670" width="70" height="6"
                     scaleImage="FillFrame">
                <expression>
                    <![CDATA[this.getClass().getClassLoader().getResourceAsStream("config/export/common/transparent.png")]]></expression>
                <hyperlinkTooltipExpression><![CDATA["Vorname"]]></hyperlinkTooltipExpression>
                <property name="com.jaspersoft.studio.unit.width" value="px"/>
                <property name="com.jaspersoft.studio.unit.height" value="px"/>
            </element>
            <element kind="textField" uuid="a98510f6-d7d4-4117-8950-be4e88e892ea" x="32" y="680" width="248" height="23"
                     style="primaryText" textAdjust="ScaleFont">
                <expression><![CDATA[$P{person}.getFirstname()]]></expression>
            </element>

            <element kind="image" uuid="e25ee9e4-0a52-4445-bfb9-d7ebfc08bd1c" x="32" y="703" width="70" height="6"
                     scaleImage="FillFrame">
                <expression>
                    <![CDATA[this.getClass().getClassLoader().getResourceAsStream("config/export/common/transparent.png")]]></expression>
                <hyperlinkTooltipExpression><![CDATA["Nachname"]]></hyperlinkTooltipExpression>
                <property name="com.jaspersoft.studio.unit.width" value="px"/>
                <property name="com.jaspersoft.studio.unit.height" value="px"/>
            </element>
            <element kind="textField" uuid="2a22dddf-80cf-4867-9895-a1eae2e7dbc0" x="32" y="713" width="248" height="23"
                     style="primaryText" textAdjust="ScaleFont">
                <expression><![CDATA[$P{person}.getLastname()]]></expression>
            </element>

            <element kind="image" uuid="50afeea2-c0c4-4e45-a4c8-a66d6eb4127e" x="32" y="736" width="70" height="6"
                     scaleImage="FillFrame">
                <expression>
                    <![CDATA[this.getClass().getClassLoader().getResourceAsStream("config/export/common/transparent.png")]]></expression>
                <hyperlinkTooltipExpression><![CDATA["Geburtsdatum"]]></hyperlinkTooltipExpression>
                <property name="com.jaspersoft.studio.unit.width" value="px"/>
                <property name="com.jaspersoft.studio.unit.height" value="px"/>
            </element>
            <element kind="textField" uuid="14c9df0d-e6a3-45d5-848a-6f830834cf03" x="32" y="746" width="248" height="23"
                     style="primaryText" textAdjust="ScaleFont">
                <expression><![CDATA[($P{person}.getBirthdate())]]></expression>
            </element>

            <element kind="image" uuid="804251c1-d6de-43dd-baa8-82eda9e7b563" x="32" y="769" width="70" height="6"
                     scaleImage="FillFrame">
                <expression>
                    <![CDATA[this.getClass().getClassLoader().getResourceAsStream("config/export/common/transparent.png")]]></expression>
                <hyperlinkTooltipExpression><![CDATA["Geburtsname"]]></hyperlinkTooltipExpression>
                <property name="com.jaspersoft.studio.unit.width" value="px"/>
                <property name="com.jaspersoft.studio.unit.height" value="px"/>
            </element>
            <element kind="textField" uuid="21a989f2-53fd-46e3-a137-54699347558e" x="32" y="779" width="248" height="23"
                     style="primaryText" textAdjust="ScaleFont">
                <expression><![CDATA[$P{person}.getBirthname()]]></expression>
            </element>

            <element kind="image" uuid="8a879914-2877-4150-b2ea-480de5a1138c" x="32" y="802" width="70" height="6"
                     scaleImage="FillFrame">
                <expression>
                    <![CDATA[this.getClass().getClassLoader().getResourceAsStream("config/export/common/transparent.png")]]></expression>
                <hyperlinkTooltipExpression><![CDATA["Geburtsort"]]></hyperlinkTooltipExpression>
                <property name="com.jaspersoft.studio.unit.width" value="px"/>
                <property name="com.jaspersoft.studio.unit.height" value="px"/>
            </element>
            <element kind="textField" uuid="b79e62da-2779-4eee-8999-9872898493ed" x="32" y="812" width="248" height="23"
                     style="primaryText" textAdjust="ScaleFont">
                <expression><![CDATA[$P{person}.getBirthplace()]]></expression>
            </element>

            <element kind="component" uuid="7ac8cc27-80c9-452c-87ba-d8caa94ac7df" x="92" y="24" width="114"
                     height="114" forecolor="#000000">
                <component kind="barcode4j:QRCode" errorCorrectionLevel="H">
                    <codeExpression><![CDATA[$P{qrcode}]]></codeExpression>
                </component>
                <property name="com.jaspersoft.studio.unit.width" value="px"/>
                <property name="com.jaspersoft.studio.unit.height" value="px"/>
                <property name="com.jaspersoft.studio.unit.x" value="px"/>
            </element>

            <element kind="image" uuid="8a879914-2877-4150-b2ea-480de5a1138c" x="330" y="110" width="236" height="103"
                     scaleImage="FillFrame">
                <expression>
                    <![CDATA[this.getClass().getClassLoader().getResourceAsStream("config/export/common/transparent.png")]]></expression>
                <hyperlinkTooltipExpression>
                    <![CDATA["Logo des Bundeslandes Schleswig-Holstein"]]></hyperlinkTooltipExpression>
                <property name="com.jaspersoft.studio.unit.width" value="px"/>
                <property name="com.jaspersoft.studio.unit.height" value="px"/>
            </element>

            <element kind="image" uuid="8a879914-2877-4150-b2ea-480de5a1138c" x="330" y="494" width="238" height="48"
                     scaleImage="FillFrame">
                <expression>
                    <![CDATA[this.getClass().getClassLoader().getResourceAsStream("config/export/common/transparent.png")]]></expression>
                <hyperlinkTooltipExpression>
                    <![CDATA["Ausstellende Behörde: Landesamt für Landwirtschaft und nachhaltige Landentwicklung"]]></hyperlinkTooltipExpression>
                <property name="com.jaspersoft.studio.unit.width" value="px"/>
                <property name="com.jaspersoft.studio.unit.height" value="px"/>
            </element>

            <element kind="image" uuid="8a879914-2877-4150-b2ea-480de5a1138c" x="330" y="612" width="239" height="22"
                     scaleImage="FillFrame">
                <expression>
                    <![CDATA[this.getClass().getClassLoader().getResourceAsStream("config/export/common/transparent.png")]]></expression>
                <hyperlinkTooltipExpression>
                    <![CDATA["Rechtliche Informationen: Bitte informieren Sie sich vor der Ausübung der Fischerei über die im jeweiligen Bundesland geltenden Vorschriften."]]></hyperlinkTooltipExpression>
                <property name="com.jaspersoft.studio.unit.width" value="px"/>
                <property name="com.jaspersoft.studio.unit.height" value="px"/>
            </element>

            <element kind="image" uuid="8a879914-2877-4150-b2ea-480de5a1138c" x="297" y="30" width="297" height="22"
                     scaleImage="FillFrame">
                <expression>
                    <![CDATA[this.getClass().getClassLoader().getResourceAsStream("config/export/common/transparent.png")]]></expression>
                <hyperlinkTooltipExpression>
                    <![CDATA["Dokument-ID: $P{documentId}"]]></hyperlinkTooltipExpression>
                <property name="com.jaspersoft.studio.unit.width" value="px"/>
                <property name="com.jaspersoft.studio.unit.height" value="px"/>
            </element>
            <element kind="textField" uuid="a98510f6-d7d4-4117-8950-be4e88e892ea" x="297" y="5" width="297" height="22"
                     style="primaryText" rotation="UpsideDown" hTextAlign="Center" fontSize="8.0" forecolor="#8e97a3">
                <expression><![CDATA[$P{documentId}]]></expression>
            </element>

            <property name="com.jaspersoft.studio.layout"/>
            <property name="com.jaspersoft.studio.unit.height" value="px"/>
        </band>
    </detail>
</jasperReport>
