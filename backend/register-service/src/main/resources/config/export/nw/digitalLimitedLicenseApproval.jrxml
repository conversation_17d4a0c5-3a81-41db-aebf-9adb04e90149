<!-- Created with Jaspersoft Studio version 7.0.1.final using JasperReports Library version 7.0.1-573496633c2b4074e32f433154b543003f7d2498  -->
<jasperReport name="digitalLimitedLicenseApproval" language="java" columnCount="1" pageWidth="595" pageHeight="842" orientation="Landscape" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="8b569b21-d605-4652-a54b-3e3479dc8334">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="primaryText" default="true" mode="Transparent" forecolor="#000000" fontName="NotoSansLight" fontSize="10.0"/>
	<parameter name="limitedLicenseApproval" class="de.adesso.fischereiregister.core.model.LimitedLicenseApproval"/>
	<parameter name="formattedDateToday" class="java.lang.String"/>
	<parameter name="subject" class="java.lang.String"/>
	<parameter name="formattedLimitedLicenseApplicationDate" class="java.lang.String"/>
	<parameter name="subjectSecondPart" class="java.lang.String"/>
	<parameter name="salutation" class="java.lang.String"/>
	<parameter name="firstParagraph" class="java.lang.String"/>
	<parameter name="durationParagraph" class="java.lang.String"/>
	<parameter name="headerNotes" class="java.lang.String"/>
	<parameter name="textNotesBulletPoints" class="java.lang.String"/>
	<parameter name="headerFeesAndTaxes" class="java.lang.String"/>
	<parameter name="textFeesAndTaxes" class="java.lang.String"/>
	<parameter name="paymentAccount" class="java.lang.String"/>
	<parameter name="closing" class="java.lang.String"/>
	<parameter name="headerInstructionsOnLegalRemedies" class="java.lang.String"/>
	<parameter name="textInstructionsOnLegalRemedies" class="java.lang.String"/>
	<parameter name="footer" class="java.lang.String"/>
	<parameter name="officeAddress" class="java.lang.String"/>
	<parameter name="officePhone" class="java.lang.String"/>
	<parameter name="officeEmail" class="java.lang.String"/>
	<parameter name="nameEmployee" class="java.lang.String"/>
	<parameter name="deliveryAddress" class="java.lang.String"/>
	<background height="55" splitType="Stretch"/>
	<title splitType="Stretch"/>
	<pageHeader height="100">
		<element kind="image" uuid="2726210c-97bb-4e7b-a582-edab07d2a9b5" x="0" y="0" width="135" height="80">
			<expression><![CDATA[this.getClass().getClassLoader().getResourceAsStream("config/export/sh/state-logo.png")]]></expression>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</element>
	</pageHeader>
	<detail>
		<band height="590" splitType="Stretch">
			<element kind="textField" uuid="9952ab78-bd39-4935-b5f3-0d83d49f8d31" positionType="Float" x="0" y="70" width="458" height="20" markup="html" fontSize="12.0" textAdjust="StretchHeight" blankWhenNull="true">
				<expression><![CDATA[Dieses Dokument ist nur ein Dummy PDF und muss nocht mit Mandantenspezifischen Texten gefüllt werden.]]></expression>
				<property name="net.sf.jasperreports.allow.element.overlap" value="false"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
			</element>
			<property name="com.jaspersoft.studio.layout"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</band>
	</detail>
	<pageFooter height="112">
		<property name="com.jaspersoft.studio.unit.height" value="px"/>
	</pageFooter>
</jasperReport>
