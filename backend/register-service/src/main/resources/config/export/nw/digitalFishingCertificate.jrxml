<!-- Created with Jaspersoft Studio version 7.0.1.final using JasperReports Library version 7.0.1-573496633c2b4074e32f433154b543003f7d2498  -->
<jasperReport name="digitalFishingLicense" language="java" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="8b569b21-d605-4652-a54b-3e3479dc8334">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<parameter name="feeOD" class="java.lang.String"/>
	<parameter name="feeOffice" class="java.lang.String"/>
	<parameter name="formatedPassedOn" class="java.lang.String"/>
	<parameter name="fishingCertificateId" class="java.lang.String"/>
	<parameter name="person" class="de.adesso.fischereiregister.core.model.Person"/>
	<background height="55" splitType="Stretch"/>
	<title splitType="Stretch"/>
	<pageHeader height="87">
		<element kind="image" uuid="1eda496c-4841-436b-9aba-b429d816884c" stretchType="ElementGroupHeight" x="378" y="0" width="128" height="77">
			<expression><![CDATA[this.getClass().getClassLoader().getResourceAsStream("config/export/nw/flag.png")]]></expression>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</element>
		<element kind="image" uuid="2726210c-97bb-4e7b-a582-edab07d2a9b5" x="0" y="0" width="135" height="80">
			<expression><![CDATA[this.getClass().getClassLoader().getResourceAsStream("config/export/common/logoFisch.png")]]></expression>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</element>
	</pageHeader>
	<detail>
		<band height="490" splitType="Stretch">
			<element kind="textField" uuid="a98510f6-d7d4-4117-8950-be4e88e892ea" x="213" y="200" width="450" height="20">
				<expression><![CDATA[$P{person}.getFirstname()]]></expression>
			</element>
			<element kind="staticText" uuid="7828148a-db18-49cd-bae8-5462e8de9259" x="33" y="201" width="100" height="20" bold="true">
				<text><![CDATA[Vorname]]></text>
			</element>
			<element kind="textField" uuid="2a22dddf-80cf-4867-9895-a1eae2e7dbc0" x="213" y="220" width="450" height="20">
				<expression><![CDATA[$P{person}.getLastname()]]></expression>
			</element>
			<element kind="staticText" uuid="c9f11bb5-96b4-4e75-a4b6-48317d3ade39" x="33" y="221" width="100" height="20" bold="true">
				<text><![CDATA[Nachname]]></text>
			</element>
			<element kind="textField" uuid="b79e62da-2779-4eee-8999-9872898493ed" x="213" y="281" width="450" height="20">
				<expression><![CDATA[$P{feeOffice}]]></expression>
			</element>
			<element kind="staticText" uuid="61f07258-6dc7-4287-b698-a212b2303eb6" x="33" y="281" width="180" height="20" bold="true">
				<text><![CDATA[Gebühr Fischereischein Behörde]]></text>
			</element>
			<element kind="textField" uuid="14c9df0d-e6a3-45d5-848a-6f830834cf03" x="213" y="261" width="276" height="20">
				<expression><![CDATA[($P{feeOD})]]></expression>
			</element>
			<element kind="staticText" uuid="e5a6b7f5-d0e8-43be-8f9a-8972f1de673f" x="33" y="261" width="180" height="20" bold="true">
				<text><![CDATA[Gebühr Fischereischein OD]]></text>
			</element>
			<element kind="staticText" uuid="1af9a004-407f-407c-9831-5905e8874c5e" x="33" y="181" width="100" height="20" bold="true">
				<text><![CDATA[Titel]]></text>
			</element>
			<element kind="textField" uuid="9214262d-750d-45df-9140-543d5c0d38a1" x="213" y="180" width="440" height="20" blankWhenNull="true">
				<expression><![CDATA[$P{person}.getTitle()]]></expression>
			</element>
			<element kind="staticText" uuid="b54e62de-e509-45af-938e-06b93b5cc625" x="-20" y="56" width="595" height="30" fontSize="20.0" hTextAlign="Center" vTextAlign="Middle">
				<text><![CDATA[Zugangscode Fischereischein]]></text>
			</element>
			<element kind="staticText" uuid="91d92b83-2a2a-4b4e-bf70-848c3f2a8e6c" x="33" y="126" width="100" height="20" bold="true">
				<text><![CDATA[Ausstellungsdatum]]></text>
			</element>
			<element kind="textField" uuid="d8ad23d4-fb11-4c1a-b979-a0db8094694a" x="213" y="126" width="440" height="20" blankWhenNull="true">
				<expression><![CDATA[$P{formatedPassedOn}]]></expression>
			</element>
			<element kind="staticText" uuid="c54726f0-0324-478a-ab3f-6bdd7ba4f7d7" x="33" y="151" width="180" height="20" bold="true">
				<text><![CDATA[Zugangscode Fischereischein]]></text>
			</element>
			<element kind="textField" uuid="4ece6395-5c26-4a59-a088-fcf93617409e" x="213" y="151" width="440" height="20" blankWhenNull="true">
				<expression><![CDATA[$P{fishingCertificateId}]]></expression>
			</element>
			<property name="com.jaspersoft.studio.layout"/>
		</band>
	</detail>
	<pageFooter height="16"/>
</jasperReport>
