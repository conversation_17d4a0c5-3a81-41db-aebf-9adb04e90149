spring:
  application:
    name: register-service
  cloud:
    aws:
      s3:
        endpoint: ${S3_ENDPOINT}
        region: ${S3_REGION}
        bucket: ${S3_BUCKET}
        path-style-access-enabled: true
      credentials:
        access-key: ${S3_ACCESS_KEY}
        secret-key: ${S3_SECRET_KEY}
      clientConfig:
        connection-timeout: 10000
        max-connections: 100
        max-error-retry: 3
        socket-timeout: 10000
  liquibase:
    enabled: true
    change-log: classpath:config/liquibase/db.changelog-master.xml
  jpa:
    hibernate:
      ddl-auto: none
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
  datasource:
    jdbcUrl: ${DATABASE_URL}
    username: ${DATABASE_USERNAME}
    password: ${DATABASE_PASSWORD}
    driverClassName: org.postgresql.Driver

  jackson:
    default-property-inclusion: NON_NULL

  security:
    oauth2:
      client:
        provider:
          keycloak:
            issuer-uri: ${KEYCLOAK_ISSUER_URI}
            user-name-attribute: preferred_username
        registration:
          keycloak:
            client-id: ${KEYCLOAK_CLIENT_ID}
            client-secret: ${K<PERSON><PERSON><PERSON>OAK_CLIENT_SECRET}
            authorization-grant-type: authorization_code
            scope: openid
      resourceserver:
        jwt:
          issuer-uri: ${KEYCLOAK_ISSUER_URI}
  mail:
    host: ${SMTP_HOST}
    port: ${SMTP_PORT}
    username: ${SMTP_USERNAME}
    password: ${SMTP_PASSWORD}
    from:
      address: ${SMTP_FROM}
    defaultEncoding: UTF-8
    properties:
      mail:
        transport:
          protocol: smtp
        smtp:
          auth: false
          starttls:
            enable: true
    debug: false

message-service:
  token-uri: ${OS_INBOX_TOKEN_URI}
  client-id: ${OS_INBOX_CLIENT_ID}
  client-secret: ${OS_INBOX_CLIENT_SECRET}
  authorization-grant-type: client_credentials
  scope: ${OS_INBOX_CLIENT_SCOPE}
  resource: ${OS_INBOX_RESOURCE}
  base-urn: ${OS_INBOX_BASE_URN}

axon:
  axonserver:
    enabled: false
  serializer:
    general: jackson
    events: jackson
    messages: jackson

server:
  servlet:
    context-path: /api

management:
  endpoint:
    health:
      probes:
        enabled: true
  health:
    livenessState:
      enabled: true
    readinessState:
      enabled: true
  endpoints:
    web:
      exposure:
        include: health
  tracing:
    sampling:
      probability: 1.0
  metrics:
    distribution:
      percentiles-histogram:
        http:
          server:
            requests: true

logging:
  level:
    root: info
  pattern:
    level: "%5p [${spring.application.name:},%X{traceId:-},%X{spanId:-}]"

test-data-import:
  upload-endpoint-enabled: false
  local-file-at-startup-enabled: false
  path: classpath:config/testdata/test-data.csv

security:
  groups: groups
  realm_access: realm_access
  roles_claim: roles
  realm_access_claim: realm_access
  resource_access_claim: resource_access
  prefix_realm_role: ROLE_realm_
  prefix_resource_role: ROLE_

order:
  enabled: false
  template_version: v1
  base_urn: https://hersteller.de
  endpoint_path: /beispiel/api/fishing-license-order
  days_until_deletion: 30

migrations:
  # Will force a replay of ALL events on startup forcing also all upcasters to be applied.
  # This might cause heavy initial lag, since replaying events is very expensive.
  replay_events_on_startup: true
  initial_delay_in_minutes: 2 # Initial delay after app startup to wait, until migration is attempted
  retry:
    attempts: 10 # total number of attempts before the application will accept migration does not work
    delay_in_seconds: 30 # Delay in seconds between each migration attempt
