package de.adesso.fischereiregister.registerservice.ban;

import de.adesso.fischereiregister.RegisterServiceApplication;
import de.adesso.fischereiregister.registerservice.common.security.mocking.WithMockSecurityContext;
import de.adesso.fischereiregister.utils.DateUtils;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.openapitools.model.TemporaryBan;
import org.openapitools.model.TemporaryBanRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDate;
import java.util.UUID;

import static de.adesso.fischereiregister.registerservice.common.JsonParser.asJsonString;
import static org.hamcrest.Matchers.notNullValue;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;


@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK, classes = RegisterServiceApplication.class)
@AutoConfigureMockMvc(addFilters = false)
@WithMockSecurityContext()
public class TemporaryBanIntegrationTest {

    public static final String PATH = "/register-entries/{registerEntryId}/ban/temporary";

    @Autowired
    private MockMvc mockMvc;

    @Test
    @DisplayName("""
                POST "/api/register-entries/{registerEntryId}/ban/temporary"
                Verify that the request is rejected with a 404 if registerEntryId does not match an existing entry
            """)
    void nonExistentRegisterEntryId() throws Exception {
        //GIVEN
        final String registerEntryId = UUID.randomUUID().toString();

        final String path = PATH.replaceFirst("\\{registerEntryId}", registerEntryId);

        final TemporaryBan ban = new TemporaryBan();
        ban.setFileNumber("fileNumber");
        ban.setFrom(LocalDate.of(1990, 1, 1).format(DateUtils.GERMAN_DATE_TIME_FORMATTER));
        ban.setTo(LocalDate.of(2100, 1, 1).format(DateUtils.GERMAN_DATE_TIME_FORMATTER));

        TemporaryBanRequest banRequest = new TemporaryBanRequest();
        banRequest.setBan(ban);


        //WHEN
        //THEN
        mockMvc.perform(
                        post(path).contentType(MediaType.APPLICATION_JSON).content(asJsonString(banRequest)))
                .andExpect(status().isNotFound());

    }

    @Test
    @DisplayName("""
                POST "/api/register-entries/{registerEntryId}/ban"
                Verify that the request is rejected with a 400 if the ban is empty
            """)
    void nullBan() throws Exception {
        //GIVEN

        final String registerEntryId = UUID.randomUUID().toString();

        final String path = PATH.replaceFirst("\\{registerEntryId}", registerEntryId);

        //WHEN
        //THEN
        mockMvc.perform(
                        post(path).contentType(MediaType.APPLICATION_JSON).content(asJsonString("")))
                .andExpect(status().isBadRequest());

    }


    @Test
    @DisplayName("""
                POST "/api/register-entries/{registerEntryId}/ban/temporary"
                Verify that the request is rejected for a ban with empty fields.
            """)
    void simpleBanEmptyTemporaryTest() throws Exception {
        //GIVEN

        final String registerEntryId = UUID.randomUUID().toString();
        final TemporaryBanRequest banRequest = new TemporaryBanRequest();

        final String path = PATH.replaceFirst("\\{registerEntryId}", registerEntryId);


        //WHEN
        //THEN
        mockMvc.perform(
                        post(path).contentType(MediaType.APPLICATION_JSON).content(asJsonString(banRequest)))
                .andExpect(status().is4xxClientError());


    }


    @Test
    @DisplayName("""
                POST "/api/register-entries/{registerEntryId}/ban/temporary"
                Verify that the request is accepted for a simple ban
            """)
    void simpleTemporaryBanTest() throws Exception {
        //GIVEN

        final String registerEntryId = "e4b6af13-1feb-4a4d-9c46-76298a0611cf";
        final TemporaryBan ban = new TemporaryBan();
        ban.setFileNumber("fileNumber");
        ban.setReportedBy("reporter");
        ban.setFrom(LocalDate.of(1990, 1, 1).format(DateUtils.GERMAN_DATE_TIME_FORMATTER));
        ban.setTo(LocalDate.of(2100, 1, 1).format(DateUtils.GERMAN_DATE_TIME_FORMATTER));

        final TemporaryBanRequest banRequest = new TemporaryBanRequest();
        banRequest.setBan(ban);

        final String path = PATH.replaceFirst("\\{registerEntryId}", registerEntryId);

        //WHEN
        //THEN
        mockMvc.perform(
                        post(path).contentType(MediaType.APPLICATION_JSON).content(asJsonString(banRequest)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.registerEntryId", Matchers.equalTo(registerEntryId)))
                .andExpect(jsonPath("$.ban", notNullValue()));
    }


    @Test
    @DisplayName("""
                POST "/api/register-entries/{registerEntryId}/ban/temporary"
                Verify that the request is accepted for a simple ban twice on the same registerEntryId
            """)
    void simpleBanUpdateTest() throws Exception {
        //GIVEN

        final String registerEntryId = "e4b6af13-1feb-4a4d-9c46-76298a0611cf";
        final TemporaryBan ban1 = new TemporaryBan();
        ban1.setFileNumber(UUID.randomUUID().toString());
        ban1.setReportedBy(UUID.randomUUID().toString());
        ban1.setFrom(LocalDate.of(1990, 1, 1).format(DateUtils.GERMAN_DATE_TIME_FORMATTER));
        ban1.setTo(LocalDate.of(2100, 1, 1).format(DateUtils.GERMAN_DATE_TIME_FORMATTER));

        TemporaryBanRequest banRequest1 = new TemporaryBanRequest();
        banRequest1.setBan(ban1);

        final TemporaryBan ban2 = new TemporaryBan();
        ban2.setFileNumber(UUID.randomUUID().toString());
        ban2.setReportedBy(UUID.randomUUID().toString());
        ban2.setFrom(LocalDate.of(1990, 1, 1).format(DateUtils.GERMAN_DATE_TIME_FORMATTER));
        ban2.setTo(LocalDate.of(2000, 1, 1).format(DateUtils.GERMAN_DATE_TIME_FORMATTER));

        TemporaryBanRequest banRequest2 = new TemporaryBanRequest();
        banRequest2.setBan(ban2);


        final String path = PATH.replaceFirst("\\{registerEntryId}", registerEntryId);

        //WHEN
        //THEN
        mockMvc.perform(
                        post(path).contentType(MediaType.APPLICATION_JSON).content(asJsonString(banRequest1)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.registerEntryId", Matchers.equalTo(registerEntryId)))
                .andExpect(jsonPath("$.ban", notNullValue()));

        mockMvc.perform(
                        post(path).contentType(MediaType.APPLICATION_JSON).content(asJsonString(banRequest2)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.registerEntryId", Matchers.equalTo(registerEntryId)))
                .andExpect(jsonPath("$.ban", notNullValue()));


    }


}
