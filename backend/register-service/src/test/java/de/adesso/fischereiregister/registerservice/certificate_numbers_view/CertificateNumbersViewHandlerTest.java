package de.adesso.fischereiregister.registerservice.certificate_numbers_view;

import de.adesso.fischereiregister.core.events.QualificationsProofCreatedEvent;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.QualificationsProof;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.UUID;

import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)

public class CertificateNumbersViewHandlerTest {

    @Mock
    CertificateNumbersViewService service;

    @InjectMocks
    private CertificateNumbersViewHandler eventHandler;


    @Test
    @DisplayName("Test CertificateNumbersViewHandler.on is successful vor valid data.")
    public void testOnQualificationsProofCreatedEvent() {
        //GIVEN
        final String fishingCertificateId = UUID.randomUUID().toString();
        final String examinerId = UUID.randomUUID().toString();
        final QualificationsProof proof = new QualificationsProof();
        proof.setFishingCertificateId(fishingCertificateId);
        proof.setExaminerId(examinerId);
        final Person person = DomainTestData.createPerson();
        final UUID registerEntryId = UUID.randomUUID();
        final QualificationsProofCreatedEvent event = new QualificationsProofCreatedEvent(registerEntryId,
                proof,
                person
        );

        //WHEN
        eventHandler.on(event);
        //THEN
        verify(service, times(1)).createCertificateNumbersView(registerEntryId, proof.getFishingCertificateId(), proof.getType());

    }

}
