package de.adesso.fischereiregister.registerservice.qualifications_proof;

import de.adesso.fischereiregister.RegisterServiceApplication;
import de.adesso.fischereiregister.registerservice.common.JsonParser;
import de.adesso.fischereiregister.registerservice.common.TestDataUtil;
import de.adesso.fischereiregister.registerservice.common.security.mocking.WithMockSecurityContext;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.openapitools.model.CertificateFromExaminationApplicationRequest;
import org.openapitools.model.CertificateFromExaminationApplicationRequestCertificate;
import org.openapitools.model.FederalStateAbbreviation;
import org.openapitools.model.Person;
import org.openapitools.model.PersonES;
import org.openapitools.model.PreliminaryRegisterEntryRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import static org.hamcrest.Matchers.startsWith;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;


@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK, classes = RegisterServiceApplication.class)
@AutoConfigureMockMvc(addFilters = false)
@WithMockSecurityContext()
public class QualificationsProofIntegrationTest {

    private final String PRELIMINARY_REGISTER_ENTRY_PATH = "http://localhost:8080/preliminary-register-entries";

    @Autowired
    private MockMvc mvc;

    @Test
    @DisplayName("""
            	POST /api/preliminary-register-entries with empty object
            """)
    void createPreliminaryEntryWithoutData() throws Exception {
        //GIVEN
        final String postURL = PRELIMINARY_REGISTER_ENTRY_PATH;
        //WHEN
        final ResultActions result = mvc.perform(MockMvcRequestBuilders.post(postURL)
                .content(JsonParser.asJsonString(new PreliminaryRegisterEntryRequest())).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON));
        //THEN
        result.andExpect(status().is4xxClientError());


    }


    @Test
    @DisplayName("""
            	POST /api/preliminary-register-entries should fail if dates are provided not as dd.MM.yyyy
            """)
    void createPreliminaryEntryWithDataButWrongDateFormatInPerson() throws Exception {
        //GIVEN
        final Person person = new Person()
                .firstname("test")
                .lastname("test")
                .birthname("test")
                .birthdate("2002-02-02");

        final PreliminaryRegisterEntryRequest request = new PreliminaryRegisterEntryRequest().person(person)
                .certificate(new CertificateFromExaminationApplicationRequestCertificate().passedOn("2024-02-02"));


        final String postURL = PRELIMINARY_REGISTER_ENTRY_PATH;


        //WHEN //THEN
        mvc.perform(MockMvcRequestBuilders.post(postURL)
                        .content(JsonParser.asJsonString(request))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }


    @Test
    @DisplayName("""
            	POST /api/preliminary-register-entries should fail if dates are provided not as dd.MM.yyyy
            """)
    void createPreliminaryEntryWithDataButWrongDateFormatInCertificatePassedOn() throws Exception {

        //GIVEN
        final Person person = new Person()
                .firstname("test")
                .lastname("test")
                .birthname("test")
                .birthdate("02.02.2002");

        final PreliminaryRegisterEntryRequest request = new PreliminaryRegisterEntryRequest().person(person)
                .certificate(new CertificateFromExaminationApplicationRequestCertificate().passedOn("2024-02-02"));
        final String postURL = PRELIMINARY_REGISTER_ENTRY_PATH;

        //WHEN
        //THEN
        mvc.perform(MockMvcRequestBuilders.post(postURL)
                        .content(JsonParser.asJsonString(request)).contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }


    @Test
    @DisplayName("""
            	POST /api/preliminary-register-entries with valid object should return 201
            """)
    void createPreliminaryEntryWithData() throws Exception {
        //GIVEN
        final Person person = TestDataUtil.createPersonApi();

        final PreliminaryRegisterEntryRequest request = new PreliminaryRegisterEntryRequest().person(person)
                .certificate(new CertificateFromExaminationApplicationRequestCertificate().passedOn("02.02.2024"));
        final String postURL = PRELIMINARY_REGISTER_ENTRY_PATH;

        //WHEN
        final ResultActions result = mvc.perform(MockMvcRequestBuilders.post(postURL)
                .content(JsonParser.asJsonString(request)).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON));
        //THEN
        result.andExpect(status().isCreated())
                .andExpect(jsonPath("$.fishingCertificateId", startsWith("ZF")));


    }

    @Test
    @DisplayName("""
            	POST /api/preliminary-register-entries/certificate with empty object
            """)
    void createFromExamSoftwareWithoutData() throws Exception {
        //GIVEN
        final String postURL = PRELIMINARY_REGISTER_ENTRY_PATH + "/certificate";
        //WHEN
        final ResultActions result = mvc.perform(MockMvcRequestBuilders.post(postURL)
                .content(JsonParser.asJsonString(new CertificateFromExaminationApplicationRequest())).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON));
        //THEN
        result.andExpect(status().is4xxClientError());
    }

    @Test
    @DisplayName("""
            	POST /api/preliminary-register-entries/certificate should fail if dates are provided not as dd.MM.yyyy
            """)
    void createFromExamSoftwareWithDataButWrongDateFormatInPerson() throws Exception {
        //GIVEN
        final PersonES person = new PersonES()
                .firstname("test")
                .lastname("test")
                .birthname("test")
                .birthdate("2002-02-02");

        final CertificateFromExaminationApplicationRequest request = new CertificateFromExaminationApplicationRequest()
                .person(person)
                .certificate(new CertificateFromExaminationApplicationRequestCertificate().passedOn("2024-02-02"))
                .federalState(FederalStateAbbreviation.SH);

        final String postURL = PRELIMINARY_REGISTER_ENTRY_PATH + "/certificate";

        //WHEN //THEN
        mvc.perform(MockMvcRequestBuilders.post(postURL)
                        .content(JsonParser.asJsonString(request))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }

    @Test
    @DisplayName("""
            	POST /api/preliminary-register-entries/certificate should fail if dates are provided not as dd.MM.yyyy
            """)
    void createFromExamSoftwareWithDataButWrongDateFormatInCertificatePassedOn() throws Exception {
        //GIVEN
        final PersonES person = new PersonES()
                .firstname("test")
                .lastname("test")
                .birthname("test")
                .birthdate("02.02.2002");

        final CertificateFromExaminationApplicationRequest request = new CertificateFromExaminationApplicationRequest()
                .person(person)
                .certificate(new CertificateFromExaminationApplicationRequestCertificate().passedOn("2024-02-02"))
                .federalState(FederalStateAbbreviation.SH);

        final String postURL = PRELIMINARY_REGISTER_ENTRY_PATH + "/certificate";

        //WHEN //THEN
        mvc.perform(MockMvcRequestBuilders.post(postURL)
                        .content(JsonParser.asJsonString(request))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }

    @Test
    @DisplayName("""
            	POST /api/preliminary-register-entries/certificate with valid object should return 201
            """)
    void createFromExamSoftwareWithData() throws Exception {
        //GIVEN
        final PersonES person = new PersonES()
                .firstname("test")
                .lastname("test")
                .birthname("test")
                .birthdate("02.02.2002")
                .birthplace("test");

        final CertificateFromExaminationApplicationRequest request = new CertificateFromExaminationApplicationRequest()
                .person(person)
                .certificate(new CertificateFromExaminationApplicationRequestCertificate().passedOn("02.02.2024"))
                .federalState(FederalStateAbbreviation.SH);

        final String postURL = PRELIMINARY_REGISTER_ENTRY_PATH + "/certificate";

        //WHEN
        final ResultActions result = mvc.perform(MockMvcRequestBuilders.post(postURL)
                .content(JsonParser.asJsonString(request)).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON));

        //THEN
        result.andExpect(status().isCreated())
                .andExpect(jsonPath("$.fishingCertificateId", startsWith("ZF")));
    }

    @Test
    @DisplayName("""
            	POST /api/preliminary-register-entries/certificate should fail if federalState is missing
            """)
    void createFromExamSoftwareWithoutFederalState() throws Exception {
        //GIVEN
        final PersonES person = new PersonES()
                .firstname("test")
                .lastname("test")
                .birthname("test")
                .birthdate("02.02.2002")
                .birthplace("test");

        final CertificateFromExaminationApplicationRequest request = new CertificateFromExaminationApplicationRequest()
                .person(person)
                .certificate(new CertificateFromExaminationApplicationRequestCertificate().passedOn("02.02.2024"));
        // federalState is intentionally missing

        final String postURL = PRELIMINARY_REGISTER_ENTRY_PATH + "/certificate";

        //WHEN //THEN
        mvc.perform(MockMvcRequestBuilders.post(postURL)
                        .content(JsonParser.asJsonString(request))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }
}
