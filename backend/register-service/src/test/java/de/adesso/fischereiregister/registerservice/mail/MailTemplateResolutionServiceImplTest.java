package de.adesso.fischereiregister.registerservice.mail;

import de.adesso.fischereiregister.core.model.Birthdate;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.registerservice.mail.enums.MailTemplate;
import de.adesso.fischereiregister.registerservice.tenant.TenantConfigurationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class MailTemplateResolutionServiceImplTest {

    @Mock
    private TenantConfigurationService tenantConfigurationService;

    @InjectMocks
    private MailTemplateResolutionServiceImpl mailTemplateResolutionService;

    private FederalState federalState;
    private Person person;

    private static final String DEFAULT_CITIZEN_NAME_KEY = "mail_template.default.citizen_name";
    private static final MailTemplate MAIL_TEMPLATE = MailTemplate.REGULAR_FISHING_LICENSE_CREATED;
    private static final String SUBJECT_KEY = MAIL_TEMPLATE.getSubjectKey();
    private static final String TEXT_KEY = MAIL_TEMPLATE.getTextKey();
    private static final String SUBJECT_TEMPLATE = "Test Subject";
    private static final String TEXT_TEMPLATE = "Hello {{citizenFullname}}, this is a test <br> with line breaks.";
    private static final String EXPECTED_TEXT_WITH_NAME = "Hello Dr. Max Mustermann, this is a test \n with line breaks.";

    @BeforeEach
    void setUp() {
        federalState = FederalState.SH;

        person = new Person();
        person.setTitle("Dr.");
        person.setFirstname("Max");
        person.setLastname("Mustermann");
        person.setBirthdate(new Birthdate(1990, 1, 1));
    }

    @Test
    @DisplayName("MailTemplateResolutionServiceImpl.getSubject should return the subject with HTML line breaks replaced")
    void getSubject_ShouldReturnSubjectWithLineBreaksReplaced() {
        // Given
        when(tenantConfigurationService.getValue(federalState, SUBJECT_KEY)).thenReturn(SUBJECT_TEMPLATE);

        // When
        String result = mailTemplateResolutionService.getSubject(federalState, MAIL_TEMPLATE);

        // Then
        assertEquals(SUBJECT_TEMPLATE, result);
        verify(tenantConfigurationService).getValue(federalState, SUBJECT_KEY);
    }

    @Test
    @DisplayName("MailTemplateResolutionServiceImpl.getText should replace citizen name token and HTML line breaks when person is provided")
    void getText_WithPerson_ShouldReplaceNameTokenAndLineBreaks() {
        // Given
        when(tenantConfigurationService.getValue(federalState, TEXT_KEY)).thenReturn(TEXT_TEMPLATE);

        // When
        String result = mailTemplateResolutionService.getText(federalState, MAIL_TEMPLATE, person);

        // Then
        assertEquals(EXPECTED_TEXT_WITH_NAME, result);
        verify(tenantConfigurationService).getValue(federalState, TEXT_KEY);
    }


    @Test
    @DisplayName("MailTemplateResolutionServiceImpl.getText should use default name and replace HTML line breaks when person is null")
    void getText_WithNullPerson_ShouldUseDefaultNameAndReplaceLineBreaks() {
        // Given
        String defaultName = "Default Citizen";
        String expectedText = "Hello " + defaultName + ", this is a test \n with line breaks.";
        when(tenantConfigurationService.getValue(federalState, DEFAULT_CITIZEN_NAME_KEY)).thenReturn(defaultName);
        when(tenantConfigurationService.getValue(federalState, TEXT_KEY)).thenReturn(TEXT_TEMPLATE);

        // When
        String result = mailTemplateResolutionService.getText(federalState, MAIL_TEMPLATE, null);

        // Then
        assertEquals(expectedText, result);
        verify(tenantConfigurationService).getValue(federalState, TEXT_KEY);
        verify(tenantConfigurationService).getValue(federalState, DEFAULT_CITIZEN_NAME_KEY);
    }

    @Test
    @DisplayName("MailTemplateResolutionServiceImpl.getText should handle text without HTML line breaks")
    void getText_WithoutLineBreaks_ShouldNotChangeText() {
        // Given
        String textWithoutLineBreaks = "Hello {{citizenFullname}}, this is a test without line breaks.";
        String expectedText = "Hello Dr. Max Mustermann, this is a test without line breaks.";
        when(tenantConfigurationService.getValue(federalState, TEXT_KEY)).thenReturn(textWithoutLineBreaks);

        // When
        String result = mailTemplateResolutionService.getText(federalState, MAIL_TEMPLATE, person);

        // Then
        assertEquals(expectedText, result);
        verify(tenantConfigurationService).getValue(federalState, TEXT_KEY);
    }

    @Test
    @DisplayName("MailTemplateResolutionServiceImpl.getText should handle text with multiple HTML line breaks")
    void getText_WithMultipleLineBreaks_ShouldReplaceAllLineBreaks() {
        // Given
        String textWithMultipleLineBreaks = "Hello {{citizenFullname}},<br>this is a test<br>with multiple<br>line breaks.";
        String expectedText = "Hello Dr. Max Mustermann,\nthis is a test\nwith multiple\nline breaks.";
        when(tenantConfigurationService.getValue(federalState, TEXT_KEY)).thenReturn(textWithMultipleLineBreaks);

        // When
        String result = mailTemplateResolutionService.getText(federalState, MAIL_TEMPLATE, person);

        // Then
        assertEquals(expectedText, result);
        verify(tenantConfigurationService).getValue(federalState, TEXT_KEY);
    }
}
