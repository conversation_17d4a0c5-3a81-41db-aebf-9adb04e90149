package de.adesso.fischereiregister.registerservice.drools.dmn.tax;

import de.adesso.fischereiregister.registerservice.drools.dmn.tax.configuration.TaxInformationOutput;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

class TaxInformationResultTest {

    @Test
    void shouldInitializeWithValidMap() {
        // Given
        Map<String, Object> data = new HashMap<>();
        data.put(TaxInformationOutput.NETTOBETRAG_ABGABE, new BigDecimal("100.50"));
        data.put(TaxInformationOutput.VERWALTUNGSGEBUEHR, new BigDecimal("20.00"));
        data.put(TaxInformationOutput.GEBUEHR_BEHOERDENGANG, new BigDecimal("30.00"));
        data.put(TaxInformationOutput.JAHRE, new BigDecimal("2"));

        // When
        TaxInformationOutput result = new TaxInformationOutput(data);

        // Then
        assertThat(result.getNetTaxAmount()).isEqualByComparingTo("100.50");
        assertThat(result.getAdministrativeFee()).isEqualByComparingTo("20.00");
        assertThat(result.getOfficeFee()).isEqualByComparingTo("30.00");
        assertThat(result.getDuration()).isEqualTo(2);
        assertThat(result.getSummOfTaxAmount()).isEqualByComparingTo("150.50"); // 100.50 + 20.00 + 30.00
    }

    @Test
    void shouldHandleMissingValuesGracefully() {
        // Given
        Map<String, Object> data = new HashMap<>();

        // When
        TaxInformationOutput result = new TaxInformationOutput(data);

        // Then
        assertThat(result.getNetTaxAmount()).isEqualByComparingTo(BigDecimal.ZERO);
        assertThat(result.getAdministrativeFee()).isEqualByComparingTo(BigDecimal.ZERO);
        assertThat(result.getOfficeFee()).isEqualByComparingTo(BigDecimal.ZERO);
        assertThat(result.getDuration()).isEqualTo(0);
        assertThat(result.getSummOfTaxAmount()).isEqualByComparingTo(BigDecimal.ZERO);
    }

    @Test
    void shouldHandleNullValuesGracefully() {
        // Given
        Map<String, Object> data = new HashMap<>();
        data.put(TaxInformationOutput.NETTOBETRAG_ABGABE, null);
        data.put(TaxInformationOutput.VERWALTUNGSGEBUEHR, null);
        data.put(TaxInformationOutput.GEBUEHR_BEHOERDENGANG, null);
        data.put(TaxInformationOutput.JAHRE, null);

        // When
        TaxInformationOutput result = new TaxInformationOutput(data);

        // Then
        assertThat(result.getNetTaxAmount()).isEqualByComparingTo(BigDecimal.ZERO);
        assertThat(result.getAdministrativeFee()).isEqualByComparingTo(BigDecimal.ZERO);
        assertThat(result.getOfficeFee()).isEqualByComparingTo(BigDecimal.ZERO);
        assertThat(result.getDuration()).isEqualTo(0);
        assertThat(result.getSummOfTaxAmount()).isEqualByComparingTo(BigDecimal.ZERO);
    }

    @Test
    void shouldHandleNonBigDecimalValuesGracefully() {
        // Given
        Map<String, Object> data = new HashMap<>();
        data.put(TaxInformationOutput.NETTOBETRAG_ABGABE, "invalid");
        data.put(TaxInformationOutput.VERWALTUNGSGEBUEHR, 123);
        data.put(TaxInformationOutput.GEBUEHR_BEHOERDENGANG, null);
        data.put(TaxInformationOutput.JAHRE, "not a number");

        // When
        TaxInformationOutput result = new TaxInformationOutput(data);

        // Then
        assertThat(result.getNetTaxAmount()).isEqualByComparingTo(BigDecimal.ZERO);
        assertThat(result.getAdministrativeFee()).isEqualByComparingTo(BigDecimal.ZERO); // Since it's not a BigDecimal
        assertThat(result.getOfficeFee()).isEqualByComparingTo(BigDecimal.ZERO);
        assertThat(result.getDuration()).isEqualTo(0);
        assertThat(result.getSummOfTaxAmount()).isEqualByComparingTo(BigDecimal.ZERO);
    }
}
