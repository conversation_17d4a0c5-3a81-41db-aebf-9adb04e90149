package de.adesso.fischereiregister.registerservice.common;

import de.adesso.fischereiregister.core.model.Ban;
import de.adesso.fischereiregister.core.model.Birthdate;
import de.adesso.fischereiregister.core.model.Fee;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import de.adesso.fischereiregister.core.model.LimitedLicenseApproval;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.QualificationsProof;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.ValidityPeriod;
import de.adesso.fischereiregister.core.model.consent.JurisdictionConsentInfo;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.IdentificationDocumentType;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.model.type.QualificationsProofType;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import de.adesso.fischereiregister.registerservice.identification_document_view.IdentificationDocumentView;
import de.adesso.fischereiregister.view.register_entry_search.RegisterEntrySearchView;
import de.adesso.fischereiregister.registerservice.register_entry_view.RegisterEntryView;
import de.adesso.fischereiregister.utils.DateUtils;
import de.adesso.fischereiregister.utils.StringNormalizer;
import org.openapitools.model.Address;
import org.openapitools.model.AddressOS;
import org.openapitools.model.CreateRegularFishingLicenseRequest;
import org.openapitools.model.FederalStateAbbreviation;
import org.openapitools.model.OrderFishingLicenseRequestOS;
import org.openapitools.model.PayTaxRequestOS;
import org.openapitools.model.PersonOS;
import org.openapitools.model.PersonWithAddressOS;
import org.openapitools.model.ReplaceFishingLicenseRequestOS;
import org.openapitools.model.SearchItem;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class TestDataUtil extends DomainTestData {
    public final static UUID registerId = UUID.fromString("12345678-1234-1234-1234-************");
    public final static UUID banId = UUID.fromString("12345678-1234-1234-1234-123456780000");
    private final static String identificationDocumentId = "12345678-1234-1234-1234-************";
    public final static String licenseNumber = "SH12111122223331";
    public final static LicenseType licenseType = LicenseType.REGULAR;
    public final static String licenseIssuedBy = "BE";
    public final static String INBOX_REFERENCE = "sh/sh/12345678-1234-1234-1234-************";

    public final static String fishingCertificateId = "****************";

    private final static String salt = "testSalt";
    private final static String banFileNumber = "ban-1-file-number";
    private final static String banReportedByName = "Mock Polizeibehörde Kiel";
    public static final String FISHING_CERTIFICATE_CODE_**************** = "****************";
    public static final String TRANSACTION_ID_MOCK = "transactionIdMock";
    public static final String ACCOUNT_ID_MOCK = "accountIdMock";
    public static final String **************** = "****************";

    public static Jurisdiction createJurisdiction() {
        final Jurisdiction returnJurisdiction = new Jurisdiction();
        returnJurisdiction.setFederalState(federalState);

        return returnJurisdiction;
    }

    public static IdentificationDocumentView createIdentificationDocumentView() {
        final IdentificationDocumentView identificationDocumentView = new IdentificationDocumentView();

        identificationDocumentView.setId(123456L);
        identificationDocumentView.setDocumentType(IdentificationDocumentType.CARD.toString());
        identificationDocumentView.setIdentificationDocumentId(identificationDocumentId);
        identificationDocumentView.setRegisterId(registerId);
        identificationDocumentView.setLicenseNumber(licenseNumber);
        identificationDocumentView.setSalt(salt);
        return identificationDocumentView;
    }

    public static RegisterEntryView createRegisterEntryView() {
        final RegisterEntryView registerEntryView = new RegisterEntryView();

        registerEntryView.setRegisterId(registerId);
        registerEntryView.setData(createRegisterEntry());
        return registerEntryView;
    }

    public static RegisterEntryView createRegisterEntryViewWithCertificate() {
        final RegisterEntryView registerEntryView = new RegisterEntryView();

        registerEntryView.setRegisterId(registerId);
        registerEntryView.setData(createRegisterEntryWithCertificate());
        return registerEntryView;
    }

    public static org.openapitools.model.Address createAddressApi() {
        final org.openapitools.model.Address address = new org.openapitools.model.Address();
        address.setStreet(street);
        address.setStreetNumber(streetNumber);
        address.setPostcode(postalCode);
        address.setCity(city);

        return address;
    }

    public static Person createTestPerson() {
        final Person returnPerson = new Person();
        returnPerson.setFirstname(firstName);
        returnPerson.setLastname(lastName);
        returnPerson.setBirthname(birthName);
        returnPerson.setBirthdate(Birthdate.parse(birthDate.format(DateUtils.GERMAN_DATE_TIME_FORMATTER)));
        returnPerson.setBirthplace(birthPlace);

        return returnPerson;
    }

    public static JurisdictionConsentInfo createJurisdictionConsentInfo() {
        final JurisdictionConsentInfo returnConsentInfo = new JurisdictionConsentInfo();
        returnConsentInfo.setGdprAccepted(true);
        returnConsentInfo.setSubmittedByThirdParty(true);
        returnConsentInfo.setSelfDisclosureAccepted(true);

        return returnConsentInfo;
    }

    public static RegisterEntry createRegisterEntry() {
        final RegisterEntry registerEntry = new RegisterEntry();

        registerEntry.setRegisterId(registerId);
        registerEntry.setPerson(createPerson());
        registerEntry.setJurisdiction(createJurisdiction());

        final List<Tax> taxes = createAnalogTaxesWithOneTax();
        registerEntry.getTaxes().addAll(taxes);

        final List<Fee> fees = createAnalogFeesWithOneFee();
        registerEntry.getFees().addAll(fees);

        final FishingLicense fishingLicense = new FishingLicense();
        fishingLicense.setNumber("SH12-3456-7890-1234");
        fishingLicense.setIssuingFederalState(FederalState.SH);
        fishingLicense.setType(LicenseType.REGULAR);

        fishingLicense.getValidityPeriods().add(DomainTestData.createValidityPeriod(LocalDate.of(2024, 1, 1),
                LocalDate.now().plusYears(1)));

        final IdentificationDocument identificationDocument = new IdentificationDocument();
        identificationDocument.setDocumentId(identificationDocumentId);
        identificationDocument.setFishingLicense(fishingLicense);


        registerEntry.getIdentificationDocuments().add(identificationDocument);
        registerEntry.getFishingLicenses().add(fishingLicense);


        return registerEntry;
    }

    public static RegisterEntry createRegisterEntryWithCertificate() {
        final RegisterEntry registerEntry = new RegisterEntry();

        registerEntry.setRegisterId(registerId);
        registerEntry.setPerson(createPerson());
        registerEntry.setJurisdiction(createJurisdiction());

        registerEntry.getQualificationsProofs().add(createQualificationsProof());

        return registerEntry;
    }

    public static RegisterEntrySearchView createRegisterEntrySearchView() {
        final String normalizedFirstname = StringNormalizer.normalize(firstName);
        final String normalizedLastname = StringNormalizer.normalize(lastName);
        final String normalizedBirthplace = StringNormalizer.normalize(birthPlace);

        final RegisterEntrySearchView registerEntrySearchView = new RegisterEntrySearchView();

        registerEntrySearchView.setRegisterId(registerId);
        registerEntrySearchView.setBirthdate(birthDate.format(DateUtils.GERMAN_DATE_TIME_FORMATTER));
        registerEntrySearchView.setNormalizedBirthplace(normalizedBirthplace);
        registerEntrySearchView.setNormalizedName(normalizedFirstname + " " + normalizedLastname);

        final SearchItem searchRegisterEntry = new SearchItem();
        registerEntrySearchView.setData(searchRegisterEntry);

        return registerEntrySearchView;
    }

    public static FishingLicense createFishingLicense() {
        final FishingLicense fishingLicense = new FishingLicense();

        fishingLicense.setNumber(licenseNumber);
        fishingLicense.setType(licenseType);

        fishingLicense.getValidityPeriods().add(DomainTestData.createValidityPeriod(
                validFrom_Jan_2025,
                validTo_Dec_2025
        ));

        fishingLicense.setIssuingFederalState(FederalState.SH);

        return fishingLicense;
    }


    public static CreateRegularFishingLicenseRequest createRegularFishingLicenseRequest() {
        final CreateRegularFishingLicenseRequest request = new CreateRegularFishingLicenseRequest();
        request.setConsentInfo(createConsentInfoApi());
        request.setPerson(createPersonApiWithAddressApi());
        request.setFees(createFeesApi());
        request.setTaxes(createTaxesApi());
        return request;
    }

    public static List<org.openapitools.model.Tax> createTaxesApi() {

        final List<org.openapitools.model.Tax> list = new ArrayList<>();
        final org.openapitools.model.Tax tax = new org.openapitools.model.Tax();

        tax.setPaymentInfo(createPaymentInfoApiTax());
        tax.setFederalState(FederalStateAbbreviation.SH);
        tax.setValidFrom(validFrom_Jan_2025.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        tax.setValidTo(validTo_Dec_2025.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));

        list.add(tax);

        return list;
    }

    private static org.openapitools.model.PaymentInfo createPaymentInfoApiFee() {
        final org.openapitools.model.PaymentInfo paymentInfo = new org.openapitools.model.PaymentInfo();
        paymentInfo.setAmount(new BigDecimal("32.0"));
        paymentInfo.setType(org.openapitools.model.PaymentType.CASH);
        return paymentInfo;
    }

    private static org.openapitools.model.PaymentInfo createPaymentInfoApiTax() {
        final org.openapitools.model.PaymentInfo paymentInfo = new org.openapitools.model.PaymentInfo();
        paymentInfo.setAmount(new BigDecimal("17.0"));
        paymentInfo.setType(org.openapitools.model.PaymentType.ONLINE);
        return paymentInfo;
    }

    public static List<org.openapitools.model.Fee> createFeesApi() {
        final List<org.openapitools.model.Fee> list = new ArrayList<>();

        final org.openapitools.model.Fee fee = new org.openapitools.model.Fee();

        fee.setPaymentInfo(createPaymentInfoApiFee());
        fee.setFederalState(FederalStateAbbreviation.SH);
        fee.setValidFrom(validFrom_Jan_2025.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        fee.setValidTo(validTo_Dec_2025.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        list.add(fee);
        return list;
    }

    public static org.openapitools.model.Person createPersonApi() {
        final org.openapitools.model.Person returnPerson = new org.openapitools.model.Person();
        returnPerson.setFirstname(firstName);
        returnPerson.setLastname(lastName);
        returnPerson.setBirthname(birthName);
        returnPerson.setBirthdate(birthDate.format(GERMAN_DATE_TIME_FORMATTER));
        returnPerson.setBirthplace(birthPlace);
        returnPerson.setNationality(nationality);

        return returnPerson;
    }

    public static org.openapitools.model.PersonWithAddress createPersonWithAddressApi() {
        final org.openapitools.model.PersonWithAddress returnPerson = new org.openapitools.model.PersonWithAddress();
        returnPerson.setFirstname(firstName);
        returnPerson.setLastname(lastName);
        returnPerson.setBirthname(birthName);
        returnPerson.setBirthdate(birthDate.format(GERMAN_DATE_TIME_FORMATTER));
        returnPerson.setBirthplace(birthPlace);
        returnPerson.setNationality(nationality);

        returnPerson.setAddress(createAddressApi());

        return returnPerson;
    }


    public static org.openapitools.model.PersonWithAddress createPersonApiWithAddressApi() {

        final Address address = new Address();
        address.setStreet(street);
        address.setStreetNumber(streetNumber);
        address.setPostcode(postalCode);
        address.setCity(city);

        final org.openapitools.model.PersonWithAddress person = new org.openapitools.model.PersonWithAddress();

        person.setFirstname(firstName);
        person.setLastname(lastName);
        person.setBirthname(birthName);
        person.setBirthdate(birthDate.format(GERMAN_DATE_TIME_FORMATTER));
        person.setBirthplace(birthPlace);
        person.setNationality(nationality);
        person.setAddress(address);

        return person;

    }

    public static org.openapitools.model.ConsentInfo createConsentInfoApi() {
        final org.openapitools.model.ConsentInfo consentInfo = new org.openapitools.model.ConsentInfo();

        consentInfo.setGdprAccepted(true);
        consentInfo.setSubmittedByThirdParty(true);
        consentInfo.setSelfDisclosureAccepted(true);

        return consentInfo;
    }

    public static org.openapitools.model.TaxConsentInfo createTaxConsentInfoApi() {
        final org.openapitools.model.TaxConsentInfo consentInfo = new org.openapitools.model.TaxConsentInfo();

        consentInfo.setGdprAccepted(true);
        consentInfo.setSubmittedByThirdParty(true);

        return consentInfo;
    }


    public static List<IdentificationDocument> createIdentificationDocuments() {
        final List<IdentificationDocument> ies = new ArrayList<>();

        final IdentificationDocument ie = createIdentificationDocumentPDF(UUID.randomUUID().toString());
        ies.add(ie);
        return ies;
    }

    public static IdentificationDocument createIdentificationDocumentPDF(String identificationDocumentId) {
        final IdentificationDocument identificationDocument = new IdentificationDocument();
        identificationDocument.setIssuedDate(LocalDate.now());
        identificationDocument.setType(IdentificationDocumentType.PDF);
        identificationDocument.setDocumentId(identificationDocumentId);
        return identificationDocument;
    }

    public static Ban createBan() {
        final Ban ban = new Ban();
        ban.setBanId(UUID.randomUUID());
        ban.setTo(validFrom_Jan_2025);
        ban.setFrom(validTo_Dec_2025);
        ban.setFileNumber(banFileNumber);
        ban.setReportedBy(banReportedByName);
        return ban;
    }

    public static QualificationsProof createQualificationsProof() {
        final QualificationsProof qualificationsProof = new QualificationsProof();

        qualificationsProof.setType(QualificationsProofType.CERTIFICATE);
        qualificationsProof.setFishingCertificateId(TestDataUtil.fishingCertificateId);
        qualificationsProof.setFederalState(TestDataUtil.federalState);
        qualificationsProof.setPassedOn(passedOnDate);

        return qualificationsProof;
    }


    public static OrderFishingLicenseRequestOS createOrderFishingLicenseRequestOS() {

        // Add taxes to a list
        List<org.openapitools.model.Tax> taxes = new ArrayList<>();
        taxes.add(getTaxApi(createPaymentInfoApiTax()));
        taxes.add(getTaxApi(createPaymentInfoApiTax()));

        // Create OrderFishingLicenseRequestOS object
        OrderFishingLicenseRequestOS orderFishingLicenseRequest = new OrderFishingLicenseRequestOS();
        orderFishingLicenseRequest.setLicenseNumber(null);
        orderFishingLicenseRequest.setFishingCertificateCode(FISHING_CERTIFICATE_CODE_****************);
        orderFishingLicenseRequest.setTransactionId(TRANSACTION_ID_MOCK);
        orderFishingLicenseRequest.setServiceAccountId(ACCOUNT_ID_MOCK);
        orderFishingLicenseRequest.setFederalState(FederalStateAbbreviation.SH);
        orderFishingLicenseRequest.setConsentInfo(getConsentInfoApi());
        orderFishingLicenseRequest.setTaxes(taxes);
        orderFishingLicenseRequest.setFees(null); // Fees are null
        orderFishingLicenseRequest.setPerson(getPersonWithAddressOS(getAddressOS()));
        orderFishingLicenseRequest.setInboxReference(INBOX_REFERENCE);

        return orderFishingLicenseRequest;
    }

    private static PersonWithAddressOS getPersonWithAddressOS(AddressOS address) {
        PersonWithAddressOS person = new PersonWithAddressOS();
        person.setTitle("Dr.");
        person.setFirstname("Marc ID");
        person.setLastname("lastname");
        person.setBirthname("birthname");
        person.setBirthplace("Berlin");
        person.setBirthdate("08.08.1990");
        person.setAddress(address);
        person.setNationality("deutsch");
        return person;
    }

    private static AddressOS getAddressOS() {
        AddressOS address = new AddressOS();
        address.setStreet("street");
        address.setStreetNumber("12");
        address.setPostcode("12334");
        address.setCity("Kiel");
        return address;
    }

    private static org.openapitools.model.Tax getTaxApi(org.openapitools.model.PaymentInfo paymentInfo1) {
        org.openapitools.model.Tax tax1 = new org.openapitools.model.Tax();
        tax1.setFederalState(FederalStateAbbreviation.SH);
        tax1.setValidFrom("2024-01-01");
        tax1.setValidTo("2024-12-31");
        tax1.setPaymentInfo(paymentInfo1);
        return tax1;
    }

    private static org.openapitools.model.ConsentInfo getConsentInfoApi() {
        org.openapitools.model.ConsentInfo consentInfo = new org.openapitools.model.ConsentInfo();
        consentInfo.setGdprAccepted(true);
        consentInfo.setSelfDisclosureAccepted(true);
        consentInfo.setSubmittedByThirdParty(false);
        return consentInfo;
    }

    private static org.openapitools.model.TaxConsentInfo getTaxConsentInfoApi() {
        org.openapitools.model.TaxConsentInfo consentInfo = new org.openapitools.model.TaxConsentInfo();
        consentInfo.setGdprAccepted(true);
        consentInfo.setSubmittedByThirdParty(false);
        return consentInfo;
    }

    public static ReplaceFishingLicenseRequestOS createReplaceFishingLicenseRequestOS() {
        // Add taxes to a list
        List<org.openapitools.model.Tax> taxes = new ArrayList<>();
        taxes.add(getTaxApi(createPaymentInfoApiTax()));
        taxes.add(getTaxApi(createPaymentInfoApiTax()));

        // Create OrderFishingLicenseRequestOS object
        ReplaceFishingLicenseRequestOS requestOS = new ReplaceFishingLicenseRequestOS();
        requestOS.setLicenseNumber(****************);
        requestOS.setTransactionId(TRANSACTION_ID_MOCK);
        requestOS.setServiceAccountId(ACCOUNT_ID_MOCK);
        requestOS.setFederalState(FederalStateAbbreviation.SH);
        requestOS.setConsentInfo(getConsentInfoApi());
        requestOS.setTaxes(taxes);
        requestOS.setFees(null); // Fees are null
        requestOS.setPerson(getPersonWithAddressOS(getAddressOS()));
        requestOS.setInboxReference(INBOX_REFERENCE);

        return requestOS;
    }

    public static PayTaxRequestOS createPayTaxRequestOS() {
        // Add taxes to a list
        List<org.openapitools.model.Tax> taxes = new ArrayList<>();
        taxes.add(getTaxApi(createPaymentInfoApiTax()));
        taxes.add(getTaxApi(createPaymentInfoApiTax()));

        // Create OrderFishingLicenseRequestOS object
        PayTaxRequestOS requestOS = new PayTaxRequestOS();
        requestOS.setLicenseNumber(****************);
        requestOS.setTransactionId(TRANSACTION_ID_MOCK);
        requestOS.setServiceAccountId(ACCOUNT_ID_MOCK);
        requestOS.setFederalState(FederalStateAbbreviation.SH);
        requestOS.setConsentInfo(getTaxConsentInfoApi());
        requestOS.setTaxes(taxes);
        requestOS.setPerson(createPersonOS());
        requestOS.setInboxReference(INBOX_REFERENCE);

        return requestOS;
    }

    public static PersonOS createPersonOS() {
        PersonOS person = new PersonOS();
        person.setTitle("Dr.");
        person.setFirstname("Marc ID");
        person.setLastname("lastname");
        person.setBirthname("birthname");
        person.setBirthplace("Berlin");
        person.setBirthdate("08.08.1990");
        person.setNationality("deutsch");
        return person;
    }

    public static ValidityPeriod createValidityPeriod() {
        ValidityPeriod validityPeriod = new ValidityPeriod();
        validityPeriod.setValidFrom(validFrom_Jan_2025);
        validityPeriod.setValidTo(validTo_Dec_2025);
        return validityPeriod;
    }

    public static RegisterEntryView createRegisterEntryViewForLimitedLicense() {
        final RegisterEntryView registerEntryView = new RegisterEntryView();

        registerEntryView.setRegisterId(registerId);
        registerEntryView.setData(createRegisterEntryForLimitedLicense());
        return registerEntryView;
    }

    public static RegisterEntry createRegisterEntryForLimitedLicense() {
        final RegisterEntry registerEntry = new RegisterEntry();

        registerEntry.setRegisterId(registerId);
        registerEntry.setPerson(createPersonWithAddress());
        registerEntry.setJurisdiction(createJurisdiction());

        final List<Tax> taxes = createAnalogTaxesWithOneTax();
        registerEntry.getTaxes().addAll(taxes);

        final List<Fee> fees = createAnalogFeesWithOneFee();
        registerEntry.getFees().addAll(fees);

        final LimitedLicenseApproval limitedLicenseApproval = DomainTestData.createLimitedLicenseApproval();
        final FishingLicense fishingLicense = new FishingLicense();
        fishingLicense.setNumber("SH12-3456-7890-1234");
        fishingLicense.setIssuingFederalState(FederalState.SH);
        fishingLicense.setType(LicenseType.LIMITED);
        fishingLicense.setLimitedLicenseApproval(limitedLicenseApproval);

        fishingLicense.getValidityPeriods().add(DomainTestData.createValidityPeriod(LocalDate.of(2024, 1, 1),
                LocalDate.now().plusYears(1)));

        final IdentificationDocument identificationDocument = new IdentificationDocument();
        identificationDocument.setDocumentId(identificationDocumentId);
        identificationDocument.setLimitedLicenseApproval(limitedLicenseApproval);


        registerEntry.getIdentificationDocuments().add(identificationDocument);
        registerEntry.getFishingLicenses().add(fishingLicense);

        return registerEntry;
    }
}

