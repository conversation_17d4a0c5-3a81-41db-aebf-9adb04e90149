package de.adesso.fischereiregister.registerservice.apapters.inmemory;


import de.adesso.fischereiregister.registerservice.register_entry_view.RegisterEntryView;
import de.adesso.fischereiregister.registerservice.register_entry_view.RegisterEntryViewRepository;

import java.util.Map;
import java.util.Optional;
import java.util.UUID;

public class InMemoryRegisterEntryViewRepository extends InMemoryCrudRepository<RegisterEntryView, UUID> implements RegisterEntryViewRepository {

    @Override
    protected UUID getID(RegisterEntryView entity) {
        return entity.getRegisterId();
    }

    @Override
    public Optional<RegisterEntryView> findByRegisterId(UUID id) {
        return save.entrySet().stream()
                .filter(entry -> entry.getValue().getRegisterId().equals(id))
                .findFirst()
                .map(Map.Entry::getValue);
    }

}
