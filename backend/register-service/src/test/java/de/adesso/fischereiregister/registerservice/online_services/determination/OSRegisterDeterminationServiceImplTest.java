package de.adesso.fischereiregister.registerservice.online_services.determination;

import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.registerservice.online_services.model.OSRegisterDeterminationResult;
import de.adesso.fischereiregister.registerservice.online_services.model.OSRequestStatus;
import de.adesso.fischereiregister.view.register_entry_search.RegisterEntrySearchViewService;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.when;


@ExtendWith(MockitoExtension.class)
class OSRegisterDeterminationServiceImplTest {
    @Mock
    private RegisterEntrySearchViewService registerEntrySearchViewService;

    @InjectMocks
    private OSRegisterDeterminationServiceImpl osRegisterDeterminationService;

    @Test
    @DisplayName("OSRegisterDeterminationServiceImpl.determineRegisterEntryId should return RegisterEntryId when licenseNumber is provided")
    void determineRegisterEntryId_withLicenseNumber_shouldReturnRegisterEntryIdForLicenseOperation() {
        // given
        String licenseNumber = "SH85598631191522";
        UUID expectedId = UUID.randomUUID();
        when(registerEntrySearchViewService.findRegisterEntryIdByIdentificationNumber(licenseNumber))
                .thenReturn(Optional.of(expectedId));

        // when
        OSRegisterDeterminationResult result = osRegisterDeterminationService.determineRegisterEntryId(licenseNumber, null);

        // then
        assertNotNull(result.getRegisterEntryId());
        assertEquals(expectedId, result.getRegisterEntryId());
    }

    @Test
    @DisplayName("OSRegisterDeterminationServiceImpl.determineRegisterEntryId should return single RegisterEntryId when person details are provided")
    void determineRegisterEntryId_withPersonDetails_shouldReturnSingleRegisterEntryIdForLicenseOperation() {
        // given
        Person person = new Person();
        UUID expectedId = UUID.randomUUID();
        when(registerEntrySearchViewService.findRegisterEntryIdsByPersonDetails(person))
                .thenReturn(List.of(expectedId));

        // when
        OSRegisterDeterminationResult result = osRegisterDeterminationService.determineRegisterEntryId(null, person);

        // then
        assertNotNull(result.getRegisterEntryId());
        assertEquals(expectedId, result.getRegisterEntryId());
    }

    @Test
    @DisplayName("OSRegisterDeterminationServiceImpl.determineRegisterEntryId should return PERSON_NOT_FOUND failure when no person matches")
    void determineRegisterEntryId_ForLicenseOperation_withPersonDetails_shouldReturnPersonNotFoundFailure() {
        // given
        Person person = new Person();
        when(registerEntrySearchViewService.findRegisterEntryIdsByPersonDetails(person))
                .thenReturn(List.of());

        // when
        OSRegisterDeterminationResult result = osRegisterDeterminationService.determineRegisterEntryId(null, person);

        // then
        assertNull(result.getRegisterEntryId());
        assertEquals(OSRequestStatus.PERSON_NOT_FOUND, result.getOsStatus());
    }

    @Test
    @DisplayName("OSRegisterDeterminationServiceImpl.determineRegisterEntryId should return MULTIPLE_PERSONS_FOUND failure when multiple persons are found")
    void determineRegisterEntryId_ForLicenseOperation_withPersonDetails_shouldReturnMultiplePersonsFoundFailure() {
        // given
        Person person = new Person();
        when(registerEntrySearchViewService.findRegisterEntryIdsByPersonDetails(person))
                .thenReturn(List.of(UUID.randomUUID(), UUID.randomUUID()));

        // when
        OSRegisterDeterminationResult result = osRegisterDeterminationService.determineRegisterEntryId(null, person);

        // then
        assertNull(result.getRegisterEntryId());
        assertEquals(OSRequestStatus.MULTIPLE_PERSONS_FOUND, result.getOsStatus());
    }
}