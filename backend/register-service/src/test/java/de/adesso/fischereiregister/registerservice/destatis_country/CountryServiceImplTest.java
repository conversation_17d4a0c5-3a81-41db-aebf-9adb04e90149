package de.adesso.fischereiregister.registerservice.destatis_country;

import com.fasterxml.jackson.databind.ObjectMapper;
import de.adesso.fischereiregister.registerservice.destatis_country.data.CountryDataRow;
import de.adesso.fischereiregister.registerservice.destatis_country.data.DESTATISCountryFile;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class CountryServiceImplTest {

    private CountryServiceImpl countryService;

    @Mock
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        countryService = new CountryServiceImpl();
        countryService.objectMapper = objectMapper; // Inject mocked ObjectMapper
    }

    @Test
    void testIsNationalityValid_ValidNationality() throws IOException {
        // Arrange
        String validNationality = "deutsch";
        DESTATISCountryFile mockData = createMockDESTATISContryFile(validNationality);
        when(objectMapper.readValue(any(InputStream.class), eq(DESTATISCountryFile.class))).thenReturn(mockData);

        // Act
        boolean result = countryService.isNationalityValid(validNationality);

        // Assert
        assertTrue(result);
    }

    @Test
    void testIsNationalityValid_InvalidNationality() throws IOException {
        // Arrange
        String invalidNationality = "invalid";
        DESTATISCountryFile mockData = createMockDESTATISContryFile("deutsch");
        when(objectMapper.readValue(any(InputStream.class), eq(DESTATISCountryFile.class))).thenReturn(mockData);

        // Act
        boolean result = countryService.isNationalityValid(invalidNationality);

        // Assert
        assertFalse(result);
    }

    @Test
    void testLoadNationalityFile_ExceptionHandling() throws IOException {
        // Arrange
        when(objectMapper.readValue(any(InputStream.class), eq(DESTATISCountryFile.class)))
                .thenThrow(new IOException("Mocked IOException"));

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> countryService.loadNationalityFile());
    }

    private DESTATISCountryFile createMockDESTATISContryFile(String validNationality) {
        // Create mock data
        CountryDataRow row = mock(CountryDataRow.class);
        when(row.matchesAdjective(validNationality)).thenReturn(true);
        DESTATISCountryFile file = mock(DESTATISCountryFile.class);
        when(file.getDaten()).thenReturn(List.of(row));
        return file;
    }
}
