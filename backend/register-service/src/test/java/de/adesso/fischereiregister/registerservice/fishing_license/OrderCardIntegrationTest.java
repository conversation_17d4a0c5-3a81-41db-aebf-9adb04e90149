package de.adesso.fischereiregister.registerservice.fishing_license;

import de.adesso.fischereiregister.RegisterServiceApplication;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.registerservice.common.TestDataUtil;
import de.adesso.fischereiregister.registerservice.common.security.mocking.WithMockSecurityContext;
import de.adesso.fischereiregister.registerservice.register_entry_view.RegisterEntryView;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.openapitools.model.ConsentInfo;
import org.openapitools.model.CreateRegularFishingLicenseRequest;
import org.openapitools.model.FederalStateAbbreviation;
import org.openapitools.model.Tax;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.util.List;

import static de.adesso.fischereiregister.registerservice.common.JsonParser.asJsonString;
import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.notNullValue;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK, classes = RegisterServiceApplication.class)
@AutoConfigureMockMvc(addFilters = false)
@WithMockSecurityContext()
public class OrderCardIntegrationTest {

    private static final String ENDPOINT = "/register-entries/{registerEntryId}/fishing-licenses/{licenseId}/orders";

    @Autowired
    private MockMvc mvc;

    @Test
    @DisplayName("""
            	POST /api/register-entries/{registerEntryId}/fishing-licenses/{licenseId}/orders"
            	Verify that the card order endpoint returns a 4XX status code if no content is provided.
            """)
    void orderCardWithoutData() throws Exception {

        final String interpolatedPath = ENDPOINT.replaceFirst("\\{registerEntryId}", "registerEntryId")
                .replaceFirst("\\{licenseId}", "licenseId");

        mvc.perform(MockMvcRequestBuilders.post("http://localhost:8080" + interpolatedPath)
                        .content("")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().is4xxClientError());

    }


    @Test
    @DisplayName("""
            	POST /api/register-entries/{registerEntryId}/fishing-licenses/{licenseId}/orders"
            	Verify that the card order endpoint returns a 200 if a valid request is provided
            """)
    void orderCardValidRequest() throws Exception {
        final String registerEntryId = "351031b4-d8ca-4380-a49f-47159ac68b8b";
        final String licenseId = "SH85598631191522";
        final String interpolatedPath = ENDPOINT.replaceFirst("\\{registerEntryId}", registerEntryId)
                .replaceFirst("\\{licenseId}", licenseId);

        //final OrderCheckCardRequest request = new OrderCheckCardRequest();
        final CreateRegularFishingLicenseRequest request = new CreateRegularFishingLicenseRequest();

        request.setPerson(TestDataUtil.createPersonWithAddressApi());
        request.consentInfo(TestDataUtil.createConsentInfoApi());
        request.fees(TestDataUtil.createFeesApi());
        request.taxes(TestDataUtil.createTaxesApi());

        final RegisterEntryView registerEntryView = new RegisterEntryView();
        final RegisterEntry registerEntry = new RegisterEntry();
        final FishingLicense fishingLicense = new FishingLicense();
        fishingLicense.setNumber(licenseId);
        registerEntry.getFishingLicenses().add(fishingLicense);

        registerEntryView.setData(registerEntry);

        mvc.perform(MockMvcRequestBuilders.post("http://localhost:8080" + interpolatedPath)
                        .content(asJsonString(request))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.documents", hasSize(request.getTaxes().size() + 2)))  //taxes plus we expect new CARD and new PDF, license document
                .andExpect(jsonPath("$.person.firstname", Matchers.containsString(request.getPerson().getFirstname())))
                .andExpect(jsonPath("$.fishingLicense.type", Matchers.containsString(LicenseType.REGULAR.toString())))
                .andExpect(jsonPath("$.registerEntryId", notNullValue()));
    }

    @Test
    @DisplayName("""
            	POST /api/register-entries/{registerEntryId}/fishing-licenses/{licenseId}/orders"
            	Verify that the card order endpoint returns a 401 you try to file taxes for a different state
            """)
    void orderCardInvalidTax() throws Exception {
        final String registerEntryId = "351031b4-d8ca-4380-a49f-47159ac68b8b";
        final String licenseId = "SH85-5986-3119-1522";
        final String interpolatedPath = ENDPOINT.replaceFirst("\\{registerEntryId}", registerEntryId)
                .replaceFirst("\\{licenseId}", licenseId);

       // final OrderCheckCardRequest request = new OrderCheckCardRequest();
        final CreateRegularFishingLicenseRequest request = new CreateRegularFishingLicenseRequest();

        request.setPerson(TestDataUtil.createPersonApiWithAddressApi());
        request.consentInfo(new ConsentInfo());
        request.fees(TestDataUtil.createFeesApi());

        final List<Tax> taxes = TestDataUtil.createTaxesApi();
        taxes.get(0).setFederalState(FederalStateAbbreviation.BE);
        request.taxes(taxes);

        mvc.perform(MockMvcRequestBuilders.post("http://localhost:8080" + interpolatedPath)
                        .content(asJsonString(request))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());

    }

}
