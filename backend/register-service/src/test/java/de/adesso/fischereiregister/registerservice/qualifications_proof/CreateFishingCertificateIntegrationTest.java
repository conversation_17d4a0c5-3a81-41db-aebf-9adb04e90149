package de.adesso.fischereiregister.registerservice.qualifications_proof;

import com.c4_soft.springaddons.security.oauth2.test.annotations.Claims;
import com.c4_soft.springaddons.security.oauth2.test.annotations.OpenIdClaims;
import com.c4_soft.springaddons.security.oauth2.test.annotations.StringClaim;
import com.c4_soft.springaddons.security.oauth2.test.annotations.WithMockJwtAuth;
import de.adesso.fischereiregister.RegisterServiceApplication;
import de.adesso.fischereiregister.registerservice.common.TestDataUtil;
import de.adesso.fischereiregister.registerservice.common.security.mocking.WithMockSecurityContext;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.openapitools.model.CertificateFromExaminationApplicationRequestCertificate;
import org.openapitools.model.Person;
import org.openapitools.model.PreliminaryRegisterEntryRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import static de.adesso.fischereiregister.registerservice.common.JsonParser.asJsonString;
import static org.hamcrest.Matchers.startsWith;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;


@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK, classes = RegisterServiceApplication.class)
@AutoConfigureMockMvc(addFilters = false)
@WithMockSecurityContext
public class CreateFishingCertificateIntegrationTest {

    @Autowired
    private MockMvc mvc;


    @Test
    @DisplayName("""
            	POST /api/preliminary-register-entries
            	Verify that the fishing-certificates endpoint can be reached and the save of the fishing certificate is processed without an Error being thrown.
            """)
    @WithMockJwtAuth(claims = @OpenIdClaims(otherClaims = @Claims(stringClaims = {
            @StringClaim(name = "federalState", value = "SH")
    })))
    void callFishingCertificatesSuccessful() throws Exception {
        mvc.perform(MockMvcRequestBuilders.post("http://localhost:8080/preliminary-register-entries")
                        .content(asJsonString(validFishingCertificateRequest()))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(
                        status().isCreated()
                )
                .andExpect(jsonPath("$.fishingCertificateId", startsWith("ZF"))
                );
    }

    private PreliminaryRegisterEntryRequest validFishingCertificateRequest() {
        final Person person = TestDataUtil.createPersonApi();

        final CertificateFromExaminationApplicationRequestCertificate certificate = new CertificateFromExaminationApplicationRequestCertificate();
        certificate.setPassedOn("04.07.2023");

        final PreliminaryRegisterEntryRequest dto1 = new PreliminaryRegisterEntryRequest();
        dto1.setCertificate(certificate);
        dto1.setPerson(person);

        return dto1;
    }
}
