package de.adesso.fischereiregister.registerservice.utils;

import de.adesso.fischereiregister.core.model.type.FederalState;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.RepeatedTest;
import org.junit.jupiter.api.Test;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.matchesPattern;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class NumberGenerationUtilsTest {


    @Test
    @DisplayName("NumberGenerationUtils.generateCertificateId generates correct prefix")
    public void testGenerateCertificateIdCorrectPrefix() {
        // Given

        // When
        final String number = NumberGenerationUtils.generateCertificateId();

        // Then
        assertTrue(number.startsWith("ZF"));
        assertEquals(16, number.length());
        assertThat(number, matchesPattern("\\bZF\\d{14}\\b"));
    }

    @Test
    @DisplayName("NumberGenerationUtils.generateLicenseNumber prepends the correct prefix")
    public void testGenerateLicenseNumberCorrectPrefix() {
        // Given
        FederalState federalState = FederalState.SH;

        // When
        final String number = NumberGenerationUtils.generateLicenseNumber(federalState);

        // Then
        assertTrue(number.startsWith(federalState.toString()));
        assertEquals(16, number.length());
        assertThat(number, matchesPattern("\\bSH\\d{14}\\b"));
    }

    @DisplayName("NumberGenerationUtils.generateCertificateId generates correct verification numbers")
    @RepeatedTest(10)
    public void testGenerateCertificateIdCorrectVerificationNumbers() {
        // GIVEN

        // WHEN
        final String number = NumberGenerationUtils.generateCertificateId();

        // THEN
        assertThat(number, matchesPattern("\\bZF\\d{14}\\b"));

        long digits = Long.parseLong(number.substring(2));
        assertEquals(1, digits % 97);
    }

    @DisplayName("NumberGenerationUtils.generateCertificateId generates correct verification numbers")
    @RepeatedTest(10)
    public void testGenerateLicenseNumberCorrectVerificationNumbers() {
        // GIVEN

        // WHEN
        final String number = NumberGenerationUtils.generateLicenseNumber(FederalState.SH);

        // THEN
        assertThat(number, matchesPattern("\\bSH\\d{14}\\b"));

        long digits = Long.parseLong(number.substring(2));
        assertEquals(1, digits % 97);
    }
}
