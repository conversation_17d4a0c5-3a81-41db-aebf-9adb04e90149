package de.adesso.fischereiregister.registerservice.jurisdiction;

import de.adesso.fischereiregister.RegisterServiceApplication;
import de.adesso.fischereiregister.registerservice.common.TestDataUtil;
import de.adesso.fischereiregister.registerservice.common.security.mocking.WithMockSecurityContext;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.openapitools.model.FederalStateAbbreviation;
import org.openapitools.model.JurisdictionConsentInfo;
import org.openapitools.model.PaymentType;
import org.openapitools.model.Tax;
import org.openapitools.model.UpdateJurisdictionRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static de.adesso.fischereiregister.registerservice.common.JsonParser.asJsonString;
import static org.hamcrest.Matchers.containsString;
import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.notNullValue;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK, classes = RegisterServiceApplication.class)
@AutoConfigureMockMvc(addFilters = false)
@WithMockSecurityContext( federalState = "NW")
public class MoveJurisdictionBerlinIntegrationTest {

    @Autowired
    private MockMvc mvc;

    private UpdateJurisdictionRequest request;

    @BeforeEach
    void setUp() {
        request = new UpdateJurisdictionRequest();
        JurisdictionConsentInfo consentInfo = new JurisdictionConsentInfo(true, true, true, true);
        request.setConsentInfo(consentInfo);
        Tax tax = TestDataUtil.createTaxesApi().get(0);
        tax.federalState(FederalStateAbbreviation.NW);
        tax.getPaymentInfo().setAmount(BigDecimal.valueOf(22.0));
        tax.getPaymentInfo().setType(PaymentType.CARD);
        List<Tax> taxes = List.of(tax);
        request.setTaxes(taxes);
    }

    @Test
    @DisplayName("""
            POST /api/register-entries/{registerEntryId}/jurisdiction:move
            Verify that the move_authority endpoint returns a 200 OK status code.
            """)
    void moveAuthorityTestBE() throws Exception {
        mvc.perform(MockMvcRequestBuilders.post("http://localhost:8080/register-entries/1917c468-35eb-423e-b758-25990506b4f9/jurisdiction:move")
                        .content(asJsonString(request))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON)
                )
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.documents", hasSize(request.getTaxes().size()))) //taxes =1
                .andExpect(jsonPath("$.jurisdiction.federalState", Matchers.containsString("NW")))
                .andExpect(jsonPath("$.registerEntryId", notNullValue()));
    }

    @Test
    @DisplayName("""
            POST /api/register-entries/{registerEntryId}/jurisdiction:move
            Verify that the move_authority endpoint returns a 404 NOT FOUND status code if user does not exist.
            """)
    void moveAuthorityNotExists() throws Exception {
        mvc.perform(MockMvcRequestBuilders.post("http://localhost:8080/register-entries/00000000-0000-0000-0000-000000000000/jurisdiction:move")
                .content(asJsonString(request)).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)).andExpect(status().isNotFound());
    }

    @Test
    @DisplayName("""
            PUT /api/register-entries/{registerEntryId}/jurisdiction:move
            Verify that the move_authority endpoint returns a 200 OK status code when called with optional parameters as null
            """)
    void moveAuthorityTestWhenOptionalParametersAreNull() throws Exception {
        request.setTaxes(new ArrayList<>());

        mvc.perform(MockMvcRequestBuilders.post("http://localhost:8080/register-entries/351031b4-d8ca-4380-a49f-47159ac68b8b/jurisdiction:move")
                .content(asJsonString(request)).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().string(containsString("\"federalState\":\"NW\"")));
    }
}
