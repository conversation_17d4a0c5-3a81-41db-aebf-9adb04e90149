package de.adesso.fischereiregister.registerservice.online_services.message;

import de.adesso.fischereiregister.core.model.Birthdate;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.registerservice.online_services.message.enums.OSMessageTemplate;
import de.adesso.fischereiregister.registerservice.tenant.TenantConfigurationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class OSMessageTemplateResolutionServiceImplTest {

    @Mock
    private TenantConfigurationService tenantConfigurationService;

    @InjectMocks
    private OSMessageTemplateResolutionServiceImpl osMessageTemplateResolutionService;

    private FederalState federalState;
    private Person person;

    private static final String DEFAULT_CITIZEN_NAME_KEY = "os_message_template.default.citizen_name";
    private static final OSMessageTemplate OS_MESSAGE_TEMPLATE = OSMessageTemplate.REGULAR_FISHING_LICENSE_CREATED_OS;
    private static final String SUBJECT_KEY = OS_MESSAGE_TEMPLATE.getSubjectKey();
    private static final String TEXT_KEY = OS_MESSAGE_TEMPLATE.getTextKey();
    private static final String DISPLAY_NAME_KEY = OS_MESSAGE_TEMPLATE.getDisplayNameKey();
    private static final String SUBJECT_TEMPLATE = "Online Service Test Subject";
    private static final String TEXT_TEMPLATE = "Hello {{citizenFullname}}, this is an online service test <br> with line breaks.";
    private static final String DISPLAY_NAME_TEMPLATE = "DigiFischDok Fachverfahren und Register";
    private static final String EXPECTED_TEXT_WITH_NAME = "Hello Dr. Max Mustermann, this is an online service test \n with line breaks.";

    @BeforeEach
    void setUp() {
        federalState = FederalState.SH;

        person = new Person();
        person.setTitle("Dr.");
        person.setFirstname("Max");
        person.setLastname("Mustermann");
        person.setBirthdate(new Birthdate(1990, 1, 1));
    }

    @Test
    @DisplayName("OSMessageTemplateResolutionServiceImpl.getSubject should return the subject with HTML line breaks replaced")
    void getSubject_ShouldReturnSubjectWithLineBreaksReplaced() {
        // Given
        when(tenantConfigurationService.getValue(federalState, SUBJECT_KEY)).thenReturn(SUBJECT_TEMPLATE);

        // When
        String result = osMessageTemplateResolutionService.getSubject(federalState, OS_MESSAGE_TEMPLATE);

        // Then
        assertEquals(SUBJECT_TEMPLATE, result);
        verify(tenantConfigurationService).getValue(federalState, SUBJECT_KEY);
    }

    @Test
    @DisplayName("OSMessageTemplateResolutionServiceImpl.getText should replace citizen name token and HTML line breaks when person is provided")
    void getText_WithPerson_ShouldReplaceNameTokenAndLineBreaks() {
        // Given
        when(tenantConfigurationService.getValue(federalState, TEXT_KEY)).thenReturn(TEXT_TEMPLATE);

        // When
        String result = osMessageTemplateResolutionService.getText(federalState, OS_MESSAGE_TEMPLATE, person);

        // Then
        assertEquals(EXPECTED_TEXT_WITH_NAME, result);
        verify(tenantConfigurationService).getValue(federalState, TEXT_KEY);
    }

    @Test
    @DisplayName("OSMessageTemplateResolutionServiceImpl.getText should use default name and replace HTML line breaks when person is null")
    void getText_WithNullPerson_ShouldUseDefaultNameAndReplaceLineBreaks() {
        // Given
        String defaultName = "Default Citizen";
        String expectedText = "Hello " + defaultName + ", this is an online service test \n with line breaks.";
        when(tenantConfigurationService.getValue(federalState, DEFAULT_CITIZEN_NAME_KEY)).thenReturn(defaultName);
        when(tenantConfigurationService.getValue(federalState, TEXT_KEY)).thenReturn(TEXT_TEMPLATE);

        // When
        String result = osMessageTemplateResolutionService.getText(federalState, OS_MESSAGE_TEMPLATE, null);

        // Then
        assertEquals(expectedText, result);
        verify(tenantConfigurationService).getValue(federalState, TEXT_KEY);
        verify(tenantConfigurationService).getValue(federalState, DEFAULT_CITIZEN_NAME_KEY);
    }

    @Test
    @DisplayName("OSMessageTemplateResolutionServiceImpl.getDisplayName should return the display name with HTML line breaks replaced")
    void getDisplayName_ShouldReturnDisplayNameWithLineBreaksReplaced() {
        // Given
        when(tenantConfigurationService.getValue(federalState, DISPLAY_NAME_KEY)).thenReturn(DISPLAY_NAME_TEMPLATE);

        // When
        String result = osMessageTemplateResolutionService.getDisplayName(federalState, OS_MESSAGE_TEMPLATE);

        // Then
        assertEquals(DISPLAY_NAME_TEMPLATE, result);
        verify(tenantConfigurationService).getValue(federalState, DISPLAY_NAME_KEY);
    }

    @Test
    @DisplayName("OSMessageTemplateResolutionServiceImpl.getText should handle text without HTML line breaks")
    void getText_WithoutLineBreaks_ShouldNotChangeText() {
        // Given
        String textWithoutLineBreaks = "Hello {{citizenFullname}}, this is a test without line breaks.";
        String expectedText = "Hello Dr. Max Mustermann, this is a test without line breaks.";
        when(tenantConfigurationService.getValue(federalState, TEXT_KEY)).thenReturn(textWithoutLineBreaks);

        // When
        String result = osMessageTemplateResolutionService.getText(federalState, OS_MESSAGE_TEMPLATE, person);

        // Then
        assertEquals(expectedText, result);
        verify(tenantConfigurationService).getValue(federalState, TEXT_KEY);
    }

    @Test
    @DisplayName("OSMessageTemplateResolutionServiceImpl.getText should handle text with multiple HTML line breaks")
    void getText_WithMultipleLineBreaks_ShouldReplaceAllLineBreaks() {
        // Given
        String textWithMultipleLineBreaks = "Hello {{citizenFullname}},<br>this is a test<br>with multiple<br>line breaks.";
        String expectedText = "Hello Dr. Max Mustermann,\nthis is a test\nwith multiple\nline breaks.";
        when(tenantConfigurationService.getValue(federalState, TEXT_KEY)).thenReturn(textWithMultipleLineBreaks);

        // When
        String result = osMessageTemplateResolutionService.getText(federalState, OS_MESSAGE_TEMPLATE, person);

        // Then
        assertEquals(expectedText, result);
        verify(tenantConfigurationService).getValue(federalState, TEXT_KEY);
    }
}
