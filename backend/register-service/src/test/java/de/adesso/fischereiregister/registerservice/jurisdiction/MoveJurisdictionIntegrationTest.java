package de.adesso.fischereiregister.registerservice.jurisdiction;

import de.adesso.fischereiregister.RegisterServiceApplication;
import de.adesso.fischereiregister.registerservice.common.security.mocking.WithMockSecurityContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.openapitools.model.FederalStateAbbreviation;
import org.openapitools.model.JurisdictionConsentInfo;
import org.openapitools.model.PaymentInfo;
import org.openapitools.model.PaymentType;
import org.openapitools.model.Tax;
import org.openapitools.model.UpdateJurisdictionRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.math.BigDecimal;
import java.util.List;

import static de.adesso.fischereiregister.registerservice.common.JsonParser.asJsonString;
import static org.hamcrest.Matchers.containsString;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK, classes = RegisterServiceApplication.class)
@AutoConfigureMockMvc(addFilters = false)
@WithMockSecurityContext()
public class MoveJurisdictionIntegrationTest {

    @Autowired
    private MockMvc mvc;

    private UpdateJurisdictionRequest request;

    @BeforeEach
    void setUp() {
        request = new UpdateJurisdictionRequest();
        JurisdictionConsentInfo consentInfo = new JurisdictionConsentInfo(true, true, true, true);
        request.setConsentInfo(consentInfo);
        Tax tax = new Tax(FederalStateAbbreviation.SH, "2022-02-02", new PaymentInfo(BigDecimal.TEN, PaymentType.CARD));
        List<Tax> taxes = List.of(tax);
        request.setTaxes(taxes);
    }

    @Test
    @DisplayName("""
            PUT /api/register-entries/{registerEntryId}/jurisdiction:move
            Verify that the move_authority endpoint returns a 401 UNAUTHORIZED status code when trying to file a tax for a different federal state
            """)
    void moveAuthorityTest_Different_Authority_Tax() throws Exception {
        request.getTaxes().getFirst().setFederalState(FederalStateAbbreviation.BE);
        mvc.perform(MockMvcRequestBuilders.post("http://localhost:8080/register-entries/e4b6af13-1feb-4a4d-9c46-76298a0611cf/jurisdiction:move")
                        .content(asJsonString(request))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON)
                )
                .andExpect(status().isBadRequest());
    }

    @Test
    @DisplayName("""
            POST /api/register-entries/{registerEntryId}/jurisdiction:move
            Verify that the move_authority endpoint returns a 400 BAD REQUEST status code on already assigned federal state.
            """)
    void moveAuthorityTest_AlreadyAssigned() throws Exception {
        mvc.perform(MockMvcRequestBuilders.post("http://localhost:8080/register-entries/e4b6af13-1feb-4a4d-9c46-76298a0611cf/jurisdiction:move")
                        .content(asJsonString(request))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON)
                )
                .andExpect(status().isBadRequest())
                .andExpect(content().string(containsString("The citizen is already assigned to SH")));
    }
}
