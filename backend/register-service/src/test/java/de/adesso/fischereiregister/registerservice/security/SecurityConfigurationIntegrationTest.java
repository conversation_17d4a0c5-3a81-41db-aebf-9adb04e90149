package de.adesso.fischereiregister.registerservice.security;

import de.adesso.fischereiregister.RegisterServiceApplication;
import de.adesso.fischereiregister.registerservice.common.security.mocking.SecurityContextConstants;
import de.adesso.fischereiregister.registerservice.common.security.mocking.WithMockSecurityContext;
import de.adesso.fischereiregister.utils.DateUtils;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.openapitools.model.PermanentBan;
import org.openapitools.model.PermanentBanRequest;
import org.openapitools.model.TemporaryBan;
import org.openapitools.model.TemporaryBanRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.http.MediaType;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.time.LocalDate;

import static de.adesso.fischereiregister.registerservice.common.JsonParser.asJsonString;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(webEnvironment = WebEnvironment.MOCK, classes = RegisterServiceApplication.class)
@AutoConfigureMockMvc
@WithMockSecurityContext(federalState = "NW")
@DisplayName("Test Security Configuration")
public class SecurityConfigurationIntegrationTest {

    private static final String ROLE_REALM_INSPECTOR = "ROLE_realm_INSPECTOR";
    private static final String ROLE_REALM_OFFICIAL = "ROLE_realm_OFFICIAL";
    private static final String ROLE_REALM_BAN_MANAGER = "ROLE_realm_BAN_MANAGER";
    private static final String REGISTER_ENTRY_SEARCH_REST_URL = "/register-entries/search";

    @Autowired
    MockMvc mvc;

    @Test
    @DisplayName("""
                GET /api/actuator/health/liveness.
                Verify that the liveness endpoint returns a 200 OK status code without authentication.
            """)
    void testWhenGetLivenessThenOk() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/actuator/health/liveness"))
                .andExpect(status().isOk());
    }

    @Test
    @DisplayName("""
                GET /api/actuator/health/readiness.
                Verify that the readiness endpoint returns a 200 OK status code without authentication.
            """)
    void testWhenGetReadinessThenOk() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/actuator/health/readiness"))
                .andExpect(status().isOk());
    }

    @Test
    @DisplayName("""
                GET /api/register-entries.
                Verify that the register entry search endpoint returns a Forbidden Error without authentication.
            """)
    void testRegisterEntrySearchViewForbidden() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get(REGISTER_ENTRY_SEARCH_REST_URL))
                .andExpect(status().isUnauthorized());
    }

    @Test
    @DisplayName("""
                GET /api/digitize-fishing-license.
                Verify that the digitize-fishing-license endpoint returns a Unauthorized without authentication.
            """)
    void testDigitizeFishingLicenseThenUnauthorized() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/digitize-fishing-license"))
                .andExpect(status().isUnauthorized());
    }

    @Test
    @DisplayName("""
                GET /api/register-entries/search.
                Verify that the register-entries endpoint returns a 200 OK status code with authorized role.
            """)
    void testRegisterEntrySearchViewStatusOk() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get(REGISTER_ENTRY_SEARCH_REST_URL).param("search", "Max")
                        .with(SecurityMockMvcRequestPostProcessors.jwt()
                                .authorities(new SimpleGrantedAuthority(ROLE_REALM_OFFICIAL))))
                .andExpect(status().isOk());
    }

    @Test
    @DisplayName("""
                GET /api/register-entries/search.
                Verify that the register-entries endpoint returns a 403 Forbidden status for unauthorized role.
            """)
    void testRegisterEntrySearchViewStatusForbiddenWrongRole() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get(REGISTER_ENTRY_SEARCH_REST_URL)
                        .with(SecurityMockMvcRequestPostProcessors.jwt()
                                .authorities(new SimpleGrantedAuthority(ROLE_REALM_INSPECTOR))))
                .andExpect(status().is4xxClientError());
    }

    @Test
    @DisplayName("""
                DELETE /api/register-entries/{registerEntryId}/ban.
                Verify that the delete ban endpoint returns a 403 Forbidden status for unauthorized role.
            """)
    void testBanDeleteStatusForbiddenWithFederalStateButWrongRole() throws Exception {
        mvc.perform(MockMvcRequestBuilders.delete("/register-entries/e4b6af13-1feb-4a4d-9c46-76298a0611cf/ban")
                        .with(SecurityMockMvcRequestPostProcessors.jwt()
                                .authorities(new SimpleGrantedAuthority(ROLE_REALM_OFFICIAL))
                                .jwt(jwt -> jwt.claim("federalState", "SH"))))
                .andExpect(status().isForbidden());
    }

    @Test
    @DisplayName("""
                DELETE /api/register-entries/{registerEntryId}/ban.
                Verify that the delete ban endpoint returns a 200 Ok status for authorized role with federalState attribute.
            """)
    void testBanDeleteStatusOkWithFederalStateAndCorrectRole() throws Exception {
        mvc.perform(MockMvcRequestBuilders.delete("/register-entries/e4b6af13-1feb-4a4d-9c46-76298a0611cf/ban")
                        .with(SecurityMockMvcRequestPostProcessors.jwt()
                                .authorities(new SimpleGrantedAuthority(ROLE_REALM_BAN_MANAGER))
                                .jwt(jwt -> jwt.claim("federalState", "SH"))))
                .andExpect(status().isOk());
    }

    @Test
    @DisplayName("""
                POST /api/register-entries/{registerEntryId}/ban/temporary.
                Verify that the temporary ban endpoint returns a 403 Forbidden status for unauthorized role.
            """)
    void testBanTemporaryStatusForbiddenWithFederalStateButWrongRole() throws Exception {

        mvc.perform(MockMvcRequestBuilders.post("/register-entries/351031b4-d8ca-4380-a49f-47159ac68b8b/ban/temporary")
                        .contentType(MediaType.APPLICATION_JSON).content(asJsonString(createTemporaryBanRequest()))
                        .with(SecurityMockMvcRequestPostProcessors.jwt()
                                .authorities(new SimpleGrantedAuthority(ROLE_REALM_OFFICIAL))
                                .jwt(jwt -> jwt.claim("federalState", "SH"))))
                .andExpect(status().isForbidden());
    }

    @Test
    @DisplayName("""
                POST /api/register-entries/{registerEntryId}/ban/temporary.
                Verify that the temporary ban endpoint returns a 201 Created status for authorized role with federalState attribute.
            """)
    void testBanTemporaryStatusCreatedWithFederalStateAndCorrectRole() throws Exception {
        mvc.perform(MockMvcRequestBuilders.post("/register-entries/351031b4-d8ca-4380-a49f-47159ac68b8b/ban/temporary")
                        .contentType(MediaType.APPLICATION_JSON).content(asJsonString(createTemporaryBanRequest()))
                        .with(SecurityMockMvcRequestPostProcessors.jwt()
                                .authorities(new SimpleGrantedAuthority(ROLE_REALM_BAN_MANAGER))
                                .jwt(jwt -> jwt.claims(claims -> {
                                    claims.put("federalState", "SH");
                                    claims.put("given_name", "Max");
                                    claims.put("name", "Mustermann");
                                    claims.put("officeAddress", SecurityContextConstants.OFFICE_ADDRESS_CLAIM_VALUES);
                                }))
                        ))
                .andExpect(status().isCreated());
    }

    @Test
    @DisplayName("""
                POST /api/register-entries/{registerEntryId}/ban/permanent.
                Verify that the permanent ban endpoint returns a 403 Forbidden status for unauthorized role.
            """)
    void testBanPermanentStatusForbiddenWithFederalStateButWrongRole() throws Exception {
        mvc.perform(MockMvcRequestBuilders.post("/register-entries/351031b4-d8ca-4380-a49f-47159ac68b8b/ban/permanent")
                        .contentType(MediaType.APPLICATION_JSON).content(asJsonString(createPermanentBanRequest()))
                        .with(SecurityMockMvcRequestPostProcessors.jwt()
                                .authorities(new SimpleGrantedAuthority(ROLE_REALM_OFFICIAL))
                                .jwt(jwt -> jwt.claim("federalState", "SH"))))
                .andExpect(status().isForbidden());
    }

    @Test
    @DisplayName("""
                POST /api/register-entries/{registerEntryId}/ban/permanent.
                Verify that the permanent ban endpoint returns a 201 Created status for authorized role with federalState attribute.
            """)
    void testBanPermanentStatusCreatedWithFederalStateAndCorrectRole() throws Exception {
        mvc.perform(MockMvcRequestBuilders.post("/register-entries/351031b4-d8ca-4380-a49f-47159ac68b8b/ban/permanent")
                        .contentType(MediaType.APPLICATION_JSON).content(asJsonString(createPermanentBanRequest()))
                        .with(SecurityMockMvcRequestPostProcessors.jwt()
                                .authorities(new SimpleGrantedAuthority(ROLE_REALM_BAN_MANAGER))
                                .jwt(jwt -> jwt.claims(claims -> {
                                    claims.put("federalState", "SH");
                                    claims.put("given_name", "Max");
                                    claims.put("name", "Mustermann");
                                    claims.put("officeAddress", SecurityContextConstants.OFFICE_ADDRESS_CLAIM_VALUES);
                                }))
                        ))
                .andExpect(status().isCreated());
    }

    private TemporaryBanRequest createTemporaryBanRequest() {
        final TemporaryBan ban = new TemporaryBan();
        ban.setFileNumber("fileNumber");
        ban.setReportedBy("reporter");
        ban.setFrom(LocalDate.of(1990, 1, 1).format(DateUtils.GERMAN_DATE_TIME_FORMATTER));
        ban.setTo(LocalDate.of(2100, 1, 1).format(DateUtils.GERMAN_DATE_TIME_FORMATTER));
        final TemporaryBanRequest banRequest = new TemporaryBanRequest();
        banRequest.setBan(ban);
        return banRequest;
    }

    private PermanentBanRequest createPermanentBanRequest() {
        final PermanentBan ban = new PermanentBan();
        ban.setFileNumber("fileNumber");
        ban.setReportedBy("reporter");
        ban.setFrom(LocalDate.of(1990, 1, 1).format(DateUtils.GERMAN_DATE_TIME_FORMATTER));
        final PermanentBanRequest banRequest = new PermanentBanRequest();
        banRequest.setBan(ban);
        return banRequest;
    }
}
