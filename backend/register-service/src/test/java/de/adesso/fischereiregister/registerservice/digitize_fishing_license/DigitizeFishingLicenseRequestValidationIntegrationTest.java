package de.adesso.fischereiregister.registerservice.digitize_fishing_license;

import de.adesso.fischereiregister.RegisterServiceApplication;
import de.adesso.fischereiregister.registerservice.common.security.mocking.WithMockSecurityContext;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.openapitools.model.DigitizeFishingLicenseRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;

import static de.adesso.fischereiregister.registerservice.common.JsonParser.asJsonString;
import static org.hamcrest.Matchers.hasSize;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.header;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;


@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK, classes = RegisterServiceApplication.class)
@AutoConfigureMockMvc(addFilters = false)
@WithMockSecurityContext()
public class DigitizeFishingLicenseRequestValidationIntegrationTest {

    private static final String DIGITIZE_FISHING_LICENSE_URL = "/register-entries/fishing-licenses:digitize";

    @Autowired
    private MockMvc mockMvc;

    @Test
    @DisplayName("""
                POST /api/digitize-fishing-license.
                Verify that request is successful if all data is provided.
            """)
    void validationOKTest() throws Exception {

        final DigitizeFishingLicenseRequest request = DigitizeRequestFactory.validDigitizeDto();
        final String requestJSON = asJsonString(request);

        final ResultActions result = mockMvc.perform(post(DIGITIZE_FISHING_LICENSE_URL)
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJSON));

        result.andExpect(status().isCreated())
                .andExpect(header().string("Location", Matchers.matchesPattern("/api/register-entries/[0-9a-fA-F\\-]{36}")))
                .andExpect(jsonPath("$.documents", hasSize(request.getTaxes().size() + request.getPayedTaxes().size() + 2))); //taxes plus we expect CARD AND PDF License
    }
}
