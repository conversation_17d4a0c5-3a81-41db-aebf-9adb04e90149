package de.adesso.fischereiregister.registerservice.pay_fishing_tax;

import de.adesso.fischereiregister.RegisterServiceApplication;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import de.adesso.fischereiregister.registerservice.common.JsonParser;
import de.adesso.fischereiregister.registerservice.common.TestDataUtil;
import de.adesso.fischereiregister.registerservice.common.security.mocking.WithMockSecurityContext;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.openapitools.model.ExtendTaxesRequest;
import org.openapitools.model.FederalStateAbbreviation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.util.List;

import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.notNullValue;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK, classes = RegisterServiceApplication.class)
@AutoConfigureMockMvc(addFilters = false)
@WithMockSecurityContext()
public class PayFishingTaxIntegrationTest {

    @Autowired
    private MockMvc mvc;

    @Test
    @DisplayName("""
            	POST /register-entries/{registerEntryId}/taxes with valid data
            """)
    void callPayFishingTaxSuccess() throws Exception {
        final String postURL = "http://localhost:8080/register-entries/1917c468-35eb-423e-b758-25990506b4f9/taxes";
        final ExtendTaxesRequest extendTaxesRequest = validRequestDto();
        final ResultActions result = mvc.perform(MockMvcRequestBuilders
                .post(postURL)
                .content(JsonParser.asJsonString(extendTaxesRequest))
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON));

        result.andExpect(status().isCreated());
        result.andExpect(jsonPath("$.documents", hasSize(extendTaxesRequest.getTaxes().size())));
        result.andExpect(jsonPath("$.registerEntryId", notNullValue()));

    }

    @Test
    @DisplayName("""
            	POST /register-entries/{registerEntryId}/taxes with unallowed Tax Data
            """)
    void callPayFishingTaxWrongFederalState() throws Exception {
        final String postURL = "http://localhost:8080/register-entries/1917c468-35eb-423e-b758-25990506b4f9/taxes";
        final ExtendTaxesRequest extendTaxesRequest = validRequestDto();
        extendTaxesRequest.getTaxes().getFirst().setFederalState(FederalStateAbbreviation.BE); //User is set up to be in Schlewsig Holstein (SH), and trying to pay a tax for Berlin (BE)
        final ResultActions result = mvc.perform(MockMvcRequestBuilders
                .post(postURL)
                .content(JsonParser.asJsonString(extendTaxesRequest))
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON));

        result.andExpect(status().isBadRequest());
    }

    @Test
    @DisplayName("""
            	POST /register-entries/{registerEntryId}/taxes with incomplete data and client error message
            """)
    void callPayFishingTaxError() throws Exception {
        final ExtendTaxesRequest taxExtendRequest = validRequestDto();
        final String postURL = "http://localhost:8080/register-entries/" + DomainTestData.registerId + "/taxes";
        taxExtendRequest.getConsentInfo().setGdprAccepted(false);
        mvc.perform(MockMvcRequestBuilders.post(postURL)
                .content(JsonParser.asJsonString(taxExtendRequest))
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)).andExpect(status().is4xxClientError());
    }

    private ExtendTaxesRequest validRequestDto() {
        final ExtendTaxesRequest taxExtendRequest = new ExtendTaxesRequest();

        final org.openapitools.model.Person person = TestDataUtil.createPersonApi();
        final org.openapitools.model.TaxConsentInfo consentInfo = TestDataUtil.createTaxConsentInfoApi();
        final List<org.openapitools.model.Tax> taxes = TestDataUtil.createTaxesApi();

        taxExtendRequest.setPerson(person);
        taxExtendRequest.setConsentInfo(consentInfo);
        taxExtendRequest.setTaxes(taxes);

        return taxExtendRequest;
    }
}
