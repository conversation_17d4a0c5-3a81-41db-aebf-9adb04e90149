package de.adesso.fischereiregister.registerservice.drools.dmn.adapter;

import de.adesso.fischereiregister.core.exceptions.RulesProcessingException;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.registerservice.drools.dmn.DmnDroolsService;
import de.adesso.fischereiregister.registerservice.drools.dmn.license.configuration.input.DmnLicenseType;
import de.adesso.fischereiregister.registerservice.drools.dmn.license.configuration.output.LicenseInformationOutput;
import de.adesso.fischereiregister.registerservice.drools.dmn.model.DmnProcessingType;
import de.adesso.fischereiregister.registerservice.drools.dmn.tax.configuration.TaxInformationOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class TenantRulesConfigurationAdapterTest {

    @Mock
    private DmnDroolsService dmnDroolsService;

    @InjectMocks
    private TenantRulesConfigurationAdapter tenantRulesConfigurationAdapter;

    private Tax tax;


    @BeforeEach
    void setUp() {
        tax = new Tax();
        tax.setValidFrom(LocalDate.of(2022, 1, 1));
        tax.setValidTo(LocalDate.of(2025, 1, 1));
    }

    @Test
    @DisplayName("Tests the TenantRulesConfigurationAdapter to ensure all license types will be properly evaluated.")
    void testGetLicenseFeeInformation() throws RulesProcessingException {
        FederalState federalState = FederalState.SH;
        LicenseInformationOutput mockResult1 = mock(LicenseInformationOutput.class);
        LicenseInformationOutput mockResult2 = mock(LicenseInformationOutput.class);
        LicenseInformationOutput mockResult3 = mock(LicenseInformationOutput.class);

        when(dmnDroolsService.getLicenseInformation(federalState, DmnLicenseType.VACATION, DmnProcessingType.ANALOG))
                .thenReturn(List.of(mockResult1));
        when(dmnDroolsService.getLicenseInformation(federalState, DmnLicenseType.REGULAR, DmnProcessingType.ANALOG))
                .thenReturn(List.of(mockResult2));
        when(dmnDroolsService.getLicenseInformation(federalState, DmnLicenseType.LIMITED, DmnProcessingType.ANALOG))
                .thenReturn(List.of(mockResult3));

        List<LicenseInformationOutput> results = tenantRulesConfigurationAdapter.getLicenseFeeInformation(federalState);

        assertNotNull(results);
        assertEquals(3, results.size());
        assertTrue(results.contains(mockResult1));
        assertTrue(results.contains(mockResult2));
        assertTrue(results.contains(mockResult3));
    }

    @Test
    void shouldReturnTaxInformationList_WhenFederalStateIsValid() throws RulesProcessingException {
        // Given
        Map<String, Object> taxData = new HashMap<>();
        taxData.put(TaxInformationOutput.NETTOBETRAG_ABGABE, new BigDecimal("100.50"));
        taxData.put(TaxInformationOutput.VERWALTUNGSGEBUEHR, new BigDecimal("20.75"));
        taxData.put(TaxInformationOutput.GEBUEHR_BEHOERDENGANG, new BigDecimal("15.25"));
        taxData.put(TaxInformationOutput.JAHRE, new BigDecimal("5"));
        List<TaxInformationOutput> expectedResults = List.of(new TaxInformationOutput(taxData));
        when(dmnDroolsService.getTaxInformation(any(), eq(true))).thenReturn(expectedResults);

        // When
        List<TaxInformationOutput> actualResults = tenantRulesConfigurationAdapter.getTaxPriceInformation(FederalState.BE, true);

        // Then
        assertThat(actualResults).isNotNull().isNotEmpty().isEqualTo(expectedResults);
        verify(dmnDroolsService, times(1)).getTaxInformation(FederalState.BE, true);
    }
}
