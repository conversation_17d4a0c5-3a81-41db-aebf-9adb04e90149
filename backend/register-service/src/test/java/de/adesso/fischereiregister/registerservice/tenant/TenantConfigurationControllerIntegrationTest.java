package de.adesso.fischereiregister.registerservice.tenant;

import de.adesso.fischereiregister.RegisterServiceApplication;
import de.adesso.fischereiregister.registerservice.common.security.mocking.WithMockSecurityContext;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK, classes = RegisterServiceApplication.class)
@AutoConfigureMockMvc(addFilters = false)
@WithMockSecurityContext()
@DisplayName("Test for all Tenant Configuration from the DMN rules tables which can be requested by the client through a rest interface.")
public class TenantConfigurationControllerIntegrationTest {

    private static final String LICENSE_INFORMATION_ROUTE = "http://localhost:8080/tenant/license-information";
    private static final String TAX_INFORMATION_ROUTE = "http://localhost:8080/tenant/tax-information";

    @Autowired
    private MockMvc mvc;

    @Test
    @DisplayName("""
            	GET /api/tenant/license-information
            	Verify that the get license information endpoint returns a 200 OK status code and also checks the response object is valid
            """)
    void testGetLicenseInformation() throws Exception {


        mvc.perform(MockMvcRequestBuilders.get(LICENSE_INFORMATION_ROUTE)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[0].licenseType", Matchers.containsString("VACATION")));
    }

    @Test
    @DisplayName("""
            	GET /api/tenant/tax-information
            	Verify that the get tax information endpoint returns a 200 OK status code and also checks the response object is valid
            """)
    void testGetTaxInformation() throws Exception {


        mvc.perform(MockMvcRequestBuilders.get(TAX_INFORMATION_ROUTE)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[0].duration", Matchers.equalTo(Integer.valueOf(1))))
                .andExpect(jsonPath("$[0].price", Matchers.equalTo(Integer.valueOf(25))));
    }

}