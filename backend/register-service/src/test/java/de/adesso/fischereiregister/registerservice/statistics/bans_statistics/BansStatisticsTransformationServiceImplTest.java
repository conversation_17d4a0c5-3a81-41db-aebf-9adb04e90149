package de.adesso.fischereiregister.registerservice.statistics.bans_statistics;

import de.adesso.fischereiregister.view.bans_statistics.persistance.BansStatisticsView;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

class BansStatisticsTransformationServiceImplTest {

    private BansStatisticsTransformationServiceImpl transformationService;

    @BeforeEach
    void setUp() {
        transformationService = new BansStatisticsTransformationServiceImpl();
    }

    @Test
    @DisplayName("transformToBansStatistics should correctly group by year and sum issued and expired counts")
    void transformToBansStatistics_ShouldGroupByYearCorrectly() {
        // given
        BansStatisticsView entry1 = createStatisticsView(2023, "BY", 5, 2, 3);
        BansStatisticsView entry2 = createStatisticsView(2023, "BE", 3, 1, 2);
        BansStatisticsView entry3 = createStatisticsView(2024, "BY", 8, 3, 4);
        BansStatisticsView entry4 = createStatisticsView(2024, "BE", 4, 2, 1);

        List<BansStatisticsView> views = List.of(entry1, entry2, entry3, entry4);
        List<Integer> yearsToQuery = List.of(2023, 2024);

        // when
        List<BansStatistics> result = transformationService.transformToBansStatistics(views, yearsToQuery);

        // then
        assertThat(result).hasSize(2);

        // Check 2024 statistics (should be first due to descending order)
        BansStatistics stats2024 = result.get(0);
        assertThat(stats2024.year()).isEqualTo(2024);
        assertThat(stats2024.data().issued()).isEqualTo(12); // 8 + 4
        assertThat(stats2024.data().started()).isEqualTo(5); // 4 + 1
        assertThat(stats2024.data().expired()).isEqualTo(5); // 3 + 2

        // Check 2023 statistics
        BansStatistics stats2023 = result.get(1);
        assertThat(stats2023.year()).isEqualTo(2023);
        assertThat(stats2023.data().issued()).isEqualTo(8); // 5 + 3
        assertThat(stats2023.data().started()).isEqualTo(5); // 3 + 2
        assertThat(stats2023.data().expired()).isEqualTo(3); // 2 + 1
    }

    @Test
    @DisplayName("transformToBansStatistics should handle empty list")
    void transformToBansStatistics_ShouldHandleEmptyList() {
        // given
        List<BansStatisticsView> views = List.of();
        List<Integer> yearsToQuery = List.of(2023, 2024);

        // when
        List<BansStatistics> result = transformationService.transformToBansStatistics(views, yearsToQuery);

        // then
        assertThat(result).hasSize(2); // Should return entries for all requested years with zero values
        assertThat(result.get(0).data().issued()).isEqualTo(0);
        assertThat(result.get(0).data().expired()).isEqualTo(0);
        assertThat(result.get(0).data().started()).isEqualTo(0);
        assertThat(result.get(1).data().issued()).isEqualTo(0);
        assertThat(result.get(1).data().expired()).isEqualTo(0);
        assertThat(result.get(1).data().started()).isEqualTo(0);
    }

    @Test
    @DisplayName("transformToBansStatistics should handle single entry")
    void transformToBansStatistics_ShouldHandleSingleEntry() {
        // given
        BansStatisticsView entry = createStatisticsView(2023, "BY", 5, 2, 3);
        List<BansStatisticsView> views = List.of(entry);
        List<Integer> yearsToQuery = List.of(2023);

        // when
        List<BansStatistics> result = transformationService.transformToBansStatistics(views, yearsToQuery);

        // then
        assertThat(result).hasSize(1);
        assertThat(result.get(0).year()).isEqualTo(2023);
        assertThat(result.get(0).data().issued()).isEqualTo(5);
        assertThat(result.get(0).data().expired()).isEqualTo(2);
        assertThat(result.get(0).data().started()).isEqualTo(3);
    }

    @Test
    @DisplayName("transformToBansStatistics should handle empty years list")
    void transformToBansStatistics_ShouldHandleEmptyYearsList() {
        // given
        BansStatisticsView entry = createStatisticsView(2023, "BY", 5, 2, 3);
        List<BansStatisticsView> views = List.of(entry);
        List<Integer> yearsToQuery = List.of();

        // when
        List<BansStatistics> result = transformationService.transformToBansStatistics(views, yearsToQuery);

        // then
        assertThat(result).isEmpty();
    }

    @Test
    @DisplayName("transformToBansStatistics should fill missing years with zero values")
    void transformToBansStatistics_ShouldFillMissingYearsWithZeroValues() {
        // given
        BansStatisticsView entry = createStatisticsView(2023, "BY", 5, 2, 3);
        // Note: No data for 2024
        List<BansStatisticsView> views = List.of(entry);
        List<Integer> yearsToQuery = List.of(2023, 2024);

        // when
        List<BansStatistics> result = transformationService.transformToBansStatistics(views, yearsToQuery);

        // then
        assertThat(result).hasSize(2);

        // Check 2024 has zero values (should be first due to descending order)
        BansStatistics stats2024 = result.get(0);
        assertThat(stats2024.year()).isEqualTo(2024);
        assertThat(stats2024.data().issued()).isEqualTo(0);
        assertThat(stats2024.data().expired()).isEqualTo(0);
        assertThat(stats2024.data().started()).isEqualTo(0);

        // Check 2023 has data
        BansStatistics stats2023 = result.get(1);
        assertThat(stats2023.year()).isEqualTo(2023);
        assertThat(stats2023.data().issued()).isEqualTo(5);
        assertThat(stats2023.data().expired()).isEqualTo(2);
        assertThat(stats2023.data().started()).isEqualTo(3);
    }

    @Test
    @DisplayName("transformToBansStatistics should sort results by year in descending order")
    void transformToBansStatistics_ShouldSortByYearDescending() {
        // given
        BansStatisticsView entry1 = createStatisticsView(2022, "BY", 1, 1, 1);
        BansStatisticsView entry2 = createStatisticsView(2024, "BY", 3, 3, 3);
        BansStatisticsView entry3 = createStatisticsView(2023, "BY", 2, 2, 2);

        List<BansStatisticsView> views = List.of(entry1, entry2, entry3);
        List<Integer> yearsToQuery = List.of(2022, 2023, 2024);

        // when
        List<BansStatistics> result = transformationService.transformToBansStatistics(views, yearsToQuery);

        // then
        assertThat(result).hasSize(3);
        assertThat(result.get(0).year()).isEqualTo(2024); // First should be highest year
        assertThat(result.get(1).year()).isEqualTo(2023);
        assertThat(result.get(2).year()).isEqualTo(2022); // Last should be lowest year
    }

    private BansStatisticsView createStatisticsView(int year, String federalState, int issuedCount, int expiredCount, int startedCount) {
        BansStatisticsView view = new BansStatisticsView();
        view.setYear(year);
        view.setFederalState(federalState);
        view.setIssuedCount(issuedCount);
        view.setExpiredCount(expiredCount);
        view.setStartedCount(startedCount);
        return view;
    }
}
