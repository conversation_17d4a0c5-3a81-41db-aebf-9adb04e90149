package de.adesso.fischereiregister.registerservice.destatis_country.model;

import de.adesso.fischereiregister.registerservice.destatis_country.data.CountryDataRow;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

class CountryDataRowTest {

    @Test
    void testMatchesAdjectiveExactMatch() {
        CountryDataRow country = new CountryDataRow();
        country.setAdjective("German");

        assertTrue(country.matchesAdjective("German"), "Should return true for exact match.");
    }

    @Test
    void testMatchesAdjectiveCaseInsensitiveMatch() {
        CountryDataRow country = new CountryDataRow();
        country.setAdjective("German");

        assertTrue(country.matchesAdjective("german"), "Should return true for case-insensitive match.");
    }

    @Test
    void testMatchesAdjectiveDifferentValue() {
        CountryDataRow country = new CountryDataRow();
        country.setAdjective("German");

        assertFalse(country.matchesAdjective("French"), "Should return false for different value.");
    }

    @Test
    void testMatchesAdjectiveNullValue() {
        CountryDataRow country = new CountryDataRow();
        country.setAdjective("German");

        assertFalse(country.matchesAdjective(null), "Should return false for null input value.");
    }

    @Test
    void testMatchesAdjectiveNullAdjective() {
        CountryDataRow country = new CountryDataRow();
        country.setAdjective(null);

        assertFalse(country.matchesAdjective("German"), "Should return false if the object's adjective is null.");
    }

    @Test
    void testMatchesAdjectiveNullBoth() {
        CountryDataRow country = new CountryDataRow();
        country.setAdjective(null);

        assertFalse(country.matchesAdjective(null), "Should return false if both input value and adjective are null.");
    }
}
