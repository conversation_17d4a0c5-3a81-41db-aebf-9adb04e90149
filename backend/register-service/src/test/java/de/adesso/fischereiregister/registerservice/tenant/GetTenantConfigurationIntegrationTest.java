package de.adesso.fischereiregister.registerservice.tenant;

import de.adesso.fischereiregister.RegisterServiceApplication;
import de.adesso.fischereiregister.registerservice.common.security.mocking.WithMockSecurityContext;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK, classes = RegisterServiceApplication.class)
@AutoConfigureMockMvc(addFilters = false)
@WithMockSecurityContext()
@DisplayName("Test for specific tenant configuration loading from yaml Files")
public class GetTenantConfigurationIntegrationTest {

    private static final String TENANT_URL = "http://localhost:8080/tenant-configuration/load";

    private static final String FEDERAL_STATE = "federalState";

    @Mock
    SecurityContext securityContextMock;

    @Mock
    JwtAuthenticationToken authenticationMock;

    @Mock
    Jwt jwtMock;

    @Autowired
    private MockMvc mvc;

    @Test
    @DisplayName("Test load default file")
    void loadDefault() throws Exception {
        String expectedContent = "{\"login\":{\"button\":\"DefaultButtonValue\"}}";

        when(securityContextMock.getAuthentication()).thenReturn(authenticationMock);
        when(authenticationMock.getPrincipal()).thenReturn(jwtMock);
        when(jwtMock.getClaimAsString(eq(FEDERAL_STATE))).thenReturn("BY");

        SecurityContextHolder.setContext(securityContextMock);

        ResultActions result = mvc
                .perform(MockMvcRequestBuilders.get(TENANT_URL)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON));

        result.andExpect(status().isOk());
        result.andExpect(content().json(expectedContent));
    }

    @Test
    @DisplayName("Test load of tenant file")
    void loadSH() throws Exception {
        String expectedContent = "{\"login\":{\"button\":\"Schleswig-Holstein-ButtonValue\"}}";

        ResultActions result = mvc
                .perform(MockMvcRequestBuilders.get(TENANT_URL)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON));

        result.andExpect(status().isOk());
        result.andExpect(content().json(expectedContent));
    }

    @Test
    @DisplayName("Checks that the vacation button from the default file is added")
    void testCheckIfValueIsAddedToTennant() throws Exception {
        String expectedContent = "{"
                + "  \"home_page\": {\n"
                + "    \"add_section\": {\n"
                + "      \"vacation_button\": \"Urlauberfischereischein\"\n"
                + "    }\n"
                + "  }\n"
                + "}";

        ResultActions result = mvc
                .perform(MockMvcRequestBuilders.get(TENANT_URL)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON));

        result.andExpect(status().isOk());
        result.andExpect(content().json(expectedContent));
    }

    @Test
    @DisplayName("Checks that existing values in the tennant file are not overwritten.")
    void testTenantValueIsNotOverriden() throws Exception {
        String expectedContent = "{\n\"header\": {\n\"federal_state_logo\": {\n\"url\": \"https://upload.wikimedia.org/wikipedia/commons/thumb/e/e4/Schleswig-Holstein.svg/800px-Schleswig-Holstein.svg.png\"\n}}}";

        ResultActions result = mvc
                .perform(MockMvcRequestBuilders.get(TENANT_URL)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON));

        result.andExpect(status().isOk());
        result.andExpect(content().json(expectedContent));
    }

    @Test
    @DisplayName("Checks that a value which is only in the default tenant file is not deleted or modified.")
    void testTenantValueIsNotDeleted() throws Exception {
        String expectedContent = "{\"test_header\":{\"cancel\":\"aditional-button\"}}";

        ResultActions result = mvc
                .perform(MockMvcRequestBuilders.get(TENANT_URL)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON));

        result.andExpect(status().isOk());
        result.andExpect(content().json(expectedContent));
    }

    @Test
    @DisplayName("Checks that a value which is in a tree which exists in tenant file and also in the default file is not deleted or modified.")
    void testTenantValueInTreeIsNotDeleted() throws Exception {
        String expectedContent = "{\"header\":{\"user\":{\"test_not_deleted\":\"test_not_deleted\"}}}";

        ResultActions result = mvc
                .perform(MockMvcRequestBuilders.get(TENANT_URL)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON));

        result.andExpect(status().isOk());
        result.andExpect(content().json(expectedContent));
    }
}
