package de.adesso.fischereiregister.registerservice.statistics.inspections_statistics;

import de.adesso.fischereiregister.protocol.service.model.InspectionsStatisticsResult;
import de.adesso.fischereiregister.protocol.service.model.InspectionsStatisticsResultData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

class InspectionsStatisticsTransformationServiceImplTest {

    private InspectionsStatisticsTransformationServiceImpl transformationService;

    @BeforeEach
    void setUp() {
        transformationService = new InspectionsStatisticsTransformationServiceImpl();
    }

    @Test
    @DisplayName("InspectionsStatisticsTransformationServiceImpl.transformToInspectionsStatistics Should correctly transform data by year")
    void transformToInspectionsStatistics_ShouldTransformDataCorrectly() {
        // given
        InspectionsStatisticsResult result1 = createStatisticsResult(2023, 5, 12);
        InspectionsStatisticsResult result2 = createStatisticsResult(2024, 8, 20);

        List<InspectionsStatisticsResult> results = List.of(result1, result2);
        List<Integer> yearsToQuery = List.of(2023, 2024);

        // when
        List<InspectionsStatistics> transformedResults = transformationService.transformToInspectionsStatistics(results, yearsToQuery);

        // then
        assertThat(transformedResults).hasSize(2);

        // Check 2023 statistics
        InspectionsStatistics stats2023 = transformedResults.stream()
                .filter(s -> s.year() == 2023)
                .findFirst()
                .orElseThrow();
        assertThat(stats2023.year()).isEqualTo(2023);
        assertThat(stats2023.data().activeInspectors()).isEqualTo(5);
        assertThat(stats2023.data().numberOfInspections()).isEqualTo(12);

        // Check 2024 statistics
        InspectionsStatistics stats2024 = transformedResults.stream()
                .filter(s -> s.year() == 2024)
                .findFirst()
                .orElseThrow();
        assertThat(stats2024.year()).isEqualTo(2024);
        assertThat(stats2024.data().activeInspectors()).isEqualTo(8);
        assertThat(stats2024.data().numberOfInspections()).isEqualTo(20);
    }

    @Test
    @DisplayName("InspectionsStatisticsTransformationServiceImpl.transformToInspectionsStatistics Should handle empty list")
    void transformToInspectionsStatistics_ShouldHandleEmptyList() {
        // given
        List<InspectionsStatisticsResult> results = List.of();
        List<Integer> yearsToQuery = List.of(2023, 2024);

        // when
        List<InspectionsStatistics> transformedResults = transformationService.transformToInspectionsStatistics(results, yearsToQuery);

        // then
        assertThat(transformedResults).hasSize(2); // Should return entries for all requested years with zero values

        transformedResults.forEach(stats -> {
            assertThat(stats.data().activeInspectors()).isEqualTo(0);
            assertThat(stats.data().numberOfInspections()).isEqualTo(0);
        });
    }

    @Test
    @DisplayName("InspectionsStatisticsTransformationServiceImpl.transformToInspectionsStatistics Should handle single entry")
    void transformToInspectionsStatistics_ShouldHandleSingleEntry() {
        // given
        InspectionsStatisticsResult result = createStatisticsResult(2023, 3, 7);
        List<InspectionsStatisticsResult> results = List.of(result);
        List<Integer> yearsToQuery = List.of(2023);

        // when
        List<InspectionsStatistics> transformedResults = transformationService.transformToInspectionsStatistics(results, yearsToQuery);

        // then
        assertThat(transformedResults).hasSize(1);
        assertThat(transformedResults.get(0).year()).isEqualTo(2023);
        assertThat(transformedResults.get(0).data().activeInspectors()).isEqualTo(3);
        assertThat(transformedResults.get(0).data().numberOfInspections()).isEqualTo(7);
    }

    @Test
    @DisplayName("InspectionsStatisticsTransformationServiceImpl.transformToInspectionsStatistics Should handle empty years list")
    void transformToInspectionsStatistics_ShouldHandleEmptyYearsList() {
        // given
        InspectionsStatisticsResult result = createStatisticsResult(2023, 3, 7);
        List<InspectionsStatisticsResult> results = List.of(result);
        List<Integer> yearsToQuery = List.of();

        // when
        List<InspectionsStatistics> transformedResults = transformationService.transformToInspectionsStatistics(results, yearsToQuery);

        // then
        assertThat(transformedResults).isEmpty();
    }

    @Test
    @DisplayName("InspectionsStatisticsTransformationServiceImpl.transformToInspectionsStatistics Should fill missing years with zero values")
    void transformToInspectionsStatistics_ShouldFillMissingYearsWithZeroValues() {
        // given
        InspectionsStatisticsResult result = createStatisticsResult(2023, 3, 7);
        // Note: No data for 2024
        List<InspectionsStatisticsResult> results = List.of(result);
        List<Integer> yearsToQuery = List.of(2023, 2024);

        // when
        List<InspectionsStatistics> transformedResults = transformationService.transformToInspectionsStatistics(results, yearsToQuery);

        // then
        assertThat(transformedResults).hasSize(2);

        // Check 2023 has data
        InspectionsStatistics stats2023 = transformedResults.stream()
                .filter(s -> s.year() == 2023)
                .findFirst()
                .orElseThrow();
        assertThat(stats2023.data().activeInspectors()).isEqualTo(3);
        assertThat(stats2023.data().numberOfInspections()).isEqualTo(7);

        // Check 2024 has zero values
        InspectionsStatistics stats2024 = transformedResults.stream()
                .filter(s -> s.year() == 2024)
                .findFirst()
                .orElseThrow();
        assertThat(stats2024.data().activeInspectors()).isEqualTo(0);
        assertThat(stats2024.data().numberOfInspections()).isEqualTo(0);
    }

    @Test
    @DisplayName("InspectionsStatisticsTransformationServiceImpl.transformToInspectionsStatistics Should sort results by year in descending order")
    void transformToInspectionsStatistics_ShouldSortByYearDescending() {
        // given
        InspectionsStatisticsResult result1 = createStatisticsResult(2022, 1, 2);
        InspectionsStatisticsResult result2 = createStatisticsResult(2024, 3, 6);
        InspectionsStatisticsResult result3 = createStatisticsResult(2023, 2, 4);

        List<InspectionsStatisticsResult> results = List.of(result1, result2, result3);
        List<Integer> yearsToQuery = List.of(2022, 2023, 2024);

        // when
        List<InspectionsStatistics> transformedResults = transformationService.transformToInspectionsStatistics(results, yearsToQuery);

        // then
        assertThat(transformedResults).hasSize(3);
        assertThat(transformedResults.get(0).year()).isEqualTo(2024); // First should be highest year
        assertThat(transformedResults.get(1).year()).isEqualTo(2023);
        assertThat(transformedResults.get(2).year()).isEqualTo(2022); // Last should be lowest year
    }

    @Test
    @DisplayName("InspectionsStatisticsTransformationServiceImpl.transformToInspectionsStatistics Should handle null data gracefully")
    void transformToInspectionsStatistics_ShouldHandleNullDataGracefully() {
        // given
        InspectionsStatisticsResult resultWithNullData = new InspectionsStatisticsResult();
        resultWithNullData.setYear(2023);
        resultWithNullData.setData(null); // Null data

        InspectionsStatisticsResult resultWithNullValues = new InspectionsStatisticsResult();
        resultWithNullValues.setYear(2024);
        InspectionsStatisticsResultData dataWithNulls = new InspectionsStatisticsResultData();
        dataWithNulls.setActiveInspectors(null);
        dataWithNulls.setNumberOfInspections(null);
        resultWithNullValues.setData(dataWithNulls);

        List<InspectionsStatisticsResult> results = List.of(resultWithNullData, resultWithNullValues);
        List<Integer> yearsToQuery = List.of(2023, 2024);

        // when
        List<InspectionsStatistics> transformedResults = transformationService.transformToInspectionsStatistics(results, yearsToQuery);

        // then
        assertThat(transformedResults).hasSize(2);

        // Check 2023 with null data
        InspectionsStatistics stats2023 = transformedResults.stream()
                .filter(s -> s.year() == 2023)
                .findFirst()
                .orElseThrow();
        assertThat(stats2023.data().activeInspectors()).isEqualTo(0);
        assertThat(stats2023.data().numberOfInspections()).isEqualTo(0);

        // Check 2024 with null values
        InspectionsStatistics stats2024 = transformedResults.stream()
                .filter(s -> s.year() == 2024)
                .findFirst()
                .orElseThrow();
        assertThat(stats2024.data().activeInspectors()).isEqualTo(0);
        assertThat(stats2024.data().numberOfInspections()).isEqualTo(0);
    }

    @Test
    @DisplayName("InspectionsStatisticsTransformationServiceImpl.transformToInspectionsStatistics Should handle exception during transformation")
    void transformToInspectionsStatistics_ShouldHandleExceptionDuringTransformation() {
        // given
        List<InspectionsStatisticsResult> results = null; // This will cause an exception
        List<Integer> yearsToQuery = List.of(2023);

        // when & then
        assertThatThrownBy(() -> transformationService.transformToInspectionsStatistics(results, yearsToQuery))
                .isInstanceOf(RuntimeException.class)
                .hasMessage("Failed to transform inspections statistics");
    }

    private InspectionsStatisticsResult createStatisticsResult(int year, int activeInspectors, int numberOfInspections) {
        InspectionsStatisticsResult result = new InspectionsStatisticsResult();
        result.setYear(year);
        
        InspectionsStatisticsResultData data = new InspectionsStatisticsResultData();
        data.setActiveInspectors(activeInspectors);
        data.setNumberOfInspections(numberOfInspections);
        result.setData(data);
        
        return result;
    }
}
