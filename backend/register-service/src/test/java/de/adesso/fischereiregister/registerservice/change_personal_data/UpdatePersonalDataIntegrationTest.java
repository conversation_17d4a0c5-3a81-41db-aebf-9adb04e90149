package de.adesso.fischereiregister.registerservice.change_personal_data;

import de.adesso.fischereiregister.RegisterServiceApplication;
import de.adesso.fischereiregister.registerservice.common.TestDataUtil;
import de.adesso.fischereiregister.registerservice.common.security.mocking.WithMockSecurityContext;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.openapitools.model.ConsentInfo;
import org.openapitools.model.Person;
import org.openapitools.model.PersonWithAddress;
import org.openapitools.model.UpdatePersonRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import static de.adesso.fischereiregister.registerservice.common.JsonParser.asJsonString;
import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.notNullValue;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK, classes = RegisterServiceApplication.class)
@AutoConfigureMockMvc(addFilters = false)
@WithMockSecurityContext(userId = "12345")
public class UpdatePersonalDataIntegrationTest {

    @Autowired
    private MockMvc mvc;

    @Test
    @DisplayName("""
            	PUT /api/register-entries/registerEntryId/person
            	Verify that the change_personal_data endpoint returns a 200 OK status code.
            """)
    void changePersonalDataTest() throws Exception {
        UpdatePersonRequest request = validRequest();

        mvc.perform(MockMvcRequestBuilders.put("http://localhost:8080/register-entries/e4b6af13-1feb-4a4d-9c46-76298a0611cf/person")
                        .content(asJsonString(request))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.documents", hasSize(request.getTaxes().size()))) //taxes =1
                .andExpect(jsonPath("$.person.firstname", Matchers.containsString(request.getPerson().getFirstname())))
                .andExpect(jsonPath("$.registerEntryId", notNullValue()));

    }

    @Test
    @DisplayName("""
            	PUT /api/register-entries/registerEntryId/person
            	Verify that the change_personal_data endpoint returns an error if no register entry exists which could be modified.
            """)
    void changePersonalDataNoExistingRegisterEntry() throws Exception {

        mvc.perform(MockMvcRequestBuilders.put("http://localhost:8080/register-entries/12334234-1feb-4a4d-9c46-1232143243/person")
                .content(asJsonString(validRequest())).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)).andExpect(status().is4xxClientError());

    }


    public static UpdatePersonRequest validRequest() {
        UpdatePersonRequest request = new UpdatePersonRequest();

        final PersonWithAddress person = new PersonWithAddress();
        person.setFirstname("firstname");
        person.setLastname("lastname");
        person.setBirthdate("01.01.2000");

        request.setPerson(person);
        request.setTaxes(TestDataUtil.createTaxesApi());
        request.setConsentInfo(new ConsentInfo());

        return request;

    }
}
