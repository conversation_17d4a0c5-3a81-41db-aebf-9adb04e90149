package de.adesso.fischereiregister.registerservice.drools.dmn;

import de.adesso.fischereiregister.core.exceptions.RulesProcessingException;
import de.adesso.fischereiregister.core.model.Birthdate;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.registerservice.common.TestDataUtil;
import de.adesso.fischereiregister.registerservice.drools.dmn.license.configuration.input.DmnLicenseType;
import de.adesso.fischereiregister.registerservice.drools.dmn.license.configuration.input.LicenseInformationInput;
import de.adesso.fischereiregister.registerservice.drools.dmn.license.configuration.output.LicenseInformationOutput;
import de.adesso.fischereiregister.registerservice.drools.dmn.license.validation.input.LicenseValidationInput;
import de.adesso.fischereiregister.registerservice.drools.dmn.license.validation.output.LicenseValidationOutput;
import de.adesso.fischereiregister.registerservice.drools.dmn.model.DmnProcessingType;
import de.adesso.fischereiregister.registerservice.drools.dmn.tax.configuration.TaxInformationOutput;
import de.adesso.fischereiregister.registerservice.drools.dmn.tax.validation.TaxValidationInput;
import de.adesso.fischereiregister.registerservice.drools.dmn.tax.validation.TaxValidationOutput;
import org.junit.Assert;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.kie.api.runtime.KieSession;
import org.kie.dmn.api.core.DMNContext;
import org.kie.dmn.api.core.DMNDecisionResult;
import org.kie.dmn.api.core.DMNMessage;
import org.kie.dmn.api.core.DMNModel;
import org.kie.dmn.api.core.DMNResult;
import org.kie.dmn.api.core.DMNRuntime;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class DmnDroolsServiceTest {

    @Mock
    private KieSession kieSession;

    @Mock
    private DMNRuntime dmnRuntime;

    @Mock
    private DMNModel dmnModel;

    @Mock
    private DMNResult dmnResult;

    @Mock
    private DMNContext dmnContext;

    @InjectMocks
    private DmnDroolsService dmnDroolsService;

    @BeforeEach
    void setUp() {

        MockitoAnnotations.openMocks(this);
        dmnDroolsService = new DmnDroolsService(kieSession);
    }

    @Mock
    private DMNDecisionResult decisionResult;

    @Mock
    private DMNMessage dmnMessage;

    // --- Tests for isDecisionInvalid ---

    @Test
    void testIsDecisionInvalid_WhenSkipped_ShouldReturnTrue() {
        when(decisionResult.getEvaluationStatus()).thenReturn(DMNDecisionResult.DecisionEvaluationStatus.SKIPPED);
        assertTrue(dmnDroolsService.isDecisionInvalid(decisionResult));
    }

    @Test
    void testIsDecisionInvalid_WhenFailed_ShouldReturnTrue() {
        when(decisionResult.getEvaluationStatus()).thenReturn(DMNDecisionResult.DecisionEvaluationStatus.FAILED);
        assertTrue(dmnDroolsService.isDecisionInvalid(decisionResult));
    }

    @Test
    void testIsDecisionInvalid_WhenNotEvaluated_ShouldReturnTrue() {
        when(decisionResult.getEvaluationStatus()).thenReturn(DMNDecisionResult.DecisionEvaluationStatus.NOT_EVALUATED);
        assertTrue(dmnDroolsService.isDecisionInvalid(decisionResult));
    }

    @Test
    void testIsDecisionInvalid_WhenValid_ShouldReturnFalse() {
        when(decisionResult.getEvaluationStatus()).thenReturn(DMNDecisionResult.DecisionEvaluationStatus.SUCCEEDED);
        assertFalse(dmnDroolsService.isDecisionInvalid(decisionResult));
    }

    @Test
    void testIsDecisionInvalid_WhenNullDecision_ShouldReturnFalse() {
        assertFalse(dmnDroolsService.isDecisionInvalid(null));
    }

    // --- Tests for formatDecisionError ---

    @Test
    void testFormatDecisionError_WithMultipleMessages_ShouldReturnFormattedString() {
        when(dmnMessage.getText()).thenReturn("Error 1", "Error 2");
        when(decisionResult.getMessages()).thenReturn(List.of(dmnMessage, dmnMessage));

        String result = dmnDroolsService.formatDecisionError(decisionResult);
        assertEquals("Rules error: Error 1 | Error 2", result);
    }

    @Test
    void testFormatDecisionError_WithSingleMessage_ShouldReturnSingleError() {
        when(dmnMessage.getText()).thenReturn("Single Error");
        when(decisionResult.getMessages()).thenReturn(List.of(dmnMessage));

        String result = dmnDroolsService.formatDecisionError(decisionResult);
        assertEquals("Rules error: Single Error", result);
    }

    @Test
    void testFormatDecisionError_WithNoMessages_ShouldReturnEmptyError() {
        when(decisionResult.getMessages()).thenReturn(Collections.emptyList());

        String result = dmnDroolsService.formatDecisionError(decisionResult);
        assertEquals("Rules error: ", result);
    }

    @Test
    void testFormatDecisionError_WithNullDecisionResult_ShouldThrowException() {
        assertThrows(NullPointerException.class, () -> dmnDroolsService.formatDecisionError(null));
    }


    @Test
    void testDoesModelForTenantExistWhenModelExists() {
        // Arrange
        String modelName = "existingModel";
        when(dmnRuntime.getModel(DmnDroolsService.MODEL_NAMESPACE, modelName)).thenReturn(dmnModel);

        // Act
        boolean exists = dmnDroolsService.doesModelForTenantExist(dmnRuntime, modelName);

        // Assert
        assertTrue(exists);
    }

    @Test
    void testDoesModelForTenantExistWhenModelDoesNotExist() {
        // Arrange
        String modelName = "nonExistingModel";
        when(dmnRuntime.getModel(DmnDroolsService.MODEL_NAMESPACE, modelName)).thenReturn(null);

        // Act
        boolean exists = dmnDroolsService.doesModelForTenantExist(dmnRuntime, modelName);

        // Assert
        assertFalse(exists);
    }

    @Test
    void testGetModelNameWhenTenantModelExists() {
        // Arrange
        String federalState = "TestState";
        when(dmnRuntime.getModel(DmnDroolsService.MODEL_NAMESPACE, federalState)).thenReturn(dmnModel);

        // Act
        String modelName = dmnDroolsService.getModelName(federalState, dmnRuntime);

        // Assert
        assertEquals(federalState, modelName);
    }

    @Test
    void testGetModelNameWhenTenantModelDoesNotExist() {
        // Arrange
        String federalState = "TestState";
        when(dmnRuntime.getModel(DmnDroolsService.MODEL_NAMESPACE, federalState)).thenReturn(null);

        // Act
        String modelName = dmnDroolsService.getModelName(federalState, dmnRuntime);

        // Assert
        assertEquals("default", modelName);
    }

    @Test
    void testEvaluateLicenseRules_SuccessfulEvaluation() throws RulesProcessingException {
        LicenseValidationInput input = new LicenseValidationInput(7, DmnLicenseType.REGULAR, LocalDate.now());

        String federalState = "TestState";
        String modelName = "default";

        when(kieSession.getKieRuntime(DMNRuntime.class)).thenReturn(dmnRuntime);
        when(dmnRuntime.getModel(DmnDroolsService.MODEL_NAMESPACE, modelName)).thenReturn(dmnModel);
        when(dmnRuntime.newContext()).thenReturn(dmnContext);

        // Mock DMNResult and Decision Results
        DMNDecisionResult decisionResult = mock(DMNDecisionResult.class);
        when(decisionResult.getEvaluationStatus()).thenReturn(DMNDecisionResult.DecisionEvaluationStatus.SUCCEEDED);
        when(dmnRuntime.evaluateByName(dmnModel, dmnContext, "Fischereischein Regeln")).thenReturn(dmnResult);
        when(dmnResult.getDecisionResultByName("Fischereischein Regeln")).thenReturn(decisionResult);

        ArrayList<HashMap<String, Object>> decisionOutput = new ArrayList<>();
        HashMap<String, Object> singleResult = new HashMap<>();
        singleResult.put("Fehlermeldung", "Die beantragende Person muss mindestens 12 Jahre oder älter sein");
        decisionOutput.add(singleResult);

        when(decisionResult.getResult()).thenReturn(decisionOutput);

        List<DMNDecisionResult> decisionResults = List.of(decisionResult);
        when(dmnResult.getDecisionResults()).thenReturn(decisionResults);
        when(dmnRuntime.evaluateAll(dmnModel, dmnContext)).thenReturn(dmnResult); // here normally the dmnModel

        // Act
        List<LicenseValidationOutput> result = dmnDroolsService.evaluateRules("Fischereischein Regeln", LicenseValidationOutput::new, input, federalState);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        LicenseValidationOutput details = result.get(0);
        Assert.assertTrue("Die beantragende Person muss mindestens 12 Jahre oder älter sein".equalsIgnoreCase(details.getErrorMessage()));
    }

    @Test
    void testEvaluateTaxRules_SuccessfulEvaluation() throws RulesProcessingException {
        FederalState federalState = FederalState.SH;
        String modelName = "default";

        when(kieSession.getKieRuntime(DMNRuntime.class)).thenReturn(dmnRuntime);
        when(dmnRuntime.getModel(DmnDroolsService.MODEL_NAMESPACE, modelName)).thenReturn(dmnModel);
        when(dmnRuntime.newContext()).thenReturn(dmnContext);

        // Mock DMNResult and Decision Results
        DMNDecisionResult decisionResult = mock(DMNDecisionResult.class);
        when(decisionResult.getEvaluationStatus()).thenReturn(DMNDecisionResult.DecisionEvaluationStatus.SUCCEEDED);
        when(dmnRuntime.evaluateByName(dmnModel, dmnContext, "Abgaben")).thenReturn(dmnResult);
        when(dmnResult.getDecisionResultByName("Abgaben")).thenReturn(decisionResult);

        ArrayList<HashMap<String, Object>> decisionOutput = new ArrayList<>();
        HashMap<String, Object> singleResult = new HashMap<>();
        singleResult.put("Nettobetrag Abgabe", new BigDecimal("23"));
        singleResult.put("Verwaltungsgebühr", new BigDecimal("5"));
        singleResult.put("Gebühr Behördengang", new BigDecimal("6"));
        singleResult.put("Jahre", new BigDecimal(1));
        decisionOutput.add(singleResult);

        when(decisionResult.getResult()).thenReturn(decisionOutput);

        List<DMNDecisionResult> decisionResults = List.of(decisionResult);
        when(dmnResult.getDecisionResults()).thenReturn(decisionResults);
        when(dmnRuntime.evaluateAll(dmnModel, dmnContext)).thenReturn(dmnResult); // here normally the dmnModel

        // Act
        List<TaxInformationOutput> result = dmnDroolsService.getTaxInformation(federalState, false);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        TaxInformationOutput taxInformationResult = result.get(0);
        assertEquals(1, taxInformationResult.getDuration());
        assertEquals(new BigDecimal("6"), taxInformationResult.getOfficeFee());
        assertEquals(new BigDecimal("23"), taxInformationResult.getNetTaxAmount());
        assertEquals(new BigDecimal("5"), taxInformationResult.getAdministrativeFee());
        assertEquals(new BigDecimal("34"), taxInformationResult.getSummOfTaxAmount());
    }

    @Test
    void testEvaluateLiceneRules_SuccessfulEvaluation() throws RulesProcessingException {
        LicenseInformationInput input = new LicenseInformationInput(DmnLicenseType.VACATION,
                LocalDate.of(2023, 12, 02),
                DmnProcessingType.ANALOG);

        FederalState federalState = FederalState.SH;
        String modelName = "default";

        when(kieSession.getKieRuntime(DMNRuntime.class)).thenReturn(dmnRuntime);
        when(dmnRuntime.getModel(DmnDroolsService.MODEL_NAMESPACE, modelName)).thenReturn(dmnModel);
        when(dmnRuntime.newContext()).thenReturn(dmnContext);

        // Mock DMNResult and Decision Results
        DMNDecisionResult decisionResult = mock(DMNDecisionResult.class);
        when(decisionResult.getEvaluationStatus()).thenReturn(DMNDecisionResult.DecisionEvaluationStatus.SUCCEEDED);
        when(dmnRuntime.evaluateByName(dmnModel, dmnContext, "Konfiguration Fischereischein")).thenReturn(dmnResult);
        when(dmnResult.getDecisionResultByName("Konfiguration Fischereischein")).thenReturn(decisionResult);

        ArrayList<HashMap<String, Object>> decisionOutput = new ArrayList<>();
        HashMap<String, Object> singleResult = new HashMap<>();
        singleResult.put(LicenseInformationOutput.GUELTIGKEITSTYP, "TAG");
        singleResult.put(LicenseInformationOutput.SPAETESTES_BESTELLDATUM, LocalDate.of(2023, 12, 02));
        singleResult.put(LicenseInformationOutput.GEBUEHR_IN_EURO, new BigDecimal("67.89"));
        singleResult.put(LicenseInformationOutput.GUELTIGKEITSOPTIONEN, new ArrayList<>(List.of(28)));
        singleResult.put(LicenseInformationOutput.IST_VERLAENGERBAR, true);
        singleResult.put(LicenseInformationOutput.IST_VERFUEGBAR, true);
        singleResult.put(LicenseInformationOutput.IST_BEFRISTBAR, true);
        decisionOutput.add(singleResult);

        when(decisionResult.getResult()).thenReturn(decisionOutput);

        List<DMNDecisionResult> decisionResults = List.of(decisionResult);
        when(dmnResult.getDecisionResults()).thenReturn(decisionResults);
        when(dmnRuntime.evaluateAll(dmnModel, dmnContext)).thenReturn(dmnResult); // here normally the dmnModel

        // Act
        List<LicenseInformationOutput> result = dmnDroolsService.getLicenseInformation(federalState, DmnLicenseType.VACATION, DmnProcessingType.ANALOG);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        LicenseInformationOutput details = result.get(0);
        assertEquals(details.getLicenseType().toString(), de.adesso.fischereiregister.core.model.type.LicenseType.VACATION.toString());
        assertNotNull(details);
        assertTrue(details.isExtendable());
    }

    @Test
    void testEvaluateLiceneRules_SuccessfulEvaluationForeigner() throws RulesProcessingException {
        LicenseInformationInput input = new LicenseInformationInput(
                DmnLicenseType.VACATION,
                LocalDate.of(2023, 12, 02),
                (DmnProcessingType.ANALOG));

        FederalState federalState = FederalState.SH;
        String modelName = "default";

        when(kieSession.getKieRuntime(DMNRuntime.class)).thenReturn(dmnRuntime);
        when(dmnRuntime.getModel(DmnDroolsService.MODEL_NAMESPACE, modelName)).thenReturn(dmnModel);
        when(dmnRuntime.newContext()).thenReturn(dmnContext);

        // Mock DMNResult and Decision Results
        DMNDecisionResult decisionResult = mock(DMNDecisionResult.class);
        when(decisionResult.getEvaluationStatus()).thenReturn(DMNDecisionResult.DecisionEvaluationStatus.SUCCEEDED);
        when(dmnRuntime.evaluateByName(dmnModel, dmnContext, "Konfiguration Fischereischein")).thenReturn(dmnResult);
        when(dmnResult.getDecisionResultByName("Konfiguration Fischereischein")).thenReturn(decisionResult);

        ArrayList<HashMap<String, Object>> decisionOutput = new ArrayList<>();
        HashMap<String, Object> singleResult = new HashMap<>();
        singleResult.put(LicenseInformationOutput.GUELTIGKEITSTYP, "TAG");
        singleResult.put(LicenseInformationOutput.SPAETESTES_BESTELLDATUM, LocalDate.of(2023, 12, 02));
        singleResult.put(LicenseInformationOutput.GEBUEHR_IN_EURO, new BigDecimal("67.89"));
        singleResult.put(LicenseInformationOutput.GUELTIGKEITSOPTIONEN, new ArrayList<>(List.of(28)));
        singleResult.put(LicenseInformationOutput.IST_VERLAENGERBAR, true);
        singleResult.put(LicenseInformationOutput.IST_VERFUEGBAR, true);
        singleResult.put(LicenseInformationOutput.IST_BEFRISTBAR, true);
        decisionOutput.add(singleResult);

        when(decisionResult.getResult()).thenReturn(decisionOutput);

        List<DMNDecisionResult> decisionResults = List.of(decisionResult);
        when(dmnResult.getDecisionResults()).thenReturn(decisionResults);
        when(dmnRuntime.evaluateAll(dmnModel, dmnContext)).thenReturn(dmnResult);

        // Act
        List<LicenseInformationOutput> result = dmnDroolsService.getLicenseInformation(federalState, DmnLicenseType.VACATION, DmnProcessingType.ANALOG);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        LicenseInformationOutput details = result.get(0);
        assertEquals(details.getLicenseType().toString(), de.adesso.fischereiregister.core.model.type.LicenseType.VACATION.toString());
        assertTrue(details.isExtendable());
        assertNotNull(details);
    }


    @Test
    void shouldReturnFeeWhenRuleExists() throws RulesProcessingException {

        LicenseInformationInput input = new LicenseInformationInput(DmnLicenseType.REGULAR,
                LocalDate.of(2023, 12, 02),
                DmnProcessingType.ANALOG);

        String modelName = "default";

        when(kieSession.getKieRuntime(DMNRuntime.class)).thenReturn(dmnRuntime);
        when(dmnRuntime.getModel(DmnDroolsService.MODEL_NAMESPACE, modelName)).thenReturn(dmnModel);
        when(dmnRuntime.newContext()).thenReturn(dmnContext);

        // Mock DMNResult and Decision Results
        DMNDecisionResult decisionResult = mock(DMNDecisionResult.class);
        when(decisionResult.getEvaluationStatus()).thenReturn(DMNDecisionResult.DecisionEvaluationStatus.SUCCEEDED);
        when(dmnRuntime.evaluateByName(dmnModel, dmnContext, "Konfiguration Fischereischein")).thenReturn(dmnResult);
        when(dmnResult.getDecisionResultByName("Konfiguration Fischereischein")).thenReturn(decisionResult);

        ArrayList<HashMap<String, Object>> decisionOutput = new ArrayList<>();
        HashMap<String, Object> singleResult = new HashMap<>();
        singleResult.put(LicenseInformationOutput.GEBUEHR_IN_EURO, BigDecimal.valueOf(12));
        singleResult.put(LicenseInformationOutput.SPAETESTES_BESTELLDATUM, LocalDate.of(2023, 12, 02));
        singleResult.put(LicenseInformationOutput.GUELTIGKEITSTYP, "TAG");
        singleResult.put(LicenseInformationOutput.GUELTIGKEITSOPTIONEN, new ArrayList<>(List.of(28)));
        singleResult.put(LicenseInformationOutput.IST_VERLAENGERBAR, true);
        singleResult.put(LicenseInformationOutput.IST_VERFUEGBAR, true);
        singleResult.put(LicenseInformationOutput.IST_BEFRISTBAR, true);

        decisionOutput.add(singleResult);

        when(decisionResult.getResult()).thenReturn(decisionOutput);

        List<DMNDecisionResult> decisionResults = List.of(decisionResult);
        when(dmnResult.getDecisionResults()).thenReturn(decisionResults);
        when(dmnRuntime.evaluateAll(dmnModel, dmnContext)).thenReturn(dmnResult); // here normally the dmnModel
        int expectedFee = 12;
        // Act
        List<LicenseInformationOutput> retrieveFee = dmnDroolsService.getLicenseInformation(FederalState.SH, DmnLicenseType.REGULAR, DmnProcessingType.ANALOG);

        assertNotNull(retrieveFee);
        assertEquals(expectedFee, retrieveFee.get(0).getFeeAmount());
    }

    @Test
    void testEvaluateNewTenantRules_SuccessfulEvaluation() throws RulesProcessingException {
        // Arrange
        FederalState federalState = FederalState.SH;
        String modelName = "default";

        de.adesso.fischereiregister.core.model.Person person = TestDataUtil.createPerson();
        person.setBirthdate(new Birthdate(1990, 1, 1));

        when(kieSession.getKieRuntime(DMNRuntime.class)).thenReturn(dmnRuntime);
        when(dmnRuntime.getModel(DmnDroolsService.MODEL_NAMESPACE, modelName)).thenReturn(dmnModel);
        when(dmnRuntime.newContext()).thenReturn(dmnContext);

        // Mock DMNResult and Decision Results
        DMNDecisionResult decisionResult = mock(DMNDecisionResult.class);
        when(decisionResult.getEvaluationStatus()).thenReturn(DMNDecisionResult.DecisionEvaluationStatus.SUCCEEDED);
        when(dmnRuntime.evaluateByName(dmnModel, dmnContext, "Abgaben Regeln")).thenReturn(dmnResult);
        when(dmnResult.getDecisionResultByName("Abgaben Regeln")).thenReturn(decisionResult);

        ArrayList<HashMap<String, Object>> decisionOutput = new ArrayList<>();
        HashMap<String, Object> singleResult = new HashMap<>();
        singleResult.put("Fehlermeldung", "Test Exception");
        decisionOutput.add(singleResult);

        when(decisionResult.getResult()).thenReturn(decisionOutput);

        List<DMNDecisionResult> decisionResults = List.of(decisionResult);
        when(dmnResult.getDecisionResults()).thenReturn(decisionResults);
        when(dmnRuntime.evaluateAll(dmnModel, dmnContext)).thenReturn(dmnResult);

        // Act
        List<TaxValidationOutput> result = dmnDroolsService.evaluateTaxValidationRules(new TaxValidationInput(person.getBirthdate().getAge(), 2025, LocalDate.now()), federalState);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        TaxValidationOutput details = result.get(0);
        assertEquals("Test Exception", details.getErrorMessage());
    }
}
