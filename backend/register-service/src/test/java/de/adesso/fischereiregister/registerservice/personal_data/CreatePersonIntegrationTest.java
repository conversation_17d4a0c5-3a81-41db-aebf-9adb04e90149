package de.adesso.fischereiregister.registerservice.personal_data;

import de.adesso.fischereiregister.RegisterServiceApplication;
import de.adesso.fischereiregister.registerservice.common.JsonParser;
import de.adesso.fischereiregister.registerservice.common.TestDataUtil;
import de.adesso.fischereiregister.registerservice.common.security.mocking.WithMockSecurityContext;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.openapitools.model.CreatePersonRequest;
import org.openapitools.model.Person;
import org.openapitools.model.Tax;
import org.openapitools.model.TaxConsentInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.util.List;

import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.notNullValue;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = RegisterServiceApplication.class)
@AutoConfigureMockMvc(addFilters = false)
@WithMockSecurityContext()
public class CreatePersonIntegrationTest {

    @Autowired
    private MockMvc mvc;

    @Test
    @DisplayName("""
            	POST /register-entries/personal-data with valid data and successful record creation
            """)
    void callCreatePersonalDataSuccess() throws Exception {
        final CreatePersonRequest createPersonRequest = validPersonalDataRequestDto();

        final ResultActions result = mvc.perform(MockMvcRequestBuilders.post("http://localhost:8080/register-entries/person")
                .content(JsonParser.asJsonString(createPersonRequest))
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON));

        result.andExpect(MockMvcResultMatchers.status().isCreated());
        result.andExpect(jsonPath("$.documents", hasSize(createPersonRequest.getTaxes().size())));
        result.andExpect(jsonPath("$.person.firstname", Matchers.containsString(createPersonRequest.getPerson().getFirstname())));
        result.andExpect(jsonPath("$.registerEntryId", notNullValue()));

    }

    @Test
    @DisplayName("""
            	POST /register-entries/personal-data with incomplete data and client error message
            """)
    void callCreatePersonalDataError() throws Exception {
        final CreatePersonRequest incompleteCreatePersonRequestDto = validPersonalDataRequestDto();
        incompleteCreatePersonRequestDto.getPerson().setFirstname(null);
        mvc.perform(MockMvcRequestBuilders.post("http://localhost:8080/register-entries/person")
                        .content(JsonParser.asJsonString(incompleteCreatePersonRequestDto))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().is4xxClientError());
    }

    private CreatePersonRequest validPersonalDataRequestDto() {

        final Person person = TestDataUtil.createPersonApi();

        TaxConsentInfo consentInfo = new TaxConsentInfo();
        consentInfo.setGdprAccepted(true);
        consentInfo.setSubmittedByThirdParty(false);

        final List<Tax> taxes = TestDataUtil.createTaxesApi();

        final CreatePersonRequest request = new CreatePersonRequest();
        request.setPerson(person);
        request.setConsentInfo(consentInfo);
        request.setTaxes(taxes);

        return request;
    }
}
