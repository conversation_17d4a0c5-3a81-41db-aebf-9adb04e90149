package de.adesso.fischereiregister.registerservice.register_entry;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import de.adesso.fischereiregister.RegisterServiceApplication;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.openapitools.model.SearchItem;
import org.openapitools.model.SearchItemFishingLicensesInner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = RegisterServiceApplication.class)
@AutoConfigureMockMvc(addFilters = false)
public class RegisterEntrySearchIntegrationTest {

	private static final String SEARCH_PARAM = "search";

	@Autowired
	private MockMvc mvc;

	private ObjectMapper mapper;

	@BeforeEach
	void setUp() {
		mapper = new ObjectMapper();
		mapper.registerModule(new JavaTimeModule());
	}

	@Test
	@DisplayName("""
				GET /api/register-entries/search.
				Verify that the API returns the expected result when searching by name.
			""")
	void testSearchByName() throws Exception {
		MvcResult searchResult = mvc.perform(MockMvcRequestBuilders.get("http://localhost:8080/register-entries/search")
				.param(SEARCH_PARAM, "Max").contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON))
				.andReturn();

		List<SearchItem> list = mapper.readValue(searchResult.getResponse().getContentAsString(),
				new TypeReference<List<SearchItem>>() {
				});
		assertFalse(list.isEmpty(), "Suchergebnis Liste sollte mindestens 1 Element enthalten");
		assertEquals("Max", list.get(0).getPerson().getFirstname());
	}

	@Test
	@DisplayName("""
				GET /api/register-entries/search.
				Verify that the API returns the expected result when searching by name and birthplace.
			""")
	void testSearchByNameAndBirthplace() throws Exception {
		MvcResult searchResult = mvc.perform(MockMvcRequestBuilders.get("http://localhost:8080/register-entries/search")
				.param(SEARCH_PARAM, "Max,Musterstadt").contentType(MediaType.APPLICATION_JSON)
				.accept(MediaType.APPLICATION_JSON))
				.andReturn();

		List<SearchItem> list = mapper.readValue(searchResult.getResponse().getContentAsString(),
				new TypeReference<List<SearchItem>>() {
				});
		assertFalse(list.isEmpty(), "Suchergebnis Liste sollte mindestens 1 Element enthalten");
		assertEquals("Max", list.get(0).getPerson().getFirstname());
		assertEquals("Musterstadt", list.get(0).getPerson().getBirthplace());
	}

	@Test
	@DisplayName("""
				GET /api/register-entries/search.
				Verify that the API returns the expected result when searching by license number.
			""")
	void testSearchByLicenseNumber() throws Exception {
		MvcResult searchResult = mvc.perform(MockMvcRequestBuilders.get("http://localhost:8080/register-entries/search")
				.param(SEARCH_PARAM, "SH85-5986-3119-1523").contentType(MediaType.APPLICATION_JSON)
				.accept(MediaType.APPLICATION_JSON))
				.andReturn();

		List<SearchItem> list = mapper.readValue(searchResult.getResponse().getContentAsString(),
				new TypeReference<List<SearchItem>>() {
				});
		assertFalse(list.isEmpty(), "Suchergebnis Liste sollte mindestens 1 Element enthalten");
		List<SearchItemFishingLicensesInner> resultFishingLicenses = list.get(0).getFishingLicenses();
		assertEquals("SH85-5986-3119-1523", resultFishingLicenses.get(resultFishingLicenses.size() - 1).getNumber());
	}

	@Test
	@DisplayName("""
				GET /api/register-entries/search.
				Verify that the API returns the expected result when searching by birthdate.
			""")
	void testSearchByBirthdate() throws Exception {
		MvcResult searchResult = mvc.perform(MockMvcRequestBuilders.get("http://localhost:8080/register-entries/search")
				.param(SEARCH_PARAM, "01.01.2000").contentType(MediaType.APPLICATION_JSON)
				.accept(MediaType.APPLICATION_JSON))
				.andReturn();

		List<SearchItem> list = mapper.readValue(searchResult.getResponse().getContentAsString(),
				new TypeReference<List<SearchItem>>() {
				});
		String expectedBirthdate = "01.01.2000";
		assertFalse(list.isEmpty(), "Suchergebnis Liste sollte mindestens 1 Element enthalten");
		assertEquals(expectedBirthdate, list.get(0).getPerson().getBirthdate());
	}

	@Test
	@DisplayName("""
				GET /api/register-entries/search.
				Verify that the API returns multiple entries for ambiguous name.
			""")
	void testSearchLikeByName() throws Exception {
		MvcResult searchResult = mvc.perform(MockMvcRequestBuilders.get("http://localhost:8080/register-entries/search")
				.param(SEARCH_PARAM, "M").contentType(MediaType.APPLICATION_JSON)
				.accept(MediaType.APPLICATION_JSON))
				.andReturn();

		List<SearchItem> list = mapper.readValue(searchResult.getResponse().getContentAsString(),
				new TypeReference<List<SearchItem>>() {
				});
		assertTrue(list.size() > 1);
	}

	@Test
	@DisplayName("""
				GET /api/register-entries/search.
				Verify that the API returns a single match for ambiguous name combined with unique birthplace.
			""")
	void testSearchAmbiguousNameUniqueBirthplace() throws Exception {
		MvcResult searchResult = mvc.perform(MockMvcRequestBuilders.get("http://localhost:8080/register-entries/search")
				.param(SEARCH_PARAM, "M,Musterstadt").contentType(MediaType.APPLICATION_JSON)
				.accept(MediaType.APPLICATION_JSON))
				.andReturn();

		List<SearchItem> list = mapper.readValue(searchResult.getResponse().getContentAsString(),
				new TypeReference<List<SearchItem>>() {
				});
		assertEquals(1, list.size());
	}

	@Test
	@DisplayName("""
				GET /api/register-entries/search.
				Verify that the API returns a 400 status code when searching with an empty query.
			""")
	void testSearchEmpty() throws Exception {
		MvcResult searchResult = mvc.perform(MockMvcRequestBuilders.get("http://localhost:8080/register-entries/search")
				.param(SEARCH_PARAM, "").contentType(MediaType.APPLICATION_JSON)
				.accept(MediaType.APPLICATION_JSON))
				.andReturn();

		assertEquals(400, searchResult.getResponse().getStatus());
	}

	@Test
	@DisplayName("""
				GET /api/register-entries/search.
				Verify that the API returns a 404 status code when searching for an unknown license number.
			""")
	void testSearchUnknownLicenseNumber() throws Exception {
		MvcResult searchResult = mvc.perform(MockMvcRequestBuilders.get("http://localhost:8080/register-entries/search")
				.param(SEARCH_PARAM, "9999-9999-9999-9999").contentType(MediaType.APPLICATION_JSON)
				.accept(MediaType.APPLICATION_JSON))
				.andReturn();

		assertEquals(404, searchResult.getResponse().getStatus());
	}

	@Test
	@DisplayName("""
				GET /api/register-entries/search.
				Verify that the API returns a 404 status code when searching for an unknown name.
			""")
	void testSearchUnknownName() throws Exception {
		MvcResult searchResult = mvc.perform(MockMvcRequestBuilders.get("http://localhost:8080/register-entries/search")
				.param(SEARCH_PARAM, "Unknown").contentType(MediaType.APPLICATION_JSON)
				.accept(MediaType.APPLICATION_JSON))
				.andReturn();

		assertEquals(404, searchResult.getResponse().getStatus());
	}

	@Test
	@DisplayName("""
				GET /api/register-entries/search.
				Verify that the API returns a 404 status code when searching for an unknown birthdate.
			""")
	void testSearchUnknownBirthdate() throws Exception {
		MvcResult searchResult = mvc.perform(MockMvcRequestBuilders.get("http://localhost:8080/register-entries/search")
				.param(SEARCH_PARAM, "01.01.9999").contentType(MediaType.APPLICATION_JSON)
				.accept(MediaType.APPLICATION_JSON))
				.andReturn();

		assertEquals(404, searchResult.getResponse().getStatus());
	}

	@Test
	@DisplayName("""
				GET /api/register-entries/search.
				Verify that the API returns a 404 status code when searching for an unknown name and birthplace.
			""")
	void testSearchUnknownNameAndBirthplace() throws Exception {
		MvcResult searchResult = mvc.perform(MockMvcRequestBuilders.get("http://localhost:8080/register-entries/search")
				.param(SEARCH_PARAM, "Unknown,Unknown").contentType(MediaType.APPLICATION_JSON)
				.accept(MediaType.APPLICATION_JSON))
				.andReturn();

		assertEquals(404, searchResult.getResponse().getStatus());
	}
}
