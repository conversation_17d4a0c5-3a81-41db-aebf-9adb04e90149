package de.adesso.fischereiregister.registerservice.drools.dmn.model;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;

class DmnProcessingTypeTest {

    @Test
    @DisplayName("Tests the procesing type values")
    void testGetValue() {
        assertEquals("ANALOG", DmnProcessingType.ANALOG.getValue());
        assertEquals("DIGITAL", DmnProcessingType.DIGITAL.getValue());
    }

    @Test
    @DisplayName("Tests the procesing type values as strings")
    void testToString() {
        assertEquals("ANALOG", DmnProcessingType.ANALOG.toString());
        assertEquals("DIGITAL", DmnProcessingType.DIGITAL.toString());
    }

    @Test
    @DisplayName("Tests the procesing type enum values")
    void testEnumValues() {
        DmnProcessingType[] values = DmnProcessingType.values();
        assertEquals(2, values.length);
        assertArrayEquals(new DmnProcessingType[] {
                DmnProcessingType.ANALOG,
                DmnProcessingType.DIGITAL
        }, values);
    }

    @Test
    @DisplayName("Tests the procesing type enum values strings")
    void testEnumFromString() {
        assertEquals(DmnProcessingType.ANALOG, DmnProcessingType.valueOf("ANALOG"));
        assertEquals(DmnProcessingType.DIGITAL, DmnProcessingType.valueOf("DIGITAL"));
    }
}
