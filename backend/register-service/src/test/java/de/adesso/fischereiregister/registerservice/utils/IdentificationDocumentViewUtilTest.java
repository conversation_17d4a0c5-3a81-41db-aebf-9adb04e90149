package de.adesso.fischereiregister.registerservice.utils;

import de.adesso.fischereiregister.registerservice.identification_document_view.IdentificationDocumentView;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;


import static org.junit.jupiter.api.Assertions.assertEquals;
import de.adesso.fischereiregister.registerservice.identification_document_view.IdentificationDocumentViewUtil;

class IdentificationDocumentViewUtilTest {



    @Test
    @DisplayName("FishingCertificateUtils.formatFishingCertificateNumber should return a well readable version of a fishing certificate number consisting of four blocks of four symbols each separated by '-")
    public void testFormatFishingCertificateNumber() {
        //GIVEN
        final String unformattedNumber = "SH00111122223333";
        final IdentificationDocumentView view = new IdentificationDocumentView();
        view.setLicenseNumber(unformattedNumber);
        //WHEN
        final String formattedNumber = IdentificationDocumentViewUtil.formatLicenceNumber(view);
        //THEN
        assertEquals("SH00-1111-2222-3333", formattedNumber);
    }
}
