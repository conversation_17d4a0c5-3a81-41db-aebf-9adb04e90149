package de.adesso.fischereiregister.registerservice.identification_document_view;

import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.IdentificationDocumentType;
import de.adesso.fischereiregister.registerservice.common.TestDataUtil;
import de.adesso.fischereiregister.registerservice.security.UserDetailsService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class IdentificationDocumentViewServiceImplTest {

    @Mock
    private IdentificationDocumentViewRepository identificationDocumentViewRepository;

    @Mock
    private UserDetailsService userDetailsService;

    @InjectMocks
    private IdentificationDocumentViewServiceImpl service;


    @Test
    void testCreateIdentificationDocumentView_savesValidView() {
        UUID registerId = UUID.randomUUID();
        String salt = "someSalt";
        String documentId = "doc123";

        IdentificationDocument identificationDocument = new IdentificationDocument();
        identificationDocument.setDocumentId(documentId);
        identificationDocument.setType(IdentificationDocumentType.PDF);
        identificationDocument.setFishingLicense(new FishingLicense());
        List<IdentificationDocument> identificationDocuments = new ArrayList<>();
        identificationDocuments.add(identificationDocument);

        service.createIdentificationDocumentViews(registerId, salt, identificationDocuments);

        verify(identificationDocumentViewRepository, times(1)).saveAll(anyList());
    }


    @Test
    void testCreateNewAvailableNumber_generatesUniqueNumber() {

        when(identificationDocumentViewRepository.findAllNumbers()).thenReturn(Collections.emptyList());
        String newNumber = service.createNewAvailableFishingLicenseNumber(TestDataUtil.registerId, FederalState.SH);

        assertNotNull(newNumber);
        assertTrue(newNumber.startsWith(FederalState.SH.toString()));
    }

    @Test
    void testFindByLicenseNumber_returnsView() {
        String licenseId = "license123";
        IdentificationDocumentView view = new IdentificationDocumentView();
        when(identificationDocumentViewRepository.findByLicenseNumber(licenseId)).thenReturn(Optional.of(view));

        Optional<IdentificationDocumentView> result = service.findByLicenseNumber(licenseId);

        assertTrue(result.isPresent());
        assertEquals(view, result.get());
    }




}

