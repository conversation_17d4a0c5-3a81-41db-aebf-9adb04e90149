package de.adesso.fischereiregister.registerservice.online_services;

import de.adesso.fischereiregister.RegisterServiceApplication;
import de.adesso.fischereiregister.registerservice.common.JsonParser;
import de.adesso.fischereiregister.registerservice.common.TestDataUtil;
import de.adesso.fischereiregister.registerservice.common.security.mocking.WithMockSecurityContext;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.openapitools.model.CreateVacationLicenseRequestOS;
import org.openapitools.model.FederalStateAbbreviation;
import org.openapitools.model.Fee;
import org.openapitools.model.OrderFishingLicenseRequestOS;
import org.openapitools.model.PayTaxRequestOS;
import org.openapitools.model.PaymentInfo;
import org.openapitools.model.PaymentType;
import org.openapitools.model.PersonOS;
import org.openapitools.model.ReplaceFishingLicenseRequestOS;
import org.openapitools.model.ValidityPeriod;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.math.BigDecimal;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK, classes = RegisterServiceApplication.class)
@AutoConfigureMockMvc(addFilters = false)
@WithMockSecurityContext()
class OnlineServiceIntegrationTest {

    @Autowired
    private MockMvc mvc;

    @Test
    @DisplayName("""
            	POST /os/v1/fishing-license/ Create fishing license with valid data
            """)
    void onlineServiceControllerCreate() throws Exception {
        final String postURL = "http://localhost:8080/os/v1/fishing-license";
        final OrderFishingLicenseRequestOS requestOS = TestDataUtil.createOrderFishingLicenseRequestOS();
        final ResultActions result = mvc.perform(MockMvcRequestBuilders
                .post(postURL)
                .content(JsonParser.asJsonString(requestOS))
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON));

        result.andExpect(status().isOk());

    }

    @Test
    @DisplayName("""
            	POST /os/v1/fishing-license:replace Replace Card Test
            """)
    void onlineServiceControllerReplace() throws Exception {
        final String postURL = "http://localhost:8080/os/v1/fishing-license:replace";
        final ReplaceFishingLicenseRequestOS requestOS = TestDataUtil.createReplaceFishingLicenseRequestOS();
        final ResultActions result = mvc.perform(MockMvcRequestBuilders
                .get(postURL)
                .content(JsonParser.asJsonString(requestOS))
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON));

        result.andExpect(status().isOk());
    }

    @Test
    @DisplayName("""
            	POST /os/v1/fishing-license/tax pay tax
            """)
    void onlineServiceControllerTaxPayed() throws Exception {
        final String postURL = "http://localhost:8080/os/v1/fishing-license/tax";
        final PayTaxRequestOS requestOS = TestDataUtil.createPayTaxRequestOS();
        final ResultActions result = mvc.perform(MockMvcRequestBuilders
                .put(postURL)
                .content(JsonParser.asJsonString(requestOS))
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON));

        result.andExpect(status().isOk());

    }

    @Test
    @DisplayName("""
            POST /os/v1/fishing-license/vacation Add vacation license
            """)
    void onlineServiceControllerVacation() throws Exception {
        final String postURL = "http://localhost:8080/os/v1/fishing-license/vacation";
        final CreateVacationLicenseRequestOS requestOS = new CreateVacationLicenseRequestOS();

        PersonOS person = new PersonOS();

        person.setTitle("Dr.");
        person.setFirstname("Max Zwei");
        person.setLastname("lastname");
        person.setBirthname("birthname");
        person.setBirthplace("city");
        person.setBirthdate("08.08.2024");
        person.setNationality("deutsch");


        requestOS.setConsentInfo(TestDataUtil.createConsentInfoApi());
        requestOS.setPerson(person);
        requestOS.setServiceAccountId("serviceAccountId");
        requestOS.setFederalState(FederalStateAbbreviation.SH);
        requestOS.setInboxReference("inboxReference");
        requestOS.setTransactionId("transactionId");

        ValidityPeriod period = new ValidityPeriod();
        period.setValidFrom("2023-02-02");
        period.setValidTo("2023-03-12");

        requestOS.setValidityPeriod(period);

        Fee fee = new Fee();
        fee.setFederalState(FederalStateAbbreviation.SH);
        fee.setValidFrom("2023-02-02");
        fee.setValidTo("2023-03-12");
        PaymentInfo paymentInfo = new PaymentInfo();
        paymentInfo.setAmount(BigDecimal.valueOf(29));
        paymentInfo.setType(PaymentType.ONLINE);
        fee.setPaymentInfo(paymentInfo);

        requestOS.setFee(fee);

        final ResultActions result = mvc.perform(MockMvcRequestBuilders
                .post(postURL)
                .content(JsonParser.asJsonString(requestOS))
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON));

        result.andExpect(status().isOk());
    }
}