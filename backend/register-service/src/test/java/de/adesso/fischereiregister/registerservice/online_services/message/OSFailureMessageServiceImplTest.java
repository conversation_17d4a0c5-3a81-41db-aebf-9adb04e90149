package de.adesso.fischereiregister.registerservice.online_services.message;

import de.adesso.fischereiregister.core.model.Birthdate;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.message.service.OSInboxService;
import de.adesso.fischereiregister.message.service.model.OSMessage;
import de.adesso.fischereiregister.registerservice.common.TestDataUtil;
import de.adesso.fischereiregister.registerservice.online_services.message.enums.OSMessageTemplate;
import de.adesso.fischereiregister.registerservice.online_services.model.OSRequestStatus;
import de.adesso.fischereiregister.registerservice.protocol.ProtocolService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static de.adesso.fischereiregister.core.utils.PersonUtils.getFullName;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class OSFailureMessageServiceImplTest {

    @Mock
    private OSInboxService osInboxService;

    @Mock
    private ProtocolService protocolService;

    @InjectMocks
    private OSFailureMessageServiceImpl osFailureService;

    private String serviceAccountId;
    private String licenseNumber;
    private Person person;
    private FederalState federalState;

    public static final String SUBJECT = "Fischereidokumente online: Ihr digitaler Fischereischein";

    public static final String FAILURE_MESSAGE = """
            Guten Tag %s,
            
            leider ist bei der Verarbeitung Ihres Antrags auf <Ausstellung eines Fischereischeins / Ersatzausstellung eines Fischereischeins> ein Fehler aufgetreten. Ihr Antrag konnte daher noch nicht abschließend bearbeitet werden.\s
            
            Um den Sachverhalt zu klären, bitten wir Sie, sich an die obere Fischereibehörde zu wenden. Halten Sie dort bitte die Zusammenfassung Ihres Antrags aus dem Onlinedienst für Rückfragen bereit.
            
            Wir bedauern die entstandenen Unannehmlichkeiten und danken Ihnen für Ihr Verständnis.
            
            Freundliche Grüße,
            
            Ihr Team der oberen Fischereibehörde Schleswig-Holstein, digitale Leitstelle.
            
            Sie erreichen uns wie folgt:
            
            Per Mail: <EMAIL>
            
            Telefonisch: 04347/704-311
            
            Postalisch: Landesamt für Landwirtschaft und nachhaltige Landentwicklung SH (LLnL)
            Abt. Fischerei
            Hamburger Chaussee 25, 24220 Flintbek""";

    @Mock
    private OSMessageTemplateResolutionService OSMessageTemplateResolutionService;

    @BeforeEach
    void setUp() {
        serviceAccountId = "9ffd450f-eec8-4f63-973e-3eb6c1fe39cb";
        licenseNumber = "****************";

        person = new Person();
        person.setTitle("DR.");
        person.setFirstname("Max");
        person.setLastname("Mustermann");
        person.setBirthdate(new Birthdate(1990, 1, 1));
        person.setNationality("deutsch");

        federalState = FederalState.SH;
        osFailureService = new OSFailureMessageServiceImpl(osInboxService, protocolService, OSMessageTemplateResolutionService);

    }

    @Test
    @DisplayName("OSFailureServiceImpl.handleLicenseCreationFailure should handle PERSON_NOT_FOUND")
    void handleLicenseCreationFailure_withPersonNotFound_shouldLogAndSendMessage() {
        // given
        // when
        osFailureService.handleFailure(OSRequestStatus.PERSON_NOT_FOUND_CREATE_NEW_REGISTER_ENTRY, serviceAccountId, licenseNumber, person, federalState, OSMessageTemplate.REGULAR_FISHING_LICENSE_CREATED_OS_FAILURE);

        // then
        verify(protocolService).protocolFailedMessage(licenseNumber, person);
        verify(osInboxService).sendMessage(eq(serviceAccountId), any(OSMessage.class));
    }

    @Test
    @DisplayName("OSFailureServiceImpl.handleLicenseCreationFailure should handle MULTIPLE_PERSONS_FOUND")
    void handleLicenseCreationFailure_withMultiplePersonsFound_shouldLogAndSendMessage() {
        // given
        // when
        osFailureService.handleFailure(OSRequestStatus.MULTIPLE_PERSONS_FOUND, serviceAccountId, licenseNumber, person, federalState, OSMessageTemplate.REGULAR_FISHING_LICENSE_CREATED_OS_FAILURE);

        // then
        verify(protocolService).protocolFailedMessage(licenseNumber, person);
        verify(osInboxService).sendMessage(eq(serviceAccountId), any(OSMessage.class));
    }

    @Test
    @DisplayName("OSFailureServiceImpl.handleLicenseCreationFailure should handle UNEXPECTED_ERROR")
    void handleLicenseCreationFailure_withUnexpectedError_shouldLogAndSendMessage() {
        // given
        // when
        osFailureService.handleFailure(OSRequestStatus.UNEXPECTED_ERROR, serviceAccountId, licenseNumber, person, federalState, OSMessageTemplate.REGULAR_FISHING_LICENSE_CREATED_OS_FAILURE);

        // then
        verify(protocolService).protocolFailedMessage(licenseNumber, person);
        verify(osInboxService).sendMessage(eq(serviceAccountId), any(OSMessage.class));
    }

    @Test
    @DisplayName("OSFailureServiceImpl.handleLicenseReplacementFailure should handle PERSON_NOT_FOUND")
    void handleLicenseReplacementFailure_withPersonNotFound_shouldLogAndSendMessage() {
        // given
        // when
        osFailureService.handleFailure(OSRequestStatus.PERSON_NOT_FOUND_CREATE_NEW_REGISTER_ENTRY, serviceAccountId, licenseNumber, person, federalState, OSMessageTemplate.REGULAR_FISHING_LICENSE_REPLACED_OS_FAILURE);

        // then
        verify(protocolService).protocolFailedMessage(licenseNumber, person);
        verify(osInboxService).sendMessage(eq(serviceAccountId), any(OSMessage.class));
    }

    @Test
    @DisplayName("OSFailureServiceImpl.handleLicenseReplacementFailure should handle MULTIPLE_PERSONS_FOUND")
    void handleLicenseReplacementFailure_withMultiplePersonsFound_shouldLogAndSendMessage() {
        // when
        osFailureService.handleFailure(OSRequestStatus.MULTIPLE_PERSONS_FOUND, serviceAccountId, licenseNumber, person, federalState, OSMessageTemplate.REGULAR_FISHING_LICENSE_REPLACED_OS_FAILURE);

        // then
        verify(protocolService).protocolFailedMessage(licenseNumber, person);
        verify(osInboxService).sendMessage(eq(serviceAccountId), any(OSMessage.class));
    }

    @Test
    @DisplayName("OSFailureServiceImpl.handleLicenseReplacementFailure should handle UNEXPECTED_ERROR")
    void handleLicenseReplacementFailure_withUnexpectedError_shouldLogAndSendMessage() {
        // given
        OSMessage expectedMessage = new OSMessage(SUBJECT, String.format(FAILURE_MESSAGE, getFullName(person)));
        // when
        osFailureService.handleFailure(OSRequestStatus.UNEXPECTED_ERROR, serviceAccountId, licenseNumber, person, federalState, OSMessageTemplate.REGULAR_FISHING_LICENSE_REPLACED_OS_FAILURE);

        // then
        verify(protocolService).protocolFailedMessage(licenseNumber, person);
        verify(osInboxService).sendMessage(eq(serviceAccountId), any(OSMessage.class));
    }


    @Test
    @DisplayName("OSFailureServiceImpl.handleLicenseReplacementFailure should handle PERSON_NOT_FOUND when the person data is not given")
    void handleLicenseReplacementFailure_withUnexpectedErrorAndNullPerson_shouldLogAndSendMessage() {
        // given
        Person person = TestDataUtil.createPerson();
        // when
        osFailureService.handleFailure(OSRequestStatus.UNEXPECTED_ERROR, serviceAccountId, licenseNumber, person, federalState, OSMessageTemplate.REGULAR_FISHING_LICENSE_REPLACED_OS_FAILURE);

        // then
        verify(protocolService).protocolFailedMessage(licenseNumber, person);
        verify(osInboxService).sendMessage(eq(serviceAccountId), any(OSMessage.class));
    }

    @Test
    @DisplayName("OSFailureServiceImpl.handleTaxPaymentFailure should handle PERSON_NOT_FOUND")
    void handleTaxPaymentFailure_withPersonNotFound_shouldLogAndSendMessage() {
        // given
        // when
        osFailureService.handleFailure(OSRequestStatus.PERSON_NOT_FOUND_CREATE_NEW_REGISTER_ENTRY, serviceAccountId, licenseNumber, person, federalState, OSMessageTemplate.FISHING_TAX_CREATED_OS_FAILURE);

        // then
        verify(protocolService).protocolFailedMessage(licenseNumber, person);
        verify(osInboxService).sendMessage(eq(serviceAccountId), any(OSMessage.class));
    }

    @Test
    @DisplayName("OSFailureServiceImpl.handleTaxPaymentFailure should handle MULTIPLE_PERSONS_FOUND")
    void handleTaxPaymentFailure_withMultiplePersonsFound_shouldLogAndSendMessage() {
        // given
        // when
        osFailureService.handleFailure(OSRequestStatus.MULTIPLE_PERSONS_FOUND, serviceAccountId, licenseNumber, person, federalState, OSMessageTemplate.FISHING_TAX_CREATED_OS_FAILURE);

        // then
        verify(protocolService).protocolFailedMessage(licenseNumber, person);
        verify(osInboxService).sendMessage(eq(serviceAccountId), any(OSMessage.class));
    }

    @Test
    @DisplayName("OSFailureServiceImpl.handleTaxPaymentFailure should handle UNEXPECTED_ERROR")
    void handleTaxPaymentFailure_withUnexpectedError_shouldLogAndSendMessage() {
        // when
        osFailureService.handleFailure(OSRequestStatus.UNEXPECTED_ERROR, serviceAccountId, licenseNumber, person, federalState, OSMessageTemplate.FISHING_TAX_CREATED_OS_FAILURE);

        // then
        verify(protocolService).protocolFailedMessage(licenseNumber, person);
        verify(osInboxService).sendMessage(eq(serviceAccountId), any(OSMessage.class));
    }

    @Test
    @DisplayName("OSFailureServiceImpl.handleTaxPaymentFailure should handle PERSON_NOT_FOUND when the person data is not given")
    void handleTaxPaymentFailure_withUnexpectedErrorAndNullPerson_shouldLogAndSendMessage() {
        // when
        osFailureService.handleFailure(OSRequestStatus.UNEXPECTED_ERROR, serviceAccountId, licenseNumber, person, federalState, OSMessageTemplate.FISHING_TAX_CREATED_OS_FAILURE);

        // then
        verify(protocolService).protocolFailedMessage(licenseNumber, person);
        verify(osInboxService).sendMessage(eq(serviceAccountId), any(OSMessage.class));
    }
}
