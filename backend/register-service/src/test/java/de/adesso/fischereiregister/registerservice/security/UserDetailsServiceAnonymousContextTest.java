package de.adesso.fischereiregister.registerservice.security;


import de.adesso.fischereiregister.registerservice.common.security.mocking.WithMockAnonymousSecurityContext;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * The usual UserDetailsService test can only check for the successful scenarios since the mock securityContext
 * provides all necessary information. here all the tests for the behavior if an AnonymousSecurityContext
 * which does not contain the user information, is provided
 *
 */
@WithMockAnonymousSecurityContext()
@DisplayName("UserDetailService tests for behavior when a AnonymousAuthenticationToken is provided")
public class UserDetailsServiceAnonymousContextTest {

    private final UserDetailsService service = new UserDetailsServiceImpl();


    @Test
    @DisplayName("userDetailsService.getUserId()  correctly reads user id from auth information")
    void testGetUserId() {
        //Given
        //When
        Optional<String> receivedUserId = service.getUserId();
        //Then
        assertTrue(receivedUserId.isEmpty());

    }


    @Test
    @DisplayName("userDetailsService.getCertificationIssuer() correctly reads organisation from auth information")
    void testGetCertificationIssuer() {
        //Given
        //When
        Optional<String> receivedOrganisation = service.getCertificationIssuer();
        //Then
        assertTrue(receivedOrganisation.isEmpty());

    }

    @Test
    @DisplayName("userDetailsService.getFederalState() throws an exception if no valid authentication is available")
    void testGetFederalState() {
        //Given
        //When
        final String federalState = service.getFederalState();

        //Then
        assertTrue(federalState.isEmpty());

    }

}
