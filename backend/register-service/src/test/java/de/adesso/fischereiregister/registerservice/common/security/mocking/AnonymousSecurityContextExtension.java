package de.adesso.fischereiregister.registerservice.common.security.mocking;

import de.adesso.fischereiregister.registerservice.security.UserDetailsService;
import org.junit.jupiter.api.extension.BeforeEachCallback;
import org.junit.jupiter.api.extension.ExtensionContext;
import org.mockito.Mockito;
import org.springframework.security.authentication.AnonymousAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;


import static org.mockito.Mockito.when;

public class AnonymousSecurityContextExtension implements BeforeEachCallback {

    @Override
    public void beforeEach(ExtensionContext context) {
        MockedServices result = getMockedServices();
        when(result.securityContextMock().getAuthentication()).thenReturn(result.authenticationMock());
        SecurityContextHolder.setContext(result.securityContextMock());
    }



    private static MockedServices getMockedServices() {
        SecurityContext    securityContextMock = Mockito.mock(SecurityContext.class);
        Authentication     authenticationMock  = Mockito.mock(AnonymousAuthenticationToken.class);
        UserDetailsService userDetailsService  = Mockito.mock(UserDetailsService.class);
        return new MockedServices(securityContextMock, authenticationMock, userDetailsService);
    }

    private record MockedServices(SecurityContext securityContextMock, Authentication authenticationMock, UserDetailsService userDetailsService) {
    }


}