package de.adesso.fischereiregister.registerservice.common;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.Reader;
import java.sql.Clob;
import java.sql.SQLException;
import java.util.logging.Level;
import java.util.logging.Logger;

import static java.util.stream.Collectors.joining;

public class ClobUtil {

    // Logger instance
    private static final Logger logger = Logger.getLogger(ClobUtil.class.getName());

    // Private constructor to prevent instantiation
    private ClobUtil() {
        throw new UnsupportedOperationException("Utility class");
    }

    public static String clobToString(Clob clob) {
        try (Reader reader = clob.getCharacterStream(); BufferedReader br = new BufferedReader(reader)) {
            return br.lines().collect(joining(System.lineSeparator()));
        } catch (SQLException | IOException e) {
            logger.log(Level.SEVERE, "Error converting CLOB to String", e);
            return "";
        }
    }
}
