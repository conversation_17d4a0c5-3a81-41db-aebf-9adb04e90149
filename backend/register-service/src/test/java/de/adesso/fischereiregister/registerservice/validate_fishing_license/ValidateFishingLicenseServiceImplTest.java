package de.adesso.fischereiregister.registerservice.validate_fishing_license;

import de.adesso.fischereiregister.core.model.Ban;
import de.adesso.fischereiregister.core.model.Birthdate;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.ports.InspectorProtocolServicePort;
import de.adesso.fischereiregister.registerservice.common.TestDataUtil;
import de.adesso.fischereiregister.registerservice.identification_document_view.IdentificationDocumentView;
import de.adesso.fischereiregister.registerservice.identification_document_view.IdentificationDocumentViewService;
import de.adesso.fischereiregister.registerservice.register_entry_view.RegisterEntryView;
import de.adesso.fischereiregister.registerservice.register_entry_view.RegisterEntryViewService;
import de.adesso.fischereiregister.registerservice.security.HashingAdapter;
import de.adesso.fischereiregister.registerservice.security.UserDetailsService;
import de.adesso.fischereiregister.registerservice.tenant.TenantConfigurationServiceImpl;
import de.adesso.fischereiregister.registerservice.validate_fishing_license.model.FishingLicenseValidationResult;
import de.adesso.fischereiregister.registerservice.validate_fishing_license.model.FishingLicenseValidationStatus;
import de.adesso.fischereiregister.utils.DateUtils;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ValidateFishingLicenseServiceImplTest {

    @InjectMocks
    private ValidateFishingLicenseServiceImpl service;

    @Mock
    private UserDetailsService userDetailsService;

    @Mock
    HashingAdapter hashingAdapter;

    @Mock
    private RegisterEntryViewService registerEntryViewService;

    @Mock
    private IdentificationDocumentViewService identificationDocumentViewService;

    @Mock
    private TenantConfigurationServiceImpl tenantConfigurationService;

    @Mock
    private InspectorProtocolServicePort inspectorProtocolServicePort;

    @Test
    void testIsBanned_ShouldReturnTrueForPermanentBan() {
        // Arrange
        var ban = new Ban();
        ban.setFrom(LocalDate.of(2022, 1, 1));
        ban.setTo(null);

        // Act
        boolean result = service.isBanned(ban);

        // Assert
        assertTrue(result, "Expected the license to be banned when the ban end date is null (permanent ban).");
    }

    @Test
    void testIsBanned_ShouldReturnFalseWhenNoBanStartDate() {
        // Arrange
        var ban = new Ban();
        ban.setFrom(null);
        ban.setTo(null);

        // Act
        boolean result = service.isBanned(ban);

        // Assert
        assertFalse(result, "Expected the license not to be banned when there is no ban start date.");
    }


    @Test
    void testHashMatches_ShouldReturnTrueForValidHash() {
        // Arrange
        RegisterEntry view = new RegisterEntry();
        view.setRegisterId(UUID.fromString("e4b6af13-1feb-4a4d-9c46-76298a0611cf"));
        view.setPerson(new Person());
        view.getPerson().setFirstname("John");
        view.getPerson().setLastname("Doe");
        view.getPerson().setBirthdate(Birthdate.parse(LocalDate.of(1990, 1, 1).format(DateUtils.GERMAN_DATE_TIME_FORMATTER)));
        var salt = "someSalt";

        when(hashingAdapter.calculateHash(any(), any(), any())).thenReturn("");
        // Act
        boolean result = service.hashMatches(view, salt, "");

        // Assert
        assertTrue(result, "Expected the hash to match the provided value.");
    }


    @Test
    @DisplayName("ValidateFishingLicenseServiceImpl.validateRegister should classify license invalid because no fishingLicense is set in the identification documents")
    void testValidateFishingLicenseNoFishingLicense() {
        //GIVEN
        final RegisterEntryView registerEntryView = TestDataUtil.createRegisterEntryView();
        final IdentificationDocumentView identificationDocumentView = TestDataUtil.createIdentificationDocumentView();
        final String hash = "";

        registerEntryView.getData().getIdentificationDocuments().getFirst().setFishingLicense(null);

        when(hashingAdapter.calculateHash(any(), any(), any())).thenReturn("");
        when(userDetailsService.getFederalState()).thenReturn(TestDataUtil.testFederalState);
        when(identificationDocumentViewService.getIdentificationDocumentView(identificationDocumentView.getIdentificationDocumentId())).thenReturn(identificationDocumentView);
        when(registerEntryViewService.findByRegisterId(registerEntryView.getRegisterId())).thenReturn(registerEntryView);
        when(tenantConfigurationService.getValue(FederalState.valueOf(TestDataUtil.testFederalState), ValidateFishingLicenseServiceImpl.LICENSE_NOT_FOUND_NOTE)).thenReturn("License Note");
        //WHEN
        final FishingLicenseValidationResult result = service.validateFishingLicense(identificationDocumentView.getLicenseNumber(),
                identificationDocumentView.getIdentificationDocumentId(), hash);
        //THEN
        assertEquals(FishingLicenseValidationStatus.INVALID, result.getLicenseStatus());
    }

    @Test
    @DisplayName("ValidateFishingLicenseServiceImpl.validateRegister should classify license invalid because validity is expired")
    void testValidateFishingLicenseFishingLicenseExpired() {
        //GIVEN
        final RegisterEntryView registerEntryView = TestDataUtil.createRegisterEntryView();
        final IdentificationDocumentView identificationDocumentView = TestDataUtil.createIdentificationDocumentView();
        final String hash = "";
        final String federalState = "SH";

        final FishingLicense fishingLicense = registerEntryView.getData()
                .getIdentificationDocuments()
                .getFirst()
                .getFishingLicense();

        fishingLicense.getValidityPeriods().clear();
        fishingLicense.getValidityPeriods().add(
                TestDataUtil.createValidityPeriod(LocalDate.now().minusYears(2),
                        LocalDate.now().minusYears(1)));

        when(hashingAdapter.calculateHash(any(), any(), any())).thenReturn("");
        when(userDetailsService.getFederalState()).thenReturn(federalState);
        when(identificationDocumentViewService.getIdentificationDocumentView(identificationDocumentView.getIdentificationDocumentId())).thenReturn(identificationDocumentView);
        when(registerEntryViewService.findByRegisterId(registerEntryView.getRegisterId())).thenReturn(registerEntryView);
        //WHEN
        final FishingLicenseValidationResult result = service.validateFishingLicense(identificationDocumentView.getLicenseNumber(),
                identificationDocumentView.getIdentificationDocumentId(), hash);
        //THEN
        assertEquals(FishingLicenseValidationStatus.INVALID, result.getLicenseStatus());
    }

    @Test
    @DisplayName("ValidateFishingLicenseServiceImpl.validateRegister should classify license invalid because it has no validityPeriod for the given federal State")
    void testValidateFishingLicenseFishingLicenseHasNoValidityForCurrentFederalState() {
        //GIVEN
        final RegisterEntryView registerEntryView = TestDataUtil.createRegisterEntryView();
        final IdentificationDocumentView identificationDocumentView = TestDataUtil.createIdentificationDocumentView();
        final String hash = "";

        final FishingLicense fishingLicense = registerEntryView.getData()
                .getIdentificationDocuments()
                .getFirst()
                .getFishingLicense();

        fishingLicense.getValidityPeriods().clear();
        fishingLicense.getValidityPeriods().add(TestDataUtil.createValidityPeriod(LocalDate.now().minusYears(2),
                        LocalDate.now().minusYears(1)));

        when(userDetailsService.getFederalState()).thenReturn("BE");
        when(hashingAdapter.calculateHash(any(), any(), any())).thenReturn("");
        when(identificationDocumentViewService.getIdentificationDocumentView(identificationDocumentView.getIdentificationDocumentId())).thenReturn(identificationDocumentView);
        when(registerEntryViewService.findByRegisterId(registerEntryView.getRegisterId())).thenReturn(registerEntryView);
        //WHEN
        final FishingLicenseValidationResult result = service.validateFishingLicense(identificationDocumentView.getLicenseNumber(),
                identificationDocumentView.getIdentificationDocumentId(), hash);
        //THEN
        assertEquals(FishingLicenseValidationStatus.INVALID, result.getLicenseStatus());
    }

    @Test
    @DisplayName("ValidateFishingLicenseServiceImpl.validateRegister should classify license as BANNED")
    void testValidateFishingLicenseFishingLicenseBanned() {
        //GIVEN
        final RegisterEntryView registerEntryView = TestDataUtil.createRegisterEntryView();
        final IdentificationDocumentView identificationDocumentView = TestDataUtil.createIdentificationDocumentView();
        final String hash = "";
        final String federalState = "SH";
        final Ban ban = new Ban();

        final FishingLicense fishingLicense = registerEntryView.getData()
                .getIdentificationDocuments()
                .getFirst()
                .getFishingLicense();

        fishingLicense.getValidityPeriods().clear();
        fishingLicense.getValidityPeriods().add(
                TestDataUtil.createValidityPeriod(LocalDate.now().minusYears(1),
                        LocalDate.now().plusYears(1)));

        ban.setFrom(LocalDate.now().minusYears(1));
        ban.setTo(LocalDate.now().plusYears(1));
        registerEntryView.getData().setBan(ban);

        when(hashingAdapter.calculateHash(any(), any(), any())).thenReturn("");
        when(userDetailsService.getFederalState()).thenReturn(federalState);
        when(identificationDocumentViewService.getIdentificationDocumentView(identificationDocumentView.getIdentificationDocumentId())).thenReturn(identificationDocumentView);
        when(registerEntryViewService.findByRegisterId(registerEntryView.getRegisterId())).thenReturn(registerEntryView);
        //WHEN
        final FishingLicenseValidationResult result = service.validateFishingLicense(identificationDocumentView.getLicenseNumber(),
                identificationDocumentView.getIdentificationDocumentId(), hash);
        //THEN
        assertEquals(FishingLicenseValidationStatus.BANNED, result.getLicenseStatus());
    }

    @Test
    @DisplayName("ValidateFishingLicenseServiceImpl.validateRegister should classify license as valid")
    void testValidateFishingLicenseValidLicense() {
        //GIVEN
        final RegisterEntryView registerEntryView = TestDataUtil.createRegisterEntryView();
        final IdentificationDocumentView identificationDocumentView = TestDataUtil.createIdentificationDocumentView();
        final String hash = "";
        final String federalState = "SH";

        final FishingLicense fishingLicense = registerEntryView.getData()
                .getIdentificationDocuments()
                .getFirst()
                .getFishingLicense();

        fishingLicense.getValidityPeriods().clear();
        fishingLicense.getValidityPeriods().add(
                TestDataUtil.createValidityPeriod(LocalDate.now().minusYears(1),
                        LocalDate.now().plusYears(1)));

        when(hashingAdapter.calculateHash(any(), any(), any())).thenReturn("");
        when(userDetailsService.getFederalState()).thenReturn(federalState);
        when(identificationDocumentViewService.getIdentificationDocumentView(identificationDocumentView.getIdentificationDocumentId())).thenReturn(identificationDocumentView);
        when(registerEntryViewService.findByRegisterId(registerEntryView.getRegisterId())).thenReturn(registerEntryView);
        //WHEN
        final FishingLicenseValidationResult result = service.validateFishingLicense(identificationDocumentView.getLicenseNumber(),
                identificationDocumentView.getIdentificationDocumentId(), hash);
        //THEN
        assertEquals(FishingLicenseValidationStatus.VALID, result.getLicenseStatus());
    }


    @Test
    @DisplayName("ValidateFishingLicenseServiceImpl.validateRegister should classify missing taxes as invalid")
    void testValidateFishingLicenseNoTaxes() {
        //GIVEN
        final RegisterEntryView registerEntryView = TestDataUtil.createRegisterEntryView();
        final IdentificationDocumentView identificationDocumentView = TestDataUtil.createIdentificationDocumentView();
        final String hash = "";
        final String federalState = "SH";

        registerEntryView.getData().getTaxes().clear();

        when(hashingAdapter.calculateHash(any(), any(), any())).thenReturn("");
        when(userDetailsService.getFederalState()).thenReturn(federalState);
        when(identificationDocumentViewService.getIdentificationDocumentView(identificationDocumentView.getIdentificationDocumentId())).thenReturn(identificationDocumentView);
        when(registerEntryViewService.findByRegisterId(registerEntryView.getRegisterId())).thenReturn(registerEntryView);
        //WHEN
        final FishingLicenseValidationResult result = service.validateFishingLicense(identificationDocumentView.getLicenseNumber(),
                identificationDocumentView.getIdentificationDocumentId(), hash);
        //THEN
        assertEquals(FishingLicenseValidationStatus.VALID, result.getLicenseStatus());
        assertFalse(result.getTaxValid());
    }

    @Test
    @DisplayName("ValidateFishingLicenseServiceImpl.validateRegister should classify missing taxes as invalid")
    void testValidateFishingLicenseEmptyTaxes() {
        //GIVEN
        final RegisterEntryView registerEntryView = TestDataUtil.createRegisterEntryView();
        final IdentificationDocumentView identificationDocumentView = TestDataUtil.createIdentificationDocumentView();
        final String hash = "";
        final String federalState = "SH";

        registerEntryView.getData().getTaxes().clear();

        when(hashingAdapter.calculateHash(any(), any(), any())).thenReturn("");
        when(userDetailsService.getFederalState()).thenReturn(federalState);
        when(identificationDocumentViewService.getIdentificationDocumentView(identificationDocumentView.getIdentificationDocumentId())).thenReturn(identificationDocumentView);
        when(registerEntryViewService.findByRegisterId(registerEntryView.getRegisterId())).thenReturn(registerEntryView);
        //WHEN
        final FishingLicenseValidationResult result = service.validateFishingLicense(identificationDocumentView.getLicenseNumber(),
                identificationDocumentView.getIdentificationDocumentId(), hash);
        //THEN
        assertEquals(FishingLicenseValidationStatus.VALID, result.getLicenseStatus());
        assertFalse(result.getTaxValid());
    }

    @Test
    @DisplayName("ValidateFishingLicenseServiceImpl.validateRegister should classify taxes as invalid because the federal state does not match")
    void testValidateFishingLicenseWrongFederalStateForTaxes() {
        //GIVEN
        final RegisterEntryView registerEntryView = TestDataUtil.createRegisterEntryView();
        final IdentificationDocumentView identificationDocumentView = TestDataUtil.createIdentificationDocumentView();
        final String hash = "";
        final String federalState = "SH";


        registerEntryView.getData().getTaxes().getFirst().setFederalState("BE");


        when(hashingAdapter.calculateHash(any(), any(), any())).thenReturn("");
        when(userDetailsService.getFederalState()).thenReturn(federalState);
        when(identificationDocumentViewService.getIdentificationDocumentView(identificationDocumentView.getIdentificationDocumentId())).thenReturn(identificationDocumentView);
        when(registerEntryViewService.findByRegisterId(registerEntryView.getRegisterId())).thenReturn(registerEntryView);
        //WHEN
        final FishingLicenseValidationResult result = service.validateFishingLicense(identificationDocumentView.getLicenseNumber(),
                identificationDocumentView.getIdentificationDocumentId(), hash);
        //THEN
        assertEquals(FishingLicenseValidationStatus.VALID, result.getLicenseStatus());
        assertFalse(result.getTaxValid());
    }

    @Test
    @DisplayName("ValidateFishingLicenseServiceImpl.validateRegister should classify taxes as invalid because the federal state does not match")
    void testValidateFishingLicenseTaxesExpired() {
        //GIVEN
        final RegisterEntryView registerEntryView = TestDataUtil.createRegisterEntryView();
        final IdentificationDocumentView identificationDocumentView = TestDataUtil.createIdentificationDocumentView();
        final String hash = "";
        final String federalState = "SH";


        registerEntryView.getData().getTaxes().getFirst().setValidTo(LocalDate.now().minusYears(1));


        when(hashingAdapter.calculateHash(any(), any(), any())).thenReturn("");
        when(userDetailsService.getFederalState()).thenReturn(federalState);
        when(identificationDocumentViewService.getIdentificationDocumentView(identificationDocumentView.getIdentificationDocumentId())).thenReturn(identificationDocumentView);
        when(registerEntryViewService.findByRegisterId(registerEntryView.getRegisterId())).thenReturn(registerEntryView);
        //WHEN
        final FishingLicenseValidationResult result = service.validateFishingLicense(identificationDocumentView.getLicenseNumber(),
                identificationDocumentView.getIdentificationDocumentId(), hash);
        //THEN
        assertFalse(result.getTaxValid());
    }

    @Test
    @DisplayName("ValidateFishingLicenseServiceImpl.validateRegister should classify taxes as valid")
    void testValidateFishingLicenseValidTaxes() {
        //GIVEN
        final RegisterEntryView registerEntryView = TestDataUtil.createRegisterEntryView();
        final IdentificationDocumentView identificationDocumentView = TestDataUtil.createIdentificationDocumentView();
        final String hash = "";
        final String federalState = "SH";


        when(hashingAdapter.calculateHash(any(), any(), any())).thenReturn("");
        when(userDetailsService.getFederalState()).thenReturn(federalState);
        when(identificationDocumentViewService.getIdentificationDocumentView(identificationDocumentView.getIdentificationDocumentId())).thenReturn(identificationDocumentView);
        when(registerEntryViewService.findByRegisterId(registerEntryView.getRegisterId())).thenReturn(registerEntryView);
        //WHEN
        final FishingLicenseValidationResult result = service.validateFishingLicense(identificationDocumentView.getLicenseNumber(),
                identificationDocumentView.getIdentificationDocumentId(), hash);
        //THEN
        assertTrue(result.getTaxValid());
    }


    @Test
    void shouldReturnRegularLicenseBecauseTheVacationLicenseIsForAnOtherFederalState() {
        // Given: A register entry with TWO valid fishing licenses, but one is vacation and its for an other federal state
        FishingLicense license1 = TestDataUtil.createFishingLicense();
        license1.setNumber("license1");
        FishingLicense license2 = TestDataUtil.createFishingLicense();
        license2.setNumber("license2");
        license2.setType(LicenseType.VACATION);
        license2.setIssuingFederalState(FederalState.BE);
        final String hash = "";
        final String federalState = TestDataUtil.testFederalState;
        final String salt = "salt";

        IdentificationDocument doc1 = TestDataUtil.createIdentificationDocumentPDF("doc1");
        doc1.setFishingLicense(license1);
        IdentificationDocument doc2 = TestDataUtil.createIdentificationDocumentPDF("doc2");
        doc2.setFishingLicense(license2);

        Person person = TestDataUtil.createPerson();

        RegisterEntry registerEntry1 = new RegisterEntry();
        registerEntry1.setRegisterId(TestDataUtil.registerId);
        registerEntry1.setPerson(person);
        registerEntry1.getFishingLicenses().add(license1);
        registerEntry1.getFishingLicenses().add(license2);
        registerEntry1.getIdentificationDocuments().add(doc2);
        registerEntry1.getIdentificationDocuments().add(doc1);

        final RegisterEntryView registerEntryView = new RegisterEntryView();
        registerEntryView.setData(registerEntry1);
        registerEntryView.setRegisterId(registerEntry1.getRegisterId());
        final IdentificationDocumentView identificationDocumentView = new IdentificationDocumentView();
        identificationDocumentView.setIdentificationDocumentId(doc2.getDocumentId());
        identificationDocumentView.setSalt(salt);
        identificationDocumentView.setRegisterId(registerEntry1.getRegisterId());
        identificationDocumentView.setLicenseNumber(license2.getLegacyNumber());

        when(hashingAdapter.calculateHash(any(), any(), any())).thenReturn("");
        when(userDetailsService.getFederalState()).thenReturn(federalState);
        when(identificationDocumentViewService.getIdentificationDocumentView(doc2.getDocumentId())).thenReturn(identificationDocumentView);
        when(registerEntryViewService.findByRegisterId(identificationDocumentView.getRegisterId())).thenReturn(registerEntryView);
        // When
        FishingLicenseValidationResult result = service.validateFishingLicense(license2.getNumber(), doc2.getDocumentId(), hash);

        // Then
        assertTrue(license1.getNumber().equalsIgnoreCase(result.getLicenseNumber()));
        assertEquals(LicenseType.REGULAR, result.getLicenseType());
        assertEquals(FishingLicenseValidationStatus.VALID, result.getLicenseStatus());
        assertEquals("", result.getLicenseNote());
    }

    @Test
    void shouldReturnNoteWhenLicenseNotFound() {
        // Given: A register entry with NO valid fishing license
        final RegisterEntryView registerEntryView = TestDataUtil.createRegisterEntryView();
        registerEntryView.getData().getFishingLicenses().clear();
        registerEntryView.getData().getIdentificationDocuments().clear();

        final IdentificationDocumentView identificationDocumentView = TestDataUtil.createIdentificationDocumentView();
        final String hash = "";
        final String federalState = "BE";


        when(hashingAdapter.calculateHash(any(), any(), any())).thenReturn("");
        when(userDetailsService.getFederalState()).thenReturn(federalState);
        when(identificationDocumentViewService.getIdentificationDocumentView(identificationDocumentView.getIdentificationDocumentId())).thenReturn(identificationDocumentView);
        when(registerEntryViewService.findByRegisterId(registerEntryView.getRegisterId())).thenReturn(registerEntryView);
        when(tenantConfigurationService.getValue(FederalState.valueOf(federalState), ValidateFishingLicenseServiceImpl.LICENSE_NOT_FOUND_NOTE)).thenReturn("License Note");

        // When
        final FishingLicenseValidationResult result = service.validateFishingLicense(identificationDocumentView.getLicenseNumber(),
                identificationDocumentView.getIdentificationDocumentId(), hash);
        // Then
        assertNull(result.getLicenseNumber()); // No valid license
        assertNotNull(result.getLicenseNote()); // Ensure the note is present
        assertEquals(FishingLicenseValidationStatus.INVALID, result.getLicenseStatus()); // Should be INVALID
    }

    @Test
    void shouldReturnEmptyStringWhenTitleIsNull() {
        // Given
        final RegisterEntryView registerEntryView = TestDataUtil.createRegisterEntryView();
        registerEntryView.getData().getPerson().setTitle(null); // Set title to null
        final IdentificationDocumentView identificationDocumentView = TestDataUtil.createIdentificationDocumentView();
        final String hash = "";
        final String federalState = "SH";
        final String salt = identificationDocumentView.getSalt();

        // Mock the hashingAdapter to return the same hash that we are passing in
        when(hashingAdapter.calculateHash(any(), any(), any())).thenReturn("");
        when(userDetailsService.getFederalState()).thenReturn(federalState);
        when(identificationDocumentViewService.getIdentificationDocumentView(identificationDocumentView.getIdentificationDocumentId())).thenReturn(identificationDocumentView);
        when(registerEntryViewService.findByRegisterId(registerEntryView.getRegisterId())).thenReturn(registerEntryView);

        // When
        final FishingLicenseValidationResult result = service.validateFishingLicense(identificationDocumentView.getLicenseNumber(),
                identificationDocumentView.getIdentificationDocumentId(), hash);

        // Then
        assertNotNull(result.getTitle());
        assertEquals("", result.getTitle());
    }

    @Test
    void testIsLicenseCurrentlyValidInFederalState_ValidRegularLicense() {
        FishingLicense license = new FishingLicense();
        license.setType(de.adesso.fischereiregister.core.model.type.LicenseType.REGULAR);
        license.getValidityPeriods().add(TestDataUtil.createValidityPeriod(LocalDate.now().minusDays(1), LocalDate.now().plusDays(1)));

        assertTrue(service.isLicenseCurrentlyValidInFederalState("SH", license));
    }

    @Test
    void testIsLicenseCurrentlyValidInFederalState_ValidVacationLicense() {
        FishingLicense license = new FishingLicense();
        license.setType(LicenseType.VACATION);
        license.setIssuingFederalState(FederalState.SH);
        license.getValidityPeriods().add(TestDataUtil.createValidityPeriod(LocalDate.now().minusDays(1), LocalDate.now().plusDays(1)));

        assertTrue(service.isLicenseCurrentlyValidInFederalState("SH", license));
    }

    @Test
    void testIsLicenseCurrentlyValidInFederalState_InvalidVacationLicenseWrongState() {
        FishingLicense license = new FishingLicense();
        license.setType(LicenseType.VACATION);
        license.setIssuingFederalState(FederalState.BE);
        license.getValidityPeriods().add(TestDataUtil.createValidityPeriod(LocalDate.now().minusDays(1), LocalDate.now().plusDays(1)));

        assertFalse(service.isLicenseCurrentlyValidInFederalState("SH", license));
    }

    @Test
    void testIsLicenseCurrentlyValidInFederalState_InvalidLicenseExpired() {
        FishingLicense license = new FishingLicense();
        license.setType(de.adesso.fischereiregister.core.model.type.LicenseType.REGULAR);
        license.getValidityPeriods().add(TestDataUtil.createValidityPeriod(LocalDate.now().minusDays(10), LocalDate.now().minusDays(1)));

        assertFalse(service.isLicenseCurrentlyValidInFederalState("SH", license));
    }

    @Test
    void testIsLicenseCurrentlyValidInFederalState_InvalidLicenseStartsInFuture() {
        FishingLicense license = new FishingLicense();
        license.setType(LicenseType.REGULAR);
        license.getValidityPeriods().add(TestDataUtil.createValidityPeriod(LocalDate.now().plusDays(1), LocalDate.now().plusDays(10)));

        assertFalse(service.isLicenseCurrentlyValidInFederalState("SH", license));
    }

    @Test
    @DisplayName("validateFishingLicense should create a protocol entry with registerEntryId of it exists")
    void shouldLogAlsoTheRegisterEntryWhenItExists() {
        // Given
        final RegisterEntryView registerEntryView = TestDataUtil.createRegisterEntryView();
        registerEntryView.getData().getPerson().setTitle(null); // Set title to null
        final IdentificationDocumentView identificationDocumentView = TestDataUtil.createIdentificationDocumentView();
        final String hash = "";
        final String federalState = "SH";
        final String salt = identificationDocumentView.getSalt();

        // Mock the hashingAdapter to return the same hash that we are passing in
        when(hashingAdapter.calculateHash(any(), any(), any())).thenReturn("");
        when(userDetailsService.getFederalState()).thenReturn(federalState);
        when(identificationDocumentViewService.getIdentificationDocumentView(identificationDocumentView.getIdentificationDocumentId())).thenReturn(identificationDocumentView);
        when(registerEntryViewService.findByRegisterId(registerEntryView.getRegisterId())).thenReturn(registerEntryView);

        // When
        final FishingLicenseValidationResult result = service.validateFishingLicense(identificationDocumentView.getLicenseNumber(),
                identificationDocumentView.getIdentificationDocumentId(), hash);


        verify(inspectorProtocolServicePort).createProtocolEntry(
                any(), // userId
                eq(identificationDocumentView.getRegisterId().toString()), // registerEntryId
                any(), // federalState
                any() // timestamp
        );

        // Then
        assertNotNull(result.getTitle());
        assertEquals("", result.getTitle());
    }

}