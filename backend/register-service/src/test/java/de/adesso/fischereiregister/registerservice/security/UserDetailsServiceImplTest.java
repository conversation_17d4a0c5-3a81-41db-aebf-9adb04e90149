package de.adesso.fischereiregister.registerservice.security;


import de.adesso.fischereiregister.core.model.user.UserDetails;
import de.adesso.fischereiregister.registerservice.common.StringInjectionUtil;
import de.adesso.fischereiregister.registerservice.common.security.mocking.SecurityContextConstants;
import de.adesso.fischereiregister.registerservice.common.security.mocking.WithMockSecurityContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

@WithMockSecurityContext()
public class UserDetailsServiceImplTest {

    private final UserDetailsServiceImpl service = new UserDetailsServiceImpl();

    @BeforeEach
    void setUp() {
        //Given
        StringInjectionUtil.injectPrivateField(service,"idClaimIdentifier", "sub" );
        StringInjectionUtil.injectPrivateField(service, "officeAddressClaimIdentifier", "officeAddress");
        StringInjectionUtil.injectPrivateField(service, "examinationClaimIdentifier", "examination");
        StringInjectionUtil.injectPrivateField(service, "issuerClaimIdentifier", "issuer");
    }

    @Test
    @DisplayName("userDetailsService.getUserId()  correctly reads user id from auth information")
    void testGetUserId() {
        //When
        Optional<String> receivedUserId = service.getUserId();
        //Then
        assertTrue(receivedUserId.isPresent());
        assertEquals(SecurityContextConstants.MOCK_USER_ID, receivedUserId.get());

    }


    @Test
    @DisplayName("userDetailsService.getCertificationIssuer() correctly reads organisation from auth information")
    void testGetCertificationIssuer() {
        //When
        Optional<String> receivedOrganisation = service.getCertificationIssuer();
        //Then
        assertTrue(receivedOrganisation.isPresent());
        assertEquals(SecurityContextConstants.MOCK_CERTIFICATION_ISSUER, receivedOrganisation.get());

    }

    @Test
    @DisplayName("userDetailsService.getOffice() correctly reads office from auth information")
    void testGetOffice() {
        //When
        Optional<String> office = service.getOffice();
        //Then
        assertTrue(office.isPresent());

    }

    @Test
    @DisplayName("userDetailsService.getUserDetails() correctly gets the User Details from auth information")
    void testGetUserDetails() {
        //When
        Optional<UserDetails> userDetails = service.getUserDetails();

        //Then
        assertTrue(userDetails.isPresent());
        assertEquals(SecurityContextConstants.MOCK_USER_ID, userDetails.get().getUserId());
        assertEquals(SecurityContextConstants.MOCK_OFFICE_ADDRESS_AS_STRING, userDetails.get().getParsedOfficeAddress());
    }
}
