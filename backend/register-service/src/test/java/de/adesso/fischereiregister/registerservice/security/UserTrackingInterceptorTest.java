package de.adesso.fischereiregister.registerservice.security;

import org.axonframework.commandhandling.CommandMessage;
import org.axonframework.commandhandling.GenericCommandMessage;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.function.BiFunction;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class UserTrackingInterceptorTest {

    @Mock
    UserDetailsService userDetailsService;

    private CommandMessage<CommandMessage<?>> commandMessage = null;
    private List<CommandMessage<CommandMessage<?>>> messages = null;

    @BeforeEach
    public void setUp() throws Exception {
        commandMessage = GenericCommandMessage.asCommandMessage("");
        messages = new ArrayList<>();
        messages.add(commandMessage);
    }


    @Test
    @DisplayName("UserTrackingInterceptor.handle().apply() should throw a NullPointerException if no userDetailsSerivce is provided")
    void noUserService() {
        //Given
        final var interceptor = new UserTrackingInterceptor(null);
        final BiFunction<Integer, CommandMessage<?>, CommandMessage<?>> function = interceptor.handle(messages);


        //When
        //Then
        assertThrows(NullPointerException.class, () ->
                function.apply(0, commandMessage));
    }

    @Test
    @DisplayName("UserTrackingInterceptor.handle().apply() should set the 'SYSTEM' User if no userId could be found.")
    void noLoggedInUser() {
        //Given

        final var interceptor = new UserTrackingInterceptor(userDetailsService);
        final BiFunction<Integer, CommandMessage<?>, CommandMessage<?>> function = interceptor.handle(messages);
        final CommandMessage<?> resultMessage = function.apply(0, commandMessage);


        //When
        final var metadata = resultMessage.getMetaData();
        //Then
        assertEquals("SYSTEM", metadata.get(UserTrackingInterceptor.USER_ID_METADATA_TAG));
        assertTrue(metadata.containsKey(UserTrackingInterceptor.TIMESTAMP_METADATA_TAG));
    }

    @Test
    @DisplayName("UserTrackingInterceptor.handle().apply() should return a CommandMessage with the mockUserId in its metadata")
    void successTest() {
        //Given
        when(userDetailsService.getUserId()).thenReturn(Optional.of("mockUserId"));

        final var interceptor = new UserTrackingInterceptor(userDetailsService);
        final BiFunction<Integer, CommandMessage<?>, CommandMessage<?>> function = interceptor.handle(messages);
        final CommandMessage<?> resultMessage = function.apply(0, commandMessage);

        //When
        final var metadata = resultMessage.getMetaData();
        //Then
        assertEquals("mockUserId", metadata.get(UserTrackingInterceptor.USER_ID_METADATA_TAG));
        assertTrue(metadata.containsKey(UserTrackingInterceptor.TIMESTAMP_METADATA_TAG));

    }
}