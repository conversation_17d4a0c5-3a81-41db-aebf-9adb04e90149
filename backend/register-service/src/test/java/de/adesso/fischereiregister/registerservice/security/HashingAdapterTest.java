package de.adesso.fischereiregister.registerservice.security;

import de.adesso.fischereiregister.core.model.Birthdate;
import de.adesso.fischereiregister.core.model.Person;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.UUID;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class HashingAdapterTest {
    private final HashingAdapter hashingAdapter = new HashingAdapter();

    @ParameterizedTest
    @MethodSource("provideStringsForHashing")
    @DisplayName("hashingAdatper.calculateHash should return the correct SHA-512 hash for the input")
    public void testCalculateHashReturnsCorrectHash(UUID registerId, String firstname, String lastname, Birthdate birthdate, String salt, String expectedHash) {
        // GIVEN
        Person person = new Person();
        person.setFirstname(firstname);
        person.setLastname(lastname);
        person.setBirthdate(birthdate);

        // WHEN
        String hash = hashingAdapter.calculateHash(registerId, person, salt);

        // THEN
        assertEquals(expectedHash, hash);
    }

    private static Stream<Arguments> provideStringsForHashing() {
        UUID registerId1 = UUID.fromString("b77c4f0b-62b6-4884-b078-4e1e5f872193");
        UUID registerId2 = UUID.fromString("b77c4f0b-62b6-4884-b078-4e1e5f872194");

        return Stream.of(
                Arguments.of(registerId1, "Ich1", "Darfalles1", new Birthdate(13, 8, 1997), "e3e939b348", "3a93286091"),
                Arguments.of(registerId1, "Ich2", "Darfalles2", new Birthdate(14, 9, 1998), "e3e939b341", "b09aecdea2"),
                Arguments.of(registerId1, "Ich3", "Darfalles3", new Birthdate(15, 10, 1999), "e3e939b342", "32f4ed00ec"),
                Arguments.of(registerId1, "Ich4", "Darfalles4", new Birthdate(16, 11, 2000), "e3e939b343", "b7475cea4f"),
                Arguments.of(registerId2, "Ich5", "Darfalles5", new Birthdate(17, 12, 2001), "e3e939b344", "954dbfffc7"),
                Arguments.of(registerId2, "Ich6", "Darfalles6", new Birthdate(18, 1, 2002), "e3e939b346", "855580268e"),
                Arguments.of(registerId2, "Ich7", "Darfalles7", new Birthdate(19, 2, 2003), "e3e939b345", "650dd42701"),
                Arguments.of(registerId2, "Ich8", "Darfalles8", new Birthdate(20, 3, 2004), "e3e939b347", "4c287c0129")
        );
    }

    @Test
    @DisplayName("hashingAdatper.getQROrNFCDataForLicense should return the correct qr code data")
    public void testGetQROrNFCDataForLicenseReturnsCorrectData() {
        // GIVEN
        UUID registerId = UUID.fromString("b77c4f0b-62b6-4884-b078-4e1e5f872193");
        String licenseNumber = "SH12345678901234";
        String salt = "e3e939b348";
        String documentId = "doc123";
        Person person = new Person();
        person.setFirstname("Ich1");
        person.setLastname("Darfalles1");
        person.setBirthdate(new Birthdate(13, 8, 1997));

        // WHEN
        String qrCodeData = hashingAdapter.getQROrNFCDataForLicense(registerId, licenseNumber, person, documentId, salt);

        // THEN
        assertEquals("doc123;3a93286091;SH12345678901234", qrCodeData);
    }

    @Test
    @DisplayName("hashingAdatper.getQROrNFCDataForTax should return the correct qr code data")
    public void testGetQROrNFCDataForTaxReturnsCorrectData() {
        // GIVEN
        UUID registerId = UUID.fromString("b77c4f0b-62b6-4884-b078-4e1e5f872193");
        String salt = "e3e939b348";
        String documentId = "doc123";
        Person person = new Person();
        person.setFirstname("Ich1");
        person.setLastname("Darfalles1");
        person.setBirthdate(new Birthdate(13, 8, 1997));

        // WHEN
        String qrCodeData = hashingAdapter.getQROrNFCDataForTax(registerId, person, documentId, salt);

        // THEN
        assertEquals("doc123;3a93286091;", qrCodeData);
    }
}
