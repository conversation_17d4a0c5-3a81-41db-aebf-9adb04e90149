package de.adesso.fischereiregister.registerservice.identification_document_view;

import de.adesso.fischereiregister.core.events.FishingTaxPayedEvent;
import de.adesso.fischereiregister.core.events.JurisdictionMovedEvent;
import de.adesso.fischereiregister.core.events.LimitedLicenseCreatedEvent;
import de.adesso.fischereiregister.core.events.PersonCreatedEvent;
import de.adesso.fischereiregister.core.events.PersonalDataChangedEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseCreatedEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseDigitizedEvent;
import de.adesso.fischereiregister.core.events.ReplacementCardOrderedEvent;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import de.adesso.fischereiregister.registerservice.common.TestDataUtil;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.UUID;

import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class IdentificationDocumentViewHandlerTest {


    @Mock
    IdentificationDocumentViewService service;

    @InjectMocks
    private IdentificationDocumentViewHandler eventHandler;


    @Test
    @DisplayName("IdentificationDocumentViewHandler should correctly handle a FishingLicenseDigitizedEvent")
    void testOnFishingLicenseDigitizedEvent() {


        //GIVEN
        final UUID registerEntryId = UUID.randomUUID();
        final String salt = "salt";
        final List<IdentificationDocument> identificationDocuments = TestDataUtil.createIdentificationDocuments();

        //WHEN
        final RegularLicenseDigitizedEvent event = new RegularLicenseDigitizedEvent(registerEntryId, salt, null, null, null, List.of(), List.of(), List.of(), identificationDocuments, null, null, null);
        eventHandler.on(event);
        //THEN

        verify(service, times(1)).createIdentificationDocumentViews(registerEntryId, salt, identificationDocuments);

    }

    @Test
    @DisplayName("IdentificationDocumentViewHandler should correctly handle a PersonCreatedEvent")
    void testOnPersonCreatedEvent() {


        //GIVEN
        final UUID registerEntryId = UUID.randomUUID();
        final String salt = "salt";
        final List<IdentificationDocument> identificationDocuments = TestDataUtil.createIdentificationDocuments();

        //WHEN
        final PersonCreatedEvent event = new PersonCreatedEvent(
                registerEntryId,
                null,
                List.of(),
                List.of(),
                salt,
                identificationDocuments,
                null,
                null,
                null,
                null,
                null,
                null,
                SubmissionType.ANALOG);
        eventHandler.on(event);
        //THEN

        verify(service, times(1)).createIdentificationDocumentViews(registerEntryId, salt, identificationDocuments);

    }

    @Test
    @DisplayName("IdentificationDocumentViewHandler should correctly handle a JurisdictionMovedEvent")
    void testOnJurisdictionMovedEvent() {


        //GIVEN
        final UUID registerEntryId = UUID.randomUUID();
        final String salt = "salt";
        final List<IdentificationDocument> identificationDocuments = TestDataUtil.createIdentificationDocuments();

        //WHEN
        final JurisdictionMovedEvent event = new JurisdictionMovedEvent(registerEntryId,
                null,
                null,
                null,
                null,
                List.of(),
                salt,
                "office",
                identificationDocuments,
                SubmissionType.ANALOG);

        eventHandler.on(event);
        //THEN

        verify(service, times(1)).createIdentificationDocumentViews(registerEntryId, salt, identificationDocuments);

    }

    @Test
    @DisplayName("IdentificationDocumentViewHandler should correctly handle a FishingTaxPayedEvent")
    void testOnFishingTaxPayedEvent() {


        //GIVEN
        final UUID registerEntryId = UUID.randomUUID();
        final String salt = "salt";
        final List<IdentificationDocument> identificationDocuments = TestDataUtil.createIdentificationDocuments();

        //WHEN
        final FishingTaxPayedEvent event = new FishingTaxPayedEvent(registerEntryId,
                null,
                null,
                List.of(),
                salt,
                identificationDocuments,
                null,
                null,
                null,
                null,
                 SubmissionType.ANALOG);
        eventHandler.on(event);
        //THEN

        verify(service, times(1)).createIdentificationDocumentViews(registerEntryId, salt, identificationDocuments);

    }

    @Test
    @DisplayName("IdentificationDocumentViewHandler should correctly handle a PersonalDataChangedEvent")
    void testOnPersonalDataChangedEvent() {


        //GIVEN
        final UUID registerEntryId = UUID.randomUUID();
        final String salt = "salt";
        final List<IdentificationDocument> identificationDocuments = TestDataUtil.createIdentificationDocuments();


        //WHEN
        final PersonalDataChangedEvent event = new PersonalDataChangedEvent(registerEntryId, null, List.of(), null, salt, identificationDocuments, null);
        eventHandler.on(event);
        //THEN

        verify(service, times(1)).createIdentificationDocumentViews(registerEntryId, salt, identificationDocuments);

    }

    @Test
    @DisplayName("IdentificationDocumentViewHandler should correctly handle a FishingLicenseCreatedEvent")
    void testOnFishingLicenseCreatedEvent() {


        //GIVEN
        final UUID registerEntryId = UUID.randomUUID();
        final String salt = "salt";
        final List<IdentificationDocument> identificationDocuments = TestDataUtil.createIdentificationDocuments();

        //WHEN
        final RegularLicenseCreatedEvent event = new RegularLicenseCreatedEvent(
                registerEntryId,
                salt,
                null,
                null,
                List.of(),
                List.of(),
                null,
                identificationDocuments,
                null,
                null,
                null,
                null,
                null,
                null,
                SubmissionType.ANALOG);
        eventHandler.on(event);
        //THEN

        verify(service, times(1)).createIdentificationDocumentViews(registerEntryId, salt, identificationDocuments);

    }

    @Test
    @DisplayName("IdentificationDocumentViewHandler should correctly handle a ReplacementCardOrderedEvent")
    void testOnReplacementCardOrderedEvent() {


        //GIVEN
        final UUID registerEntryId = UUID.randomUUID();
        final String salt = "salt";
        final List<IdentificationDocument> identificationDocuments = TestDataUtil.createIdentificationDocuments();

        //WHEN
        final ReplacementCardOrderedEvent event = new ReplacementCardOrderedEvent(
                registerEntryId,
                null,
                null,
                identificationDocuments,
                salt,
                null,
                null,
                null,
                List.of(),
                List.of(),
                null,
                null,
                null,
                null,
                SubmissionType.ANALOG);
        eventHandler.on(event);
        //THEN

        verify(service, times(1)).createIdentificationDocumentViews(registerEntryId, salt, identificationDocuments);

    }

    @Test
    @DisplayName("IdentificationDocumentViewHandler should correctly handle a LimitedLicenseCreatedEvent")
    void testOnLimitedLicenseCreatedEvent() {
        //GIVEN
        final UUID registerEntryId = UUID.randomUUID();
        final String salt = "salt";
        final List<IdentificationDocument> identificationDocuments = TestDataUtil.createIdentificationDocuments();

        //WHEN
        FishingLicense fishingLicense = new FishingLicense();
        fishingLicense.setIssuingFederalState(FederalState.valueOf("SH"));
        Jurisdiction jurisdiction = DomainTestData.createJurisdiction();
        jurisdiction.setFederalState("SH");
        //GIVEN

        final LimitedLicenseCreatedEvent event = new LimitedLicenseCreatedEvent(
                registerEntryId,
                salt,
                null,
                null,
                List.of(),
                List.of(),
                fishingLicense,
                identificationDocuments,
                jurisdiction,
                "office",
                "",
                null,
                null,
                null,
                SubmissionType.ANALOG
        );
        eventHandler.on(event);
        //THEN

        verify(service, times(1)).createIdentificationDocumentViews(registerEntryId, salt, identificationDocuments);

    }


}
