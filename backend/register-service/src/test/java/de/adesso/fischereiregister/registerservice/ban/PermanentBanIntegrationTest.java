package de.adesso.fischereiregister.registerservice.ban;

import de.adesso.fischereiregister.RegisterServiceApplication;
import de.adesso.fischereiregister.registerservice.common.security.mocking.WithMockSecurityContext;
import de.adesso.fischereiregister.utils.DateUtils;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.openapitools.model.PermanentBan;
import org.openapitools.model.PermanentBanRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDate;
import java.util.UUID;

import static de.adesso.fischereiregister.registerservice.common.JsonParser.asJsonString;
import static org.hamcrest.Matchers.notNullValue;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;


@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK, classes = RegisterServiceApplication.class)
@AutoConfigureMockMvc(addFilters = false)
@WithMockSecurityContext()
public class PermanentBanIntegrationTest {

    public static final String PATH = "/register-entries/{registerEntryId}/ban/permanent";


    @Autowired
    private MockMvc mockMvc;

    @Test
    @DisplayName("""
                POST "/api/register-entries/{registerEntryId}/ban/permanent"
                Verify that the request is rejected with a 400 if the ban is empty
            """)
    void nullBan() throws Exception {
        //GIVEN

        final String registerEntryId = UUID.randomUUID().toString();

        final String path = PATH.replaceFirst("\\{registerEntryId}", registerEntryId);

        //WHEN
        //THEN
        mockMvc.perform(
                        post(path).contentType(MediaType.APPLICATION_JSON).content(asJsonString("")))
                .andExpect(status().isBadRequest());

    }


    @Test
    @DisplayName("""
                POST "/api/register-entries/{registerEntryId}/ban/permanent"
                Verify that the request is rejected for a ban with empty fields.
            """)
    void simpleBanEmptyPermanentTest() throws Exception {
        //GIVEN

        final String registerEntryId = UUID.randomUUID().toString();
        final PermanentBanRequest banRequest = new PermanentBanRequest();

        final String path = PATH.replaceFirst("\\{registerEntryId}", registerEntryId);


        //WHEN
        //THEN
        mockMvc.perform(
                        post(path).contentType(MediaType.APPLICATION_JSON).content(asJsonString(banRequest)))
                .andExpect(status().is4xxClientError());


    }


    @Test
    @DisplayName("""
                POST "/api/register-entries/{registerEntryId}/ban/permanent"
                Verify that the request is accepted for a simple ban
            """)
    void simplePermanentBanTest() throws Exception {
        //GIVEN

        final String registerEntryId = "e4b6af13-1feb-4a4d-9c46-76298a0611cf";
        final PermanentBan ban = new PermanentBan();
        ban.setFileNumber("fileNumber");
        ban.setReportedBy("reporter");
        ban.setFrom(LocalDate.of(1990, 1, 1).format(DateUtils.GERMAN_DATE_TIME_FORMATTER));

        final PermanentBanRequest banRequest = new PermanentBanRequest();
        banRequest.setBan(ban);

        final String path = PATH.replaceFirst("\\{registerEntryId}", registerEntryId);

        //WHEN
        //THEN
        mockMvc.perform(
                        post(path).contentType(MediaType.APPLICATION_JSON).content(asJsonString(banRequest)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.registerEntryId", Matchers.equalTo(registerEntryId)))
                .andExpect(jsonPath("$.ban", notNullValue()));
    }

    @Test
    @DisplayName("""
                POST "/api/register-entries/{registerEntryId}/ban/permanent"
                Verify that the request is accepted for a simple ban twice on the same registerEntryId
            """)
    void simpleBanUpdateTest() throws Exception {
        //GIVEN

        final String registerEntryId = "e4b6af13-1feb-4a4d-9c46-76298a0611cf";
        final PermanentBan ban1 = new PermanentBan();
        ban1.setFileNumber(UUID.randomUUID().toString());
        ban1.setReportedBy(UUID.randomUUID().toString());
        ban1.setFrom(LocalDate.of(1990, 1, 1).format(DateUtils.GERMAN_DATE_TIME_FORMATTER));

        PermanentBanRequest banRequest1 = new PermanentBanRequest();
        banRequest1.setBan(ban1);

        final PermanentBan ban2 = new PermanentBan();
        ban2.setFileNumber(UUID.randomUUID().toString());
        ban2.setReportedBy(UUID.randomUUID().toString());
        ban2.setFrom(LocalDate.of(1990, 1, 1).format(DateUtils.GERMAN_DATE_TIME_FORMATTER));

        PermanentBanRequest banRequest2 = new PermanentBanRequest();
        banRequest2.setBan(ban2);


        final String path = PATH.replaceFirst("\\{registerEntryId}", registerEntryId);

        //WHEN
        //THEN
        mockMvc.perform(
                        post(path).contentType(MediaType.APPLICATION_JSON).content(asJsonString(banRequest1)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.registerEntryId", notNullValue()))
                .andExpect(jsonPath("$.ban", notNullValue()));

        mockMvc.perform(
                        post(path).contentType(MediaType.APPLICATION_JSON).content(asJsonString(banRequest2)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.registerEntryId", Matchers.equalTo(registerEntryId)))
                .andExpect(jsonPath("$.ban", notNullValue()));


    }


}
