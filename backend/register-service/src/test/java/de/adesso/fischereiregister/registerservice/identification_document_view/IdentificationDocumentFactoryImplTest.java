package de.adesso.fischereiregister.registerservice.identification_document_view;

import de.adesso.fischereiregister.core.interceptables.InterceptableDocumentNumberService;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.type.IdentificationDocumentType;
import de.adesso.fischereiregister.core.model.utils.IdentificationDocumentFactory;
import de.adesso.fischereiregister.core.model.utils.IdentificationDocumentFactoryImpl;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

class IdentificationDocumentFactoryImplTest {


    private static final String identificationDocumentId = "docWithLicense123";
    private static final String fishingLicenseNumber = "XX00-0000-0000-0000";
    private IdentificationDocumentFactory identificationDocumentFactoryImpl = new IdentificationDocumentFactoryImpl(new InterceptableDocumentNumberService());

    @Test
    @DisplayName("identificationDocumentFactory.createIdentificationDocumentForTax should create a valid identification document for a payed tax")
    void testCreateIdentificationDocumentForTax() {
        // Given
        final FishingLicense fishingLicense = new FishingLicense();
        fishingLicense.setNumber(fishingLicenseNumber);

        final Tax tax = new Tax();
        tax.setTaxId(UUID.randomUUID().toString());

        // When
        final IdentificationDocument resultDocument = identificationDocumentFactoryImpl.createIdentificationDocumentForTax(tax, DomainTestData.registerId);

        // Then
        assertNotNull(resultDocument);
        assertNotNull(resultDocument.getDocumentId());
        assertEquals(IdentificationDocumentType.PDF, resultDocument.getType());
        assertEquals(LocalDate.now(), resultDocument.getIssuedDate());
        assertNull(resultDocument.getFishingLicense());
        assertEquals(tax, resultDocument.getTax());
    }

}
