package de.adesso.fischereiregister.registerservice.online_services;

import com.fasterxml.jackson.core.JsonProcessingException;
import de.adesso.fischereiregister.core.events.FishingTaxPayedEvent;
import de.adesso.fischereiregister.core.events.LicenseExtendedEvent;
import de.adesso.fischereiregister.core.events.LimitedLicenseApplicationCreatedEvent;
import de.adesso.fischereiregister.core.events.PersonCreatedEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseCreatedEvent;
import de.adesso.fischereiregister.core.events.ReplacementCardOrderedEvent;
import de.adesso.fischereiregister.core.events.VacationLicenseCreatedEvent;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.registerservice.common.TestDataUtil;
import de.adesso.fischereiregister.registerservice.online_services.eventhandling.MessageServiceHandler;
import de.adesso.fischereiregister.registerservice.online_services.message.OSSuccessMessageService;
import org.axonframework.eventhandling.ReplayStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class MessageServiceHandlerTest {

    @Mock
    private OSSuccessMessageService osSuccessMessageService;

    @InjectMocks
    private MessageServiceHandler messageServiceHandler;

    @Test
    void testOnReplacementCardOrderedEvent_SendsMessage() throws JsonProcessingException {
        ReplacementCardOrderedEvent event = mock(ReplacementCardOrderedEvent.class);
        when(event.inboxReference()).thenReturn("Inbox123");
        when(event.federalState()).thenReturn(FederalState.BB);


        messageServiceHandler.on(event, ReplayStatus.REGULAR);

        verify(osSuccessMessageService).handleLicenseReplacementSuccess(
                eq("Inbox123"), any(), any(), any(), any(), any()
        );
    }

    @Test
    void testOnReplacementCardOrderedEvent_SendsMessageNoInboxId() throws JsonProcessingException {
        ReplacementCardOrderedEvent event = mock(ReplacementCardOrderedEvent.class);
        when(event.inboxReference()).thenReturn("");


        messageServiceHandler.on(event, ReplayStatus.REGULAR);

        verifyNoInteractions(osSuccessMessageService);
    }

    @Test
    void testOnReplacementCardOrderedEvent_SkipsReplay() throws JsonProcessingException {
        ReplacementCardOrderedEvent event = mock(ReplacementCardOrderedEvent.class);

        messageServiceHandler.on(event, ReplayStatus.REPLAY);

        verifyNoInteractions(osSuccessMessageService);
    }

    @Test
    void testOnFishingLicenseCreatedEvent_SkipsReplay() throws JsonProcessingException {
        RegularLicenseCreatedEvent event = mock(RegularLicenseCreatedEvent.class);

        messageServiceHandler.on(event, ReplayStatus.REPLAY);

        verifyNoInteractions(osSuccessMessageService);
    }

    @Test
    void testOnFishingLicenseCreatedEvent_SendsMessage() throws JsonProcessingException {
        RegularLicenseCreatedEvent event = mock(RegularLicenseCreatedEvent.class);
        when(event.inboxReference()).thenReturn("Inbox123");

        Jurisdiction jurisdiction = new Jurisdiction();
        jurisdiction.setFederalState(FederalState.SH.toString());
        when(event.jurisdiction()).thenReturn(jurisdiction);

        messageServiceHandler.on(event, ReplayStatus.REGULAR);

        verify(osSuccessMessageService).handleRegularLicenseCreationSuccess(
                eq("Inbox123"), any(), any(), any(), any(), any()
        );
    }

    @Test
    void testOnFishingLicenseCreatedEvent_InboxEmpty() throws JsonProcessingException {
        RegularLicenseCreatedEvent event = mock(RegularLicenseCreatedEvent.class);
        when(event.inboxReference()).thenReturn("");

        Jurisdiction jurisdiction = new Jurisdiction();
        jurisdiction.setFederalState(FederalState.SH.toString());
        when(event.jurisdiction()).thenReturn(jurisdiction);

        messageServiceHandler.on(event, ReplayStatus.REGULAR);

        verifyNoInteractions(osSuccessMessageService);
    }

    @Test
    void testOnPersonCreatedEvent_SendsMessage() throws JsonProcessingException {
        PersonCreatedEvent event = mock(PersonCreatedEvent.class);
        when(event.inboxReference()).thenReturn("Inbox123");
        when(event.federalState()).thenReturn("SH");

        messageServiceHandler.on(event, ReplayStatus.REGULAR);

        verify(osSuccessMessageService).handleRegularLicenseCreationSuccess(
                eq("Inbox123"), any(), any(), any(), any(), any()
        );
    }

    @Test
    void testOnVacationLicenseCreatedEvent_SendsMessage() throws JsonProcessingException {
        VacationLicenseCreatedEvent event = mock(VacationLicenseCreatedEvent.class);
        when(event.inboxReference()).thenReturn("Inbox123");
        when(event.fishingLicense()).thenReturn(TestDataUtil.createFishingLicense());

        messageServiceHandler.on(event, ReplayStatus.REGULAR);

        verify(osSuccessMessageService).handleVacationLicenseCreationSuccess(
                eq("Inbox123"), any(), any(), any(), any(), any()
        );
    }

    @Test
    void testOnLicenseExtendedEvent_SendsMessage() throws JsonProcessingException {
        LicenseExtendedEvent event = mock(LicenseExtendedEvent.class);
        when(event.inboxReference()).thenReturn("Inbox123");

        messageServiceHandler.on(event, ReplayStatus.REGULAR);

        verify(osSuccessMessageService).handleFishingLicenseExtendedSuccess(
                eq("Inbox123"), any(), any(), any(), any()
        );
    }

    @Test
    void testOnPersonCreatedEvent_SendsMessageNoInboxId() throws JsonProcessingException {
        PersonCreatedEvent event = mock(PersonCreatedEvent.class);

        messageServiceHandler.on(event, ReplayStatus.REPLAY);

        verifyNoInteractions(osSuccessMessageService);
    }

    @Test
    void testOnPersonCreatedEvent_SendsMessageInboxEmpty() throws JsonProcessingException {
        PersonCreatedEvent event = mock(PersonCreatedEvent.class);
        when(event.inboxReference()).thenReturn("");
        when(event.federalState()).thenReturn("SH");

        messageServiceHandler.on(event, ReplayStatus.REGULAR);

        verifyNoInteractions(osSuccessMessageService);
    }

    @Test
    void testOnFishingTaxPayedEvent_SendsMessage() throws JsonProcessingException {
        FishingTaxPayedEvent event = mock(FishingTaxPayedEvent.class);
        when(event.inboxReference()).thenReturn("Inbox123");

        messageServiceHandler.on(event, ReplayStatus.REGULAR);

        verify(osSuccessMessageService).handleTaxPaymentSuccess(
                eq("Inbox123"), any(), any(), any(), any()
        );
    }

    @Test
    void testOnFishingTaxPayedEvent_SendsMessageEmptyInboxId() throws JsonProcessingException {
        FishingTaxPayedEvent event = mock(FishingTaxPayedEvent.class);
        when(event.inboxReference()).thenReturn("");

        messageServiceHandler.on(event, ReplayStatus.REGULAR);

        verifyNoInteractions(osSuccessMessageService);
    }

    @Test
    void testOnFishingTaxPayedEvent_SkipsReplay() throws JsonProcessingException {
        FishingTaxPayedEvent event = mock(FishingTaxPayedEvent.class);

        messageServiceHandler.on(event, ReplayStatus.REPLAY);

        verifyNoInteractions(osSuccessMessageService);
    }

    @Test
    void testOnVacationLicenseCreatedEvent_SkipsReplay() throws JsonProcessingException {
        VacationLicenseCreatedEvent event = mock(VacationLicenseCreatedEvent.class);

        messageServiceHandler.on(event, ReplayStatus.REPLAY);

        verifyNoInteractions(osSuccessMessageService);
    }

    @Test
    void testOnVacationLicenseExtensionEvent_SkipsReplay() throws JsonProcessingException {
        LicenseExtendedEvent event = mock(LicenseExtendedEvent.class);

        messageServiceHandler.on(event, ReplayStatus.REPLAY);

        verifyNoInteractions(osSuccessMessageService);
    }

    @Test
    void testOnVacationLicenseExtensionEvent_SendsMessageEmptyInboxId() throws JsonProcessingException {
        LicenseExtendedEvent event = mock(LicenseExtendedEvent.class);
        when(event.inboxReference()).thenReturn("");

        messageServiceHandler.on(event, ReplayStatus.REGULAR);

        verifyNoInteractions(osSuccessMessageService);
    }

    @Test
    void testOnVacationLicenseCreatedEvent_SendsMessageEmptyInboxId() throws JsonProcessingException {
        VacationLicenseCreatedEvent event = mock(VacationLicenseCreatedEvent.class);
        when(event.inboxReference()).thenReturn("");

        messageServiceHandler.on(event, ReplayStatus.REGULAR);

        verifyNoInteractions(osSuccessMessageService);
    }

    @Test
    @DisplayName("Test LimitedLicenseCreatedEvent does not send message during replay")
    public void testOnLimitedLicenseCreatedEvent_SkipsReplay() throws JsonProcessingException {
        // Arrange
        final de.adesso.fischereiregister.core.events.LimitedLicenseCreatedEvent event = mock(de.adesso.fischereiregister.core.events.LimitedLicenseCreatedEvent.class);

        // Act
        messageServiceHandler.on(event, ReplayStatus.REPLAY);

        // Assert
        verifyNoInteractions(osSuccessMessageService);
    }

    @Test
    @DisplayName("Test LimitedLicenseCreatedEvent does not send message when inboxReference is empty")
    public void testOnLimitedLicenseCreatedEvent_SendsMessageEmptyInboxId() throws JsonProcessingException {
        // Arrange
        final de.adesso.fischereiregister.core.events.LimitedLicenseCreatedEvent event = mock(de.adesso.fischereiregister.core.events.LimitedLicenseCreatedEvent.class);
        when(event.inboxReference()).thenReturn("");

        // Act
        messageServiceHandler.on(event, ReplayStatus.REGULAR);

        // Assert
        verifyNoInteractions(osSuccessMessageService);
    }

    @Test
    @DisplayName("Test LimitedLicenseCreatedEvent sends message when inboxReference is provided")
    public void testOnLimitedLicenseCreatedEvent_SendsMessage() throws JsonProcessingException {
        // Arrange
        final de.adesso.fischereiregister.core.events.LimitedLicenseCreatedEvent event = mock(de.adesso.fischereiregister.core.events.LimitedLicenseCreatedEvent.class);
        when(event.inboxReference()).thenReturn("Inbox123");

        FishingLicense license = new FishingLicense();
        license.setIssuingFederalState(FederalState.SH);
        when(event.fishingLicense()).thenReturn(license);


        // Act
        messageServiceHandler.on(event, ReplayStatus.REGULAR);

        // Assert
        verify(osSuccessMessageService).handleLimitedLicenseCreated(
                eq("Inbox123"), eq(FederalState.SH), any(), any(), any(), any()
        );
    }


    @Test
    @DisplayName("Test LimitedLicenseApplicationCreatedEvent does not send message during replay")
    public void testOnLimitedLicenseApplicationCreatedEvent_SkipsReplay() throws JsonProcessingException {
        // Arrange
        final LimitedLicenseApplicationCreatedEvent event = mock(de.adesso.fischereiregister.core.events.LimitedLicenseApplicationCreatedEvent.class);

        // Act
        messageServiceHandler.on(event, ReplayStatus.REPLAY);

        // Assert
        verifyNoInteractions(osSuccessMessageService);
    }


    @Test
    @DisplayName("Test LimitedLicenseApplicationCreatedEvent does not send message when inboxReference is empty")
    public void testOnLimitedLicenseApplicationCreatedEvent_SendsMessageEmptyInboxId() throws JsonProcessingException {
        // Arrange
        final LimitedLicenseApplicationCreatedEvent event = mock(de.adesso.fischereiregister.core.events.LimitedLicenseApplicationCreatedEvent.class);
        when(event.inboxReference()).thenReturn("");

        // Act
        messageServiceHandler.on(event, ReplayStatus.REGULAR);

        // Assert
        verifyNoInteractions(osSuccessMessageService);
    }

    @Test
    @DisplayName("Test LimitedLicenseApplicationCreatedEvent sends message when inboxReference is provided")
    public void testOnLimitedLicenseApplicationCreatedEvent_SendsMessage() throws JsonProcessingException {
        // Arrange
        final LimitedLicenseApplicationCreatedEvent event = mock(de.adesso.fischereiregister.core.events.LimitedLicenseApplicationCreatedEvent.class);
        when(event.inboxReference()).thenReturn("Inbox123");
        when(event.limitedLicenseApplication()).thenReturn(TestDataUtil.createLimitedLicenseApplication());

        // Act
        messageServiceHandler.on(event, ReplayStatus.REGULAR);

        // Assert
        verify(osSuccessMessageService).handleLimitedLinseApplicationPending(
                eq("Inbox123"), any(), any(), any()
        );
    }

    @Test
    @DisplayName("Test LimitedLicenseApplicationRejectedEvent does not send message during replay")
    public void testOnLimitedLicenseApplicationRejectedEvent_SkipsReplay() throws JsonProcessingException {
        // Arrange
        final de.adesso.fischereiregister.core.events.LimitedLicenseApplicationRejectedEvent event = mock(de.adesso.fischereiregister.core.events.LimitedLicenseApplicationRejectedEvent.class);

        // Act
        messageServiceHandler.on(event, ReplayStatus.REPLAY);

        // Assert
        verifyNoInteractions(osSuccessMessageService);
    }

    @Test
    @DisplayName("Test LimitedLicenseApplicationRejectedEvent sends message when inboxReference is provided")
    public void testOnLimitedLicenseApplicationRejectedEvent_SendsMessage() throws JsonProcessingException {
        // Arrange
        final de.adesso.fischereiregister.core.events.LimitedLicenseApplicationRejectedEvent event = mock(de.adesso.fischereiregister.core.events.LimitedLicenseApplicationRejectedEvent.class);
        when(event.inboxReference()).thenReturn("Inbox123");
        when(event.limitedLicenseApplication()).thenReturn(TestDataUtil.createLimitedLicenseApplication());

        // Act
        messageServiceHandler.on(event, ReplayStatus.REGULAR);

        // Assert
        verify(osSuccessMessageService).handleLimitedLicenseApplicationRejected(
                eq("Inbox123"), any(), any(), any()
        );
    }
}
