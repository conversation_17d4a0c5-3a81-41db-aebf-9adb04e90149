package de.adesso.fischereiregister.registerservice.validate_fishing_license;

import de.adesso.fischereiregister.core.model.Ban;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

class InspectorViewServiceImplTest {

    private final ValidateFishingLicenseServiceImpl service = new ValidateFishingLicenseServiceImpl(null, null, null, null, null, null);

    @Test
    void testIsBanned_PermanentBan() {
        // Arrange
        Ban ban = new Ban();
        ban.setFrom(LocalDate.of(2022, 1, 1));
        ban.setTo(null);

        // Act
        boolean isBanned = service.isBanned(ban);

        // Assert
        assertTrue(isBanned, "License should be banned if there is a permanent ban (no end date).");
    }

    @Test
    void testIsBanned_BanWithEndDateInFuture() {
        // Arrange
        Ban ban = new Ban();
        ban.setFrom(LocalDate.MIN);
        ban.setTo(LocalDate.MAX);

        // Act
        boolean isBanned = service.isBanned(ban);

        // Assert
        assertTrue(isBanned, "License should be banned if ban end date is in the future.");
    }

    @Test
    void testIsBanned_BanWithEndDateInPast() {
        // Arrange
        Ban ban = new Ban();
        ban.setFrom(LocalDate.of(2022, 1, 1));
        ban.setTo(LocalDate.of(2022, 12, 31));

        // Act
        boolean isBanned = service.isBanned(ban);

        // Assert
        assertFalse(isBanned, "License should not be banned if ban end date is in the past.");
    }
}
