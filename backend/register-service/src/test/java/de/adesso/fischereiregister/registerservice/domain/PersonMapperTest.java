package de.adesso.fischereiregister.registerservice.domain;

import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.registerservice.domain.mapper.PersonMapper;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertNotNull;

public class PersonMapperTest {

    @DisplayName("PersonMapperImps.toPerson should properly convert")
    @Test
    public void testToPerson() {
        //GIVEN
        final org.openapitools.model.Person person = new org.openapitools.model.Person();
        person.setBirthdate("00.00.2000");
        //WHEN
        final Person domainPerson = PersonMapper.INSTANCE.toPerson(person);
        //THEN
        assertNotNull(domainPerson);

    }


}
