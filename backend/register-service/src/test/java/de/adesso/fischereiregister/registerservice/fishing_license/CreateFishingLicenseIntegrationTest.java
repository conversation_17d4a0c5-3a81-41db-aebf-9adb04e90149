package de.adesso.fischereiregister.registerservice.fishing_license;

import de.adesso.fischereiregister.RegisterServiceApplication;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.registerservice.common.TestDataUtil;
import de.adesso.fischereiregister.registerservice.common.security.mocking.WithMockSecurityContext;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.openapitools.model.CreateRegularFishingLicenseRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import static de.adesso.fischereiregister.registerservice.common.JsonParser.asJsonString;
import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.notNullValue;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.header;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK, classes = RegisterServiceApplication.class)
@AutoConfigureMockMvc(addFilters = false)
@WithMockSecurityContext()
public class CreateFishingLicenseIntegrationTest {

    @Autowired
    private MockMvc mvc;

    @Test
    @DisplayName("""
            	POST /api/register-entries/{registerEntryId}/fishing-licenses
            	Verify that the create license endpoint returns a 200 OK status code.
            """)
    void createFishingLicenseTest() throws Exception {
        final String registerId = "afe495b5-b82f-494f-b972-cc5dbf76c9c2";
        final CreateRegularFishingLicenseRequest request = TestDataUtil.createRegularFishingLicenseRequest();

        mvc.perform(MockMvcRequestBuilders
                        .post("http://localhost:8080/register-entries/" + registerId + "/fishing-licenses/regular")
                        .content(asJsonString(request))  // Pass valid JSON data
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isCreated())
                .andExpect(header().string("Location", Matchers.matchesPattern(
                        "/api/register-entries/[0-9a-fA-F\\-]{36}/fishing-licenses/[A-Z0-9]+"
                )))
                .andExpect(jsonPath("$.documents", hasSize(request.getTaxes().size() + 2)))//taxes plus we expect CARD AND PDF License
                .andExpect(jsonPath("$.person.firstname", Matchers.containsString(request.getPerson().getFirstname())))
                .andExpect(jsonPath("$.fishingLicense.type", Matchers.containsString(LicenseType.REGULAR.toString())))
                .andExpect(jsonPath("$.registerEntryId", notNullValue()));
    }

}
