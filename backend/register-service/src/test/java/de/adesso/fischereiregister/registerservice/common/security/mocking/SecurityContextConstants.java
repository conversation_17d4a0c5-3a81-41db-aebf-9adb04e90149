package de.adesso.fischereiregister.registerservice.common.security.mocking;

import java.util.HashMap;
import java.util.Map;

public class SecurityContextConstants {

    public static final String MOCK_USER_ID = "01234567-0000-0000-0000-000000000000";
    public static final String MOCK_FEDERAL_STATE = "SH";
    public static final String MOCK_OFFICE_ADDRESS_AS_STRING = "Fischereibehörde Kiel Fleethörn 29-31 24103 Kiel";
    public static final String USER_ID_CLAIM_IDENTIFIER = "sub";
    public static final String FEDERAL_STATE_CLAIM_IDENTIFIER = "federalState";
    public static final String EXAMINATION_CLAIM_IDENTIFIER = "examination";
    public static final String OFFICE_ADDRESS_CLAIM_IDENTIFIER = "officeAddress";
    public static final String OFFICE_AS_STRING = "Kiel";

    public static String MOCK_CERTIFICATION_ISSUER = "MOCK_CERTIFICATION_ISSUER";

    public static Map<String, Object> EXAMINATION_CLAIMS = Map.of("issuer", MOCK_CERTIFICATION_ISSUER);
    public static Map<String, Object> OFFICE_ADDRESS_CLAIM_VALUES = new HashMap<>(Map.of(
            "streetNumber", "29-31",
            "city", "Kiel",
            "street", "Fleethörn",
            "postcode", "24103",
            "office", "Fischereibehörde Kiel"
    ));
}
