package de.adesso.fischereiregister.registerservice.digitize_fishing_license;

import de.adesso.fischereiregister.RegisterServiceApplication;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.registerservice.common.security.mocking.WithMockSecurityContext;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.openapitools.model.DigitizeFishingLicenseRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.util.List;

import static de.adesso.fischereiregister.registerservice.common.JsonParser.asJsonString;
import static de.adesso.fischereiregister.registerservice.common.security.mocking.SecurityContextConstants.MOCK_FEDERAL_STATE;
import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.notNullValue;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK, classes = RegisterServiceApplication.class)
@AutoConfigureMockMvc(addFilters = false)
@WithMockSecurityContext()
public class DigitizeFishingLicenseIntegrationTest {

    private static final String DIGITIZE_ROUTE = "http://localhost:8080/register-entries/fishing-licenses:digitize";

    @Autowired
    private MockMvc mvc;

    @Test
    @DisplayName("""
            	POST /api/digitize-fishing-license.
            	Verify that the digitize-fishing-license endpoint returns a 200 OK status code.
            """)
    void digitizeRegisterEntryTest() throws Exception {

        final DigitizeFishingLicenseRequest digitizeRequest =  DigitizeRequestFactory.validDigitizeDto();

        mvc.perform(MockMvcRequestBuilders.post(DIGITIZE_ROUTE)
                .content(asJsonString(digitizeRequest))
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isCreated())
                .andExpect(header().string("Location", Matchers.matchesPattern("/api/register-entries/[0-9a-fA-F\\-]{36}")))
                .andExpect(jsonPath("$.documents", hasSize(digitizeRequest.getTaxes().size() +digitizeRequest.getPayedTaxes().size() + 2))) //taxes plus we expect CARD AND PDF License
                .andExpect(jsonPath("$.person.firstname", Matchers.containsString(digitizeRequest.getPerson().getFirstname())))
                .andExpect(jsonPath("$.jurisdiction.federalState", Matchers.containsString(MOCK_FEDERAL_STATE)))
                .andExpect(jsonPath("$.fishingLicense.type", Matchers.containsString(LicenseType.REGULAR.toString())))
                .andExpect(jsonPath("$.registerEntryId", notNullValue()));
    }

    @Test
    @DisplayName("""
            POST /api/digitize-fishing-license
            Verify that digitization is successful, when optional parameters are empty
            """)
    void digitizeRegisterEntryWhenOptionalParametersAreNullTest() throws Exception {
        final DigitizeFishingLicenseRequest request = DigitizeRequestFactory.validDigitizeDto();
        request.setPayedTaxes(List.of());
        request.setTaxes(List.of());

        mvc.perform(MockMvcRequestBuilders.post(DIGITIZE_ROUTE)
                .content(asJsonString(request)).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isCreated())
                .andExpect(header().string("Location", Matchers.matchesPattern("/api/register-entries/[0-9a-fA-F\\-]{36}")))
                .andExpect(jsonPath("$.documents", hasSize(2))); //No taxes but we expect CARD AND PDF License
    }
}
