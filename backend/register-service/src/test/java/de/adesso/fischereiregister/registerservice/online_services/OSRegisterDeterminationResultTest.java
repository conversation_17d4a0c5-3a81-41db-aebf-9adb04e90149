package de.adesso.fischereiregister.registerservice.online_services;

import de.adesso.fischereiregister.registerservice.online_services.model.OSRegisterDeterminationResult;
import de.adesso.fischereiregister.registerservice.online_services.model.OSRequestStatus;
import org.junit.jupiter.api.Test;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

class OSRegisterDeterminationResultTest {

    @Test
    void testSuccess() {
        UUID registerEntryId = UUID.randomUUID();
        OSRegisterDeterminationResult result = OSRegisterDeterminationResult.success(registerEntryId);

        assertTrue(result.isSuccessful());
        assertEquals(registerEntryId, result.getRegisterEntryId());
        assertNull(result.getOsStatus());
    }

    @Test
    void testSuccessWithStatus() {
        UUID registerEntryId = UUID.randomUUID();
        OSRequestStatus status = OSRequestStatus.PERSON_NOT_FOUND_CREATE_NEW_REGISTER_ENTRY;
        OSRegisterDeterminationResult result = OSRegisterDeterminationResult.successWithStatus(registerEntryId, status);

        assertTrue(result.isSuccessful());
        assertEquals(registerEntryId, result.getRegisterEntryId());
        assertEquals(status, result.getOsStatus());
    }

    @Test
    void testStatus() {
        OSRequestStatus status = OSRequestStatus.PERSON_NOT_FOUND_CREATE_NEW_REGISTER_ENTRY;
        OSRegisterDeterminationResult result = OSRegisterDeterminationResult.status(status);

        assertFalse(result.isSuccessful());
        assertNull(result.getRegisterEntryId());
        assertEquals(status, result.getOsStatus());
    }

    @Test
    void testSuccessWithNullRegisterEntryIdThrowsException() {
        assertThrows(IllegalArgumentException.class, () -> OSRegisterDeterminationResult.success(null));
    }

    @Test
    void testSuccessWithStatusWithNullRegisterEntryIdThrowsException() {
        assertThrows(IllegalArgumentException.class, () -> OSRegisterDeterminationResult.successWithStatus(null, OSRequestStatus.PERSON_NOT_FOUND_CREATE_NEW_REGISTER_ENTRY));
    }

    @Test
    void testStatusWithNullStatusThrowsException() {
        assertThrows(IllegalArgumentException.class, () -> OSRegisterDeterminationResult.status(null));
    }
}