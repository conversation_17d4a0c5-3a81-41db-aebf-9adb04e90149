package de.adesso.fischereiregister.registerservice.drools.dmn.adapter;

import de.adesso.fischereiregister.core.exceptions.RulesProcessingException;
import de.adesso.fischereiregister.core.model.Birthdate;
import de.adesso.fischereiregister.core.model.Fee;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.validation.ValidationResult;
import de.adesso.fischereiregister.registerservice.common.TestDataUtil;
import de.adesso.fischereiregister.registerservice.drools.dmn.DmnDroolsService;
import de.adesso.fischereiregister.registerservice.drools.dmn.license.configuration.input.LicenseInformationInput;
import de.adesso.fischereiregister.registerservice.drools.dmn.license.configuration.output.LicenseInformationOutput;
import de.adesso.fischereiregister.registerservice.drools.dmn.license.validation.input.LicenseValidationInput;
import de.adesso.fischereiregister.registerservice.drools.dmn.license.validation.output.LicenseValidationOutput;
import de.adesso.fischereiregister.registerservice.drools.dmn.qualifications_proof.QualificationsProofValidationOutput;
import de.adesso.fischereiregister.registerservice.drools.dmn.tax.configuration.TaxInformationInput;
import de.adesso.fischereiregister.registerservice.drools.dmn.tax.configuration.TaxInformationOutput;
import de.adesso.fischereiregister.registerservice.drools.dmn.tax.validation.TaxValidationInput;
import de.adesso.fischereiregister.registerservice.drools.dmn.tax.validation.TaxValidationOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class TenantRulesValidationAdapterTest {

    @Mock
    private DmnDroolsService dmnDroolsService;

    @InjectMocks
    private TenantRulesValidationAdapter tenantRulesValidationAdapter;

    private FederalState federalState;
    private Person person;
    private ValidationResult validationResult;

    @BeforeEach
    void setUp() {
        federalState = FederalState.SH;
        person = new Person();
        person.setBirthdate(new Birthdate(1990, 1, 1));
        validationResult = new ValidationResult();
    }

    @Test
    @DisplayName("Should validate fishing license rules successfully")
    void shouldValidateUsingFishingLicenseRulesSuccessfully() throws RulesProcessingException {
        when(dmnDroolsService.evaluateLicenseValidationRules(any(), any())).thenReturn(List.of());

        tenantRulesValidationAdapter.validateUsingFishingLicenseRules(federalState, person, LicenseType.REGULAR, validationResult);

        assertThat(validationResult.hasErrors()).isFalse();
        verify(dmnDroolsService, times(1)).evaluateLicenseValidationRules(any(LicenseValidationInput.class), eq(federalState));
    }

    @Test
    @DisplayName("Should add error note when fishing license rules validation fails")
    void shouldAddErrorNoteWhenFishingLicenseRulesValidationFails() throws RulesProcessingException {
        Map<String, Object> result = new HashMap<>();
        result.put("Fehlermeldung", "Some exception message");
        LicenseValidationOutput output = new LicenseValidationOutput(result);
        when(dmnDroolsService.evaluateLicenseValidationRules(any(), any())).thenReturn(List.of(output));

        tenantRulesValidationAdapter.validateUsingFishingLicenseRules(federalState, person, LicenseType.REGULAR, validationResult);

        assertThat(validationResult.hasErrors()).isTrue();
        assertThat(validationResult.getErrorNotes()).contains("Some exception message");
    }

    @Test
    @DisplayName("Should validate fishing tax rules successfully")
    void shouldValidateUsingFishingTaxRulesSuccessfully() throws RulesProcessingException {
        Tax tax = new Tax();
        tax.setValidTo(LocalDate.of(2025, 1, 1));
        when(dmnDroolsService.evaluateTaxValidationRules(any(), any())).thenReturn(List.of());

        tenantRulesValidationAdapter.validateUsingFishingTaxRules(federalState, person, List.of(tax), validationResult);

        assertThat(validationResult.hasErrors()).isFalse();
        verify(dmnDroolsService, times(1)).evaluateTaxValidationRules(any(TaxValidationInput.class), eq(federalState));
    }

    @Test
    @DisplayName("Should add error note when fishing tax rules validation fails")
    void shouldAddErrorNoteWhenFishingTaxRulesValidationFails() throws RulesProcessingException {
        Tax tax = new Tax();
        tax.setValidTo(LocalDate.of(2025, 1, 1));
        Map<String, Object> result = new HashMap<>();
        result.put("Fehlermeldung", "Some exception message");

        TaxValidationOutput output = new TaxValidationOutput(result);

        when(dmnDroolsService.evaluateTaxValidationRules(any(), any())).thenReturn(List.of(output));

        tenantRulesValidationAdapter.validateUsingFishingTaxRules(federalState, person, List.of(tax), validationResult);

        assertThat(validationResult.hasErrors()).isTrue();
        assertThat(validationResult.getErrorNotes()).contains("Some exception message");
    }

    @Test
    @DisplayName("Should validate tax information successfully")
    void shouldValidateTaxesSuccessfully() throws RulesProcessingException {
        Tax tax = TestDataUtil.createAnalogTax();
        tax.setValidFrom(LocalDate.of(2022, 1, 1));
        tax.setValidTo(LocalDate.of(2025, 1, 1));
        tax.getPaymentInfo().setAmount(150.0);
        Map<String, Object> result = new HashMap<>();
        result.put(TaxInformationOutput.NETTOBETRAG_ABGABE, BigDecimal.valueOf(100));
        result.put(TaxInformationOutput.VERWALTUNGSGEBUEHR, BigDecimal.valueOf(20));
        result.put(TaxInformationOutput.GEBUEHR_BEHOERDENGANG, BigDecimal.valueOf(30));
        result.put(TaxInformationOutput.JAHRE, BigDecimal.valueOf(5));
        TaxInformationOutput output = new TaxInformationOutput(result);
        when(dmnDroolsService.evaluateTaxRules(any(), any())).thenReturn(List.of(output));

        tenantRulesValidationAdapter.validateTaxes(federalState, List.of(tax), true, validationResult);

        assertThat(validationResult.hasErrors()).isFalse();
        verify(dmnDroolsService, times(1)).evaluateTaxRules(any(TaxInformationInput.class), eq(federalState));
    }

    @Test
    @DisplayName("Should add error note when tax information validation fails")
    void shouldAddErrorNoteWhenTaxInformationValidationFails() throws RulesProcessingException {
        Tax tax = TestDataUtil.createAnalogTax();
        tax.setValidFrom(LocalDate.of(2022, 1, 1));
        tax.setValidTo(LocalDate.of(2025, 1, 1));
        tax.getPaymentInfo().setAmount(120.0);
        Map<String, Object> result = new HashMap<>();
        result.put(TaxInformationOutput.NETTOBETRAG_ABGABE, BigDecimal.valueOf(100));
        result.put(TaxInformationOutput.VERWALTUNGSGEBUEHR, BigDecimal.valueOf(20));
        result.put(TaxInformationOutput.GEBUEHR_BEHOERDENGANG, BigDecimal.valueOf(30));
        result.put(TaxInformationOutput.JAHRE, BigDecimal.valueOf(5));

        TaxInformationOutput taxInformationOutput = new TaxInformationOutput(result);
        when(dmnDroolsService.evaluateTaxRules(any(), any())).thenReturn(List.of(taxInformationOutput));

        tenantRulesValidationAdapter.validateTaxes(federalState, List.of(tax), true, validationResult);

        assertThat(validationResult.hasErrors()).isTrue();
        assertThat(validationResult.getErrorNotes()).contains("Tax amount is not correct, expected Value: 150.0");
    }

    @Test
    @DisplayName("Should validate license information successfully")
    void shouldValidateFeesForFishingLicenseSuccessfully() throws RulesProcessingException {
        Fee fee = TestDataUtil.createAnalogFee();
        fee.getPaymentInfo().setAmount(100.0);
        Map<String, Object> result = new HashMap<>();
        result.put(LicenseInformationOutput.GEBUEHR_IN_EURO, BigDecimal.valueOf(100));
        result.put(LicenseInformationOutput.SPAETESTES_BESTELLDATUM, LocalDate.of(2023, 12, 31));
        result.put(LicenseInformationOutput.GUELTIGKEITSTYP, "VOLLES_JAHR");
        result.put(LicenseInformationOutput.GUELTIGKEITSOPTIONEN, new ArrayList<BigDecimal>() {{
            add(BigDecimal.valueOf(1));
            add(BigDecimal.valueOf(2));
            add(BigDecimal.valueOf(3));
        }});
        result.put(LicenseInformationOutput.IST_VERLAENGERBAR, true);
        result.put(LicenseInformationOutput.IST_VERFUEGBAR, true);
        result.put(LicenseInformationOutput.IST_BEFRISTBAR, true);

        LicenseInformationOutput output = new LicenseInformationOutput(result);
        when(dmnDroolsService.evaluateLicenseRules(any(), any())).thenReturn(List.of(output));

        tenantRulesValidationAdapter.validateFeesForFishingLicense(federalState, LicenseType.REGULAR, List.of(fee), validationResult);

        assertThat(validationResult.hasErrors()).isFalse();
        verify(dmnDroolsService, times(1)).evaluateLicenseRules(any(LicenseInformationInput.class), eq(federalState));
    }

    @Test
    @DisplayName("Should add error note when license information validation fails")
    void shouldAddErrorNoteWhenLicenseInformationValidationFails() throws RulesProcessingException {
        Fee fee = TestDataUtil.createAnalogFee();
        fee.getPaymentInfo().setAmount(120.0);
        Map<String, Object> result = new HashMap<>();
        result.put(LicenseInformationOutput.GEBUEHR_IN_EURO, BigDecimal.valueOf(100));
        result.put(LicenseInformationOutput.SPAETESTES_BESTELLDATUM, LocalDate.of(2023, 12, 31));
        result.put(LicenseInformationOutput.GUELTIGKEITSTYP, "VOLLES_JAHR");
        result.put(LicenseInformationOutput.GUELTIGKEITSOPTIONEN, new ArrayList<BigDecimal>() {{
            add(BigDecimal.valueOf(1));
            add(BigDecimal.valueOf(2));
            add(BigDecimal.valueOf(3));
        }});
        result.put(LicenseInformationOutput.IST_VERLAENGERBAR, true);
        result.put(LicenseInformationOutput.IST_VERFUEGBAR, true);
        result.put(LicenseInformationOutput.IST_BEFRISTBAR, true);

        LicenseInformationOutput output = new LicenseInformationOutput(result);
        when(dmnDroolsService.evaluateLicenseRules(any(), any())).thenReturn(List.of(output));

        tenantRulesValidationAdapter.validateFeesForFishingLicense(federalState, LicenseType.REGULAR, List.of(fee), validationResult);

        assertThat(validationResult.hasErrors()).isTrue();
        assertThat(validationResult.getErrorNotes()).contains("Fee amount is not correct, expected Value: 100");
    }


    @Test
    @DisplayName("Should add error note when qualification proof rules validation fails")
    void shouldAddErrorNoteWhenQualificationProofRulesValidationFails() throws RulesProcessingException {
        Map<String, Object> result = new HashMap<>();
        result.put("Fehlermeldung", "Some exception message");

        QualificationsProofValidationOutput output = new QualificationsProofValidationOutput(result);

        when(dmnDroolsService.evaluateQualificationValidationRules(any(), any())).thenReturn(List.of(output));

        tenantRulesValidationAdapter.validateUsingQualificationProofRules(federalState, person, validationResult);

        assertThat(validationResult.hasErrors()).isTrue();
        assertThat(validationResult.getErrorNotes()).contains("Some exception message");
    }
}