package de.adesso.fischereiregister.registerservice.security;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.oauth2.jwt.Jwt;

@ExtendWith(MockitoExtension.class)
@DisplayName("Tests our implementation of KeycloakJwtRolesConverter")
class KeycloakJwtRolesConverterTest {

	@Mock
	SecurityProperties securityPropertiesMock;

	@InjectMocks
	JwtRolesConverterImpl converter;

	Map<String, Collection<String>> realmAccessForTest;

	@Test
	@DisplayName("extractResourceRoles should convert realm claims into granted authorities")
	void testExtractResourceRolesRealsRoles() {
		// Given
		when(securityPropertiesMock.getRealmAccessClaim()).thenReturn("realm_claim");
		when(securityPropertiesMock.getRolesClaim()).thenReturn("roles");
		Jwt jwtMock = mock(Jwt.class);
		when(jwtMock.getClaim("realm_claim")).thenReturn(realmAccessForTest);

		// When
		Collection<GrantedAuthority> result = converter.extractResourceRoles(jwtMock);

		// Then
		assertFalse(result.isEmpty());
	}

	@BeforeEach
	private void beforeTests() {
		realmAccessForTest = new HashMap<>() {
			private static final long serialVersionUID = -3045056520467118827L;

			{
				put("roles", Arrays.asList("offline_access", "default-roles-digifischdok_dev", "OFFICAL",
						"uma_authorization", "PERSONAL_DATA_ADMINISTRATOR", "EXAM_DATA_ADMINISTRATOR"));
			}
		};

	}

	@Test
	@DisplayName("extractResourceRoles should convert resource claims into granted authorities")
	void testExtractResourceRolesResourceRoles() {
		when(securityPropertiesMock.getRealmAccessClaim()).thenReturn("resource_access");
		when(securityPropertiesMock.getRolesClaim()).thenReturn("roles");

		Jwt jwtMock = mock(Jwt.class);
		when(jwtMock.getClaim("resource_access")).thenReturn(realmAccessForTest);

		// When
		Collection<GrantedAuthority> result = converter.extractResourceRoles(jwtMock);

		// Then
		assertFalse(result.isEmpty());
	}

}
