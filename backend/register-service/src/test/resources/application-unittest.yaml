spring:
  jpa.properties.hibernate.dialect: org.hibernate.dialect.H2Dialect
  datasource:
    driverClassName: org.h2.Driver
    jdbcUrl: jdbc:h2:mem:db;DB_CLOSE_DELAY=-1;Mode=PostgreSQL;NON_KEYWORDS=YEAR
    username: sa
    password: sa
  cloud:
    aws:
      s3:
        endpoint: http://localhost:9000
        region: eu-central-1
        bucket: digifischdok
      credentials:
        access-key: minioadmin
        secret-key: minioadmin
  security:
    oauth2:
      client:
        registration:
          keycloak:
            client-id: backend
            client-secret: ""
            authorization-grant-type: authorization_code
            scope: openid
        provider:
          keycloak:
            issuer-uri: https://digifischdok-iam.dsecurecloud.de:8443/realms/digifischdok_dev
            user-name-attribute: preferred_username
      resourceserver:
        jwt:
          issuer-uri: https://digifischdok-iam.dsecurecloud.de:8443/realms/digifischdok_dev
  mail:
    host: localhost
    port: 1025
    username: none
    password: none
    from:
      address: <EMAIL>
    defaultEncoding: UTF-8
    properties:
      mail:
        transport:
          protocol: smtp
        smtp:
          auth: false
          starttls:
            enable: true
    debug: true

  liquibase:
    change-log: "classpath:config/liquibase/changelog-test.xml"

message-service:
  token-uri: https://idp.serviceportal-stage.gemeinsamonline.de/webidp2/connect/token
  client-id: urn:digifischdok:stage
  client-secret: secret
  authorization-grant-type: client_credentials
  scope: access_urn:dataport:od:digifischdok:stage:go:DigiFischDok,default
  resource: urn:dataport:osi:postfach:rz2:stage:go
  base-urn: https://api-gateway-stage.dataport.de:443

management:
  endpoints:
    web:
      exposure:
        include: health,info,logfile,loggers,threaddump
  info:
    java:
      enabled: true
    env:
      enabled: true
  tracing:
    sampling:
      probability: 1.0
  metrics:
    distribution:
      percentiles-histogram:
        http:
          server:
            requests: true

keycloak:
  enabled: true
#spring.security.oauth2.client.provider.keycloak.issuer-uri=https://auth.dev.echolot.app/realms/digifischdok_dev
#spring.security.oauth2.resourceserver.jwt.issuer-uri=https://auth.dev.echolot.app/realms/digifischdok_dev

security:
  groups: groups
  realm_access: realm_access
  roles_claim: roles

test-data-import:
  upload-endpoint-enabled: true
  local-file-at-startup-enabled: true
  path: classpath:config/testdata/test-data.csv