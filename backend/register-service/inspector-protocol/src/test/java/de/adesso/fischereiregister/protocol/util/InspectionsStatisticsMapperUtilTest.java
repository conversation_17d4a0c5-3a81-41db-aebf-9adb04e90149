package de.adesso.fischereiregister.protocol.util;

import de.adesso.fischereiregister.protocol.persistence.result.model.ActiveInspectorsResult;
import de.adesso.fischereiregister.protocol.persistence.result.model.NumberOfInspectionsResult;
import de.adesso.fischereiregister.protocol.service.model.InspectionsStatisticsResult;
import de.adesso.fischereiregister.protocol.service.model.InspectionsStatisticsResultData;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

class InspectionsStatisticsMapperUtilTest {

    @Test
    void testMapToInspectionsStatistics() {
        // Arrange
        NumberOfInspectionsResult numberOfInspectionsResult = new NumberOfInspectionsResult(5, 2025);
        ActiveInspectorsResult activeInspectorsResult = new ActiveInspectorsResult(1, 2025);

        List<ActiveInspectorsResult> activeInspectorsResults = List.of(activeInspectorsResult);
        List<NumberOfInspectionsResult> numberOfInspectionsResults = List.of(numberOfInspectionsResult);

        // Act
        List<InspectionsStatisticsResult> result = InspectionsStatisticsMapperUtil.mapToInspectionsStatistics(activeInspectorsResults, numberOfInspectionsResults);

        // Assert
        assertEquals(1, result.size());
        InspectionsStatisticsResult statisticsResult = result.get(0);
        assertEquals(2025, statisticsResult.getYear());
        InspectionsStatisticsResultData data = statisticsResult.getData();

        assertEquals(5, data.getNumberOfInspections());
        assertEquals(1, data.getActiveInspectors());
    }
}