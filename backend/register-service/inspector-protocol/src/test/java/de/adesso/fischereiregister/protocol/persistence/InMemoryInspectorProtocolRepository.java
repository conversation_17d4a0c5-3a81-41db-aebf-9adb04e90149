package de.adesso.fischereiregister.protocol.persistence;

import de.adesso.fischereiregister.protocol.persistence.result.model.ActiveInspectorsResult;
import de.adesso.fischereiregister.protocol.persistence.result.model.NumberOfInspectionsResult;

import java.util.*;
import java.util.stream.Collectors;

public class InMemoryInspectorProtocolRepository implements InspectorProtocolRepository {

    private final Map<Long, InspectorProtocol> database = new HashMap<>();
    private long idCounter = 1;

    @Override
    public <S extends InspectorProtocol> S save(S entity) {
        if (entity.getId() == null) {
            entity.setId(idCounter++);
        }
        database.put(entity.getId(), entity);
        return entity;
    }

    @Override
    public <S extends InspectorProtocol> Iterable<S> saveAll(Iterable<S> entities) {
        entities.forEach(this::save);
        return entities;
    }

    @Override
    public Optional<InspectorProtocol> findById(UUID id) {
        return Optional.ofNullable(database.get(id));
    }

    @Override
    public boolean existsById(UUID id) {
        return database.containsKey(id);
    }

    @Override
    public Iterable<InspectorProtocol> findAll() {
        return new ArrayList<>(database.values());
    }

    @Override
    public Iterable<InspectorProtocol> findAllById(Iterable<UUID> ids) {
        List<InspectorProtocol> result = new ArrayList<>();
        for (UUID id : ids) {
            InspectorProtocol protocol = database.get(id);
            if (protocol != null) {
                result.add(protocol);
            }
        }
        return result;
    }

    @Override
    public long count() {
        return database.size();
    }

    @Override
    public void deleteById(UUID id) {
        database.remove(id);
    }

    @Override
    public void delete(InspectorProtocol entity) {
        database.remove(entity.getId());
    }

    @Override
    public void deleteAllById(Iterable<? extends UUID> ids) {
        ids.forEach(this::deleteById);
    }

    @Override
    public void deleteAll(Iterable<? extends InspectorProtocol> entities) {
        entities.forEach(this::delete);
    }

    @Override
    public void deleteAll() {
        database.clear();
    }

    @Override
    public List<ActiveInspectorsResult> selectActiveInspectors(List<Integer> years, String federalState) {
        return database.values().stream()
                .filter(protocol -> federalState == null || protocol.getInspectorFederalState().equals(federalState))
                .filter(protocol -> years == null || years.contains(protocol.getInspectionTimestamp().getYear()))
                .collect(Collectors.groupingBy(
                        protocol -> protocol.getInspectionTimestamp().getYear(),
                        Collectors.mapping(InspectorProtocol::getInspectorUserId, Collectors.toSet())
                ))
                .entrySet().stream()
                .map(entry -> new ActiveInspectorsResult(entry.getValue().size(), entry.getKey()))
                .sorted(Comparator.comparing(ActiveInspectorsResult::year))
                .collect(Collectors.toList());
    }

    @Override
    public List<NumberOfInspectionsResult> selectNumberOfInspections(List<Integer> years, String federalState) {
        return database.values().stream()
                .filter(protocol -> federalState == null || protocol.getInspectorFederalState().equals(federalState))
                .filter(protocol -> years == null || years.contains(protocol.getInspectionTimestamp().getYear()))
                .collect(Collectors.groupingBy(
                        protocol -> protocol.getInspectionTimestamp().getYear(),
                        Collectors.counting()
                ))
                .entrySet().stream()
                .map(entry -> new NumberOfInspectionsResult(entry.getValue().intValue(), entry.getKey()))
                .sorted(Comparator.comparing(NumberOfInspectionsResult::year))
                .collect(Collectors.toList());
    }

    @Override
    public List<Integer> findDistinctYears() {
        return database.values().stream()
                .map(protocol -> protocol.getInspectionTimestamp().getYear())
                .distinct()
                .sorted(Comparator.reverseOrder()) // Descending order as specified in the query
                .collect(Collectors.toList());
    }
}
