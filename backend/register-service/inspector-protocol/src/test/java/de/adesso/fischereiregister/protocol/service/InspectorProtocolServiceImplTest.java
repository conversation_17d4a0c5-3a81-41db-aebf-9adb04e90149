package de.adesso.fischereiregister.protocol.service;

import de.adesso.fischereiregister.protocol.persistence.InMemoryInspectorProtocolRepository;
import de.adesso.fischereiregister.protocol.persistence.InspectorProtocol;
import de.adesso.fischereiregister.protocol.service.model.InspectionsStatisticsResult;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

class InspectorProtocolServiceImplTest {

    @Test
    @DisplayName("createProtocolEntry should save the protocol entry with correct values")
    void testCreateProtocolEntry() {
        // given
        InMemoryInspectorProtocolRepository repository = new InMemoryInspectorProtocolRepository();
        InspectorProtocolServiceImpl service = new InspectorProtocolServiceImpl(repository);

        String inspectorUserId = "user123";
        String registerEntryId = "entry456";
        String inspectorFederalState = "StateX";
        LocalDateTime inspectionTimestamp = LocalDateTime.now();

        // when
        service.createProtocolEntry(inspectorUserId, registerEntryId, inspectorFederalState, inspectionTimestamp);

        // then
        assertEquals(1, repository.count());
        InspectorProtocol savedProtocol = ((List<InspectorProtocol>) repository.findAll()).get(0);
        assertEquals(inspectorUserId, savedProtocol.getInspectorUserId());
        assertEquals(registerEntryId, savedProtocol.getRegisterEntryId());
        assertEquals(inspectorFederalState, savedProtocol.getInspectorFederalState());
        assertEquals(inspectionTimestamp, savedProtocol.getInspectionTimestamp());
    }

    @Test
    void testCreateProtocolEntryWithNullRegisterEntryId() {
        // given
        InMemoryInspectorProtocolRepository repository = new InMemoryInspectorProtocolRepository();
        InspectorProtocolServiceImpl service = new InspectorProtocolServiceImpl(repository);

        String inspectorUserId = "user123";
        String registerEntryId = null;
        String inspectorFederalState = "StateX";
        LocalDateTime inspectionTimestamp = LocalDateTime.now();

        // when
        service.createProtocolEntry(inspectorUserId, registerEntryId, inspectorFederalState, inspectionTimestamp);

        // then
        assertEquals(1, repository.count());
        InspectorProtocol savedProtocol = ((List<InspectorProtocol>) repository.findAll()).get(0);
        assertEquals(inspectorUserId, savedProtocol.getInspectorUserId());
        assertNull(savedProtocol.getRegisterEntryId());
        assertEquals(inspectorFederalState, savedProtocol.getInspectorFederalState());
        assertEquals(inspectionTimestamp, savedProtocol.getInspectionTimestamp());
    }

    @Test
    void testGetInspectionsStatisticsWithValidData() {
        // given
        InMemoryInspectorProtocolRepository repository = new InMemoryInspectorProtocolRepository();
        InspectorProtocolServiceImpl service = new InspectorProtocolServiceImpl(repository);

        String federalState = "StateA";
        List<Integer> years = List.of(2021, 2023);

        // Create test data
        InspectorProtocol protocol1 = new InspectorProtocol();
        protocol1.setInspectorUserId("inspector1");
        protocol1.setRegisterEntryId("entry1");
        protocol1.setInspectorFederalState(federalState);
        protocol1.setInspectionTimestamp(LocalDateTime.of(2021, 6, 15, 10, 0));
        repository.save(protocol1);

        InspectorProtocol protocol2 = new InspectorProtocol();
        protocol2.setInspectorUserId("inspector2");
        protocol2.setRegisterEntryId("entry2");
        protocol2.setInspectorFederalState(federalState);
        protocol2.setInspectionTimestamp(LocalDateTime.of(2021, 8, 20, 14, 30));
        repository.save(protocol2);

        InspectorProtocol protocol3 = new InspectorProtocol();
        protocol3.setInspectorUserId("inspector1");
        protocol3.setRegisterEntryId("entry3");
        protocol3.setInspectorFederalState(federalState);
        protocol3.setInspectionTimestamp(LocalDateTime.of(2023, 3, 10, 9, 15));
        repository.save(protocol3);

        // when
        List<InspectionsStatisticsResult> result = service.getInspectionsStatistics(years, federalState);

        // then
        assertEquals(2, result.size());
        assertEquals(2021, result.get(0).getYear());
        assertEquals(2023, result.get(1).getYear());
    }

    @Test
    void testGetInspectionsStatisticsWithEmptyYears() {
        // given
        InMemoryInspectorProtocolRepository repository = new InMemoryInspectorProtocolRepository();
        InspectorProtocolServiceImpl service = new InspectorProtocolServiceImpl(repository);

        String federalState = "StateA";
        List<Integer> years = List.of();

        // Create test data
        InspectorProtocol protocol1 = new InspectorProtocol();
        protocol1.setInspectorUserId("inspector1");
        protocol1.setRegisterEntryId("entry1");
        protocol1.setInspectorFederalState(federalState);
        protocol1.setInspectionTimestamp(LocalDateTime.of(2021, 6, 15, 10, 0));
        repository.save(protocol1);

        InspectorProtocol protocol2 = new InspectorProtocol();
        protocol2.setInspectorUserId("inspector2");
        protocol2.setRegisterEntryId("entry2");
        protocol2.setInspectorFederalState(federalState);
        protocol2.setInspectionTimestamp(LocalDateTime.of(2023, 8, 20, 14, 30));
        repository.save(protocol2);

        // when
        List<InspectionsStatisticsResult> result = service.getInspectionsStatistics(years, federalState);

        // then
        assertEquals(2, result.size());
    }

    @Test
    void testGetInspectionsStatisticsWithNullFederalState() {
        // given
        InMemoryInspectorProtocolRepository repository = new InMemoryInspectorProtocolRepository();
        InspectorProtocolServiceImpl service = new InspectorProtocolServiceImpl(repository);

        String federalState = null;
        List<Integer> years = List.of(2021);

        // Create test data with different federal states
        InspectorProtocol protocol1 = new InspectorProtocol();
        protocol1.setInspectorUserId("inspector1");
        protocol1.setRegisterEntryId("entry1");
        protocol1.setInspectorFederalState("StateA");
        protocol1.setInspectionTimestamp(LocalDateTime.of(2021, 6, 15, 10, 0));
        repository.save(protocol1);

        InspectorProtocol protocol2 = new InspectorProtocol();
        protocol2.setInspectorUserId("inspector2");
        protocol2.setRegisterEntryId("entry2");
        protocol2.setInspectorFederalState("StateB");
        protocol2.setInspectionTimestamp(LocalDateTime.of(2021, 8, 20, 14, 30));
        repository.save(protocol2);

        // when
        List<InspectionsStatisticsResult> result = service.getInspectionsStatistics(years, federalState);

        // then
        assertEquals(1, result.size());
        assertEquals(2021, result.get(0).getYear());
    }

    @Test
    @DisplayName("getAvailableYears should return list of available years from repository")
    void testGetAvailableYears() {
        // given
        InMemoryInspectorProtocolRepository repository = new InMemoryInspectorProtocolRepository();
        InspectorProtocolServiceImpl service = new InspectorProtocolServiceImpl(repository);

        // Create test data with different years
        InspectorProtocol protocol1 = new InspectorProtocol();
        protocol1.setInspectorUserId("inspector1");
        protocol1.setRegisterEntryId("entry1");
        protocol1.setInspectorFederalState("StateA");
        protocol1.setInspectionTimestamp(LocalDateTime.of(2023, 6, 15, 10, 0));
        repository.save(protocol1);

        InspectorProtocol protocol2 = new InspectorProtocol();
        protocol2.setInspectorUserId("inspector2");
        protocol2.setRegisterEntryId("entry2");
        protocol2.setInspectorFederalState("StateB");
        protocol2.setInspectionTimestamp(LocalDateTime.of(2022, 8, 20, 14, 30));
        repository.save(protocol2);

        InspectorProtocol protocol3 = new InspectorProtocol();
        protocol3.setInspectorUserId("inspector3");
        protocol3.setRegisterEntryId("entry3");
        protocol3.setInspectorFederalState("StateC");
        protocol3.setInspectionTimestamp(LocalDateTime.of(2021, 3, 10, 9, 15));
        repository.save(protocol3);

        // when
        List<Integer> result = service.getAvailableYears();

        // then
        assertEquals(3, result.size());
        assertEquals(2023, result.get(0)); // Should be in descending order
        assertEquals(2022, result.get(1));
        assertEquals(2021, result.get(2));
    }

    @Test
    @DisplayName("getAvailableYears should return empty list when no data exists")
    void testGetAvailableYearsEmpty() {
        // given
        InMemoryInspectorProtocolRepository repository = new InMemoryInspectorProtocolRepository();
        InspectorProtocolServiceImpl service = new InspectorProtocolServiceImpl(repository);

        // when
        List<Integer> result = service.getAvailableYears();

        // then
        assertEquals(0, result.size());
    }
}