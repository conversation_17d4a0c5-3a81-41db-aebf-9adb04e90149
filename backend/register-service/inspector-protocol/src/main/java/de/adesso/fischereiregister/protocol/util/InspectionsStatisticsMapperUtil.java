package de.adesso.fischereiregister.protocol.util;

import de.adesso.fischereiregister.protocol.persistence.result.model.ActiveInspectorsResult;
import de.adesso.fischereiregister.protocol.persistence.result.model.NumberOfInspectionsResult;
import de.adesso.fischereiregister.protocol.service.model.InspectionsStatisticsResult;
import de.adesso.fischereiregister.protocol.service.model.InspectionsStatisticsResultData;

import java.util.List;

/**
 * Utility class for mapping inspection statistics results.
 */
public class InspectionsStatisticsMapperUtil {

    private InspectionsStatisticsMapperUtil() {
        // Utility class, no instantiation needed
    }

    /**
     * Maps from the persistence results to the results which are similar to the ones used in the OpenAPI.
     *
     * @param activeInspectorsResults The list of active inspectors results.
     * @param numberOfInspectionsResults The list of number of inspections results.
     * @return A list of mapped inspection statistics results.
     */
    public static List<InspectionsStatisticsResult> mapToInspectionsStatistics(
            List<ActiveInspectorsResult> activeInspectorsResults,
            List<NumberOfInspectionsResult> numberOfInspectionsResults) {

        return activeInspectorsResults.stream()
                .map(numberOfInspectionsResult -> {
                    InspectionsStatisticsResult inspectionsStatistics = new InspectionsStatisticsResult();
                    InspectionsStatisticsResultData inspectionsStatisticsData = new InspectionsStatisticsResultData();
                    inspectionsStatistics.setYear(numberOfInspectionsResult.year());
                    inspectionsStatisticsData.setActiveInspectors(numberOfInspectionsResult.activeInspectorsCount());
                    inspectionsStatisticsData.setNumberOfInspections(
                            numberOfInspectionsResults.stream()
                                    .filter(result -> result.year().intValue() == numberOfInspectionsResult.year().intValue())
                                    .findFirst()
                                    .map(NumberOfInspectionsResult::numberOfInspections)
                                    .orElse(0)
                    );
                    inspectionsStatistics.setData(inspectionsStatisticsData);
                    return inspectionsStatistics;
                })
                .toList();
    }
}