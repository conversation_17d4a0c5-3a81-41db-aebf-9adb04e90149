<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd"
                   context="inspector_protocol">

    <!-- ChangeSet for Protocol Table -->
    <changeSet id="1.0.0" author="madalina-iuliana.gheorghe">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="inspector_protocol"/>
            </not>
        </preConditions>
        <createTable tableName="inspector_protocol">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints nullable="false" primaryKey="true"/>
            </column>

            <column name="inspector_user_id" type="varchar(255)">
                <constraints nullable="false" primaryKey="false"/>
            </column>

            <column name="register_entry_id" type="varchar(255)">
                <constraints nullable="true"/>
            </column>

            <column name="inspection_timestamp" type="timestamp">
                <constraints nullable="false"/>
            </column>

            <column name="inspector_federal_state" type="varchar(10)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="1.0.0-index" author="madalina-iuliana.gheorghe">
        <preConditions onFail="MARK_RAN">
            <not>
                <indexExists indexName="idx_protocol_id" tableName="inspector_protocol"/>
            </not>
        </preConditions>
    </changeSet>
</databaseChangeLog>