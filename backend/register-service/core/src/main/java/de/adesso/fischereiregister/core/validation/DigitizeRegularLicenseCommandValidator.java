package de.adesso.fischereiregister.core.validation;

import de.adesso.fischereiregister.core.commands.DigitizeRegularLicenseCommand;
import de.adesso.fischereiregister.core.exceptions.AggregateValidationException;
import de.adesso.fischereiregister.core.exceptions.ClientInputValidationException;
import de.adesso.fischereiregister.core.exceptions.RulesProcessingException;
import de.adesso.fischereiregister.core.exceptions.SystemConfigValidationException;
import de.adesso.fischereiregister.core.model.Address;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.ports.CountryService;
import de.adesso.fischereiregister.core.ports.TenantRulesValidationPort;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static de.adesso.fischereiregister.core.validation.utils.ValidationUtils.validateFieldRequired;

@Component
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class DigitizeRegularLicenseCommandValidator extends AbstractValidator implements CommandValidator<DigitizeRegularLicenseCommand> {

    public DigitizeRegularLicenseCommandValidator(CountryService countryService, TenantRulesValidationPort tenantRulesValidationPort) {
        super(countryService, tenantRulesValidationPort);
    }

    @Override
    public void validateOrThrow(DigitizeRegularLicenseCommand command) throws AggregateValidationException, RulesProcessingException {
        validateUserDetails(command.userDetails());

        if (command.userDetails() != null) {
            validateFieldRequired(command.userDetails().getParsedOfficeAddress(), OFFICE_ADDRESS, validationResult);
            validateFieldRequired(command.userDetails().getOffice(), OFFICE, validationResult);
        }

        validateFieldRequired(command.salt(), SALT, validationResult);

        if (validationResult.hasErrors()) {
            // If the user details or the salt values are invalid, it is due to a system misconfiguration, so we return an exception accordingly.
            throw new SystemConfigValidationException(validationResult);
        }

        validatePerson(command.person(), true);
        final Address address = Optional.ofNullable(command.person())
                .map(person -> person.getAddress() != null ? person.getAddress() : person.getOfficeAddress())
                .orElse(null);
        validateAddress(address);
        validateQualificationProofs(command.qualificationsProofs());
        validateFees(command.fees());
        validateTaxes(command.taxes());
        validatePreviouslyPayedTaxes(command.payedTaxes());
        validateConsentInfo(command.consentInfo());

        validateFieldRequired(command.fees(), FEES, validationResult);

        validateTenantRules(FederalState.valueOf(command.userDetails().getFederalState()), command.person(), command.taxes(), command.fees(), LicenseType.REGULAR);

        if (validationResult.hasErrors()) {
            throw new ClientInputValidationException(validationResult);
        }
    }
}
