package de.adesso.fischereiregister.core.processors;

import de.adesso.fischereiregister.core.commands.CreateRegularLicenseCommand;
import de.adesso.fischereiregister.core.events.AxonEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseCreatedEvent;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.core.model.user.UserDetails;
import de.adesso.fischereiregister.core.model.utils.FishingLicenseFactory;
import de.adesso.fischereiregister.core.model.utils.IdentificationDocumentFactory;
import de.adesso.fischereiregister.core.utils.LicenseUtils;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.UUID;

@Component
@AllArgsConstructor
public class CreateRegularLicenseCommandProcessor implements CommandProcessor<CreateRegularLicenseCommand> {

    private final IdentificationDocumentFactory identificationDocumentFactory;
    private final FishingLicenseFactory fishingLicenseFactory;

    @Override
    public List<AxonEvent> process(
            CreateRegularLicenseCommand command, RegisterEntry registerEntry) {

        checkWhetherApplicableOrThrow(registerEntry);

        RegularLicenseCreatedEvent event = createEvent(command);

        return List.of(event);
    }

    private RegularLicenseCreatedEvent createEvent(CreateRegularLicenseCommand command) {

        final FishingLicense fishingLicense = createFishingLicenseWithDefaults(command.registerId(), command.userDetails());

        final List<IdentificationDocument> identificationDocuments = identificationDocumentFactory.createInitialLicenseDocuments(fishingLicense, command.registerId());
        command.taxes().forEach(t -> identificationDocuments.add(identificationDocumentFactory.createIdentificationDocumentForTax(t, command.registerId())));

        final Jurisdiction newJurisdiction = new Jurisdiction();
        newJurisdiction.setFederalState(command.userDetails().getFederalState());

        return new RegularLicenseCreatedEvent(
                command.registerId(),
                command.salt(),
                command.consentInfo(),
                command.person(),
                command.fees(),
                command.taxes(),
                fishingLicense,
                identificationDocuments,
                newJurisdiction,
                command.userDetails().getOffice(),
                command.userDetails().getParsedOfficeAddress(),
                null,
                null,
                null,
                SubmissionType.ANALOG
        );
    }

    public FishingLicense createFishingLicenseWithDefaults(UUID registerEntryId, UserDetails userDetails) {
        return fishingLicenseFactory.createRegularFishingLicense(
                registerEntryId,
                FederalState.valueOf(userDetails.getFederalState())
        );
    }

    private void checkWhetherApplicableOrThrow(RegisterEntry registerEntry) {
        if (LicenseUtils.hasRegularLicense(registerEntry)) {
            throw new IllegalArgumentException("A regular license was found, it can not be added a second one for register Id: " + registerEntry.getRegisterId());
        }

        if (!LicenseUtils.hasValidCertificate(registerEntry)) {
            throw new IllegalStateException("No qualifications proof of type fishing certificate found for register Id: " + registerEntry.getRegisterId());
        }
    }
}
