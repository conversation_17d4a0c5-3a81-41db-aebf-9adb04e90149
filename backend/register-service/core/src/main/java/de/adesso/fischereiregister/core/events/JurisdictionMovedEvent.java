package de.adesso.fischereiregister.core.events;

import de.adesso.fischereiregister.core.model.Ban;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.consent.JurisdictionConsentInfo;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import org.axonframework.modelling.command.TargetAggregateIdentifier;
import org.axonframework.serialization.Revision;

import java.util.List;
import java.util.UUID;

@Revision("2.0")
public record JurisdictionMovedEvent(
        @TargetAggregateIdentifier
        UUID registerId,
        Jurisdiction previousJurisdiction,
        Jurisdiction newJurisdiction,
        JurisdictionConsentInfo consentInfo,
        Ban ban, // if given it means the person had a Ban at the moment of moving
        List<Tax> taxes,
        String salt,
        String issuedByOffice,
        List<IdentificationDocument> identificationDocuments,
        SubmissionType submissionType
) implements AxonEvent {

    public JurisdictionMovedEvent {
        assert taxes != null : "The list of taxes should not be null";
        assert identificationDocuments != null : "The list of identificationDocuments should not be null";
    }
}
