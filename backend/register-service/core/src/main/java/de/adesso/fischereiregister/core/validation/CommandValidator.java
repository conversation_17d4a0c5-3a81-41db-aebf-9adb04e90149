package de.adesso.fischereiregister.core.validation;

import de.adesso.fischereiregister.core.exceptions.AggregateValidationException;
import de.adesso.fischereiregister.core.exceptions.RulesProcessingException;
import de.adesso.fischereiregister.core.model.RegisterEntry;

public interface CommandValidator<T> {
    default void validateOrThrow(final T command) throws AggregateValidationException, RulesProcessingException {
        throw new UnsupportedOperationException("Validation without RegisterEntry CommandValidator<" + command.getClass().getSimpleName() + "> is not supported.");
    }

    default void validateOrThrow(final T command, final RegisterEntry registerEntry) throws AggregateValidationException, RulesProcessingException {
        validateOrThrow(command);
    }
}
