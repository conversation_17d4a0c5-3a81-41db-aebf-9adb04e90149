package de.adesso.fischereiregister.core.exceptions;

import de.adesso.fischereiregister.core.validation.ValidationResult;
import lombok.Getter;

import java.io.Serial;

@Getter
public abstract class AggregateValidationException extends RuntimeException {

    @Serial
    private static final long serialVersionUID = -3807670830989004600L;

    private final ValidationResult validationResult;

    protected AggregateValidationException(ValidationResult validationResult) {
        this.validationResult = validationResult;
    }

}
