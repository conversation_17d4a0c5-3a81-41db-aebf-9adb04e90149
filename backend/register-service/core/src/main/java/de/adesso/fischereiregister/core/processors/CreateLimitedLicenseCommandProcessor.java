package de.adesso.fischereiregister.core.processors;

import de.adesso.fischereiregister.core.commands.CreateLimitedLicenseCommand;
import de.adesso.fischereiregister.core.events.AxonEvent;
import de.adesso.fischereiregister.core.events.LimitedLicenseCreatedEvent;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.core.model.utils.FishingLicenseFactory;
import de.adesso.fischereiregister.core.model.utils.IdentificationDocumentFactory;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AllArgsConstructor
public class CreateLimitedLicenseCommandProcessor implements CommandProcessor<CreateLimitedLicenseCommand> {

    private final IdentificationDocumentFactory identificationDocumentFactory;
    private final FishingLicenseFactory fishingLicenseFactory;

    @Override
    public List<AxonEvent> process(CreateLimitedLicenseCommand command, RegisterEntry registerEntry) {

        return List.of(createEvent(command, registerEntry));
    }

    private LimitedLicenseCreatedEvent createEvent(CreateLimitedLicenseCommand command, RegisterEntry registerEntry) {
        FishingLicense limitedFishingLicense = fishingLicenseFactory.createLimitedFishingLicense(command.registerId(), command.validityPeriod(), FederalState.valueOf(command.userDetails().getFederalState()), command.limitedLicenseApproval());

        List<IdentificationDocument> identificationDocuments = identificationDocumentFactory.createInitialLicenseDocuments(limitedFishingLicense, command.registerId());
        command.taxes().forEach(t -> identificationDocuments.add(identificationDocumentFactory.createIdentificationDocumentForTax(t, command.registerId())));

        final Jurisdiction newJurisdiction = new Jurisdiction();
        newJurisdiction.setFederalState(command.userDetails().getFederalState());

        // In case of limited licenses, the documents should always be sent to the OS inbox
        final String inboxReference = registerEntry.getInboxReference();

        return new LimitedLicenseCreatedEvent(
                command.registerId(),
                command.salt(),
                command.consentInfo(),
                command.person(),
                command.fees(),
                command.taxes(),
                limitedFishingLicense,
                identificationDocuments,
                newJurisdiction,
                command.userDetails().getOffice(),
                command.userDetails().getParsedOfficeAddress(),
                inboxReference,
                null,
                null,
                SubmissionType.ANALOG
        );
    }
}
