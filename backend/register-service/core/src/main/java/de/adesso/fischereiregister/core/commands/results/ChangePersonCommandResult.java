package de.adesso.fischereiregister.core.commands.results;

import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Person;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Getter
@SuperBuilder
public class ChangePersonCommandResult extends CommandResult {
    private Person person;
    protected final List<IdentificationDocument> documents;
}
