package de.adesso.fischereiregister.core.processors;

import de.adesso.fischereiregister.core.commands.RejectLimitedLicenseApplicationCommand;
import de.adesso.fischereiregister.core.events.AxonEvent;
import de.adesso.fischereiregister.core.events.LimitedLicenseApplicationRejectedEvent;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class RejectLimitedLicenseApplicationCommandProcessor implements CommandProcessor<RejectLimitedLicenseApplicationCommand> {

    @Override
    public List<AxonEvent> process(RejectLimitedLicenseApplicationCommand command, RegisterEntry registerEntry) {
        final LimitedLicenseApplicationRejectedEvent event = new LimitedLicenseApplicationRejectedEvent(
                command.registerEntryId(),
                registerEntry.getLimitedLicenseApplication(),
                registerEntry.getPerson(),
                command.userDetails().getOffice(),
                registerEntry.getInboxReference()
        );

        return List.of(event);
    }
}
