package de.adesso.fischereiregister.core.model.utils;

import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.LimitedLicenseApproval;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.ValidityPeriod;
import de.adesso.fischereiregister.core.model.type.IdentificationDocumentType;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.ports.DocumentNumberService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Slf4j
@Component
@AllArgsConstructor
public class IdentificationDocumentFactoryImpl implements IdentificationDocumentFactory {

    DocumentNumberService documentNumberService;

    @Override
    public IdentificationDocument createIdentificationDocumentCardForFishingLicense(FishingLicense fishingLicense, UUID registerEntryId) {
        final IdentificationDocument identificationDocument = new IdentificationDocument();

        identificationDocument.setIssuedDate(LocalDate.now());
        identificationDocument.setFishingLicense(fishingLicense);
        identificationDocument.setDocumentId(documentNumberService.createNewDocumentNumber(registerEntryId, IdentificationDocumentType.CARD));
        identificationDocument.setType(IdentificationDocumentType.CARD);

        // Verify that the license has only one validity period.
        if (fishingLicense.getValidityPeriods().size() != 1) {
            throw new IllegalStateException("Tried creating a card document with multiple validity periods.");
        }

        // only one validity period is allowed here and it is checked above
        identificationDocument.setValidFrom(fishingLicense.getValidityPeriods().get(0).getValidFrom());
        identificationDocument.setValidTo(fishingLicense.getValidityPeriods().get(0).getValidTo());

        return identificationDocument;
    }

    @Override
    public List<IdentificationDocument> createInitialLicenseDocuments(FishingLicense fishingLicense, UUID registerEntryId) {
        List<IdentificationDocument> pdfDocuments = fishingLicense.getValidityPeriods().stream()
                .map(validityPeriod -> createPDFDocumentForValidityPeriod(fishingLicense, validityPeriod, registerEntryId))
                .collect(Collectors.toList());

        if (fishingLicense.getType() == LicenseType.REGULAR || fishingLicense.getType() == LicenseType.LIMITED) {
            IdentificationDocument cardDocument = createIdentificationDocumentCardForFishingLicense(fishingLicense, registerEntryId);
            pdfDocuments.add(cardDocument);
        }

        if (fishingLicense.getType() == LicenseType.LIMITED) {

            pdfDocuments.add(createIdentificationDocumentForLimitedLicenseApproval(fishingLicense.getLimitedLicenseApproval(), registerEntryId));
        }

        return pdfDocuments;
    }

    /**
     * Creates an PDF document for the corresponding validity period.
     *
     * @param fishingLicense
     * @param validityPeriod
     * @return
     */
    @Override
    public IdentificationDocument createPDFDocumentForValidityPeriod(FishingLicense fishingLicense, ValidityPeriod validityPeriod, UUID registerEntryId) {
        final IdentificationDocument identificationDocument = new IdentificationDocument();
        final String documentId = documentNumberService.createNewDocumentNumber(registerEntryId, IdentificationDocumentType.PDF);

        identificationDocument.setIssuedDate(LocalDate.now());
        identificationDocument.setFishingLicense(fishingLicense);
        identificationDocument.setDocumentId(documentId);
        identificationDocument.setType(IdentificationDocumentType.PDF);

        identificationDocument.setValidFrom(validityPeriod.getValidFrom());
        identificationDocument.setValidTo(validityPeriod.getValidTo());

        return identificationDocument;
    }

    @Override
    public IdentificationDocument createIdentificationDocumentForTax(Tax tax, UUID registerEntryId) {
        final IdentificationDocument identificationDocument = new IdentificationDocument();
        identificationDocument.setDocumentId(documentNumberService.createNewDocumentNumber(registerEntryId, IdentificationDocumentType.PDF));
        identificationDocument.setTax(tax);
        identificationDocument.setIssuedDate(LocalDate.now());
        identificationDocument.setType(IdentificationDocumentType.PDF);

        identificationDocument.setValidFrom(tax.getValidFrom());
        identificationDocument.setValidTo(tax.getValidTo());

        return identificationDocument;
    }

    private IdentificationDocument createIdentificationDocumentForLimitedLicenseApproval(LimitedLicenseApproval limitedLicenseApproval, UUID registerEntryId) {
        final IdentificationDocument identificationDocument = new IdentificationDocument();
        identificationDocument.setDocumentId(documentNumberService.createNewDocumentNumber(registerEntryId, IdentificationDocumentType.PDF));
        identificationDocument.setLimitedLicenseApproval(limitedLicenseApproval);
        identificationDocument.setIssuedDate(LocalDate.now());
        identificationDocument.setType(IdentificationDocumentType.PDF);
        identificationDocument.setValidFrom(LocalDate.now());

        return identificationDocument;
    }

}
