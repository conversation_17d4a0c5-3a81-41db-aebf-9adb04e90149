package de.adesso.fischereiregister.core.processors;

import de.adesso.fischereiregister.core.events.AxonEvent;
import de.adesso.fischereiregister.core.model.RegisterEntry;

import java.util.List;

/**
 * This interface should always be implemented, when a new Event Processpr is implemented.
 * <p>
 * Tje purpose of this processor is to subsection and standardize command handlers in the aggregate.
 * <p>
 * Note that generic interfaces cannot be resolved by axon using method injection, so the implementing class has to be used.
 *
 * @param <TCommand> Type of the command from which the Event is created.
 */
public interface CommandProcessor<TCommand> {
    List<AxonEvent> process(TCommand command, RegisterEntry registerEntry);
}
