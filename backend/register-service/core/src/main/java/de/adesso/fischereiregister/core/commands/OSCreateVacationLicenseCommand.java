package de.adesso.fischereiregister.core.commands;

import de.adesso.fischereiregister.core.model.Fee;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.ValidityPeriod;
import de.adesso.fischereiregister.core.model.consent.ConsentInfo;
import de.adesso.fischereiregister.core.model.type.FederalState;
import org.axonframework.modelling.command.TargetAggregateIdentifier;

import java.util.UUID;

public record OSCreateVacationLicenseCommand(
        @TargetAggregateIdentifier UUID registerId,
        Person person,
        ConsentInfo consentInfo,
        Fee fee,
        String salt,
        FederalState federalState,
        ValidityPeriod validityPeriod,
        String transactionId,
        String serviceAccountId,
        String inboxReference
) {
}
