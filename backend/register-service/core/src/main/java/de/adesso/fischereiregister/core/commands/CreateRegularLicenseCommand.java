package de.adesso.fischereiregister.core.commands;

import de.adesso.fischereiregister.core.model.Fee;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.consent.ConsentInfo;
import de.adesso.fischereiregister.core.model.user.UserDetails;
import org.axonframework.modelling.command.TargetAggregateIdentifier;

import java.util.List;
import java.util.UUID;

/**
 * Starts in the search results. There is a fishing exam (and a RegisterEntry
 * available in the system).
 */
public record CreateRegularLicenseCommand(
        @TargetAggregateIdentifier UUID registerId,
        String salt,
        ConsentInfo consentInfo,
        Person person,
        List<Fee> fees,
        List<Tax> taxes,
        UserDetails userDetails) {

    public CreateRegularLicenseCommand {
        assert fees != null : "The list of fees should not be null";
        assert taxes != null : "The list of taxes should not be null";
    }

}
