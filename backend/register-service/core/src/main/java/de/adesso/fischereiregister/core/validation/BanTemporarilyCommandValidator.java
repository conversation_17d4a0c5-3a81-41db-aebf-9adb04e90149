package de.adesso.fischereiregister.core.validation;

import de.adesso.fischereiregister.core.commands.BanTemporarilyCommand;
import de.adesso.fischereiregister.core.exceptions.AggregateValidationException;
import de.adesso.fischereiregister.core.exceptions.ClientInputValidationException;
import de.adesso.fischereiregister.core.exceptions.SystemConfigValidationException;
import de.adesso.fischereiregister.core.ports.CountryService;
import de.adesso.fischereiregister.core.ports.TenantRulesValidationPort;
import de.adesso.fischereiregister.core.validation.utils.DIN91379ValidationNorm;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import static de.adesso.fischereiregister.core.validation.utils.ValidationUtils.validateFieldRequired;
import static de.adesso.fischereiregister.core.validation.utils.ValidationUtils.validateStringField;

@Component
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class BanTemporarilyCommandValidator extends AbstractValidator implements CommandValidator<BanTemporarilyCommand> {

    public BanTemporarilyCommandValidator(CountryService countryService, TenantRulesValidationPort tenantRulesValidationPort) {
        super(countryService, tenantRulesValidationPort);
    }

    @Override
    public void validateOrThrow(BanTemporarilyCommand command) throws AggregateValidationException {
        validateUserDetails(command.userDetails());

        if (validationResult.hasErrors()) {
            // If the user details are invalid, it is due to a system misconfiguration, so we return an exception accordingly.
            throw new SystemConfigValidationException(validationResult);
        }

        validateFieldRequired(command.reportedBy(), BAN_REPORTED_BY, validationResult);
        validateFieldRequired(command.fileNumber(), BAN_FILE_NUMBER, validationResult);
        validateFieldRequired(command.from(), BAN_FROM, validationResult);
        validateFieldRequired(command.to(), BAN_TO, validationResult);

        validateStringField(command.reportedBy(), BAN_REPORTED_BY, 200, DIN91379ValidationNorm.GROUP_B_GENERAL_NAMES, validationResult);
        validateStringField(command.fileNumber(), BAN_FILE_NUMBER, 150, DIN91379ValidationNorm.GROUP_B_GENERAL_NAMES, validationResult);

        if (command.from() != null && command.to() != null && command.to().isBefore(command.from())) {
            validationResult.addErrorNote("The Ban Start Date (from) cannot be after the Ban End Date (to)");
        }

        if (validationResult.hasErrors()) {
            throw new ClientInputValidationException(validationResult);
        }
    }
}
