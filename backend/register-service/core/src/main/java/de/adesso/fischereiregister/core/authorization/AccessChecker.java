package de.adesso.fischereiregister.core.authorization;

import de.adesso.fischereiregister.core.model.Jurisdiction;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.user.UserDetails;
import de.adesso.fischereiregister.core.model.user.UserRole;

import java.util.List;

/**
 * The {@code AccessChecker} class ensures that operations are performed within the frame of the proper jurisdiction
 */
public class AccessChecker {

    private AccessChecker() {
    }

    public static boolean commandTaxesMatchUserJurisdiction(List<Tax> taxes, UserDetails userDetails) {
        if (userDetails == null) {
            throw new IllegalArgumentException("userDetails must not be null");
        }

        if (userDetails.isSystem() || taxes == null || taxes.isEmpty()) { // SYSTEM user does not have federal state so here the federal state is unimportant
            return true;
        }

        // Online Service users do not have a federal state and can create taxes for every federal state
        if (userDetails.getRoles().contains(UserRole.ONLINE_SERVICE)) {
            return true;
        }

        return taxes.stream().allMatch(tax -> taxIsForFederalState(tax, userDetails.getFederalState()));
    }

    private static boolean taxIsForFederalState(Tax tax, String federalState) {
        return tax != null && federalState != null && federalState.equalsIgnoreCase(tax.getFederalState());
    }

    public static boolean registerEntryInUserJurisdiction(Jurisdiction jurisdiction, UserDetails userDetails) {
        if (userDetails == null) {
            throw new IllegalArgumentException("userDetails must not be null");
        }
        // SYSTEM user has jurisdiction over all registers
        if (userDetails.isSystem()){
            return true;
        }
        if(jurisdiction == null) {
            return false;
        }
        return jurisdiction.getFederalState().equalsIgnoreCase(userDetails.getFederalState());
    }

}
