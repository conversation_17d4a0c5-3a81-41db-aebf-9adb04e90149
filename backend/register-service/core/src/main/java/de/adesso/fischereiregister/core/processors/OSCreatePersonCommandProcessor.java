package de.adesso.fischereiregister.core.processors;

import de.adesso.fischereiregister.core.commands.OSCreatePersonCommand;
import de.adesso.fischereiregister.core.events.AxonEvent;
import de.adesso.fischereiregister.core.events.PersonCreatedEvent;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.core.model.utils.IdentificationDocumentFactory;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Component
@AllArgsConstructor
public class OSCreatePersonCommandProcessor extends OSCommandProcessor implements CommandProcessor<OSCreatePersonCommand> {

    private final IdentificationDocumentFactory identificationDocumentFactory;

    @Override
    public List<AxonEvent> process(OSCreatePersonCommand command, RegisterEntry registerEntry) {
        final Jurisdiction newjurisdiction = new Jurisdiction();
        newjurisdiction.setFederalState(command.federalState());

        final List<IdentificationDocument> taxIdentificationDocuments = new ArrayList<>();
        command.taxes().forEach(t -> taxIdentificationDocuments.add(identificationDocumentFactory.createIdentificationDocumentForTax(t, command.registerId())));

        // we also have to set the transaction ID in the payment info of the taxes
        processTaxesTransactionId(command.transactionId(), command.taxes());

        return List.of(new PersonCreatedEvent(
                command.registerId(),
                command.person(),
                command.taxes(),
                Collections.emptyList(),
                command.salt(),
                taxIdentificationDocuments,
                command.consentInfo(),
                null,
                command.inboxReference(),
                command.serviceAccountId(),
                command.transactionId(),
                command.federalState(),
                SubmissionType.ONLINE));
    }
}
