package de.adesso.fischereiregister.core.events;

import de.adesso.fischereiregister.core.model.LimitedLicenseApplication;
import de.adesso.fischereiregister.core.model.Person;
import org.axonframework.modelling.command.TargetAggregateIdentifier;

import java.util.UUID;

public record LimitedLicenseApplicationRejectedEvent(
        @TargetAggregateIdentifier UUID registerEntryId,
        LimitedLicenseApplication limitedLicenseApplication, // The limited license application that was denied
        Person person, // Only needed to OS message
        String issuedByOffice,
        String inboxReference // To which inbox the notification should be sent, that application was not successful
) implements AxonEvent {
}
