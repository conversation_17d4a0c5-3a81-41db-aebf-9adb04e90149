package de.adesso.fischereiregister.core.validation;


import de.adesso.fischereiregister.core.commands.MoveJurisdictionCommand;
import de.adesso.fischereiregister.core.exceptions.AggregateValidationException;
import de.adesso.fischereiregister.core.exceptions.ClientInputValidationException;
import de.adesso.fischereiregister.core.exceptions.RulesProcessingException;
import de.adesso.fischereiregister.core.exceptions.SystemConfigValidationException;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.ports.CountryService;
import de.adesso.fischereiregister.core.ports.TenantRulesValidationPort;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import static de.adesso.fischereiregister.core.validation.utils.ValidationUtils.validateFieldRequired;

@Component
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class MoveJurisdictionCommandValidator extends AbstractValidator implements CommandValidator<MoveJurisdictionCommand> {

    public MoveJurisdictionCommandValidator(CountryService countryService, TenantRulesValidationPort tenantRulesValidationPort) {
        super(countryService, tenantRulesValidationPort);
    }

    @Override
    public void validateOrThrow(MoveJurisdictionCommand command) throws AggregateValidationException, RulesProcessingException {
        validateUserDetails(command.userDetails());

        if (command.userDetails() != null) {
            validateFieldRequired(command.userDetails().getOffice(), OFFICE, validationResult);
        }

        validateFieldRequired(command.salt(), SALT, validationResult);

        if (validationResult.hasErrors()) {
            // if the user details, or the salt values are invalid is because of a System misconfiguration, so we return an exception accordingly
            throw new SystemConfigValidationException(validationResult);
        }

        validateConsentInfo(command.consentInfo());
        validateTaxes(command.taxes());

        if (command.userDetails() != null) {
            validateTenantRules(FederalState.valueOf(command.userDetails().getFederalState()), null, command.taxes(), null, null);
        }


        if (validationResult.hasErrors()) {
            throw new ClientInputValidationException(validationResult);
        }
    }
}
