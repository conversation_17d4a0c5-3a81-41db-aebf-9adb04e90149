package de.adesso.fischereiregister.core.validation.helper;

import de.adesso.fischereiregister.core.model.user.UserDetails;
import de.adesso.fischereiregister.core.validation.ValidationResult;
import de.adesso.fischereiregister.core.validation.utils.ValidationUtils;

public class UserDetailsValidatorHelper {

    private static final String USER_DETAILS = "User Details";
    private static final String USER_ID = "User Id";

    private UserDetailsValidatorHelper() {
    }

    public static void validate(UserDetails userDetails, ValidationResult validationResult) {
        ValidationUtils.validateFieldRequired(userDetails, USER_DETAILS, validationResult);
        if (userDetails != null) {
            ValidationUtils.validateFieldRequired(userDetails.getUserId(), USER_ID, validationResult);
        }
    }
}