package de.adesso.fischereiregister.core.model;

import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.LimitedLicenseApplicationStatus;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;

import java.time.LocalDate;
import java.util.UUID;

@Getter
@Setter
@FieldNameConstants
public class LimitedLicenseApplication {
    private UUID id;

    private LimitedLicenseApplicationStatus status;

    private LocalDate createdAt;

    private FederalState federalState;

    private String disabilityCertificateFileURL;
}
