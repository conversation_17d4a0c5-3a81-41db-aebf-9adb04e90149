package de.adesso.fischereiregister.core.processors;

import de.adesso.fischereiregister.core.commands.OSCreateRegularLicenseCommand;
import de.adesso.fischereiregister.core.events.AxonEvent;
import de.adesso.fischereiregister.core.events.JurisdictionMovedEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseCreatedEvent;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.consent.ConsentInfo;
import de.adesso.fischereiregister.core.model.consent.JurisdictionConsentInfo;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.core.model.utils.FishingLicenseFactory;
import de.adesso.fischereiregister.core.model.utils.IdentificationDocumentFactory;
import de.adesso.fischereiregister.core.utils.LicenseUtils;
import de.adesso.fischereiregister.core.utils.PersonUtils;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@AllArgsConstructor
@Component
public class OSCreateRegularLicenseCommandProcessor extends OSCommandProcessor implements CommandProcessor<OSCreateRegularLicenseCommand> {

    private final IdentificationDocumentFactory identificationDocumentFactory;
    private final FishingLicenseFactory fishingLicenseFactory;

    @Override
    public List<AxonEvent> process(final OSCreateRegularLicenseCommand command, final RegisterEntry registerEntry) {
        List<AxonEvent> events = new ArrayList<>();

        if (registerEntry.getJurisdiction() == null || !registerEntry.getJurisdiction().getFederalState().equals(command.federalState())) {
            final JurisdictionMovedEvent movedEvent = createMoveJurisdictionEvent(registerEntry, command.federalState(), command.consentInfo());
            events.add(movedEvent);
        }

        final RegularLicenseCreatedEvent createdEvent = createLicenseCreatedEvent(command, registerEntry);
        events.add(createdEvent);

        return events;
    }

    private JurisdictionMovedEvent createMoveJurisdictionEvent(RegisterEntry registerEntry, String federalState, ConsentInfo consentInfo) {
        Jurisdiction previousJurisdiction = registerEntry.getJurisdiction();

        Jurisdiction newJurisdiction = new Jurisdiction();
        newJurisdiction.setFederalState(federalState);

        // For OS services no proof of move is needed, thus verification is set automatically
        JurisdictionConsentInfo jurisdictionConsentInfo = new JurisdictionConsentInfo();
        jurisdictionConsentInfo.setProofOfMoveVerified(true);
        jurisdictionConsentInfo.setGdprAccepted(consentInfo.getGdprAccepted());
        jurisdictionConsentInfo.setSelfDisclosureAccepted(consentInfo.getSelfDisclosureAccepted());
        jurisdictionConsentInfo.setSubmittedByThirdParty(consentInfo.getSubmittedByThirdParty());

        return new JurisdictionMovedEvent(
                registerEntry.getRegisterId(),
                previousJurisdiction,
                newJurisdiction,
                jurisdictionConsentInfo,
                registerEntry.getBan(),
                List.of(),
                null, // Since no identification documents are created, no salt is needed,
                null,
                List.of(), // Don't create Identification documents, this will be done in the second event
                SubmissionType.ONLINE
        );
    }

    private RegularLicenseCreatedEvent createLicenseCreatedEvent(OSCreateRegularLicenseCommand command, RegisterEntry registerEntry) {

        checkWhetherCommandApplicableOrThrow(command, registerEntry);

        final FishingLicense fishingLicense = createFishingLicense(command.registerId(), command, registerEntry.getJurisdiction());

        final List<IdentificationDocument> documents = createDocuments(fishingLicense, command.taxes(), command.registerId());

        final Jurisdiction newJurisdiction = new Jurisdiction();
        newJurisdiction.setFederalState(command.federalState());

        // we also have to set the transaction ID in the payment info of the taxes or fees
        processFeesTransactionId(command.transactionId(), command.fees());
        processTaxesTransactionId(command.transactionId(), command.taxes());

        return new RegularLicenseCreatedEvent(command.registerId(),
                command.salt(),
                command.consentInfo(),
                command.person(),
                command.fees(),
                command.taxes(),
                fishingLicense,
                documents,
                newJurisdiction,
                null,
                command.federalState(),
                command.inboxReference(),
                command.serviceAccountId(),
                command.transactionId(),
                SubmissionType.ONLINE
        );
    }

    private FishingLicense createFishingLicense(UUID registerEntryId, OSCreateRegularLicenseCommand command, Jurisdiction jurisdiction) {
        // Setting the issuingFederalState. Might be null if only the certificate exists.
        final String issuingFederalState = jurisdiction != null ?
                jurisdiction.getFederalState() :
                command.federalState();

        return fishingLicenseFactory.createRegularFishingLicense(
                registerEntryId,
                // null ist in dem fall richtig weil keine Office Address existiert
                FederalState.valueOf(issuingFederalState)
        );
    }

    private List<IdentificationDocument> createDocuments(FishingLicense license, List<Tax> taxes, UUID registerEntryId) {
        final List<IdentificationDocument> licenseDocuments = identificationDocumentFactory.createInitialLicenseDocuments(license, registerEntryId);

        final List<IdentificationDocument> taxDocuments = taxes.stream()
                .map(tax -> identificationDocumentFactory.createIdentificationDocumentForTax(tax, registerEntryId))
                .collect(Collectors.toCollection(ArrayList::new));

        List<IdentificationDocument> documents = new ArrayList<>();
        documents.addAll(licenseDocuments);
        documents.addAll(taxDocuments);

        return documents;
    }

    private void checkWhetherCommandApplicableOrThrow(OSCreateRegularLicenseCommand command, RegisterEntry registerEntry) {
        // ONLY If the request is submitted by a third party, the person request data should
        boolean submittedByThirdParty = command.consentInfo().getSubmittedByThirdParty();

        if (submittedByThirdParty
                && !PersonUtils.matches(registerEntry.getPerson(), command.person())) {
            throw new IllegalStateException("For a third party request person data has to match, for: " + command.registerId());
        }

        if (LicenseUtils.hasRegularLicense(registerEntry)) {
            throw new IllegalArgumentException("A regular license was found, it can not be added a second one for register Id: " + command.registerId());
        }

        if (!LicenseUtils.hasValidCertificate(registerEntry)) {
            throw new IllegalStateException("No qualifications proof of type fishing certificate found for register Id: " + command.registerId());
        }
    }
}
