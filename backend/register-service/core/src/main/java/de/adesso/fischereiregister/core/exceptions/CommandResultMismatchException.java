package de.adesso.fischereiregister.core.exceptions;

import lombok.Getter;

import java.io.Serial;

@Getter
public class CommandResultMismatchException extends RuntimeException {

    @Serial
    private static final long serialVersionUID = 2118346148232139783L;

    public CommandResultMismatchException(Class<?> expectedType, Class<?> actualType) {
        super("The return of the command had an unexpected format. Expected" + expectedType.getSimpleName() + "but was " + actualType.getSimpleName());
    }
}
