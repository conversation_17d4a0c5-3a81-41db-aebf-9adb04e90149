package de.adesso.fischereiregister.core.processors;

import de.adesso.fischereiregister.core.commands.OSReplaceCardCommand;
import de.adesso.fischereiregister.core.events.AxonEvent;
import de.adesso.fischereiregister.core.events.JurisdictionMovedEvent;
import de.adesso.fischereiregister.core.events.ReplacementCardOrderedEvent;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.consent.ConsentInfo;
import de.adesso.fischereiregister.core.model.consent.JurisdictionConsentInfo;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.core.model.utils.IdentificationDocumentFactory;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
@AllArgsConstructor
public class OSReplacementCardOrderedCommandProcessor extends OSCommandProcessor implements CommandProcessor<OSReplaceCardCommand> {

    private final IdentificationDocumentFactory identificationDocumentFactory;

    public List<AxonEvent> process(OSReplaceCardCommand command, RegisterEntry registerEntry) {
        List<AxonEvent> events = new ArrayList<>();

        if (!registerEntry.getJurisdiction().getFederalState().equals(command.federalState())) {
            JurisdictionMovedEvent movedEvent = createMoveJurisdictionEvent(registerEntry, command.federalState(), command.consentInfo());
            events.add(movedEvent);
        }

        ReplacementCardOrderedEvent reorderEvent = createReorderEvent(command, registerEntry);
        events.add(reorderEvent);

        return events;
    }

    private JurisdictionMovedEvent createMoveJurisdictionEvent(RegisterEntry registerEntry, String federalState, ConsentInfo consentInfo) {
        Jurisdiction previousJurisdiction = registerEntry.getJurisdiction();

        Jurisdiction newJurisdiction = new Jurisdiction();
        newJurisdiction.setFederalState(federalState);

        // For OS services no proof of move is needed, thus verification is set automatically
        JurisdictionConsentInfo jurisdictionConsentInfo = new JurisdictionConsentInfo();
        jurisdictionConsentInfo.setProofOfMoveVerified(true);
        jurisdictionConsentInfo.setGdprAccepted(consentInfo.getGdprAccepted());
        jurisdictionConsentInfo.setSelfDisclosureAccepted(consentInfo.getSelfDisclosureAccepted());
        jurisdictionConsentInfo.setSubmittedByThirdParty(consentInfo.getSubmittedByThirdParty());

        return new JurisdictionMovedEvent(
                registerEntry.getRegisterId(),
                previousJurisdiction,
                newJurisdiction,
                jurisdictionConsentInfo,
                registerEntry.getBan(),
                List.of(),
                null, // Since no identification documents are created, no salt is needed,
                null,
                List.of(), // Don't create Identification documents, this will be done in the second event
                SubmissionType.ONLINE
        );
    }

    private ReplacementCardOrderedEvent createReorderEvent(OSReplaceCardCommand command, RegisterEntry registerEntry) throws IllegalStateException {
        FishingLicense license = getLicenseForReorder(registerEntry, command.licenseNumber());

        FederalState federalState = FederalState.valueOf(command.federalState());

        final List<IdentificationDocument> documents = getNewLicenseDocuments(command, license);

        // we also have to set the transaction ID in the payment info of the taxes or fees
        processFeesTransactionId(command.transactionId(), command.fees());
        processTaxesTransactionId(command.transactionId(), command.taxes());
        
        return new ReplacementCardOrderedEvent(
                registerEntry.getRegisterId(),
                license,
                command.person(),
                documents,
                command.salt(),
                federalState,
                null,
                command.federalState(),
                command.fees(),
                command.taxes(),
                command.consentInfo(),
                command.inboxReference(),
                command.serviceAccountId(),
                command.transactionId(),
                SubmissionType.ONLINE
        );
    }


    private FishingLicense getLicenseForReorder(RegisterEntry registerEntry, String licenseNumber) throws IllegalStateException {
        List<FishingLicense> regularLicenses = registerEntry.getFishingLicenses().stream()
                .filter(license -> license.getType() == LicenseType.REGULAR)
                .toList();

        // If there is more then one Regular license, a fishingLicense ID from the command MUST match
        // In most cases, there is only one regular license, so the ID may be ignored.
        if (regularLicenses.size() > 1) {
            regularLicenses = regularLicenses.stream()
                    .filter(license -> license.getNumber().equals(licenseNumber))
                    .toList();
        }

        return regularLicenses.stream()
                .findAny()
                .orElseThrow(() -> new IllegalStateException("Tried reordering regular fishing license card, but no matching card could be found."));
    }

    private List<IdentificationDocument> getNewLicenseDocuments(OSReplaceCardCommand command, FishingLicense license) {
        final List<IdentificationDocument> documents = new ArrayList<>();

        final List<IdentificationDocument> taxDocuments = command.taxes().stream()
                .map(tax -> identificationDocumentFactory.createIdentificationDocumentForTax(tax, command.registerId()))
                .collect(Collectors.toCollection(ArrayList::new));
        documents.addAll(taxDocuments);


        // All Documents have to be created when a new license is ordered
        final List<IdentificationDocument> licensePDFDocuments = license.getValidityPeriods().stream()
                .filter(period -> period.getValidTo() == null || period.getValidTo().isAfter(LocalDate.now()))
                .map(period -> identificationDocumentFactory.createPDFDocumentForValidityPeriod(license, period, command.registerId()))
                .collect(Collectors.toCollection(ArrayList::new));
        documents.addAll(licensePDFDocuments);

        final IdentificationDocument replacementCardDocument = identificationDocumentFactory.createIdentificationDocumentCardForFishingLicense(license, command.registerId());
        documents.add(replacementCardDocument);

        return documents;
    }
}
