package de.adesso.fischereiregister.core.validation.utils;

import de.adesso.fischereiregister.core.validation.ValidationResult;

import java.util.Collection;
import java.util.regex.Pattern;

public class ValidationUtils {

    private ValidationUtils() {
    }

    public static void validateStringField(String field, String fieldName, int maxLength, DIN91379ValidationNorm validationNorm, ValidationResult validationResult) {
        validateStringField(field, fieldName, maxLength, validationNorm.getRegexPattern(), validationResult);
    }

    public static void validateEmailField(String email, String fieldName, ValidationResult validationResult) {
        if(!isValidEmail(email)) {
            validationResult.addErrorNote(fieldName + " is not a valid email address");
        }
    }

    public static boolean isValidEmail(String email) {
        return email != null && email.matches(RegexConstants.EMAIL_REGEX);
    }

    public static void validateStringField(String field, String fieldName, int maxLength, Pattern regexPattern, ValidationResult validationResult) {
        if (field == null) {
            return;
        }
        if (field.length() > maxLength) {
            validationResult.addErrorNote(fieldName + " is too long (max length: " + maxLength + ")");
        }

        if (regexPattern == null) {
            return;
        }

        if (!regexPattern.matcher(field).matches()) {
            validationResult.addErrorNote(fieldName + " contains invalid characters.");
        }
    }


    public static void validateFieldRequired(String field, String fieldName, ValidationResult validationResult) {
        if (field == null || field.isBlank()) {
            validationResult.addErrorNote(fieldName + " is required");
        }
    }

    public static <T> void validateFieldRequired(Collection<T> field, String fieldName, ValidationResult validationResult) {
        if (field == null || field.isEmpty()) {
            validationResult.addErrorNote(fieldName + " are required");
        }
    }

    public static <T> void validateFieldRequired(T field, String fieldName, ValidationResult validationResult) {
        if (field == null) {
            validationResult.addErrorNote(fieldName + " is required");
        }
    }

    public static void validateBooleanFieldIsTrue(Boolean field, String fieldName, ValidationResult validationResult) {
        if (field == null || !field) {
            validationResult.addErrorNote(fieldName + " must be set to true");
        }
    }
}