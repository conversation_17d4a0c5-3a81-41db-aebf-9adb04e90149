package de.adesso.fischereiregister.core.events;

import de.adesso.fischereiregister.core.model.Ban;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import org.axonframework.modelling.command.TargetAggregateIdentifier;

import java.util.UUID;

public record BannedEvent(
        @TargetAggregateIdentifier UUID registerId,
        Ban ban,
        Jurisdiction jurisdiction
) implements AxonEvent {
}
