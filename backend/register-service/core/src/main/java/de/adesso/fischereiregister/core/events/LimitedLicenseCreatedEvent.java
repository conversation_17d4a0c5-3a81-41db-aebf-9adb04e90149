package de.adesso.fischereiregister.core.events;

import de.adesso.fischereiregister.core.model.Fee;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.consent.LimitedLicenseConsentInfo;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import org.axonframework.modelling.command.TargetAggregateIdentifier;

import java.util.List;
import java.util.UUID;

public record LimitedLicenseCreatedEvent (
        @TargetAggregateIdentifier UUID registerId,
        String salt,
        LimitedLicenseConsentInfo limitedLicenseConsentInfo,
        Person person,
        List<Fee> fees,
        List<Tax> taxes,
        FishingLicense fishingLicense,
        List<IdentificationDocument> identificationDocuments,
        Jurisdiction jurisdiction,
        String issuedByOffice,
        String issuedByAddress,
        String inboxReference, // if null no message will be send to the online service portal (OS)
        String serviceAccountId, // id of the service account in the online service portal (OS)
        String transactionId, // transaction id of the online service portal (OS)
        SubmissionType submissionType
) implements AxonEvent {

    public LimitedLicenseCreatedEvent {
        assert fees != null : "The list of fees should not be null";
        assert taxes != null : "The list of taxes should not be null";
        assert identificationDocuments != null : "The list of identificationDocuments should not be null";
    }
}
