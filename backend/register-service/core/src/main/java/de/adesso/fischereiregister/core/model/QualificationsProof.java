package de.adesso.fischereiregister.core.model;

import de.adesso.fischereiregister.core.model.type.QualificationsProofType;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

@Getter
@Setter
@EqualsAndHashCode
public class QualificationsProof {

    /**
     * Type of QualificationsProof
     */
    private QualificationsProofType type;



    /**
     * Certificate Id
     */
    private String fishingCertificateId;


    /**
     * Id  Id
     */
    private String otherFormOfProofId;
    
    /**
     * The federal state (Bundesland) where the certificate was issued.
     */
    private String federalState;

    /**
     * The examiner who issued the certificate.
     */
    private String examinerId;

    /**
     * The date when the certificate examination was passed.
     */
    private LocalDate passedOn;

    /**
     * The jurisdiction or entity that issued the certificate.
     */
    private String issuedBy;

    public QualificationsProof(String examinerId,
                               String fishingCertificateId,
                               String issuedBy,
                               QualificationsProofType type,
                               String federalState,
                               LocalDate passedOn) {

        this.examinerId = examinerId;
        this.fishingCertificateId = fishingCertificateId;
        this.issuedBy = issuedBy;
        this.type = type;
        this.federalState = federalState;
        this.passedOn = passedOn;
    }

    public QualificationsProof() {
    }
}
