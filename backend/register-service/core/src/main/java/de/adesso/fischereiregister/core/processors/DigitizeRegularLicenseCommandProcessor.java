package de.adesso.fischereiregister.core.processors;

import de.adesso.fischereiregister.core.commands.DigitizeRegularLicenseCommand;
import de.adesso.fischereiregister.core.events.AxonEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseDigitizedEvent;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.user.UserDetails;
import de.adesso.fischereiregister.core.model.utils.FishingLicenseFactory;
import de.adesso.fischereiregister.core.model.utils.IdentificationDocumentFactory;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Component
@AllArgsConstructor
public class DigitizeRegularLicenseCommandProcessor implements CommandProcessor<DigitizeRegularLicenseCommand> {

    private final IdentificationDocumentFactory identificationDocumentFactory;
    private final FishingLicenseFactory fishingLicenseFactory;

    @Override
    public List<AxonEvent> process(DigitizeRegularLicenseCommand command, RegisterEntry registerEntry) {
        final UserDetails userDetails = command.userDetails();

        final Jurisdiction jurisdiction = new Jurisdiction();
        jurisdiction.setFederalState(userDetails.getFederalState());

        // here it is critical to take the registerEntryId from the command as the given register Entry is not created yet
        final FishingLicense fishingLicense = createFishingLicenseWithDefaults(command.registerId(), userDetails);

        final List<Tax> combinedTaxes = new ArrayList<>();
        combinedTaxes.addAll(command.taxes());
        combinedTaxes.addAll(command.payedTaxes());

        final List<IdentificationDocument> identificationDocuments = identificationDocumentFactory.createInitialLicenseDocuments(fishingLicense, command.registerId());
        combinedTaxes.forEach(tax -> identificationDocuments.add(identificationDocumentFactory.createIdentificationDocumentForTax(tax, command.registerId())));

        final RegularLicenseDigitizedEvent event = new RegularLicenseDigitizedEvent(command.registerId(),
                command.salt(),
                command.person(),
                jurisdiction,
                fishingLicense,
                command.fees(),
                combinedTaxes,
                command.qualificationsProofs(),
                identificationDocuments,
                command.consentInfo(),
                userDetails.getOffice(),
                command.userDetails().getParsedOfficeAddress()
        );

        return List.of(event);
    }

    public FishingLicense createFishingLicenseWithDefaults(UUID registerEntryId, UserDetails userDetails) {
        return fishingLicenseFactory.createRegularFishingLicense(
                registerEntryId,
                FederalState.valueOf(userDetails.getFederalState())
        );
    }
}
