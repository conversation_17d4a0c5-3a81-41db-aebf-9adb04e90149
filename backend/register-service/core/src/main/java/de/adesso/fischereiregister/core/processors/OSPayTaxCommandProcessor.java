package de.adesso.fischereiregister.core.processors;

import de.adesso.fischereiregister.core.commands.OSPayTaxCommand;
import de.adesso.fischereiregister.core.events.AxonEvent;
import de.adesso.fischereiregister.core.events.FishingTaxPayedEvent;
import de.adesso.fischereiregister.core.exceptions.PersonNotChangeableException;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.core.model.utils.IdentificationDocumentFactory;
import de.adesso.fischereiregister.core.utils.PersonUtils;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
@AllArgsConstructor
public class OSPayTaxCommandProcessor extends OSCommandProcessor implements CommandProcessor<OSPayTaxCommand> {

    private final IdentificationDocumentFactory identificationDocumentFactory;

    @Override
    public List<AxonEvent> process(OSPayTaxCommand command, RegisterEntry registerEntry) {
        final List<IdentificationDocument> identificationDocuments = command.taxes().stream()
                .map(tax -> identificationDocumentFactory.createIdentificationDocumentForTax(tax, command.registerId()))
                .collect(Collectors.toCollection(ArrayList::new));

        // Person data cannot be changes with this command
        if (!PersonUtils.matches(registerEntry.getPerson(), command.person())) {
            throw new PersonNotChangeableException();
        }

        // we also have to set the transaction ID in the payment info of the taxes
        processTaxesTransactionId(command.transactionId(), command.taxes());

        final FishingTaxPayedEvent event = new FishingTaxPayedEvent(
                command.registerId(),
                command.consentInfo(),
                command.person(),
                command.taxes(),
                command.salt(),
                identificationDocuments,
                null,
                command.inboxReference(),
                command.serviceAccountId(),
                command.transactionId(),
                SubmissionType.ONLINE
        );

        return List.of(event);
    }
}
