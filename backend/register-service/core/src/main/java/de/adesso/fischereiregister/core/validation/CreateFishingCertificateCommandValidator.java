package de.adesso.fischereiregister.core.validation;

import de.adesso.fischereiregister.core.commands.CreateFishingCertificateCommand;
import de.adesso.fischereiregister.core.exceptions.AggregateValidationException;
import de.adesso.fischereiregister.core.exceptions.ClientInputValidationException;
import de.adesso.fischereiregister.core.exceptions.RulesProcessingException;
import de.adesso.fischereiregister.core.exceptions.SystemConfigValidationException;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.ports.CountryService;
import de.adesso.fischereiregister.core.ports.TenantRulesValidationPort;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

@Component
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class CreateFishingCertificateCommandValidator extends AbstractValidator implements CommandValidator<CreateFishingCertificateCommand> {

    public CreateFishingCertificateCommandValidator(CountryService countryService, TenantRulesValidationPort tenantRulesValidationPort) {
        super(countryService, tenantRulesValidationPort);
    }

    @Override
    public void validateOrThrow(CreateFishingCertificateCommand command) throws AggregateValidationException, RulesProcessingException {
        validateUserDetails(command.userDetails());

        if (validationResult.hasErrors()) {
            // If the user details are invalid, it is due to a system misconfiguration, so we return an exception accordingly.
            throw new SystemConfigValidationException(validationResult);
        }

        validatePerson(command.person(), false);

        tenantRulesValidationPort.validateUsingQualificationProofRules(FederalState.valueOf(command.userDetails().getFederalState()), command.person(), validationResult);

        if (validationResult.hasErrors()) {
            throw new ClientInputValidationException(validationResult);
        }
    }
}
