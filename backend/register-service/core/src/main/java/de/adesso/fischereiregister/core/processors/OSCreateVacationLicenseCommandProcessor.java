package de.adesso.fischereiregister.core.processors;

import de.adesso.fischereiregister.core.commands.OSCreateVacationLicenseCommand;
import de.adesso.fischereiregister.core.events.AxonEvent;
import de.adesso.fischereiregister.core.events.VacationLicenseCreatedEvent;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.core.model.utils.FishingLicenseFactory;
import de.adesso.fischereiregister.core.model.utils.IdentificationDocumentFactory;
import de.adesso.fischereiregister.core.service.SeparatedPrice;
import de.adesso.fischereiregister.core.service.VacationFeeSeparationService;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@AllArgsConstructor
public class OSCreateVacationLicenseCommandProcessor extends OSCommandProcessor implements CommandProcessor<OSCreateVacationLicenseCommand> {
    private FishingLicenseFactory fishingLicenseFactory;
    private IdentificationDocumentFactory identificationDocumentFactory;
    private VacationFeeSeparationService vacationFeeSeparationService;

    /**
     * @param registerEntry The RegisterEntry might be null here, since the command may be called for register entry creation
     */
    @Override
    @SneakyThrows
    public List<AxonEvent> process(OSCreateVacationLicenseCommand command, RegisterEntry registerEntry) {
        final FishingLicense license = fishingLicenseFactory.createVacationLicense(command.registerId(), command.validityPeriod(), command.federalState());

        final SeparatedPrice separatedPrice = vacationFeeSeparationService.separateTaxesAndFees(command.validityPeriod(), command.fee(), command.federalState());

        List<IdentificationDocument> documents = new ArrayList<>();

        List<IdentificationDocument> licenseDocuments = identificationDocumentFactory.createInitialLicenseDocuments(license, command.registerId());
        documents.addAll(licenseDocuments);

        List<IdentificationDocument> taxDocuments = separatedPrice.getTaxes().stream()
                .map(tax -> identificationDocumentFactory.createIdentificationDocumentForTax(tax, command.registerId()))
                .toList();
        documents.addAll(taxDocuments);

        // we also have to set the transaction ID in the payment info of the taxes or fees
        processTaxesTransactionId(command.transactionId(), separatedPrice.getTaxes());
        processFeesTransactionId(command.transactionId(), separatedPrice.getFees());

        VacationLicenseCreatedEvent event = new VacationLicenseCreatedEvent(
                command.registerId(),
                command.person(),
                command.salt(),
                command.consentInfo(),
                separatedPrice.getFees(),
                separatedPrice.getTaxes(),
                documents,
                license,
                null,
                command.inboxReference(),
                command.serviceAccountId(),
                command.transactionId(),
                SubmissionType.ONLINE
        );

        return List.of(event);
    }

}
