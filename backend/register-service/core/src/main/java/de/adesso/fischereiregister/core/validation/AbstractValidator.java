package de.adesso.fischereiregister.core.validation;

import de.adesso.fischereiregister.core.exceptions.RulesProcessingException;
import de.adesso.fischereiregister.core.model.Address;
import de.adesso.fischereiregister.core.model.Fee;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.QualificationsProof;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.ValidityPeriod;
import de.adesso.fischereiregister.core.model.consent.AbstractConsentInfo;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.model.user.UserDetails;
import de.adesso.fischereiregister.core.ports.CountryService;
import de.adesso.fischereiregister.core.ports.TenantRulesValidationPort;
import de.adesso.fischereiregister.core.validation.helper.ConsentInfoValidatorHelper;
import de.adesso.fischereiregister.core.validation.helper.FederalStateValidatorHelper;
import de.adesso.fischereiregister.core.validation.helper.FeeValidatorHelper;
import de.adesso.fischereiregister.core.validation.helper.PersonValidatorHelper;
import de.adesso.fischereiregister.core.validation.helper.QualificationsProofValidatorHelper;
import de.adesso.fischereiregister.core.validation.helper.TaxValidatorHelper;
import de.adesso.fischereiregister.core.validation.helper.TenantValidatorHelper;
import de.adesso.fischereiregister.core.validation.helper.UserDetailsValidatorHelper;
import de.adesso.fischereiregister.core.validation.helper.ValidityPeriodValidatorHelper;

import java.util.List;

public abstract class AbstractValidator {

    protected static final String LICENSE_NUMBER = "License number";
    protected static final String FISHING_CERTIFICATE_CODE = "Fishing certificate code";
    protected static final String SERVICE_ACCOUNT_ID = "Service account id";
    protected static final String TRANSACTION_ID = "Transaction id";
    protected static final String TAXES = "Taxes";
    protected static final String FEES = "Fees";
    protected static final String SALT = "Salt";
    protected static final String OFFICE = "User Office";
    protected static final String OFFICE_ADDRESS = "User Office Address";

    // Ban fields
    protected static final String BAN_FILE_NUMBER = "File Number";
    protected static final String BAN_REPORTED_BY = "Reported By";
    protected static final String BAN_FROM = "Ban Start date (from)";
    protected static final String BAN_TO = "Ban End date (to)";

    protected ValidationResult validationResult;
    protected CountryService countryService;
    protected TenantRulesValidationPort tenantRulesValidationPort;

    protected AbstractValidator(CountryService countryService, TenantRulesValidationPort tenantRulesValidationPort) {
        this.validationResult = new ValidationResult();
        this.countryService = countryService;
        this.tenantRulesValidationPort = tenantRulesValidationPort;
    }

    protected void validateQualificationProofs(List<QualificationsProof> qualificationsProofs) {
        QualificationsProofValidatorHelper.validateQualificationsProofs(qualificationsProofs, validationResult);
    }

    protected void validateTaxes(List<Tax> taxes) {
        TaxValidatorHelper.validateTaxes(taxes, validationResult);
    }

    protected void validatePreviouslyPayedTaxes(List<Tax> payedTaxes) {
        TaxValidatorHelper.validateTaxes(payedTaxes, validationResult);
        TaxValidatorHelper.validatePreviouslyPayedTaxes(payedTaxes, validationResult);
    }

    protected void validateFees(List<Fee> fees) {
        FeeValidatorHelper.validateFees(fees, validationResult);
    }

    protected void validateFederalState(String federalState) {
        FederalStateValidatorHelper.validateFederalState(federalState, validationResult);
    }

    protected void validateConsentInfo(AbstractConsentInfo consentInfo) {
        ConsentInfoValidatorHelper.validate(consentInfo, this.validationResult);
    }

    protected void validatePerson(Person person, boolean validateNationality) {
        PersonValidatorHelper.validate(person, this.countryService, this.validationResult, validateNationality);
    }

    protected void validatePersonUnchangeableData(Person person, Person otherPerson) {
        PersonValidatorHelper.validateUnchangeableDataMatches(person, otherPerson, this.validationResult);
    }

    protected void validateAddress(Address address) {
        PersonValidatorHelper.validateAddress(address, this.validationResult);
    }

    protected void validateValidityPeriod(ValidityPeriod validityPeriod) {
        ValidityPeriodValidatorHelper.validate(validityPeriod, this.validationResult);
    }

    protected void validateValidityPeriod(ValidityPeriod validityPeriod, boolean validToRequired, boolean validFromEqualsToday) {
        ValidityPeriodValidatorHelper.validate(validityPeriod, validToRequired, validFromEqualsToday, this.validationResult);
    }

    protected void validateUserDetails(UserDetails userDetails) {
        validateUserDetails(userDetails, true);
    }

    protected void validateUserDetails(UserDetails userDetails, boolean validateFederalState) {
        UserDetailsValidatorHelper.validate(userDetails, this.validationResult);
        if (userDetails != null && validateFederalState) {
            FederalStateValidatorHelper.validateFederalState(userDetails.getFederalState(), validationResult);
        }
    }

    protected void validateTenantRules(FederalState federalState, Person person, List<Tax> taxes, List<Fee> fees, LicenseType licenseType) throws RulesProcessingException {
        try {
            TenantValidatorHelper.validateTenantRules(this.tenantRulesValidationPort, federalState, person, taxes, fees, licenseType, validationResult);
        } catch (Exception e) {
            validationResult.addErrorNote("Error during tenant validation: " + e.getMessage());
        }
    }

}