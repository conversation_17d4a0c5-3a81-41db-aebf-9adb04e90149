package de.adesso.fischereiregister.core.processors;

import de.adesso.fischereiregister.core.commands.OSCreateLimitedLicenseApplicationCommand;
import de.adesso.fischereiregister.core.events.AxonEvent;
import de.adesso.fischereiregister.core.events.JurisdictionMovedEvent;
import de.adesso.fischereiregister.core.events.LimitedLicenseApplicationCreatedEvent;
import de.adesso.fischereiregister.core.model.Fee;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import de.adesso.fischereiregister.core.model.LimitedLicenseApplication;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.consent.ConsentInfo;
import de.adesso.fischereiregister.core.model.consent.JurisdictionConsentInfo;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.LimitedLicenseApplicationStatus;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Component
public class OSCreateLimitedLicenseApplicationCommandProcessor extends OSCommandProcessor implements CommandProcessor<OSCreateLimitedLicenseApplicationCommand> {

    @Override
    public List<AxonEvent> process(OSCreateLimitedLicenseApplicationCommand command, RegisterEntry registerEntry) {
        List<AxonEvent> events = new ArrayList<>();

        final LimitedLicenseApplicationCreatedEvent createdEvent = createApplicationEvent(command);
        events.add(createdEvent);

        if (registerEntry.getJurisdiction() == null || !registerEntry.getJurisdiction().getFederalState().equals(command.federalState().toString())) {
            final JurisdictionMovedEvent jurisdictionEvent = createMoveJurisdictionEvent(registerEntry, command.registerEntryId(), command.federalState(), command.consentInfo());
            events.add(jurisdictionEvent);
        }

        return events;
    }

    private JurisdictionMovedEvent createMoveJurisdictionEvent(RegisterEntry registerEntry, UUID registerEntryId, FederalState federalState, ConsentInfo consentInfo) {
        Jurisdiction previousJurisdiction = registerEntry.getJurisdiction();

        Jurisdiction newJurisdiction = new Jurisdiction();
        newJurisdiction.setFederalState(federalState.toString());

        // For OS services no proof of move is needed, thus verification is set automatically
        JurisdictionConsentInfo jurisdictionConsentInfo = new JurisdictionConsentInfo();
        jurisdictionConsentInfo.setProofOfMoveVerified(true);
        jurisdictionConsentInfo.setGdprAccepted(consentInfo.getGdprAccepted());
        jurisdictionConsentInfo.setSelfDisclosureAccepted(consentInfo.getSelfDisclosureAccepted());
        jurisdictionConsentInfo.setSubmittedByThirdParty(consentInfo.getSubmittedByThirdParty());

        return new JurisdictionMovedEvent(
                registerEntryId,
                previousJurisdiction,
                newJurisdiction,
                jurisdictionConsentInfo,
                registerEntry.getBan(),
                List.of(),
                null, // Since no identification documents are created, no salt is needed,
                null,
                List.of(), // Don't create Identification documents, this will be done in the second event
                SubmissionType.ONLINE
        );
    }

    private LimitedLicenseApplicationCreatedEvent createApplicationEvent(OSCreateLimitedLicenseApplicationCommand command) {
        final LimitedLicenseApplication limitedLicenseApplication = createLimitedLicenseApplication(command);

        List<Fee> fees = List.of(command.fee());
        processFeesTransactionId(command.transactionId(), fees);

        return new LimitedLicenseApplicationCreatedEvent(
                command.registerEntryId(),
                limitedLicenseApplication,
                command.person(),
                fees,
                command.consentInfo(),
                command.inboxReference(),
                command.serviceAccountId()
        );
    }

    private LimitedLicenseApplication createLimitedLicenseApplication(OSCreateLimitedLicenseApplicationCommand command) {
        LimitedLicenseApplication limitedLicenseApplication = new LimitedLicenseApplication();

        limitedLicenseApplication.setCreatedAt(LocalDate.now());
        limitedLicenseApplication.setStatus(LimitedLicenseApplicationStatus.PENDING);
        limitedLicenseApplication.setFederalState(command.federalState());
        limitedLicenseApplication.setDisabilityCertificateFileURL(command.disabilityCertificateFileURL());
        limitedLicenseApplication.setId(UUID.randomUUID());

        return limitedLicenseApplication;
    }
}
