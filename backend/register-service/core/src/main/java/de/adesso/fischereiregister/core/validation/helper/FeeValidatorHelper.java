package de.adesso.fischereiregister.core.validation.helper;

import de.adesso.fischereiregister.core.model.Fee;
import de.adesso.fischereiregister.core.validation.ValidationResult;

import java.util.List;

import static de.adesso.fischereiregister.core.validation.utils.ValidationUtils.validateFieldRequired;

public class FeeValidatorHelper {


    static final String FEDERAL_STATE = "Fee.federalState";
    static final String VALID_FROM = "Fee.validFrom";
    static final String PAYMENT_INFO = "Fee.paymentInfo";
    static final String PAYMENT_INFO_TYPE = "Fee.paymentInfo.type";
    static final String PAYMENT_INFO_AMOUNT = "Fee.paymentInfo.amount";

    private FeeValidatorHelper() {
    }

    public static void validateFees(List<Fee> feeList, ValidationResult validationResult) {
        feeList.forEach(fee -> validateFee(fee, validationResult));
    }

    public static void validateFee(Fee fee, ValidationResult validationResult) {
        if (fee == null) {
            return;
        }

        validateFieldRequired(fee.getFederalState(), FEDERAL_STATE, validationResult);
        validateFieldRequired(fee.getValidFrom(), VALID_FROM, validationResult);
        validateFieldRequired(fee.getPaymentInfo(), PAYMENT_INFO, validationResult);
        if (fee.getPaymentInfo() != null) {
            validateFieldRequired(fee.getPaymentInfo().getType(), PAYMENT_INFO_TYPE, validationResult);
            validateFieldRequired(fee.getPaymentInfo().getAmount(), PAYMENT_INFO_AMOUNT, validationResult);
        }
    }
}
