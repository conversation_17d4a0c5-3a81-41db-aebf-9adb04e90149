package de.adesso.fischereiregister.core.ports;

import de.adesso.fischereiregister.core.exceptions.RulesProcessingException;
import de.adesso.fischereiregister.core.model.Fee;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.validation.ValidationResult;

import java.util.List;

/**
 * Interface for validation domain data based on tenant specific rules (federal state specific conditions).
 * This approach externalizes business logic for validation because its different for different tenants and,
 * makes it flexible and easier to manage.
 * <p>
 * This Interface ensures compliance with federal state (the tenant) regulations.
 */
public interface TenantRulesValidationPort {

    /**
     * Validates domain data based on validation rules for fishing licenses which are tenant specific.
     * This method ensures compliance with federal state (the tenant) regulations.
     *
     * @param federalState     the federal state
     * @param person           the person
     * @param licenseType      the license type
     * @param validationResult the validation result to store any validation errors
     * @throws RulesProcessingException if an error occurs during rules processing
     */
    void validateUsingFishingLicenseRules(FederalState federalState, Person person, LicenseType licenseType, ValidationResult validationResult) throws RulesProcessingException;

    /**
     * Validates the given domain data based on validation rules for fishing certificates which are tenant specific.
     * This method ensures compliance with federal state (the tenant) regulations.
     *
     * @param federalState     the federal state
     * @param person           the person
     * @param validationResult the validation result to store any validation errors
     * @throws RulesProcessingException if an error occurs during rules processing
     */
    void validateUsingQualificationProofRules(FederalState federalState, Person person, ValidationResult validationResult) throws RulesProcessingException;

    /**
     * Validates the given domain data based on validation rules for fishing taxes.
     * This method ensures compliance with federal state (the tenant) regulations.
     *
     * @param federalState     the federal state
     * @param person           The Person
     * @param taxes            the list of taxes
     * @param validationResult the validation result to store any validation errors
     * @throws RulesProcessingException if an error occurs during rules processing
     */
    void validateUsingFishingTaxRules(FederalState federalState, Person person, List<Tax> taxes, ValidationResult validationResult) throws RulesProcessingException;

    /**
     * Validates the given taxes and especially the prices in the tax based on validation rules for taxes which are tenant specific.
     * This method ensures compliance with federal state (the tenant) regulations.
     *
     * @param federalState          the federal state
     * @param taxes                 the list of taxes
     * @param officeFeeAlreadyPayed whether the office fee is already paid
     * @param validationResult      the validation result to store any validation errors
     * @throws RulesProcessingException if an error occurs during rules processing
     */
    void validateTaxes(FederalState federalState, List<Tax> taxes, boolean officeFeeAlreadyPayed, ValidationResult validationResult) throws RulesProcessingException;

    /**
     * Validates the given fees and especially the prices based on validation rules for fees which are tenant specific.
     * This method ensures compliance with federal state (the tenant) regulations.
     *
     * @param federalState     the federal state
     * @param licenseType      the license type
     * @param fees             the list of fees
     * @param validationResult the validation result to store any validation errors
     * @throws RulesProcessingException if an error occurs during rules processing
     */
    void validateFeesForFishingLicense(FederalState federalState, LicenseType licenseType, List<Fee> fees, ValidationResult validationResult) throws RulesProcessingException;
}
