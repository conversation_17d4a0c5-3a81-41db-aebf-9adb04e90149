package de.adesso.fischereiregister.core.validation.helper;

import de.adesso.fischereiregister.core.exceptions.LicenseNotExtendableException;
import de.adesso.fischereiregister.core.exceptions.LicenseNotFoundException;
import de.adesso.fischereiregister.core.exceptions.RulesProcessingException;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.ValidityPeriod;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.model.type.PaymentType;
import de.adesso.fischereiregister.core.ports.TenantInformationPort;
import de.adesso.fischereiregister.core.ports.contracts.LicenseInformation;
import de.adesso.fischereiregister.core.validation.ValidationResult;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class LicenseValidatorHelper {
    private final TenantInformationPort tenantInformationPort;

    /**
     * @param registerEntry     The register entry to fetch the license from
     * @param licenseNumber     The number of the license that should be extended
     * @param newValidityPeriod The new validity period of the license
     * @param userFederalState  The federal state of the User / request
     * @param paymentType       The payment type of the corresponding request to the register (A license extension has to be paid)
     * @param validationResult  The validation result to store any validation errors
     * @throws RulesProcessingException When the Tenant rules on license extendability could not be read
     */
    public void validateLicenseIsExtendable(
            RegisterEntry registerEntry,
            String licenseNumber,
            ValidityPeriod newValidityPeriod,
            FederalState userFederalState,
            PaymentType paymentType,
            ValidationResult validationResult) throws RulesProcessingException {
        final FishingLicense fishingLicense = registerEntry.getFishingLicenses().stream()
                .filter(fl -> fl.getNumber().equals(licenseNumber))
                .findAny()
                .orElseThrow(LicenseNotFoundException::new);

        if (!fishingLicense.getIssuingFederalState().equals(userFederalState)) {
            validationResult.addErrorNote("Users can only extend licenses within their own jurisdiction. User jurisdiction: " + userFederalState.toString());
        }

        validateTenantInfo(fishingLicense, paymentType);

        validateNewPeriod(fishingLicense, newValidityPeriod, validationResult);
    }

    /**
     * Checks if a license type is available in the given tenant.
     *
     * @param federalState The federal state of the tenant
     * @param licenseType  The type of the license to check
     * @return true if the license is available, false otherwise
     * @throws RulesProcessingException if an error occurs while retrieving the license information
     */
    public boolean isLicenseAvailable(FederalState federalState, LicenseType licenseType) throws RulesProcessingException {
        final LicenseInformation licenseInfo = tenantInformationPort.getLicenseInformation(
                federalState,
                licenseType,
                PaymentType.CASH);

        return licenseInfo.isAvailable();
    }

    private void validateTenantInfo(FishingLicense fishingLicense, PaymentType paymentType) throws RulesProcessingException {
        final LicenseInformation licenseInfo = tenantInformationPort.getLicenseInformation(
                fishingLicense.getIssuingFederalState(),
                fishingLicense.getType(),
                paymentType);

        if (!licenseInfo.isExtendable()) {
            throw new LicenseNotExtendableException(fishingLicense.getNumber());
        }
    }

    private void validateNewPeriod(FishingLicense fishingLicense, ValidityPeriod newValidityPeriod, ValidationResult validationResult) {
        if (fishingLicense.getValidityPeriods().size() > 1) {
            validationResult.addErrorNote("A Vacation/Foreigner License can only be extended once");
        }

        final ValidityPeriod currentValidityPeriod = fishingLicense.getValidityPeriods().getFirst();
        if (currentValidityPeriod.getValidTo().isAfter(newValidityPeriod.getValidFrom())) {
            validationResult.addErrorNote("Der neue Gültigkeitszeitraum darf den bisherigen nicht überschneiden.");
        }
        if (currentValidityPeriod.getValidFrom().getYear() != newValidityPeriod.getValidFrom().getYear()) {
            validationResult.addErrorNote("Der neue Gültigkeitszeitraum muss in dem Selben Jahr beginnen wir der bisherige.");
        }
    }
}
