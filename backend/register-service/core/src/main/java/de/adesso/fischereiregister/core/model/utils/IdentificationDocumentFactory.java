package de.adesso.fischereiregister.core.model.utils;

import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.ValidityPeriod;

import java.util.List;
import java.util.UUID;

public interface IdentificationDocumentFactory {

    IdentificationDocument createIdentificationDocumentCardForFishingLicense(FishingLicense fishingLicense, UUID registerEntryId);

    List<IdentificationDocument> createInitialLicenseDocuments(FishingLicense fishingLicense, UUID registerEntryId);

    IdentificationDocument createPDFDocumentForValidityPeriod(FishingLicense fishingLicense, ValidityPeriod validityPeriod, UUID registerEntryId);

    IdentificationDocument createIdentificationDocumentForTax(Tax tax, UUID registerEntryId);

}
