package de.adesso.fischereiregister.core.validation.helper;

import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.validation.ValidationResult;

public class FederalStateValidatorHelper {

    private FederalStateValidatorHelper() {
    }

    public static void validateFederalState(String federalState, ValidationResult validationResult) {
        if (!FederalState.isValid(federalState)) {
            validationResult.addErrorNote("Federal state " + federalState + " is invalid.");
        }
    }

    public static void validateFederalState(FederalState federalState, ValidationResult validationResult) {
        if (federalState == null) {
            validationResult.addErrorNote("Federal state is required.");
        }
    }

}
