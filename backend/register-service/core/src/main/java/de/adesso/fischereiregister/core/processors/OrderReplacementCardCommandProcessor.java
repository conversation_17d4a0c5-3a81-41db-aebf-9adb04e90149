package de.adesso.fischereiregister.core.processors;

import de.adesso.fischereiregister.core.commands.OrderReplacementCardCommand;
import de.adesso.fischereiregister.core.events.AxonEvent;
import de.adesso.fischereiregister.core.events.ReplacementCardOrderedEvent;
import de.adesso.fischereiregister.core.exceptions.LicenseNotFoundException;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.core.model.utils.IdentificationDocumentFactory;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
@AllArgsConstructor
public class OrderReplacementCardCommandProcessor implements CommandProcessor<OrderReplacementCardCommand> {

    private final IdentificationDocumentFactory identificationDocumentFactory;

    @Override
    public List<AxonEvent> process(OrderReplacementCardCommand command, RegisterEntry registerEntry) {
        final FederalState federalState = FederalState.valueOf(command.userDetails().getFederalState());
        FishingLicense fishingLicense = getLicenseForReorder(registerEntry, command.fishingLicenseNumber());

        List<IdentificationDocument> documents = command.taxes().stream()
                .map(tax -> identificationDocumentFactory.createIdentificationDocumentForTax(tax, command.registerId()))
                .collect(Collectors.toCollection(ArrayList::new));
        final IdentificationDocument replacementCardDocument = identificationDocumentFactory.createIdentificationDocumentCardForFishingLicense(fishingLicense, command.registerId());

        documents.addAll(
                fishingLicense.getValidityPeriods().stream()
                        .filter(period -> period.getValidTo() == null || period.getValidTo().isAfter(LocalDate.now()))
                        .map(period -> identificationDocumentFactory.createPDFDocumentForValidityPeriod(fishingLicense, period, command.registerId()))
                        .collect(Collectors.toCollection(ArrayList::new)));
        documents.add(replacementCardDocument);


        ReplacementCardOrderedEvent event = new ReplacementCardOrderedEvent(
                registerEntry.getRegisterId(),
                fishingLicense,
                command.person(),
                documents,
                command.salt(),
                federalState,
                command.userDetails().getOffice(),
                command.userDetails().getParsedOfficeAddress(),
                command.fees(),
                command.taxes(),
                command.consentInfo(),
                null, // null because it is no OD call,
                null, // null because it is no OD call,
                null, // null because it is no OD call,
                SubmissionType.ANALOG
        );

        return List.of(event);
    }

    private FishingLicense getLicenseForReorder(RegisterEntry registerEntry, String licenseNumber) throws IllegalStateException {
        return registerEntry.getFishingLicenses().stream()
                .filter(license -> license.getNumber().equals(licenseNumber))
                .findAny().orElseThrow((() -> new LicenseNotFoundException("Tried reordering regular fishing license card, but no matching card could be found.")));
    }
}
