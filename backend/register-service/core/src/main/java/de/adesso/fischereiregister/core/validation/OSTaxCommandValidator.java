package de.adesso.fischereiregister.core.validation;

import de.adesso.fischereiregister.core.commands.contracts.OSTaxCommand;
import de.adesso.fischereiregister.core.exceptions.ClientInputValidationException;
import de.adesso.fischereiregister.core.exceptions.SystemConfigValidationException;
import de.adesso.fischereiregister.core.ports.CountryService;
import de.adesso.fischereiregister.core.ports.TenantRulesValidationPort;
import de.adesso.fischereiregister.core.validation.helper.OSValidationHelper;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import static de.adesso.fischereiregister.core.validation.utils.ValidationUtils.validateFieldRequired;

@Component
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class OSTaxCommandValidator extends AbstractValidator implements CommandValidator<OSTaxCommand> {

    public OSTaxCommandValidator(CountryService countryService, TenantRulesValidationPort tenantRulesValidationPort) {
        super(countryService, tenantRulesValidationPort);
    }

    @Override
    public void validateOrThrow(final OSTaxCommand command) {
        validateFieldRequired(command.salt(), SALT, validationResult);

        if (validationResult.hasErrors()) {
            // If the salt value is invalid, it is due to a system misconfiguration, so we return an exception accordingly.
            throw new SystemConfigValidationException(validationResult);
        }

        validatePerson(command.person(), true);
        // here we dont need to validate any address as for paying a tax there is no documents delivered
        validateFederalState(command.federalState());
        validateTaxes(command.taxes());
        validateConsentInfo(command.consentInfo());

        OSValidationHelper.validateOSRequiredFields(command.serviceAccountId(), command.transactionId(), command.inboxReference(), validationResult);

        if (validationResult.hasErrors()) {
            throw new ClientInputValidationException(validationResult);
        }
    }

}
