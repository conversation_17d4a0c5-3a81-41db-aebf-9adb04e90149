package de.adesso.fischereiregister.core.validation.helper;

import de.adesso.fischereiregister.core.model.Address;
import de.adesso.fischereiregister.core.model.Birthdate;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.ports.CountryService;
import de.adesso.fischereiregister.core.utils.PersonUtils;
import de.adesso.fischereiregister.core.validation.ValidationResult;
import de.adesso.fischereiregister.core.validation.utils.DIN91379ValidationNorm;
import de.adesso.fischereiregister.core.validation.utils.RegexConstants;

import java.util.Set;

import static de.adesso.fischereiregister.core.validation.utils.ValidationUtils.validateFieldRequired;
import static de.adesso.fischereiregister.core.validation.utils.ValidationUtils.validateStringField;


public class PersonValidatorHelper {

    private static final Set<String> VALID_TITLES = Set.of("DR.", "DR. HC.", "DR. EH.");

    private PersonValidatorHelper() {
    }

    public static void validate(Person person, CountryService countryService, ValidationResult validationResult, boolean validateNationality) {
        if (person == null) {
            validationResult.addErrorNote("Person Details is required");
            return;
        }

        validateFieldRequired(person.getFirstname(), "Firstname", validationResult);
        validateFieldRequired(person.getLastname(), "Lastname", validationResult);
        validateFieldRequired(person.getBirthplace(), "Birthplace", validationResult);

        validateStringField(person.getFirstname(), "Firstname", 80, DIN91379ValidationNorm.GROUP_A_PERSONS, validationResult);
        validateStringField(person.getLastname(), "Lastname", 120, DIN91379ValidationNorm.GROUP_A_PERSONS, validationResult);
        validateStringField(person.getBirthname(), "Birthname", 120, DIN91379ValidationNorm.GROUP_A_PERSONS, validationResult);
        validateStringField(person.getBirthplace(), "Birthplace", 120, DIN91379ValidationNorm.GROUP_B_GENERAL_NAMES, validationResult);

        validateBirthdate(person.getBirthdate(), validationResult);
        if (validateNationality) {
            validateNationality(person.getNationality(), countryService, validationResult);
        }
        validateTitle(person.getTitle(), validationResult);
    }

    public static void validateBirthdate(Birthdate birthdate, ValidationResult validationResult) {
        if (!BirthdateValidatorHelper.isBirthdateValid(birthdate)) {
            validationResult.addErrorNote("Birthdate is not a valid date");
        }
    }

    public static void validateNationality(String nationality, CountryService countryService, ValidationResult validationResult) {
        if (!countryService.isNationalityValid(nationality)) {
            validationResult.addErrorNote("Nationality is not valid according to the DESTATIS List, wrong value: " + nationality);
        }
    }

    public static void validateTitle(String title, ValidationResult validationResult) {
        if (title != null && !title.isBlank() && !isValidTitle(title)) { // title might be null, but if given it must be a valid title
            validationResult.addErrorNote("Title is not valid");
        }
    }

    private static boolean isValidTitle(String input) {
        return VALID_TITLES.contains(input.toUpperCase());
    }

    public static void validateAddress(Address address, ValidationResult validationResult) {
        validateFieldRequired(address, "address", validationResult);
        if (address != null) {
            // Validate all required fields
            validateFieldRequired(address.getStreetNumber(), "streetNumber", validationResult);
            validateFieldRequired(address.getStreet(), "street", validationResult);
            validateFieldRequired(address.getPostcode(), "postcode", validationResult);
            validateFieldRequired(address.getCity(), "city", validationResult);

            // Validate all string fields
            validateStringField(address.getDeliverTo(), "deliverTo", 200, DIN91379ValidationNorm.GROUP_A_PERSONS, validationResult);
            validateStringField(address.getOffice(), "office", 200, DIN91379ValidationNorm.GROUP_B_GENERAL_NAMES, validationResult);
            validateStringField(address.getStreet(), "street", 55, DIN91379ValidationNorm.GROUP_B_GENERAL_NAMES, validationResult);
            validateStringField(address.getStreetNumber(), "streetNumber", 11, DIN91379ValidationNorm.GROUP_B_GENERAL_NAMES, validationResult);
            validateStringField(address.getCity(), "city", 50, DIN91379ValidationNorm.GROUP_B_GENERAL_NAMES, validationResult);
            validateStringField(address.getDetail(), "detail", 100, DIN91379ValidationNorm.GROUP_B_GENERAL_NAMES, validationResult);

            validateStringField(address.getPostcode(), "postcode", 5, RegexConstants.POSTCODE_REGEX_PATTERN, validationResult);

        }
    }

    public static void validateUnchangeableDataMatches(Person person, Person otherPerson, ValidationResult validationResult) {
        if (person == null || otherPerson == null) return;

        if (!PersonUtils.unchangeableDataMatches(person, otherPerson)) {
            validationResult.addErrorNote("Unchangeable data [birthname, birthplace, birthdate] do not match");
        }
    }
}
