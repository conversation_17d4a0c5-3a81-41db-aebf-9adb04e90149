package de.adesso.fischereiregister.core.validation.helper;

import de.adesso.fischereiregister.core.model.ValidityPeriod;
import de.adesso.fischereiregister.core.validation.ValidationResult;

import java.time.LocalDate;

import static de.adesso.fischereiregister.core.validation.utils.ValidationUtils.validateFieldRequired;

public class ValidityPeriodValidatorHelper {
    private static final String VALIDITY_PERIOD = "Validity Period";
    private static final String VALID_FROM = "Validity Period - Valid From";
    private static final String VALID_TO = "Validity Period - Valid To";

    private ValidityPeriodValidatorHelper() {
        throw new IllegalStateException("Utility class");
    }

    public static void validate(ValidityPeriod validityPeriod, ValidationResult result) {
        validate(validityPeriod, true, false, result);
    }

    public static void validate(ValidityPeriod validityPeriod, boolean validToRequired, boolean validFromEqualsToday, ValidationResult result) {
        validateFieldRequired(validityPeriod, VALIDITY_PERIOD, result);
        if(validityPeriod != null){
            validateFieldRequired(validityPeriod.getValidFrom(), VALID_FROM, result);

            if(validToRequired) {
                validateFieldRequired(validityPeriod.getValidTo(), VALID_TO, result);
            }
            if(validFromEqualsToday) {
                validateValidFromEqualsToday(validityPeriod, result);
            }
            if (validityPeriod.getValidFrom() != null && validityPeriod.getValidTo() != null && validityPeriod.getValidTo().isBefore(validityPeriod.getValidFrom())) {
                result.addErrorNote("The Validity Period Start Date (validFrom) cannot be after the End Date (validTo)");
            }
        }
    }

    private static void validateValidFromEqualsToday(ValidityPeriod validityPeriod, ValidationResult result) {
        if (validityPeriod != null && validityPeriod.getValidFrom() != null &&
                !validityPeriod.getValidFrom().isEqual(LocalDate.now())) {
            result.addErrorNote("ValidityPeriod" + VALID_FROM + " must be today");
        }
    }
}
