package de.adesso.fischereiregister.core.validation;

import de.adesso.fischereiregister.core.commands.ExtendLicenseCommand;
import de.adesso.fischereiregister.core.exceptions.AggregateValidationException;
import de.adesso.fischereiregister.core.exceptions.ClientInputValidationException;
import de.adesso.fischereiregister.core.exceptions.RulesProcessingException;
import de.adesso.fischereiregister.core.exceptions.SystemConfigValidationException;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.model.type.PaymentType;
import de.adesso.fischereiregister.core.ports.CountryService;
import de.adesso.fischereiregister.core.ports.TenantRulesValidationPort;
import de.adesso.fischereiregister.core.validation.helper.LicenseValidatorHelper;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import static de.adesso.fischereiregister.core.validation.utils.ValidationUtils.validateFieldRequired;

@Component
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class ExtendLicenseCommandValidator extends AbstractValidator implements CommandValidator<ExtendLicenseCommand> {

    private final LicenseValidatorHelper licenseValidatorHelper;

    public ExtendLicenseCommandValidator(CountryService countryService, TenantRulesValidationPort tenantRulesValidationPort, LicenseValidatorHelper tenantInformationPort) {
        super(countryService, tenantRulesValidationPort);

        this.licenseValidatorHelper = tenantInformationPort;
    }

    @Override
    public void validateOrThrow(ExtendLicenseCommand command, RegisterEntry registerEntry) throws AggregateValidationException, RulesProcessingException {
        if (registerEntry == null) {
            validateOrThrow(command);
            return;
        }

        validateSystemRequirements(command);
        if (validationResult.hasErrors()) {
            // If the user details or the salt values are invalid, it is due to a system misconfiguration, so we return an exception accordingly.
            throw new SystemConfigValidationException(validationResult);
        }

        // Fees are needed to retrieve tenant information (paymenttype)
        validateLicenseIsExtendable(command, registerEntry);

        validateFields(command);
        validateTenantRules(FederalState.valueOf(command.userDetails().getFederalState()), command.person(), command.taxes(), command.fees(), LicenseType.VACATION);

        if (validationResult.hasErrors()) {
            throw new ClientInputValidationException(validationResult);
        }
    }

    private void validateFields(ExtendLicenseCommand command) {
        validatePerson(command.person(), true);
        validateFees(command.fees());
        validateTaxes(command.taxes());
        validateConsentInfo(command.consentInfo());
        validateValidityPeriod(command.validityPeriod());

        validateFieldRequired(command.fees(), FEES, validationResult);
        validateFieldRequired(command.licenseNumber(), LICENSE_NUMBER, validationResult);
    }

    private void validateSystemRequirements(ExtendLicenseCommand command) {
        validateUserDetails(command.userDetails());
        if (command.userDetails() != null) {
            validateFieldRequired(command.userDetails().getOffice(), OFFICE, validationResult);
        }
        validateFieldRequired(command.salt(), SALT, validationResult);
    }

    private void validateLicenseIsExtendable(ExtendLicenseCommand command, RegisterEntry registerEntry) throws RulesProcessingException {
        if (command.fees().isEmpty()) {
            return; // IN which case the validators will still return a validation error
        }

        final FederalState userFederalState = FederalState.valueOf(command.userDetails().getFederalState());
        final PaymentType paymentType = command.fees().getFirst().getPaymentInfo().getType();

        licenseValidatorHelper.validateLicenseIsExtendable(registerEntry, command.licenseNumber(), command.validityPeriod(), userFederalState, paymentType, validationResult);
    }
}
