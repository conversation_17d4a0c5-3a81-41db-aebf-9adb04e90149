package de.adesso.fischereiregister.core.events;

import de.adesso.fischereiregister.core.model.Fee;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.consent.ConsentInfo;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import org.axonframework.modelling.command.TargetAggregateIdentifier;

import java.util.List;
import java.util.UUID;

public record VacationLicenseCreatedEvent(
        @TargetAggregateIdentifier UUID registerEntryId,
        Person person,
        String salt,
        ConsentInfo consentInfo,
        List<Fee> fees,
        List<Tax> taxes,
        List<IdentificationDocument> identificationDocuments,
        FishingLicense fishingLicense,
        String issuedByOffice,
        String inboxReference, // if null no message will be send to the online service portal (OS)
        String serviceAccountId, // id of the service account in the online service portal (OS)
        String transactionId, // transaction id of the online service portal (OS)
        SubmissionType submissionType
) implements AxonEvent {
}
