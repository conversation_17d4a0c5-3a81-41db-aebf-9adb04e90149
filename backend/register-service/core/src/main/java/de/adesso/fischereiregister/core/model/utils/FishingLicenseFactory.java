package de.adesso.fischereiregister.core.model.utils;

import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.LimitedLicenseApproval;
import de.adesso.fischereiregister.core.model.ValidityPeriod;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.ports.FishingLicenseNumberService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Component
@AllArgsConstructor
public class FishingLicenseFactory {

    private final FishingLicenseNumberService fishingLicenseNumberService;

    public FishingLicense createRegularFishingLicense(UUID registerEntryId,
                                                      FederalState issuingFederalState) {

        final String licenseNumber = fishingLicenseNumberService.createNewAvailableFishingLicenseNumber(registerEntryId, issuingFederalState);

        // In case of a type REGULAR license there exists only one validity period, so it can be set directly
        // A regular fishing license has no expiration date
        final ValidityPeriod validityPeriod = new ValidityPeriod();
        validityPeriod.setValidFrom(LocalDate.now());
        validityPeriod.setValidTo(null);

        final FishingLicense fishingLicense = new FishingLicense();
        fishingLicense.setType(LicenseType.REGULAR);
        fishingLicense.setNumber(licenseNumber);
        fishingLicense.setIssuingFederalState(issuingFederalState);

        fishingLicense.setValidityPeriods(new ArrayList<>(List.of(validityPeriod)));


        return fishingLicense;
    }

    public FishingLicense createVacationLicense(UUID registerEntryId, ValidityPeriod validityPeriod, FederalState federalState) {

        final String licenseNumber = fishingLicenseNumberService.createNewAvailableFishingLicenseNumber(registerEntryId, federalState);

        final FishingLicense fishingLicense = new FishingLicense();
        fishingLicense.setType(LicenseType.VACATION);
        fishingLicense.setNumber(licenseNumber);
        fishingLicense.setIssuingFederalState(federalState);

        fishingLicense.setValidityPeriods(new ArrayList<>(List.of(validityPeriod)));

        return fishingLicense;
    }

    public FishingLicense createLimitedFishingLicense(UUID registerEntryId,
                                                      ValidityPeriod validityPeriod,
                                                      FederalState federalState,
                                                      LimitedLicenseApproval limitedLicenseApproval) {

        final String licenseNumber = fishingLicenseNumberService.createNewAvailableFishingLicenseNumber(registerEntryId, federalState);
        limitedLicenseApproval.setLimitedLicenseApprovalId(UUID.randomUUID()); // it is ok in this case to generate a new UUID here and not in a service because we do not import this a own element and also we do not have a view with this ids

        final FishingLicense fishingLicense = new FishingLicense();
        fishingLicense.setType(LicenseType.LIMITED);
        fishingLicense.setNumber(licenseNumber);
        fishingLicense.setIssuingFederalState(federalState);
        fishingLicense.setLimitedLicenseApproval(limitedLicenseApproval);

        fishingLicense.setValidityPeriods(new ArrayList<>(List.of(validityPeriod)));

        return fishingLicense;
    }
}
