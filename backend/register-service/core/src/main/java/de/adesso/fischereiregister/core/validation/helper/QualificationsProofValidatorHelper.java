package de.adesso.fischereiregister.core.validation.helper;

import de.adesso.fischereiregister.core.model.QualificationsProof;
import de.adesso.fischereiregister.core.model.type.QualificationsProofType;
import de.adesso.fischereiregister.core.validation.ValidationResult;
import de.adesso.fischereiregister.core.validation.utils.DIN91379ValidationNorm;

import java.util.List;

import static de.adesso.fischereiregister.core.validation.utils.ValidationUtils.validateFieldRequired;
import static de.adesso.fischereiregister.core.validation.utils.ValidationUtils.validateStringField;

public class QualificationsProofValidatorHelper {

    private static final String PASSED_ON = "QualificationsProof.PassedOn";
    private static final String ISSUED_BY = "QualificationsProof.IssuedBy";
    private static final String FEDERAL_STATE = "QualificationsProof.FederalState";

    private QualificationsProofValidatorHelper() {
    }

    public static void validateQualificationsProofs(List<QualificationsProof> qualificationsProofs, ValidationResult validationResult) {
        for (QualificationsProof qualificationsProof : qualificationsProofs) {
            validateFieldRequired(qualificationsProof.getPassedOn(), PASSED_ON, validationResult);
            validateFieldRequired(qualificationsProof.getIssuedBy(), ISSUED_BY, validationResult);
            // if Qualifications Proof Type is OTHER then we dont need to validate if the federal state is present
            if (qualificationsProof.getType() != QualificationsProofType.OTHER) {
                validateFieldRequired(qualificationsProof.getFederalState(), FEDERAL_STATE, validationResult);
            }
            validateStringField(qualificationsProof.getIssuedBy(), ISSUED_BY, 200, DIN91379ValidationNorm.GROUP_B_GENERAL_NAMES, validationResult);
        }
    }
}
