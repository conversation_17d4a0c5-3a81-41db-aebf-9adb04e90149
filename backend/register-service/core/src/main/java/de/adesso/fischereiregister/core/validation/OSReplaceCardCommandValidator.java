package de.adesso.fischereiregister.core.validation;

import de.adesso.fischereiregister.core.commands.OSReplaceCardCommand;
import de.adesso.fischereiregister.core.exceptions.AggregateValidationException;
import de.adesso.fischereiregister.core.exceptions.ClientInputValidationException;
import de.adesso.fischereiregister.core.exceptions.SystemConfigValidationException;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.ports.CountryService;
import de.adesso.fischereiregister.core.ports.TenantRulesValidationPort;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import static de.adesso.fischereiregister.core.validation.utils.ValidationUtils.validateFieldRequired;


@Component
@Slf4j
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class OSReplaceCardCommandValidator extends AbstractValidator implements CommandValidator<OSReplaceCardCommand> {

    private static final String INBOX_REFERENCE = "inboxReference";

    public OSReplaceCardCommandValidator(CountryService countryService, TenantRulesValidationPort tenantRulesValidationPort) {
        super(countryService, tenantRulesValidationPort);
    }

    @Override
    public void validateOrThrow(OSReplaceCardCommand command, RegisterEntry registerEntry) {
        if (registerEntry == null) {
            validateOrThrow(command);
            return;
        }

        validateFieldRequired(command.salt(), SALT, validationResult);

        if (validationResult.hasErrors()) {
            // If the salt value is invalid, it is due to a system misconfiguration, so we return an exception accordingly.
            throw new SystemConfigValidationException(validationResult);
        }

        validatePerson(command.person(), true);
        validatePersonUnchangeableData(command.person(), registerEntry.getPerson());
        validateAddress(command.person() != null ? command.person().getAddress() : null);
        validateFederalState(command.federalState());
        validateTaxes(command.taxes());
        validateFees(command.fees());
        validateConsentInfo(command.consentInfo());

        validateFieldRequired(command.licenseNumber(), LICENSE_NUMBER, validationResult);
        validateFieldRequired(command.serviceAccountId(), SERVICE_ACCOUNT_ID, validationResult);
        validateFieldRequired(command.transactionId(), TRANSACTION_ID, validationResult);
        validateFieldRequired(command.inboxReference(), INBOX_REFERENCE, validationResult);

        if (validationResult.hasErrors()) {
            throw new ClientInputValidationException(validationResult);
        }
    }


    @Override
    public void validateOrThrow(OSReplaceCardCommand command) throws AggregateValidationException {
        throw new UnsupportedOperationException("Validation without RegisterEntry for the OSCreateRegularLicenseCommandValidator is not supported.");
    }
}
