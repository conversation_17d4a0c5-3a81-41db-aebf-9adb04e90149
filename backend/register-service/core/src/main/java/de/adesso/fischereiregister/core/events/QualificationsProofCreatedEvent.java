package de.adesso.fischereiregister.core.events;

import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.QualificationsProof;
import org.axonframework.modelling.command.TargetAggregateIdentifier;

import java.util.UUID;

public record QualificationsProofCreatedEvent(
        @TargetAggregateIdentifier UUID registerEntryId,
        QualificationsProof qualificationsProof,
        Person person) implements AxonEvent {

}