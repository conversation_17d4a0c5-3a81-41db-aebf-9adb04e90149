package de.adesso.fischereiregister.core.validation.helper;

import de.adesso.fischereiregister.core.model.consent.AbstractConsentInfo;
import de.adesso.fischereiregister.core.model.consent.ConsentInfo;
import de.adesso.fischereiregister.core.model.consent.JurisdictionConsentInfo;
import de.adesso.fischereiregister.core.model.consent.LimitedLicenseConsentInfo;
import de.adesso.fischereiregister.core.validation.ValidationResult;

import static de.adesso.fischereiregister.core.validation.utils.ValidationUtils.validateBooleanFieldIsTrue;
import static de.adesso.fischereiregister.core.validation.utils.ValidationUtils.validateFieldRequired;

public class ConsentInfoValidatorHelper {

    private static final String CONSENT_INFO = "consentInfo";
    private static final String CONSENT_INFO_GDPR_ACCEPTED = "gdprAccepted";
    private static final String CONSENT_INFO_SELF_DISCLOSURE_ACCEPTED = "selfDisclosureAccepted";
    private static final String CONSENT_INFO_SUBMITTED_BY_THIRD_PARTY = "submittedByThirdParty";
    private static final String CONSENT_INFO_PROOF_OF_MOVE_VERIFIED = "proofOfMoveVerified";
    private static final String CONSENT_INFO_DISABILITY_CERTIFICATE_VERIFIED = "disablityCertificateVerified";

    private ConsentInfoValidatorHelper() {
    }

    public static void validate(AbstractConsentInfo abstractConsentInfo, ValidationResult validationResult) {
        validateFieldRequired(abstractConsentInfo, CONSENT_INFO, validationResult);
        if (abstractConsentInfo != null) {
            validateFieldRequired(abstractConsentInfo.getSubmittedByThirdParty(), CONSENT_INFO_SUBMITTED_BY_THIRD_PARTY, validationResult); // SubmittedByThirdParty can be false

            validateFieldRequired(abstractConsentInfo.getGdprAccepted(), CONSENT_INFO_GDPR_ACCEPTED, validationResult);
            validateBooleanFieldIsTrue(abstractConsentInfo.getGdprAccepted(), CONSENT_INFO_GDPR_ACCEPTED, validationResult);
            if (abstractConsentInfo instanceof ConsentInfo consentInfo) {
                validateFieldRequired(consentInfo.getSelfDisclosureAccepted(), CONSENT_INFO_SELF_DISCLOSURE_ACCEPTED, validationResult);
                validateBooleanFieldIsTrue(consentInfo.getSelfDisclosureAccepted(), CONSENT_INFO_SELF_DISCLOSURE_ACCEPTED, validationResult);
            }
            if (abstractConsentInfo instanceof JurisdictionConsentInfo jurisdictionConsentInfo) {
                validateFieldRequired(jurisdictionConsentInfo.getProofOfMoveVerified(), CONSENT_INFO_PROOF_OF_MOVE_VERIFIED, validationResult);
                validateBooleanFieldIsTrue(jurisdictionConsentInfo.getProofOfMoveVerified(), CONSENT_INFO_PROOF_OF_MOVE_VERIFIED, validationResult);
            }
            if (abstractConsentInfo instanceof LimitedLicenseConsentInfo limitedLicenseConsentInfo) {
                validateFieldRequired(limitedLicenseConsentInfo.getDisablityCertificateVerified(), CONSENT_INFO_DISABILITY_CERTIFICATE_VERIFIED, validationResult);
                validateBooleanFieldIsTrue(limitedLicenseConsentInfo.getDisablityCertificateVerified(), CONSENT_INFO_DISABILITY_CERTIFICATE_VERIFIED, validationResult);
            }
        }
    }
}
