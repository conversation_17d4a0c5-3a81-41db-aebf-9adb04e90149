package de.adesso.fischereiregister.core.commands;

import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.consent.JurisdictionConsentInfo;
import de.adesso.fischereiregister.core.model.user.UserDetails;
import org.axonframework.modelling.command.TargetAggregateIdentifier;

import java.util.List;
import java.util.UUID;

public record MoveJurisdictionCommand(
        @TargetAggregateIdentifier UUID registerId,
        JurisdictionConsentInfo consentInfo,
        String salt,
        List<Tax> taxes,
        UserDetails userDetails
) {

    public MoveJurisdictionCommand {
        assert taxes != null : "The list of taxes should not be null";
    }
}