package de.adesso.fischereiregister.core.validation;

import de.adesso.fischereiregister.core.commands.RejectLimitedLicenseApplicationCommand;
import de.adesso.fischereiregister.core.exceptions.AggregateValidationException;
import de.adesso.fischereiregister.core.exceptions.ClientInputValidationException;
import de.adesso.fischereiregister.core.exceptions.LicenseApplicationNotRejectableException;
import de.adesso.fischereiregister.core.exceptions.LicenseTypeNotSupportedException;
import de.adesso.fischereiregister.core.exceptions.RulesProcessingException;
import de.adesso.fischereiregister.core.exceptions.SystemConfigValidationException;
import de.adesso.fischereiregister.core.model.LimitedLicenseApplication;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.model.type.LimitedLicenseApplicationStatus;
import de.adesso.fischereiregister.core.ports.CountryService;
import de.adesso.fischereiregister.core.ports.TenantRulesValidationPort;
import de.adesso.fischereiregister.core.validation.helper.LicenseValidatorHelper;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import static de.adesso.fischereiregister.core.validation.utils.ValidationUtils.validateFieldRequired;

@Component
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class RejectLimitedLicenseApplicationCommandValidator extends AbstractValidator implements CommandValidator<RejectLimitedLicenseApplicationCommand> {

    private final LicenseValidatorHelper licenseValidatorHelper;

    public RejectLimitedLicenseApplicationCommandValidator(
            CountryService countryService,
            TenantRulesValidationPort tenantRulesValidationPort,
            LicenseValidatorHelper licenseValidatorHelper) {
        super(countryService, tenantRulesValidationPort);

        this.licenseValidatorHelper = licenseValidatorHelper;
    }

    @Override
    public void validateOrThrow(RejectLimitedLicenseApplicationCommand command, RegisterEntry registerEntry) throws RulesProcessingException, AggregateValidationException {
        validateUserDetails(command.userDetails());

        if (command.userDetails() != null) {
            validateFieldRequired(command.userDetails().getOffice(), OFFICE, validationResult);
        }

        if (validationResult.hasErrors()) {
            // if the user details, or the salt values are invalid is because of a System misconfiguration, so we return an exception accordingly
            throw new SystemConfigValidationException(validationResult);
        }

        // Ensure that tenant supports limited type licenses
        FederalState federalState = FederalState.valueOf(registerEntry.getJurisdiction().getFederalState());
        if (!licenseValidatorHelper.isLicenseAvailable(federalState, LicenseType.LIMITED)) {
            throw new LicenseTypeNotSupportedException(LicenseType.LIMITED, federalState);
        }

        LimitedLicenseApplication currentLimitedLicenseApplication = registerEntry.getLimitedLicenseApplication();
        if (currentLimitedLicenseApplication == null || currentLimitedLicenseApplication.getStatus() != LimitedLicenseApplicationStatus.PENDING) {
            throw new LicenseApplicationNotRejectableException("The current limited license application is not in a state that allows rejection. It must be in PENDING status.");
        }

        if (validationResult.hasErrors()) {
            throw new ClientInputValidationException(validationResult);
        }
    }
}

