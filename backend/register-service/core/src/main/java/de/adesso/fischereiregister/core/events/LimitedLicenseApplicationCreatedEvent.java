package de.adesso.fischereiregister.core.events;

import de.adesso.fischereiregister.core.model.Fee;
import de.adesso.fischereiregister.core.model.LimitedLicenseApplication;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.consent.ConsentInfo;
import org.axonframework.modelling.command.TargetAggregateIdentifier;

import java.util.List;
import java.util.UUID;

public record LimitedLicenseApplicationCreatedEvent(
        @TargetAggregateIdentifier UUID registerEntryId,
        LimitedLicenseApplication limitedLicenseApplication,
        Person person,
        List<Fee> fees,
        ConsentInfo consentInfo,
        String inboxReference,
        String serviceAccountId
) implements AxonEvent {
}
