package de.adesso.fischereiregister.core.validation.helper;

import de.adesso.fischereiregister.core.model.LimitedLicenseApproval;
import de.adesso.fischereiregister.core.model.SigningEmployee;
import de.adesso.fischereiregister.core.model.ValidityPeriod;
import de.adesso.fischereiregister.core.validation.ValidationResult;
import de.adesso.fischereiregister.core.validation.utils.DIN91379ValidationNorm;
import de.adesso.fischereiregister.core.validation.utils.ValidationUtils;
import org.springframework.stereotype.Component;

import static de.adesso.fischereiregister.core.validation.utils.ValidationUtils.validateFieldRequired;
import static de.adesso.fischereiregister.core.validation.utils.ValidationUtils.validateStringField;

@Component
public class LimitedLicenseApprovalValidatorHelper {

    private static final String LIMITED_LICENSE_APPROVAL = "limitedLicenseApproval";

    private static final String CREATED_AT = LimitedLicenseApproval.Fields.createdAt;
    private static final String CASH_REGISTER_SIGN = LimitedLicenseApproval.Fields.cashRegisterSign;
    private static final String FILE_NUMBER = LimitedLicenseApproval.Fields.fileNumber;
    private static final String JUSTIFICATION_FOR_LIMITED_DURATION_NOTICE = LimitedLicenseApproval.Fields.justificationForLimitedDurationNotice;

    private static final String PHONE = SigningEmployee.Fields.phone;
    private static final String EMAIL = SigningEmployee.Fields.email;
    private static final String NAME = SigningEmployee.Fields.name;
    private static final String PERSONAL_SIGN = SigningEmployee.Fields.personalSign;

    private LimitedLicenseApprovalValidatorHelper() {
    }

    public static void validateLimitedLicenseApproval(LimitedLicenseApproval limitedLicenseApproval, ValidityPeriod validityPeriod,
                                                      ValidationResult validationResult) {

        validateFieldRequired(limitedLicenseApproval, LIMITED_LICENSE_APPROVAL, validationResult);
        if (limitedLicenseApproval == null) {
            return;
        }

        validateFieldRequired(limitedLicenseApproval.getCreatedAt(), CREATED_AT, validationResult);

        validateFieldRequired(limitedLicenseApproval.getSigningEmployee().getPersonalSign(), PERSONAL_SIGN, validationResult);
        validateStringField(limitedLicenseApproval.getSigningEmployee().getPersonalSign(), PERSONAL_SIGN, 200, DIN91379ValidationNorm.GROUP_B_GENERAL_NAMES, validationResult);

        validateFieldRequired(limitedLicenseApproval.getSigningEmployee().getName(), NAME, validationResult);
        validateStringField(limitedLicenseApproval.getSigningEmployee().getName(), NAME, 200, DIN91379ValidationNorm.GROUP_A_PERSONS, validationResult);

        validateFieldRequired(limitedLicenseApproval.getSigningEmployee().getEmail(), EMAIL, validationResult);
        validateStringField(limitedLicenseApproval.getSigningEmployee().getEmail(), EMAIL, 200, DIN91379ValidationNorm.GROUP_B_GENERAL_NAMES, validationResult);
        ValidationUtils.validateEmailField(limitedLicenseApproval.getSigningEmployee().getEmail(), EMAIL, validationResult);

        validateFieldRequired(limitedLicenseApproval.getSigningEmployee().getPhone(), PHONE, validationResult);
        validateStringField(limitedLicenseApproval.getSigningEmployee().getPhone(), PHONE, 200, DIN91379ValidationNorm.GROUP_B_GENERAL_NAMES, validationResult);

        validateFieldRequired(limitedLicenseApproval.getFileNumber(), FILE_NUMBER, validationResult);
        validateStringField(limitedLicenseApproval.getFileNumber(), FILE_NUMBER, 200, DIN91379ValidationNorm.GROUP_B_GENERAL_NAMES, validationResult);

        validateFieldRequired(limitedLicenseApproval.getCashRegisterSign(), CASH_REGISTER_SIGN, validationResult);
        validateStringField(limitedLicenseApproval.getCashRegisterSign(), CASH_REGISTER_SIGN, 200, DIN91379ValidationNorm.GROUP_B_GENERAL_NAMES, validationResult);

        // if the validity period is limited, the justification for limited duration notice is required
        if (validityPeriod.getValidTo() != null) {
            validateFieldRequired(limitedLicenseApproval.getJustificationForLimitedDurationNotice(), JUSTIFICATION_FOR_LIMITED_DURATION_NOTICE, validationResult);
        }
        validateStringField(limitedLicenseApproval.getJustificationForLimitedDurationNotice(), JUSTIFICATION_FOR_LIMITED_DURATION_NOTICE, 440, DIN91379ValidationNorm.GROUP_B_GENERAL_NAMES, validationResult);
    }


}
