package de.adesso.fischereiregister.core.validation;

import de.adesso.fischereiregister.core.commands.ESCreateFishingCertificateCommand;
import de.adesso.fischereiregister.core.exceptions.ClientInputValidationException;
import de.adesso.fischereiregister.core.exceptions.RulesProcessingException;
import de.adesso.fischereiregister.core.exceptions.SystemConfigValidationException;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.ports.CountryService;
import de.adesso.fischereiregister.core.ports.TenantRulesValidationPort;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import static de.adesso.fischereiregister.core.validation.utils.ValidationUtils.validateFieldRequired;

@Component
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class ESCreateFishingCertificateCommandValidator extends AbstractValidator implements CommandValidator<ESCreateFishingCertificateCommand> {

    public ESCreateFishingCertificateCommandValidator(CountryService countryService, TenantRulesValidationPort tenantRulesValidationPort) {
        super(countryService, tenantRulesValidationPort);
    }

    @Override
    public void validateOrThrow(ESCreateFishingCertificateCommand command) throws RulesProcessingException {
        validateUserDetails(command.userDetails(), false);
        validateFieldRequired(command.userDetails().getCertificationIssuer(), "Certification issuer", validationResult);

        if (validationResult.hasErrors()) {
            // If the user details are invalid, it is due to a system misconfiguration, so we return an exception accordingly.
            throw new SystemConfigValidationException(validationResult);
        }

        validateFederalState(command.federalState());
        validatePerson(command.person(), false);

        tenantRulesValidationPort.validateUsingQualificationProofRules(FederalState.valueOf(command.federalState()), command.person(), validationResult);

        if (validationResult.hasErrors()) {
            throw new ClientInputValidationException(validationResult);
        }
    }
}
