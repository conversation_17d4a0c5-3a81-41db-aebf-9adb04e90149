package de.adesso.fischereiregister.core.processors;

import de.adesso.fischereiregister.core.commands.PayFishingTaxCommand;
import de.adesso.fischereiregister.core.events.AxonEvent;
import de.adesso.fischereiregister.core.events.FishingTaxPayedEvent;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.core.model.utils.IdentificationDocumentFactory;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
@AllArgsConstructor
public class PayFishingTaxCommandProcessor implements CommandProcessor<PayFishingTaxCommand> {

    private final IdentificationDocumentFactory identificationDocumentFactory;

    @Override
    public List<AxonEvent> process(PayFishingTaxCommand command, RegisterEntry registerEntry) {

        final List<IdentificationDocument> taxIdentificationDocuments = command.taxes().stream()
                .map(tax -> identificationDocumentFactory.createIdentificationDocumentForTax(tax, command.registerId()))
                .collect(Collectors.toCollection(ArrayList::new));

        return List.of(new FishingTaxPayedEvent(registerEntry.getRegisterId(),
                command.consentInfo(),
                command.person(),
                command.taxes(),
                command.salt(),
                taxIdentificationDocuments,
                command.userDetails().getOffice(),
                null,
                null,
                null,
                SubmissionType.ANALOG
        ));
    }
}
