package de.adesso.fischereiregister.core.service;

import de.adesso.fischereiregister.core.exceptions.RulesProcessingException;
import de.adesso.fischereiregister.core.model.Fee;
import de.adesso.fischereiregister.core.model.PaymentInfo;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.ValidityPeriod;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.ports.TenantInformationPort;
import de.adesso.fischereiregister.core.ports.contracts.TaxPriceInformation;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Component
@AllArgsConstructor
public class VacationFeeSeparationService {
    private TenantInformationPort tenantInformationService;

    public SeparatedPrice separateTaxesAndFees(ValidityPeriod validityPeriod, Fee fee, FederalState federalState) throws RulesProcessingException {
        final List<Tax> taxes = extractTaxes(fee, federalState, validityPeriod);

        BigDecimal totalTaxPrice = taxes.stream().reduce(
                BigDecimal.ZERO,
                (acc, tax) -> acc.add(BigDecimal.valueOf(tax.getPaymentInfo().getAmount())),
                BigDecimal::add);

        final List<Fee> remainingFees = getRemainingFees(fee, totalTaxPrice);

        return SeparatedPrice.builder()
                .fees(remainingFees)
                .taxes(taxes)
                .build();
    }

    private List<Tax> extractTaxes(Fee fee, FederalState federalState, ValidityPeriod validityPeriod) throws RulesProcessingException {
        int years = countYears(validityPeriod.getValidFrom(), validityPeriod.getValidTo());

        TaxPriceInformation taxInformation = tenantInformationService.getTaxPriceInformation(
                federalState,
                years,
                true,
                fee.getPaymentInfo().getType()
        );

        BigDecimal priceOneYear = taxInformation.price();
        List<Tax> taxes = new ArrayList<>();

        Tax tax = new Tax();
        // Set validFrom to January 1st of the validFrom year
        tax.setValidFrom(LocalDate.of(validityPeriod.getValidFrom().getYear(), 1, 1));
        // Set validTo to December 31st of the validTo year
        tax.setValidTo(LocalDate.of(validityPeriod.getValidTo().getYear(), 12, 31));

        tax.setFederalState(federalState.toString());

        PaymentInfo paymentInfo = new PaymentInfo();
        paymentInfo.setType(fee.getPaymentInfo().getType());
        paymentInfo.setAmount(priceOneYear.doubleValue());
        tax.setPaymentInfo(paymentInfo);

        taxes.add(tax);

        return taxes;
    }

    private List<Fee> getRemainingFees(Fee fee, BigDecimal totalTaxPrice) {
        Fee remainingFee = new Fee();
        remainingFee.setValidFrom(fee.getValidFrom());
        remainingFee.setValidTo(fee.getValidTo());
        remainingFee.setFederalState(fee.getFederalState());

        PaymentInfo paymentInfo = new PaymentInfo();
        paymentInfo.setType(fee.getPaymentInfo().getType());

        final BigDecimal remainingFeeAmount = BigDecimal.valueOf(fee.getPaymentInfo().getAmount()).subtract(totalTaxPrice);

        paymentInfo.setAmount(remainingFeeAmount.doubleValue());
        remainingFee.setPaymentInfo(paymentInfo);

        return List.of(remainingFee);
    }

    public static int countYears(LocalDate from, LocalDate to) {
        return (to.getYear() - from.getYear()) + 1;
    }

}
