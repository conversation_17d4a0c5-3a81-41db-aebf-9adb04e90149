package de.adesso.fischereiregister.core.model;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * Represents a physical address. (Wohnort)
 */
@Getter
@Setter
@EqualsAndHashCode
public class Address {

    /**
     * The office information
     */
    private String office;

    /**
     * The deliver to information
     */
    private String deliverTo;

    /**
     * The street name.
     */
    private String street;

    /**
     * The street number.
     */
    private String streetNumber;

    /**
     * The postal code.
     */
    private String postcode;

    /**
     * The city.
     */
    private String city;

    /**
     * The detail.
     */
    private String detail;
}