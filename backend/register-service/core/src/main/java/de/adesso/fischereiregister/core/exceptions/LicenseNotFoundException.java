package de.adesso.fischereiregister.core.exceptions;

import java.io.Serial;

public class LicenseNotFoundException extends RuntimeException {

    @Serial
    private static final long serialVersionUID = 585461325778839739L;

    public LicenseNotFoundException(String message) {
        super(message);
    }

    public LicenseNotFoundException() {
        super("Fishing license not found: The requested license does not exist or is unavailable.");
    }
}
