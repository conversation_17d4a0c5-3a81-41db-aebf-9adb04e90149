package de.adesso.fischereiregister.core.processors;

import de.adesso.fischereiregister.core.model.Fee;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.type.PaymentType;

import java.util.List;

public class OSCommandProcessor {
    // is this here needed only payments with type online should be available here
    // this code is needed because the OD services are already in work and the open-api should not be changed
    protected void processTaxesTransactionId(String transactionId, List<Tax> taxes) {
        taxes.stream()
                .filter(tax -> tax.getPaymentInfo().getType().equals(PaymentType.ONLINE))
                .map(Tax::getPaymentInfo)
                .forEach(paymentInfo -> paymentInfo.setTransactionId(transactionId));
    }

    // this code is needed because the OD services are already in work and the open-api should not be changed
    protected void processFeesTransactionId(String transactionId, List<Fee> fees) {
        fees.stream()
                .filter(fee -> fee.getPaymentInfo().getType().equals(PaymentType.ONLINE))
                .map(Fee::getPaymentInfo)
                .forEach(paymentInfo -> paymentInfo.setTransactionId(transactionId));
    }

    // this code is needed because the OD services are already in work and the open-api should not be changed
    protected void processFeeTransactionId(String transactionId, Fee fee) {
        if(fee.getPaymentInfo().getType().equals(PaymentType.ONLINE)) {
            fee.getPaymentInfo().setTransactionId(transactionId);
        }
    }
}
