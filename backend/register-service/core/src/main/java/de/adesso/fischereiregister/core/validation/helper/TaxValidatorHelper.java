package de.adesso.fischereiregister.core.validation.helper;

import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.validation.ValidationResult;

import java.time.LocalDate;
import java.util.List;

import static de.adesso.fischereiregister.core.validation.utils.ValidationUtils.validateFieldRequired;

public class TaxValidatorHelper {

    static final String FEDERAL_STATE = "Tax.federalState";
    static final String VALID_FROM = "Tax.validFrom";
    static final String PAYMENT_INFO = "Tax.paymentInfo";
    static final String PAYMENT_INFO_TYPE = "Tax.paymentInfo.type";
    static final String PAYMENT_INFO_AMOUNT = "Tax.paymentInfo.amount";

    private TaxValidatorHelper() {
    }

    public static void validateTaxes(List<Tax> taxes, ValidationResult validationResult) {

        for (Tax tax : taxes) {
            validateFieldRequired(tax.getFederalState(), FEDERAL_STATE, validationResult);
            validateFieldRequired(tax.getValidFrom(), VALID_FROM, validationResult);
            validateFieldRequired(tax.getPaymentInfo(), PAYMENT_INFO, validationResult);
            if (tax.getPaymentInfo() != null) {
                validateFieldRequired(tax.getPaymentInfo().getType(), PAYMENT_INFO_TYPE, validationResult);
                validateFieldRequired(tax.getPaymentInfo().getAmount(), PAYMENT_INFO_AMOUNT, validationResult);
            }

            if (tax.getValidFrom() != null && !isFullYear(tax)) {
                validationResult.addErrorNote("Tax payment should be for a full year (01.01.XXXX – 31.12.XXXX) but it's not.");
            }
        }
    }

    public static void validatePreviouslyPayedTaxes(List<Tax> payedTaxes, ValidationResult validationResult) {
        // Validate amount - should be 0.0
        payedTaxes.stream()
                .filter(tax -> tax.getPaymentInfo().getAmount() != 0.0)
                .map(tax -> "Previously Paid Taxes Payment amount should not be different than 0")
                .forEach(validationResult::addErrorNote);

        // Validate end date - should not be before current year
        final int currentYear = LocalDate.now().getYear();
        payedTaxes.stream()
                .filter(tax -> tax.getValidTo() != null && tax.getValidTo().getYear() < currentYear)
                .map(tax -> "Previously Paid Taxes Payment end date should not be before the actual year")
                .forEach(validationResult::addErrorNote);
    }

    private static boolean isFullYear(Tax tax) {
        if (tax.getValidTo() == null) {
            return true; // validTo can be null so in this case the full year check should return true
        }
        // Check if validFrom is 1st Jan and validTo is 31st Dec of a Valid Range
        return tax.getValidFrom().equals(LocalDate.of(tax.getValidFrom().getYear(), 1, 1)) &&
                tax.getValidTo().equals(LocalDate.of(tax.getValidTo().getYear(), 12, 31)) &&
                tax.getValidFrom().getYear() <= tax.getValidTo().getYear();
    }
}
