package de.adesso.fischereiregister.core.model;

import lombok.Getter;
import lombok.Setter;

/**
 * Class representing a person (<PERSON><PERSON>rger).
 */
@Getter
@Setter
public class Person {

    /**
     * The title of the person.
     */
    private String title;

    /**
     * The firstname of the person.
     */
    private String firstname;

    /**
     * The lastname of the person.
     */
    private String lastname;

    /**
     * The birthname of the person. This may differ from the current last name.
     */
    private String birthname;

    /**
     * The city where the person was born.
     */
    private String birthplace;

    /**
     * The birthdate of the person.
     */
    private Birthdate birthdate;

    /**
     * The address of the person. This can be null if the person doesn't have a fixed address,
     */
    private Address address;

    /**
     * Office Address, if the person has no fixed address this the address the card will be sent to.
     */
    private Address officeAddress;

    /**
     * The nationality of the person.
     */
    private String nationality;

    /**
     * The email address of the person.
     */
    private String email;


}