package de.adesso.fischereiregister.core.events;

import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.consent.ConsentInfo;
import org.axonframework.modelling.command.TargetAggregateIdentifier;

import java.util.List;
import java.util.UUID;

public record PersonalDataChangedEvent(@TargetAggregateIdentifier UUID registerId,
                                       Person person,
                                       List<Tax> taxes,
                                       ConsentInfo consentInfo,
                                       String salt,
                                       List<IdentificationDocument> identificationDocuments,
                                       String issuedByOffice) implements AxonEvent {
    public PersonalDataChangedEvent {
        assert taxes != null : "The list of taxes should not be null";
        assert identificationDocuments != null : "The list of identificationDocuments should not be null";
    }
}
