package de.adesso.fischereiregister.core.commands.contracts;

import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.consent.TaxConsentInfo;

import java.util.List;
import java.util.UUID;

public interface OSTaxCommand {
    UUID registerId();

    Person person();

    List<Tax> taxes();

    String salt();

    TaxConsentInfo consentInfo();

    String federalState();

    String inboxReference();

    String serviceAccountId();

    String transactionId();
}
