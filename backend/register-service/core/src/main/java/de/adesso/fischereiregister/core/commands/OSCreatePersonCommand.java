package de.adesso.fischereiregister.core.commands;

import de.adesso.fischereiregister.core.commands.contracts.OSTaxCommand;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.consent.TaxConsentInfo;
import org.axonframework.modelling.command.TargetAggregateIdentifier;

import java.util.List;
import java.util.UUID;

public record OSCreatePersonCommand(
        @TargetAggregateIdentifier UUID registerId,
        Person person,
        List<Tax> taxes,
        String salt,
        TaxConsentInfo consentInfo,
        String federalState,
        String inboxReference,
        String serviceAccountId,
        String transactionId
) implements OSTaxCommand {

    public OSCreatePersonCommand {
        assert taxes != null : "The list of taxes should not be null";
    }
}
