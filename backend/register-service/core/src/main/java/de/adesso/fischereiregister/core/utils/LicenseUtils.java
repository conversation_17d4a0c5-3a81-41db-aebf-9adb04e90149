package de.adesso.fischereiregister.core.utils;

import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.model.type.QualificationsProofType;

public class LicenseUtils {
    private LicenseUtils() {
    }

    public static boolean hasValidCertificate(RegisterEntry registerEntry) {
        return registerEntry.getQualificationsProofs().stream()
                .anyMatch(qualificationsProof ->
                        qualificationsProof.getType() == QualificationsProofType.CERTIFICATE &&
                                qualificationsProof.getFishingCertificateId() != null &&
                                !qualificationsProof.getFishingCertificateId().isEmpty());
    }

    public static boolean hasRegularLicense(RegisterEntry registerEntry) {
        return registerEntry.getFishingLicenses().stream()
                .anyMatch(license -> LicenseType.REGULAR.equals(license.getType()));
    }

    public static boolean hasVacationLicenseWithNumber(RegisterEntry registerEntry, String licenseNumber) {
        return registerEntry.getFishingLicenses().stream()
                .anyMatch(fishingLicense ->
                        fishingLicense.getType().equals(LicenseType.VACATION) &&
                                fishingLicense.getNumber().equals(licenseNumber));
    }
}
