package de.adesso.fischereiregister.core.processors;

import de.adesso.fischereiregister.core.commands.ChangePersonalDataCommand;
import de.adesso.fischereiregister.core.events.AxonEvent;
import de.adesso.fischereiregister.core.events.PersonalDataChangedEvent;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.utils.IdentificationDocumentFactory;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import java.util.ArrayList;

import java.util.List;
import java.util.stream.Collectors;

@Component
@AllArgsConstructor
public class ChangePersonalDataCommandProcessor implements CommandProcessor<ChangePersonalDataCommand> {

    private final IdentificationDocumentFactory identificationDocumentFactory;

    @Override
    public List<AxonEvent> process(ChangePersonalDataCommand command, RegisterEntry registerEntry) {
        final List<IdentificationDocument> identificationDocuments = command.taxes().stream()
                .map(tax -> identificationDocumentFactory.createIdentificationDocumentForTax(tax, command.registerId()))
                .collect(Collectors.toCollection(ArrayList::new));

        PersonalDataChangedEvent event = new PersonalDataChangedEvent(
                command.registerId(),
                command.person(),
                command.taxes(),
                command.consentInfo(),
                command.salt(),
                identificationDocuments,
                command.userDetails().getOffice()
        );

        return List.of(event);
    }

}
