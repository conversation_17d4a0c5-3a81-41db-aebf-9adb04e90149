package de.adesso.fischereiregister.core.processors;

import de.adesso.fischereiregister.core.commands.MoveJurisdictionCommand;
import de.adesso.fischereiregister.core.events.AxonEvent;
import de.adesso.fischereiregister.core.events.JurisdictionMovedEvent;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.core.model.user.UserDetails;
import de.adesso.fischereiregister.core.model.utils.IdentificationDocumentFactory;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
@AllArgsConstructor
public class MoveJurisdictionCommandProcessor implements CommandProcessor<MoveJurisdictionCommand> {

    private final IdentificationDocumentFactory identificationDocumentFactory;

    @Override
    public List<AxonEvent> process(MoveJurisdictionCommand command, RegisterEntry registerEntry) {
        final UserDetails userDetails = command.userDetails();

        final Jurisdiction previousJurisdiction = registerEntry.getJurisdiction();

        final Jurisdiction newJurisdiction = new Jurisdiction();
        newJurisdiction.setFederalState(userDetails.getFederalState());

        final List<IdentificationDocument> identificationDocuments = command.taxes().stream()
                .map(tax -> identificationDocumentFactory.createIdentificationDocumentForTax(tax, command.registerId()))
                .collect(Collectors.toCollection(ArrayList::new));

        return List.of(
                new JurisdictionMovedEvent(
                        command.registerId(),
                        previousJurisdiction,
                        newJurisdiction,
                        command.consentInfo(),
                        registerEntry.getBan(),
                        command.taxes(),
                        command.salt(),
                        command.userDetails().getOffice(),
                        identificationDocuments,
                        SubmissionType.ANALOG
                )
        );
    }
}
