package de.adesso.fischereiregister.core.model.user;

import lombok.Getter;
import lombok.ToString;

import java.util.Collection;

/**
 * Represents the details of a user who serves as a clerk (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>) for an authority (Behörde).
 * <p>
 * This record stores information about the clerk, including their unique user ID,
 * the federal state they are associated with (Zuständigkeit), and the office address of contact.
 * <p>
 * The record also supports a special system user through the {@link #SYSTEM_USER} constant.
 * The system user has restricted access to its fields - only userId and isSystem can be accessed,
 * while attempts to access federalState or parsedOfficeAddress will throw IllegalStateException.
 * <p>
 * userId the unique identifier of the clerk
 * federalState the federal state where the clerk operates (die Zuständigkeit)
 * parsedOfficeAddress the parsed address of the authority where the clerk operates
 * isSystem indicates whether this instance represents the system user
 */
@Getter
@ToString
public final class UserDetails {
    private static final String SYSTEM_USER_ID = "SYSTEM";

    public static final UserDetails SYSTEM_USER = new UserDetails(
            SYSTEM_USER_ID,
            null,
            null,
            null,
            null,
            // SYSTEM is god, SYST<PERSON> has all roles, all hail SYSTEM
            UserRole.ALL_ROLES,
            true
    );

    private final String userId;
    private final String federalState;
    private final String office; // the name of the institution
    private final String parsedOfficeAddress; // Office Address including the name of the institution
    private final boolean isSystem;
    private final String certificationIssuer;
    private final Collection<UserRole> roles;

    public UserDetails(String userId, String federalState, String office, String parsedOfficeAddress, String certificationIssuer, Collection<UserRole> roles) {
        this(userId, federalState, office, parsedOfficeAddress, certificationIssuer, roles, false);
    }


    private UserDetails(String userId, String federalState, String office, String parsedOfficeAddress, String certificationIssuer, Collection<UserRole> roles, boolean isSystem) {
        if (isSystem && !SYSTEM_USER_ID.equals(userId)) {
            throw new IllegalArgumentException("Cannot create system users. Use SYSTEM_USER instead.");
        }
        this.userId = userId;
        this.federalState = federalState;
        this.office = office;
        this.parsedOfficeAddress = parsedOfficeAddress;
        this.isSystem = isSystem;
        this.certificationIssuer = certificationIssuer;
        this.roles = roles;
    }

    public String getFederalState() {
        if (isSystem) {
            throw new IllegalStateException("Cannot access federalState for system user");
        }
        return federalState;
    }

    public String getParsedOfficeAddress() {
        if (isSystem) {
            throw new IllegalStateException("Cannot access parsedOfficeAddress for system user");
        }
        return parsedOfficeAddress;
    }
}