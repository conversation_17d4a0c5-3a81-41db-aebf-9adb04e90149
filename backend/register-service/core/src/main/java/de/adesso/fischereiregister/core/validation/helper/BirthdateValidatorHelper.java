package de.adesso.fischereiregister.core.validation.helper;

import de.adesso.fischereiregister.core.model.Birthdate;

import java.time.LocalDate;

public class BirthdateValidatorHelper {

    private BirthdateValidatorHelper() {
    }

    public static boolean isBirthdateValid(Birthdate birthdate) {
        if (birthdate == null) {
            return false;
        }
        if (birthdate.getDay() > 31) {
            return false;
        }
        if (birthdate.getMonth() > 12) {
            return false;
        }

        if (birthdate.getAge() > 120) {
            return false;
        }

        LocalDate today = LocalDate.now();
        LocalDate birthdateAsDate = birthdate.toLocalDate();

        return !birthdateAsDate.isAfter(today);
    }
}
