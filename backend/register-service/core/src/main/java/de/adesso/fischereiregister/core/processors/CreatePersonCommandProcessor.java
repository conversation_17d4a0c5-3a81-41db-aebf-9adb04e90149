package de.adesso.fischereiregister.core.processors;

import de.adesso.fischereiregister.core.commands.CreatePersonCommand;
import de.adesso.fischereiregister.core.events.AxonEvent;
import de.adesso.fischereiregister.core.events.PersonCreatedEvent;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.core.model.utils.IdentificationDocumentFactory;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Component
@AllArgsConstructor
public class CreatePersonCommandProcessor implements CommandProcessor<CreatePersonCommand> {

    private final IdentificationDocumentFactory identificationDocumentFactory;

    @Override
    public List<AxonEvent> process(CreatePersonCommand command, RegisterEntry registerEntry) {

        final List<IdentificationDocument> taxIdentificationDocuments = new ArrayList<>();
        command.taxes().forEach(t -> taxIdentificationDocuments.add(identificationDocumentFactory.createIdentificationDocumentForTax(t, command.registerId())));
        command.payedTaxes().forEach(t -> taxIdentificationDocuments.add(identificationDocumentFactory.createIdentificationDocumentForTax(t, command.registerId())));

        return List.of(new PersonCreatedEvent(
                command.registerId(),
                command.person(),
                command.taxes(),
                Collections.emptyList(),
                command.salt(),
                taxIdentificationDocuments,
                command.consentInfo(),
                command.userDetails().getOffice(),
                null,
                null,
                null,
                command.userDetails().getFederalState(),
                SubmissionType.ANALOG));
    }
}
