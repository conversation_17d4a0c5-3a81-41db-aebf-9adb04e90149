package de.adesso.fischereiregister.core.exceptions;

import java.io.Serial;

public class TaxInWrongJurisdictionException extends RuntimeException {

    @Serial
    private static final long serialVersionUID = -4922216212099084640L;

    public TaxInWrongJurisdictionException(String message) {
        super(message);
    }

    public TaxInWrongJurisdictionException() {
        this("Users can only pay taxes for a jurisdiction of his own");
    }
}
