package de.adesso.fischereiregister.core.model;

import de.adesso.fischereiregister.core.model.type.PaymentType;
import lombok.Getter;
import lombok.Setter;

/**
 * PaymentInfo: amount, card/cash
 */
@Getter
@Setter
public class PaymentInfo {

    /**
     * The money amount that the item costed
     */
    private Double amount;

    /**
     * The type of the item. [Card, Cash, ...]
     */
    private PaymentType type;

    /**
     * The transaction id of the payment
     */
    private String transactionId;
}