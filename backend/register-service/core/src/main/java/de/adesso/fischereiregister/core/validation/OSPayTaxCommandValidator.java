package de.adesso.fischereiregister.core.validation;

import de.adesso.fischereiregister.core.commands.OSPayTaxCommand;
import de.adesso.fischereiregister.core.exceptions.ClientInputValidationException;
import de.adesso.fischereiregister.core.exceptions.SystemConfigValidationException;
import de.adesso.fischereiregister.core.ports.CountryService;
import de.adesso.fischereiregister.core.ports.TenantRulesValidationPort;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import static de.adesso.fischereiregister.core.validation.utils.ValidationUtils.validateFieldRequired;

@Component
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class OSPayTaxCommandValidator extends AbstractValidator implements CommandValidator<OSPayTaxCommand> {

    private static final String INBOX_REFERENCE = "inboxReference";

    public OSPayTaxCommandValidator(CountryService countryService, TenantRulesValidationPort tenantRulesValidationPort) {
        super(countryService, tenantRulesValidationPort);
    }

    @Override
    public void validateOrThrow(OSPayTaxCommand command) {
        validateFieldRequired(command.salt(), SALT, validationResult);

        if (validationResult.hasErrors()) {
            // If the salt value is invalid, it is due to a system misconfiguration, so we return an exception accordingly.
            throw new SystemConfigValidationException(validationResult);
        }

        validatePerson(command.person(), true);
        // here we dont need to validate any address as for paying a tax there is no documents delivered
        validateFederalState(command.federalState());
        validateTaxes(command.taxes());
        validateConsentInfo(command.consentInfo());

        validateFieldRequired(command.serviceAccountId(), SERVICE_ACCOUNT_ID, validationResult);
        validateFieldRequired(command.transactionId(), TRANSACTION_ID, validationResult);
        validateFieldRequired(command.inboxReference(), INBOX_REFERENCE, validationResult);

        if (validationResult.hasErrors()) {
            throw new ClientInputValidationException(validationResult);
        }
    }

}
