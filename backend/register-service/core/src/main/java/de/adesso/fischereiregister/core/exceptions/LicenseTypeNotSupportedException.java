package de.adesso.fischereiregister.core.exceptions;

import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.LicenseType;

public class LicenseTypeNotSupportedException extends RuntimeException {
    public LicenseTypeNotSupportedException(LicenseType licenseType, FederalState federalState) {
        super(String.format("License type '%s' is not supported for federal state '%s'.", licenseType, federalState));
    }
}
