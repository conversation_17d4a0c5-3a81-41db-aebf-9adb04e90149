package de.adesso.fischereiregister.core.commands.results;

import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Getter
@SuperBuilder
public class JurisdictionCommandResult extends CommandResult {
    private Jurisdiction jurisdiction;
    protected final List<IdentificationDocument> documents;
}
