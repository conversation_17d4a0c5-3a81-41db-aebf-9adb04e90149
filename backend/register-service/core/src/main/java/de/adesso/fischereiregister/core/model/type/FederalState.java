package de.adesso.fischereiregister.core.model.type;

public enum FederalState {
    BW, BY, BE, BB, HB, HH, HE, MV, NI, NW, RP, SL, SN, ST, SH, TH;

    // Utility method to check if a string is a valid federal state
    public static boolean isValid(String input) {
        try {
            FederalState.valueOf(input);
            return true;
        } catch (IllegalArgumentException | NullPointerException e) {
            return false;
        }
    }
}
