package de.adesso.fischereiregister.core.exceptions;

import java.io.Serial;

public class OnlineServiceProcessingException extends RuntimeException {

    @Serial
    private static final long serialVersionUID = 3437157872608143093L;

    public OnlineServiceProcessingException(String message) {
        super(message);
    }

    public OnlineServiceProcessingException() {
        super("Online service processing error: An error occurred while processing the online service request.");
    }
}