package de.adesso.fischereiregister.core.validation.helper;

import de.adesso.fischereiregister.core.exceptions.RulesProcessingException;
import de.adesso.fischereiregister.core.model.Fee;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.ports.TenantRulesValidationPort;
import de.adesso.fischereiregister.core.validation.ValidationResult;

import java.util.List;

public class TenantValidatorHelper {

    private TenantValidatorHelper() {
    }


    /**
     * Validates tenant rules based on the provided parameters.
     *
     * @param tenantRulesValidationPort the tenant validation port
     * @param federalState              the federal state
     * @param person                    the person
     * @param taxes                     the list of taxes
     * @param fees                      the list of fees
     * @param licenseType               the license type
     * @param validationResult          the validation result to store any validation errors
     */
    public static void validateTenantRules(TenantRulesValidationPort tenantRulesValidationPort, FederalState federalState, Person person, List<Tax> taxes, List<Fee> fees, LicenseType licenseType, ValidationResult validationResult) throws RulesProcessingException {
        try {
            if (federalState != null && person != null && licenseType != null) {
                tenantRulesValidationPort.validateUsingFishingLicenseRules(federalState, person, licenseType, validationResult);
            }
            if (federalState != null && person != null && taxes != null) {
                tenantRulesValidationPort.validateUsingFishingTaxRules(federalState, person, taxes, validationResult);
            }
            if (federalState != null && taxes != null) {
                boolean officeFeeAlreadyPayed = fees != null && !fees.isEmpty(); // if fees are present, the office fee is already paid
                tenantRulesValidationPort.validateTaxes(federalState, taxes, officeFeeAlreadyPayed, validationResult);
            }
            if (federalState != null && licenseType != null && fees != null) {
                tenantRulesValidationPort.validateFeesForFishingLicense(federalState, licenseType, fees, validationResult);
            }
        } catch (Exception e) {
            validationResult.addErrorNote("Error during tenant validation: " + e.getMessage());
        }
    }
}
