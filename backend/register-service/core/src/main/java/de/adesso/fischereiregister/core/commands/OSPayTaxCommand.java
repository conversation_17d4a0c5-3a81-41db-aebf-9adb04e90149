package de.adesso.fischereiregister.core.commands;

import de.adesso.fischereiregister.core.commands.contracts.OSTaxCommand;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.consent.TaxConsentInfo;
import org.axonframework.modelling.command.TargetAggregateIdentifier;

import java.util.List;
import java.util.UUID;

public record OSPayTaxCommand(@TargetAggregateIdentifier UUID registerId,
                              Person person,
                              TaxConsentInfo consentInfo,
                              String salt,
                              List<Tax> taxes,
                              String serviceAccountId,
                              String transactionId,
                              String federalState,
                              String inboxReference
) implements OSTaxCommand {

    public OSPayTaxCommand {
        assert taxes != null : "The list of taxes should not be null";
    }
}
