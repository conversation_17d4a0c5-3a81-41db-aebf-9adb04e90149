package de.adesso.fischereiregister.core.ports;

import de.adesso.fischereiregister.core.exceptions.RulesProcessingException;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.model.type.PaymentType;
import de.adesso.fischereiregister.core.ports.contracts.LicenseInformation;
import de.adesso.fischereiregister.core.ports.contracts.TaxPriceInformation;

/**
 * Retrieves cost related tenant tax and fee information based on configurable settings.
 * This approach externalizes business logic, making it flexible and easier to manage.
 */
public interface TenantInformationPort {

    /**
     * Retrieves tenant tax regulations based on the needed input paramters like federal State/tenant information,
     * if the office fee is already paid, and the payment type which will be translated to ANALOG/DIGITAL
     *
     * @param federalState          the federal state
     * @param years                 the number of years for which the tax was payed
     * @param officeFeeAlreadyPayed whether the office fee is already paid
     * @param paymentType           the payment type
     * @return the tax information
     * @throws RulesProcessingException if an error occurs during rules processing
     */
    TaxPriceInformation getTaxPriceInformation(FederalState federalState, int years, boolean officeFeeAlreadyPayed, PaymentType paymentType) throws RulesProcessingException;

    /**
     * Retrieves tenant tax information based on the provided parameters, like federal State/tenant information,
     * and the payment type which will be translated to ANALOG/DIGITAL
     * and the license type REGULAR/VACATION/LIMITED
     *
     * @param federalState the federal state
     * @param licenseType  the license type: REGULAR/VACATION/LIMITED
     * @param paymentType  the payment type
     * @return the license information
     * @throws RulesProcessingException if an error occurs during rules processing
     */
    LicenseInformation getLicenseInformation(FederalState federalState, LicenseType licenseType, PaymentType paymentType) throws RulesProcessingException;
}
