package de.adesso.fischereiregister.core.model;


import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

@Getter
@EqualsAndHashCode
public class Birthdate {

    private final int year;
    private final int month;
    private final int day;

    public Birthdate(int year, int month, int day) {
        this.year = year;
        this.month = month;
        this.day = day;
    }

    /**
     * Convert LocalDate object to birthdate
     */
    private Birthdate(LocalDate date) {
        this(
                date.getYear(),
                date.getMonthValue(),
                date.getDayOfMonth()
        );
    }

    /**
     * Converts to a LocalDate object.
     * <p>
     * WARNING! Partial Dates will be converted as if the first date is set, i.e.
     * <p>
     * 00.02.2020 -> 01.02.2020
     * <p>
     * 00.00.2020 -> 01.01.2020
     */
    public LocalDate toLocalDate() {
        final int normalizedMonth = month == 0 ? 1 : month; // If the month is unknown, assume the person is born in january
        final int normalizedDay = day == 0 ? 1 : day; // If the day is unknown, assume the person is born on the 1st

        return LocalDate.of(year, normalizedMonth, normalizedDay);
    }

    public static Birthdate parse(String string) throws IllegalArgumentException {
        if (string != null && !string.trim().isEmpty()) {
            try {
                return tryParseAsFullyKnownDate(string);
            } catch (DateTimeParseException e) {
                return tryParseAsPartiallyKnownDate(string);
            }
        }
        throw new IllegalArgumentException("The date string can not be empty or null");
    }


    @Override
    public String toString() {
        return String.format("%02d.%02d.%04d", day, month, year);
    }


    private static Birthdate tryParseAsFullyKnownDate(String string) throws DateTimeParseException {
        //Not Using LocalDate for parsing because it allows to many invalid dates, resp. tries to interpret them as valid
        final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd.MM.yyyy");
        final LocalDate parsedDate = LocalDate.parse(string, formatter);

        if (!parsedDate.format(formatter).equals(string)) {
            throw new DateTimeParseException("Invalid date format", string, 0);
        }

        return new Birthdate(parsedDate);
    }

    private static Birthdate tryParseAsPartiallyKnownDate(String string) {
        final String[] stringParts = string.split("\\.");
        if (stringParts.length == 3) {
            final int day = Integer.parseInt(stringParts[0]);
            final int month = Integer.parseInt(stringParts[1]);
            final int year = Integer.parseInt(stringParts[2]);

            if (day != 0 || month < 0 || month > 12) {
                throw new IllegalArgumentException("Invalid partially known date: " + string);
            }
            return new Birthdate(year, month, day);
        }
        throw new IllegalArgumentException("Invalid date format: " + string);
    }

    public Integer getAge() {
        LocalDate today = LocalDate.now();
        LocalDate birthdate = toLocalDate();

        // Calculate age
        int age = today.getYear() - birthdate.getYear();

        // Adjust if today's date is before the birthday in the current year
        if (today.getMonthValue() < birthdate.getMonthValue() ||
                (today.getMonthValue() == birthdate.getMonthValue() && today.getDayOfMonth() < birthdate.getDayOfMonth())) {
            age--;
        }
        return age;
    }


}
