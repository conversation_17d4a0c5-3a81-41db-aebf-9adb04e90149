package de.adesso.fischereiregister.core.commands.results;

import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import de.adesso.fischereiregister.core.model.Person;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Getter
@SuperBuilder
public class PersonCommandResult extends CommandResult {
    private Person person;
    private Jurisdiction jurisdiction;
    protected final List<IdentificationDocument> documents;
}
