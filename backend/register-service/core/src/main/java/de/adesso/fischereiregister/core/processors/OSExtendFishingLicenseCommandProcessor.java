package de.adesso.fischereiregister.core.processors;

import de.adesso.fischereiregister.core.commands.OSExtendFishingLicenseCommand;
import de.adesso.fischereiregister.core.events.AxonEvent;
import de.adesso.fischereiregister.core.events.LicenseExtendedEvent;
import de.adesso.fischereiregister.core.exceptions.LicenseNotFoundException;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.ValidityPeriod;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.core.model.utils.IdentificationDocumentFactory;
import de.adesso.fischereiregister.core.utils.LicenseUtils;
import de.adesso.fischereiregister.core.service.SeparatedPrice;
import de.adesso.fischereiregister.core.service.VacationFeeSeparationService;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Component
@AllArgsConstructor
public class OSExtendFishingLicenseCommandProcessor extends OSCommandProcessor implements CommandProcessor<OSExtendFishingLicenseCommand> {

    private final VacationFeeSeparationService vacationFeeSeparationService;
    private final IdentificationDocumentFactory identificationDocumentFactory;

    @SneakyThrows
    @Override
    public List<AxonEvent> process(OSExtendFishingLicenseCommand command, RegisterEntry registerEntry) {
        checkWhetherApplicableOrThrow(registerEntry, command.licenseNumber());

        FederalState federalState = FederalState.valueOf(command.fee().getFederalState());

        final SeparatedPrice separatedPrice = vacationFeeSeparationService.separateTaxesAndFees(command.validityPeriod(), command.fee(), federalState);

        final List<IdentificationDocument> documents = createDocuments(separatedPrice.getTaxes(), getVacationLicense(registerEntry, command.licenseNumber()), command.registerId(), command.validityPeriod());

        // we also have to set the transaction ID in the payment info of the taxes or fees
        processFeesTransactionId(command.transactionId(), separatedPrice.getFees());
        processTaxesTransactionId(command.transactionId(), separatedPrice.getTaxes());

        return List.of(new LicenseExtendedEvent(
                command.registerId(),
                registerEntry.getPerson(),
                command.salt(),
                command.consentInfo(),
                separatedPrice.getFees(),
                separatedPrice.getTaxes(),
                command.licenseNumber(),
                command.validityPeriod(),
                documents,
                command.inboxReference(),
                command.serviceAccountId(),
                command.transactionId(),
                null,
                SubmissionType.ONLINE
        ));
    }

    private List<IdentificationDocument> createDocuments(List<Tax> taxes, FishingLicense license, UUID registerEntryId, ValidityPeriod validityPeriod) {
        final List<IdentificationDocument> taxDocuments = taxes.stream()
                .map(tax -> identificationDocumentFactory.createIdentificationDocumentForTax(tax, registerEntryId))
                .collect(Collectors.toCollection(ArrayList::new));

        List<IdentificationDocument> documents = new ArrayList<>();
        documents.add(identificationDocumentFactory.createPDFDocumentForValidityPeriod(license, validityPeriod, registerEntryId));
        documents.addAll(taxDocuments);

        return documents;
    }

    private FishingLicense getVacationLicense(RegisterEntry registerEntry, String licenseNumber) {
        return registerEntry.getFishingLicenses().stream()
                .filter(fishingLicense ->
                        fishingLicense.getType().equals(LicenseType.VACATION) &&
                                fishingLicense.getNumber().equals(licenseNumber))
                .findFirst()
                .orElseThrow(() -> new LicenseNotFoundException("Vacation license with number " + licenseNumber + " not found"));
    }


    private void checkWhetherApplicableOrThrow(RegisterEntry registerEntry, String licenseNumber) {
        if (!LicenseUtils.hasVacationLicenseWithNumber(registerEntry, licenseNumber)) {
            throw new IllegalArgumentException("No vacation license with matching number found for register Id: " + registerEntry.getRegisterId());
        }
    }
}
