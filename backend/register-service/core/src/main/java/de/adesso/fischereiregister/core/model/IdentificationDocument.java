package de.adesso.fischereiregister.core.model;

import de.adesso.fischereiregister.core.model.type.IdentificationDocumentType;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

@Getter
@Setter
public class IdentificationDocument {

    private LocalDate issuedDate;
    private String documentId;
    private IdentificationDocumentType type;
    private FishingLicense fishingLicense;
    private Tax tax;
    private LimitedLicenseApproval limitedLicenseApproval;

    /**
     * validFrom date that is printed on the PDF document.
     */
    private LocalDate validFrom;

    /**
     * validTo date that is printed on the PDF document.
     */
    private LocalDate validTo;

}
