package de.adesso.fischereiregister.core.validation;

import de.adesso.fischereiregister.core.commands.OSPayTaxCommand;
import de.adesso.fischereiregister.core.exceptions.ClientInputValidationException;
import de.adesso.fischereiregister.core.exceptions.SystemConfigValidationException;
import de.adesso.fischereiregister.core.interceptables.InterceptableCountryService;
import de.adesso.fischereiregister.core.interceptables.InterceptableTenantRulesValidationPort;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.consent.TaxConsentInfo;
import de.adesso.fischereiregister.core.ports.CountryService;
import de.adesso.fischereiregister.core.ports.TenantRulesValidationPort;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

class OSPayTaxCommandValidatorTest {

    private OSPayTaxCommandValidator validator;

    @BeforeEach
    void setUp() {
        CountryService countryService = new InterceptableCountryService();
        TenantRulesValidationPort tenantRulesValidationPort = new InterceptableTenantRulesValidationPort();
        validator = new OSPayTaxCommandValidator(countryService, tenantRulesValidationPort);
    }

    @Test
    void testValidate_AllFieldsValid() {
        // GIVEN
        Person person = DomainTestData.createPersonWithAddress();
        String federalState = "SH";
        List<Tax> taxList = DomainTestData.createAnalogTaxesWithOneTax();
        String serviceAccountId = "Service123";
        String transactionId = "Trans456";
        TaxConsentInfo consentInfo = DomainTestData.createTaxConsentInfo();

        final OSPayTaxCommand command = new OSPayTaxCommand(
                UUID.randomUUID(),
                person,
                consentInfo,
                "salt",
                taxList,
                serviceAccountId,
                transactionId,
                federalState,
                "inboxReference");

        // WHEN
        // THEN
        assertDoesNotThrow(() -> validator.validateOrThrow(command));
    }

    @Test
    void testValidate_MissingFields_ClientInputValidation() {
        // GIVEN
        Person person = null; // Missing person
        String federalState = "InvalidState"; // Invalid federal state
        List<Tax> taxList = List.of();
        String serviceAccountId = null; // Missing service account ID
        String transactionId = null; // Missing transaction ID
        TaxConsentInfo consentInfo = DomainTestData.createTaxConsentInfo();

        final OSPayTaxCommand command = new OSPayTaxCommand(
                UUID.randomUUID(),
                person,
                consentInfo,
                "salt",
                taxList,
                serviceAccountId,
                transactionId,
                federalState,
                ""); // Missing inboxReference

        // WHEN
        ClientInputValidationException exception = assertThrows(ClientInputValidationException.class, () -> validator.validateOrThrow(command));

        // THEN
        ValidationResult result = exception.getValidationResult();
        assertEquals(5, result.getErrorNotes().size());
        assertTrue(result.getErrorNotes().contains("Person Details is required"));
        assertTrue(result.getErrorNotes().contains("Federal state " + federalState + " is invalid."));
        assertTrue(result.getErrorNotes().contains("Service account id is required"));
        assertTrue(result.getErrorNotes().contains("Transaction id is required"));
        assertTrue(result.getErrorNotes().contains("inboxReference is required"));
    }

    @Test
    void testValidate_InvalidTaxes_ClientInputValidation() {
        // GIVEN
        Person person = new Person();
        String federalState = "SH";
        String serviceAccountId = "Service123";
        String transactionId = "Trans456";

        List<Tax> taxList = new ArrayList<>();
        Tax invalidTax = DomainTestData.createAnalogTax();
        invalidTax.setValidTo(LocalDate.of(2025, 3, 21)); // Invalid tax end date
        taxList.add(invalidTax);

        TaxConsentInfo consentInfo = DomainTestData.createTaxConsentInfo();

        final OSPayTaxCommand command = new OSPayTaxCommand(
                UUID.randomUUID(),
                person,
                consentInfo,
                "salt",
                taxList,
                serviceAccountId,
                transactionId,
                federalState,
                "inboxReference");

        // WHEN
        ClientInputValidationException exception = assertThrows(ClientInputValidationException.class, () -> validator.validateOrThrow(command));

        // THEN
        ValidationResult result = exception.getValidationResult();
        assertTrue(result.getErrorNotes().contains("Tax payment should be for a full year (01.01.XXXX – 31.12.XXXX) but it's not."));
    }

    @Test
    void testValidate_PersonWithInvalidFields_ClientInputValidation() {
        // GIVEN
        Person person = DomainTestData.createPerson();
        person.setFirstname(null); // Missing first name
        String federalState = "SH";
        List<Tax> taxList = DomainTestData.createAnalogTaxesWithOneTax();
        String serviceAccountId = "Service123";
        String transactionId = "Trans456";
        TaxConsentInfo consentInfo = DomainTestData.createTaxConsentInfo();

        final OSPayTaxCommand command = new OSPayTaxCommand(
                UUID.randomUUID(),
                person,
                consentInfo,
                "salt",
                taxList,
                serviceAccountId,
                transactionId,
                federalState,
                "inboxReference");

        // WHEN
        ClientInputValidationException exception = assertThrows(ClientInputValidationException.class, () -> validator.validateOrThrow(command));

        // THEN
        ValidationResult result = exception.getValidationResult();
        assertTrue(result.getErrorNotes().contains("Firstname is required"));
    }

    @Test
    void testValidate_MissingInboxReference_SystemConfigValidation() {
        // GIVEN
        Person person = DomainTestData.createPersonWithAddress();
        String federalState = "SH";
        List<Tax> taxList = DomainTestData.createAnalogTaxesWithOneTax();
        String serviceAccountId = "Service123";
        String transactionId = "Trans456";
        TaxConsentInfo consentInfo = DomainTestData.createTaxConsentInfo();

        final OSPayTaxCommand command = new OSPayTaxCommand(
                UUID.randomUUID(),
                person,
                consentInfo,
                null,
                taxList,
                serviceAccountId,
                transactionId,
                federalState,
                "inboxReference"); // Missing inboxReference

        // WHEN
        SystemConfigValidationException exception = assertThrows(SystemConfigValidationException.class, () -> validator.validateOrThrow(command));

        // THEN
        ValidationResult result = exception.getValidationResult();
        assertEquals(1, result.getErrorNotes().size());
        assertTrue(result.getErrorNotes().contains("Salt is required"));
    }
}