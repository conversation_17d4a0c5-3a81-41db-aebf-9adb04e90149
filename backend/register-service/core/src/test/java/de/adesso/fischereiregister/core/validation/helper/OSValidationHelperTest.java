package de.adesso.fischereiregister.core.validation.helper;

import de.adesso.fischereiregister.core.validation.ValidationResult;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class OSValidationHelperTest {
    @Test
    public void testOSValidationAllFieldsSet() {
        // Arrange
        ValidationResult result = new ValidationResult();

        // Act
        OSValidationHelper.validateOSRequiredFields(".", ".", ".", result);

        // Assert
        assertFalse(result.hasErrors());
    }

    @Test
    public void testOSValidationMissingFields() {
        // Arrange
        ValidationResult result = new ValidationResult();

        // Act
        OSValidationHelper.validateOSRequiredFields(null, null, null, result);

        // Assert
        assertTrue(result.hasErrors());
        assertTrue(result.getErrorNotes().contains("serviceAccountId is required"));
        assertTrue(result.getErrorNotes().contains("transactionId is required"));
        assertTrue(result.getErrorNotes().contains("inboxReference is required"));
    }
}
