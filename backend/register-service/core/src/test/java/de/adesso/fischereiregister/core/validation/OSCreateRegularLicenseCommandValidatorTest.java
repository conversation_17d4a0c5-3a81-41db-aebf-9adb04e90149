package de.adesso.fischereiregister.core.validation;

import de.adesso.fischereiregister.core.commands.OSCreateRegularLicenseCommand;
import de.adesso.fischereiregister.core.exceptions.ClientInputValidationException;
import de.adesso.fischereiregister.core.exceptions.SystemConfigValidationException;
import de.adesso.fischereiregister.core.interceptables.InterceptableCountryService;
import de.adesso.fischereiregister.core.interceptables.InterceptableTenantRulesValidationPort;
import de.adesso.fischereiregister.core.model.Fee;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.consent.ConsentInfo;
import de.adesso.fischereiregister.core.ports.CountryService;
import de.adesso.fischereiregister.core.ports.TenantRulesValidationPort;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

class OSCreateRegularLicenseCommandValidatorTest {

    private OSCreateRegularLicenseCommandValidator validator;

    @BeforeEach
    void setUp() {
        CountryService countryService = new InterceptableCountryService();
        final TenantRulesValidationPort tenantRulesValidationPort = new InterceptableTenantRulesValidationPort();
        validator = new OSCreateRegularLicenseCommandValidator(countryService, tenantRulesValidationPort);
    }

    @Test
    void testValidate_AllFieldsValid() {
        // GIVEN
        Person person = DomainTestData.createPersonWithAddress();
        String federalState = "SH";
        List<Tax> taxList = DomainTestData.createAnalogTaxesWithOneTax();
        List<Fee> feeList = List.of();
        String fishingCertificateCode = "FCC67890";
        String serviceAccountId = "Service123";
        String transactionId = "Trans456";
        ConsentInfo consentInfo = DomainTestData.createConsentInfo();

        RegisterEntry registerEntry = new RegisterEntry();
        registerEntry.setPerson(person);

        final OSCreateRegularLicenseCommand command = new OSCreateRegularLicenseCommand(
                UUID.randomUUID(),
                UUID.randomUUID().toString(),
                fishingCertificateCode,
                person,
                consentInfo,
                taxList,
                feeList,
                serviceAccountId,
                federalState,
                transactionId,
                "inboxReference");

        // WHEN
        // THEN
        assertDoesNotThrow(() -> validator.validateOrThrow(command, registerEntry));
    }

    @Test
    void testValidate_MissingFields_ClientInputValidation() {
        // GIVEN
        Person person = null;
        String federalState = "InvalidState";
        List<Tax> taxList = List.of();
        List<Fee> feeList = List.of();
        String fishingCertificateCode = null;
        String serviceAccountId = null;
        String transactionId = null;
        String inboxReference = null;
        ConsentInfo consentInfo = DomainTestData.createConsentInfo();

        RegisterEntry registerEntry = new RegisterEntry();

        final OSCreateRegularLicenseCommand command = new OSCreateRegularLicenseCommand(
                UUID.randomUUID(),
                UUID.randomUUID().toString(),
                fishingCertificateCode,
                person,
                consentInfo,
                taxList,
                feeList,
                serviceAccountId,
                federalState,
                transactionId,
                inboxReference);

        // WHEN
        ClientInputValidationException exception = assertThrows(ClientInputValidationException.class, () -> validator.validateOrThrow(command, registerEntry));

        // THEN
        ValidationResult result = exception.getValidationResult();
        assertTrue(result.hasErrors());
        assertEquals(7, result.getErrorNotes().size());
        assertTrue(result.getErrorNotes().contains("Person Details is required"));
        assertTrue(result.getErrorNotes().contains("address is required"));
        assertTrue(result.getErrorNotes().contains("Federal state " + federalState + " is invalid."));
        assertTrue(result.getErrorNotes().contains("Fishing certificate code is required"));
        assertTrue(result.getErrorNotes().contains("Service account id is required"));
        assertTrue(result.getErrorNotes().contains("Transaction id is required"));
        assertTrue(result.getErrorNotes().contains("inboxReference is required"));
    }

    @Test
    void testValidate_InvalidTaxes_ClientInputValidation() {
        // GIVEN
        Person person = new Person();
        String federalState = "SH";
        List<Fee> feeList = List.of();
        String fishingCertificateCode = "FCC67890";
        String serviceAccountId = "Service123";
        String transactionId = "Trans456";

        Tax invalidTax = DomainTestData.createAnalogTax();
        invalidTax.setValidTo(LocalDate.of(2025, 3, 21));
        ConsentInfo consentInfo = DomainTestData.createConsentInfo();

        RegisterEntry registerEntry = new RegisterEntry();
        registerEntry.setPerson(person);

        final OSCreateRegularLicenseCommand command = new OSCreateRegularLicenseCommand(
                UUID.randomUUID(),
                UUID.randomUUID().toString(),
                fishingCertificateCode,
                person,
                consentInfo,
                List.of(invalidTax),
                feeList,
                serviceAccountId,
                federalState,
                transactionId,
                "inboxReference");

        // WHEN
        ClientInputValidationException exception = assertThrows(ClientInputValidationException.class, () -> validator.validateOrThrow(command, registerEntry));

        // THEN
        ValidationResult result = exception.getValidationResult();
        assertTrue(result.hasErrors());
        assertTrue(result.getErrorNotes().contains("Tax payment should be for a full year (01.01.XXXX – 31.12.XXXX) but it's not."));
    }

    @Test
    void testValidate_MissingInboxReference_SystemConfigValidation() {
        // GIVEN
        Person person = DomainTestData.createPersonWithAddress();
        String federalState = "SH";
        List<Tax> taxList = DomainTestData.createAnalogTaxesWithOneTax();
        List<Fee> feeList = List.of();
        String fishingCertificateCode = "FCC67890";
        String serviceAccountId = "Service123";
        String transactionId = "Trans456";
        ConsentInfo consentInfo = DomainTestData.createConsentInfo();

        RegisterEntry registerEntry = new RegisterEntry();
        registerEntry.setPerson(person);

        final OSCreateRegularLicenseCommand command = new OSCreateRegularLicenseCommand(
                UUID.randomUUID(),
                null,
                fishingCertificateCode,
                person,
                consentInfo,
                taxList,
                feeList,
                serviceAccountId,
                federalState,
                transactionId,
                ""); // Missing inboxReference

        // WHEN
        SystemConfigValidationException exception = assertThrows(SystemConfigValidationException.class, () -> validator.validateOrThrow(command, registerEntry));

        // THEN
        ValidationResult result = exception.getValidationResult();
        assertTrue(result.hasErrors());
        assertEquals(1, result.getErrorNotes().size());
        assertTrue(result.getErrorNotes().contains("Salt is required"));
    }
}