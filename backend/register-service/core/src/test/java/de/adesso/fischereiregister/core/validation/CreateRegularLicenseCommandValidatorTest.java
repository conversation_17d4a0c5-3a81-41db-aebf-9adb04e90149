package de.adesso.fischereiregister.core.validation;

import de.adesso.fischereiregister.core.commands.CreateRegularLicenseCommand;
import de.adesso.fischereiregister.core.exceptions.ClientInputValidationException;
import de.adesso.fischereiregister.core.exceptions.SystemConfigValidationException;
import de.adesso.fischereiregister.core.interceptables.InterceptableCountryService;
import de.adesso.fischereiregister.core.interceptables.InterceptableTenantRulesValidationPort;
import de.adesso.fischereiregister.core.model.user.UserDetails;
import de.adesso.fischereiregister.core.model.user.UserRole;
import de.adesso.fischereiregister.core.ports.CountryService;
import de.adesso.fischereiregister.core.ports.TenantRulesValidationPort;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

class CreateRegularLicenseCommandValidatorTest {

    private CreateRegularLicenseCommandValidator commandValidator;

    @BeforeEach
    void setUp() {
        final CountryService countryService = new InterceptableCountryService();
        final TenantRulesValidationPort tenantRulesValidationPort = new InterceptableTenantRulesValidationPort();
        commandValidator = new CreateRegularLicenseCommandValidator(countryService, tenantRulesValidationPort);
    }

    @Test
    @DisplayName("CreateRegularLicenseCommandValidatorTest.validateOrThrow should not throw any error for a valid command")
    public void testValidateOrThrow_validCommand() {
        // GIVEN
        CreateRegularLicenseCommand validCommand = new CreateRegularLicenseCommand(
                UUID.randomUUID(),
                UUID.randomUUID().toString(),
                DomainTestData.createConsentInfo(),
                DomainTestData.createPersonWithAddress(),
                List.of(DomainTestData.createAnalogFee()),
                List.of(DomainTestData.createAnalogTax()),
                DomainTestData.createUserDetails(UserRole.OFFICIAL)
        );

        // WHEN
        // THEN
        assertDoesNotThrow(() -> commandValidator.validateOrThrow(validCommand));
    }

    @Test
    @DisplayName("CreateRegularLicenseCommandValidatorTest.validateOrThrow should throw a ClientInputValidationException with the respective error messages for an invalid command")
    public void testValidateOrThrow_invalidClientInputCommand() {
        // GIVEN
        CreateRegularLicenseCommand invalidCommand = new CreateRegularLicenseCommand(
                UUID.randomUUID(),
                UUID.randomUUID().toString(),
                null,
                null,
                Collections.emptyList(),
                Collections.emptyList(),
                DomainTestData.createUserDetails(UserRole.OFFICIAL)
        );

        // WHEN
        // THEN
        ClientInputValidationException exception = assertThrows(ClientInputValidationException.class, () -> commandValidator.validateOrThrow(invalidCommand));
        ValidationResult result = exception.getValidationResult();

        assertTrue(result.hasErrors());
        assertEquals(4, result.getErrorNotes().size());
        assertTrue(result.getErrorNotes().contains("Person Details is required"));
        assertTrue(result.getErrorNotes().contains("address is required"));
        assertTrue(result.getErrorNotes().contains("Fees are required"));
        assertTrue(result.getErrorNotes().contains("consentInfo is required"));
    }

    @Test
    @DisplayName("CreateRegularLicenseCommandValidatorTest.validateOrThrow should throw a SystemConfigValidationException when the user Details are invalid")
    public void testValidateOrThrow_invalidSystemConfigCommand() {
        // GIVEN
        CreateRegularLicenseCommand invalidCommand = new CreateRegularLicenseCommand(
                UUID.randomUUID(),
                null,
                DomainTestData.createConsentInfo(),
                DomainTestData.createPersonWithAddress(),
                List.of(DomainTestData.createAnalogFee()),
                List.of(DomainTestData.createAnalogTax()),
                null
        );

        // WHEN
        // THEN
        SystemConfigValidationException exception = assertThrows(SystemConfigValidationException.class, () -> commandValidator.validateOrThrow(invalidCommand));
        ValidationResult result = exception.getValidationResult();

        assertTrue(result.hasErrors());
        assertEquals(2, result.getErrorNotes().size());
        assertTrue(result.getErrorNotes().contains("User Details is required"));
        assertTrue(result.getErrorNotes().contains("Salt is required"));
    }

    @Test
    @DisplayName("CreateRegularLicenseCommandValidatorTest.validateOrThrow should throw a SystemConfigValidationException when user details are present but invalid")
    public void testValidateOrThrow_missingOffice() {
        // GIVEN
        // Create user details with null office
        UserDetails userDetailsWithoutOffice = new UserDetails(null,null,null,null,null, Set.of(UserRole.OFFICIAL));

        CreateRegularLicenseCommand invalidCommand = new CreateRegularLicenseCommand(
                UUID.randomUUID(),
                UUID.randomUUID().toString(),
                DomainTestData.createConsentInfo(),
                DomainTestData.createPersonWithAddress(),
                List.of(DomainTestData.createAnalogFee()),
                List.of(DomainTestData.createAnalogTax()),
                userDetailsWithoutOffice
        );

        // WHEN
        // THEN
        SystemConfigValidationException exception = assertThrows(SystemConfigValidationException.class, () -> commandValidator.validateOrThrow(invalidCommand));
        ValidationResult result = exception.getValidationResult();

        assertTrue(result.hasErrors());
        assertTrue(result.getErrorNotes().contains("User Office is required"));
    }
}