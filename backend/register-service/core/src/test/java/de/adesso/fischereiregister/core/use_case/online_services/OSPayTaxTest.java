package de.adesso.fischereiregister.core.use_case.online_services;

import de.adesso.fischereiregister.core.commands.CreateFishingCertificateCommand;
import de.adesso.fischereiregister.core.commands.CreateRegularLicenseCommand;
import de.adesso.fischereiregister.core.commands.MoveJurisdictionCommand;
import de.adesso.fischereiregister.core.commands.OSPayTaxCommand;
import de.adesso.fischereiregister.core.events.FishingTaxPayedEvent;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.user.UserDetails;
import de.adesso.fischereiregister.core.model.user.UserRole;
import de.adesso.fischereiregister.core.testutils.AxonFixture;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import org.axonframework.test.aggregate.AggregateTestFixture;
import org.axonframework.test.matchers.Matchers;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import java.util.List;
import java.util.UUID;

import static java.util.Collections.emptyList;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;

public class OSPayTaxTest extends OSTest {

    @Test
    @ExtendWith(AxonFixture.class)
    void withAxonTestFixture(AggregateTestFixture<RegisterEntry> fixture) {        //given
        final UUID givenId = UUID.randomUUID();
        final Person givenPerson = DomainTestData.createPersonWithAddress();
        final UserDetails givenUserDetails = DomainTestData.createUserDetails(UserRole.OFFICIAL);

        final String givenOriginalSalt = "12345";
        final String givenReplacementSalt = "67890";

        assertNotEquals(givenOriginalSalt, givenReplacementSalt);
        final FishingLicense givenFishingLicense = new FishingLicense();

        givenFishingLicense.setNumber("LN12345");

        List<Tax> taxList = DomainTestData.createAnalogTaxesWithOneTax();

        String issuedBy = "Fischfreunde Övelgönne e.V.";

        String inboxReference = "inboxReference";

        String serviceAccountId = "serviceAccount";
        String transactionId = "transactionId";

        fixture
                .givenCommands(
                        new CreateFishingCertificateCommand(givenId,
                                DomainTestData.jan_2024,
                                givenPerson,
                                DomainTestData.createUserDetails(UserRole.OFFICIAL)
                        ),
                        new MoveJurisdictionCommand(givenId, DomainTestData.createJurisdictionConsentInfo(), "anySalt", emptyList(), givenUserDetails),
                        new CreateRegularLicenseCommand(
                                givenId,
                                givenOriginalSalt,
                                DomainTestData.createConsentInfo(),
                                givenPerson,
                                List.of(DomainTestData.createAnalogFee()),
                                emptyList(),
                                givenUserDetails)
                )
                .when(
                        new OSPayTaxCommand(
                                givenId,
                                givenPerson,
                                DomainTestData.createTaxConsentInfo(),
                                "anySalt",
                                taxList,
                                serviceAccountId,
                                transactionId,
                                "SH",
                                inboxReference
                        )
                )
                .expectSuccessfulHandlerExecution()
                .expectEventsMatching(Matchers.exactSequenceOf(
                        Matchers.messageWithPayload(Matchers.matches(payload ->
                                payload.getClass().isAssignableFrom(FishingTaxPayedEvent.class)
                        ))
                ))
                .expectState(registerEntry -> {
                    // Assert register ID
                    assertEquals(givenId, registerEntry.getRegisterId());

                    // Assert qualifications proofs
                    assertEquals(1, registerEntry.getQualificationsProofs().size());
                    assertEquals(issuedBy, registerEntry.getQualificationsProofs().get(0).getIssuedBy());

                    // Assert fishing licenses
                    assertEquals(1, registerEntry.getFishingLicenses().size());

                    assertEquals(3, registerEntry.getIdentificationDocuments().size());
                    assertEquals(1, registerEntry.getTaxes().size());

                    checkOnlineServiceDataCorrectlySet(registerEntry, inboxReference, serviceAccountId, transactionId);
                });

    }
}
