package de.adesso.fischereiregister.core.interceptables;

import de.adesso.fischereiregister.core.exceptions.RulesProcessingException;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.ports.TenantRulesValidationPort;
import de.adesso.fischereiregister.core.validation.ValidationResult;

import java.util.List;

public class InterceptableTenantRulesValidationPort implements TenantRulesValidationPort {


    @Override
    public void validateUsingFishingLicenseRules(FederalState federalState, Person person, LicenseType licenseType, ValidationResult validationResult) throws RulesProcessingException {
        // noting to do here as we do not want to have any validation errors here
    }

    @Override
    public void validateUsingQualificationProofRules(FederalState federalState, Person person, ValidationResult validationResult) throws RulesProcessingException {

    }

    @Override
    public void validateUsingFishingTaxRules(FederalState federalState, Person person, List<Tax> tax, ValidationResult validationResult) throws RulesProcessingException {
        // noting to do here as we do not want to have any validation errors here
    }

    @Override
    public void validateTaxes(FederalState federalState, List<Tax> tax, boolean officeFeeAlreadyPayed, ValidationResult validationResult) throws RulesProcessingException {
        // noting to do here as we do not want to have any validation errors here
    }

    @Override
    public void validateFeesForFishingLicense(FederalState federalState, LicenseType licenseType, List<de.adesso.fischereiregister.core.model.Fee> fees, ValidationResult validationResult) throws RulesProcessingException {
        // noting to do here as we do not want to have any validation errors here
    }
}
