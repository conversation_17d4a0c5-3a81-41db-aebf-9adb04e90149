package de.adesso.fischereiregister.core.validation;

import de.adesso.fischereiregister.core.commands.MoveJurisdictionCommand;
import de.adesso.fischereiregister.core.exceptions.ClientInputValidationException;
import de.adesso.fischereiregister.core.exceptions.SystemConfigValidationException;
import de.adesso.fischereiregister.core.interceptables.InterceptableCountryService;
import de.adesso.fischereiregister.core.interceptables.InterceptableTenantRulesValidationPort;
import de.adesso.fischereiregister.core.model.consent.JurisdictionConsentInfo;
import de.adesso.fischereiregister.core.model.user.UserDetails;
import de.adesso.fischereiregister.core.model.user.UserRole;
import de.adesso.fischereiregister.core.ports.CountryService;
import de.adesso.fischereiregister.core.ports.TenantRulesValidationPort;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

class MoveJurisdictionCommandValidatorTest {

    private MoveJurisdictionCommandValidator commandValidator;

    @BeforeEach
    void setUp() {
        final CountryService countryService = new InterceptableCountryService();
        final TenantRulesValidationPort tenantRulesValidationPort = new InterceptableTenantRulesValidationPort();
        commandValidator = new MoveJurisdictionCommandValidator(countryService, tenantRulesValidationPort);
    }

    @Test
    @DisplayName("MoveJurisdictionCommandValidator.validateOrThrow should not throw any error for a valid command")
    public void testValidateOrThrow_validCommand() {
        // GIVEN
        MoveJurisdictionCommand validCommand = new MoveJurisdictionCommand(
                UUID.randomUUID(),
                DomainTestData.createJurisdictionConsentInfo(),
                UUID.randomUUID().toString(),
                List.of(DomainTestData.createAnalogTax()),
                DomainTestData.createUserDetails(UserRole.OFFICIAL)
        );

        // WHEN
        // THEN
        assertDoesNotThrow(() -> commandValidator.validateOrThrow(validCommand));
    }

    @Test
    @DisplayName("MoveJurisdictionCommandValidator.validateOrThrow should throw a ClientInputValidationException when the command is invalid")
    public void testValidateOrThrow_invalidClientInputCommand() {
        // GIVEN
        JurisdictionConsentInfo invalidJurisdictionConsentInfo = DomainTestData.createJurisdictionConsentInfo();
        invalidJurisdictionConsentInfo.setProofOfMoveVerified(false);

        MoveJurisdictionCommand invalidCommand = new MoveJurisdictionCommand(
                UUID.randomUUID(),
                invalidJurisdictionConsentInfo,
                UUID.randomUUID().toString(),
                Collections.emptyList(),
                DomainTestData.createUserDetails(UserRole.OFFICIAL)
        );

        // WHEN
        // THEN
        ClientInputValidationException exception = assertThrows(ClientInputValidationException.class, () -> commandValidator.validateOrThrow(invalidCommand));
        ValidationResult result = exception.getValidationResult();

        assertTrue(result.hasErrors());
        assertEquals(1, result.getErrorNotes().size());
        assertTrue(result.getErrorNotes().contains("proofOfMoveVerified must be set to true"));
    }

    @Test
    @DisplayName("MoveJurisdictionCommandValidator.validateOrThrow should throw a SystemConfigValidationException when user details are invalid")
    public void testValidateOrThrow_invalidSystemConfigCommand() {
        // GIVEN
        MoveJurisdictionCommand invalidCommand = new MoveJurisdictionCommand(
                UUID.randomUUID(),
                DomainTestData.createJurisdictionConsentInfo(),
                null,
                List.of(DomainTestData.createAnalogTax()),
                null
        );

        // WHEN
        // THEN
        SystemConfigValidationException exception = assertThrows(SystemConfigValidationException.class, () -> commandValidator.validateOrThrow(invalidCommand));
        ValidationResult result = exception.getValidationResult();

        assertTrue(result.hasErrors());
        assertEquals(2, result.getErrorNotes().size());
        assertTrue(result.getErrorNotes().contains("User Details is required"));
        assertTrue(result.getErrorNotes().contains("Salt is required"));
    }

    @Test
    @DisplayName("MoveJurisdictionCommandValidator.validateOrThrow should throw a SystemConfigValidationException when user details are present but invalid")
    public void testValidateOrThrow_missingOffice() {
        // GIVEN
        UserDetails userDetailsWithoutOffice = new UserDetails(null,null,null,null,null, Set.of(UserRole.OFFICIAL));

        // Create user details with null office
        MoveJurisdictionCommand invalidCommand = new MoveJurisdictionCommand(
                UUID.randomUUID(),
                DomainTestData.createJurisdictionConsentInfo(),
                UUID.randomUUID().toString(),
                List.of(DomainTestData.createAnalogTax()),
                userDetailsWithoutOffice
        );

        // WHEN
        // THEN
        SystemConfigValidationException exception = assertThrows(SystemConfigValidationException.class, () -> commandValidator.validateOrThrow(invalidCommand));
        ValidationResult result = exception.getValidationResult();

        assertTrue(result.hasErrors());
        assertEquals(3, result.getErrorNotes().size());
        assertTrue(result.getErrorNotes().contains("User Id is required"));
        assertTrue(result.getErrorNotes().contains("Federal state null is invalid."));
        assertTrue(result.getErrorNotes().contains("User Office is required"));
    }
}