package de.adesso.fischereiregister.core.validation.helper;

import de.adesso.fischereiregister.core.model.PaymentInfo;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import de.adesso.fischereiregister.core.validation.ValidationResult;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

class TaxValidatorHelperTest {

    @Test
    @DisplayName("TaxValidatorHelper.validateTaxes should add errors when tax has missing required fields")
    void testValidateTaxes_MissingRequiredFields() {
        // Given
        ValidationResult validationResult = new ValidationResult();

        // Create sample data
        List<Tax> taxList = new ArrayList<>();
        Tax validTax = DomainTestData.createAnalogTax();
        Tax invalidTax = DomainTestData.createAnalogTax();
        invalidTax.setPaymentInfo(null);
        invalidTax.setValidFrom(null);
        invalidTax.setFederalState(null);
        taxList.add(validTax);
        taxList.add(invalidTax);

        // When
        TaxValidatorHelper.validateTaxes(taxList, validationResult);

        // Then
        assertTrue(validationResult.hasErrors());
        assertEquals(3, validationResult.getErrorNotes().size());
        assertTrue(validationResult.getErrorNotes().contains(TaxValidatorHelper.FEDERAL_STATE + " is required"));
        assertTrue(validationResult.getErrorNotes().contains(TaxValidatorHelper.VALID_FROM + " is required"));
        assertTrue(validationResult.getErrorNotes().contains(TaxValidatorHelper.PAYMENT_INFO + " is required"));
    }

    @Test
    @DisplayName("TaxValidatorHelper.validateTaxes should add errors when paymentInfo has missing fields")
    void testValidateTaxes_PaymentInfoMissingFields() {
        // Given
        ValidationResult validationResult = new ValidationResult();

        Tax tax = DomainTestData.createAnalogTax();
        PaymentInfo paymentInfo = new PaymentInfo();
        tax.setPaymentInfo(paymentInfo); // PaymentInfo with null type and amount

        // When
        TaxValidatorHelper.validateTaxes(Collections.singletonList(tax), validationResult);

        // Then
        assertTrue(validationResult.hasErrors());
        assertEquals(2, validationResult.getErrorNotes().size());
        assertTrue(validationResult.getErrorNotes().contains(TaxValidatorHelper.PAYMENT_INFO_TYPE + " is required"));
        assertTrue(validationResult.getErrorNotes().contains(TaxValidatorHelper.PAYMENT_INFO_AMOUNT + " is required"));
    }

    @Test
    @DisplayName("TaxValidatorHelper.validateTaxes should add error when tax period is not a full year")
    void testValidateTaxes_NotFullYear() {
        // Given
        ValidationResult validationResult = new ValidationResult();

        Tax tax = DomainTestData.createAnalogTax();
        tax.setValidFrom(LocalDate.of(2023, 2, 1)); // Not January 1st
        tax.setValidTo(LocalDate.of(2023, 12, 31));

        // When
        TaxValidatorHelper.validateTaxes(Collections.singletonList(tax), validationResult);

        // Then
        assertTrue(validationResult.hasErrors());
        assertTrue(validationResult.getErrorNotes().contains("Tax payment should be for a full year (01.01.XXXX – 31.12.XXXX) but it's not."));
    }

    @Test
    @DisplayName("TaxValidatorHelper.validateTaxes should not add errors when tax is valid")
    void testValidateTaxes_ValidTax() {
        // Given
        ValidationResult validationResult = new ValidationResult();
        Tax validTax = DomainTestData.createAnalogTax();

        // When
        TaxValidatorHelper.validateTaxes(Collections.singletonList(validTax), validationResult);

        // Then
        assertFalse(validationResult.hasErrors());
    }

    @Test
    @DisplayName("TaxValidatorHelper.validatePreviouslyPayedTaxes should add error when amount is not 0")
    void testValidatePreviouslyPayedTaxes_AmountNotZero() {
        // Given
        ValidationResult validationResult = new ValidationResult();
        Tax tax = DomainTestData.createAnalogTax();
        // Ensure payment amount is not 0
        tax.getPaymentInfo().setAmount(10.0);

        // When
        TaxValidatorHelper.validatePreviouslyPayedTaxes(Collections.singletonList(tax), validationResult);

        // Then
        assertTrue(validationResult.hasErrors());
        assertTrue(validationResult.getErrorNotes().contains("Previously Paid Taxes Payment amount should not be different than 0"));
    }

    @Test
    @DisplayName("TaxValidatorHelper.validatePreviouslyPayedTaxes should add error when end date is before current year")
    void testValidatePreviouslyPayedTaxes_EndDateBeforeCurrentYear() {
        // Given
        ValidationResult validationResult = new ValidationResult();
        Tax tax = DomainTestData.createAnalogTax();
        int lastYear = LocalDate.now().getYear() - 1;
        tax.setValidTo(LocalDate.of(lastYear, 12, 31));
        // Ensure payment amount is 0 to isolate the end date validation
        tax.getPaymentInfo().setAmount(0.0);

        // When
        TaxValidatorHelper.validatePreviouslyPayedTaxes(Collections.singletonList(tax), validationResult);

        // Then
        assertTrue(validationResult.hasErrors());
        assertEquals(1, validationResult.getErrorNotes().size());
        assertTrue(validationResult.getErrorNotes().contains("Previously Paid Taxes Payment end date should not be before the actual year"));
    }

    @Test
    @DisplayName("TaxValidatorHelper.validatePreviouslyPayedTaxes should add multiple errors when both amount and end date are invalid")
    void testValidatePreviouslyPayedTaxes_MultipleErrors() {
        // Given
        ValidationResult validationResult = new ValidationResult();
        Tax tax = DomainTestData.createAnalogTax();
        // Set invalid amount
        tax.getPaymentInfo().setAmount(10.0);
        // Set invalid end date
        int lastYear = LocalDate.now().getYear() - 1;
        tax.setValidTo(LocalDate.of(lastYear, 12, 31));

        // When
        TaxValidatorHelper.validatePreviouslyPayedTaxes(Collections.singletonList(tax), validationResult);

        // Then
        assertTrue(validationResult.hasErrors());
        assertEquals(2, validationResult.getErrorNotes().size());
        assertTrue(validationResult.getErrorNotes().contains("Previously Paid Taxes Payment amount should not be different than 0"));
        assertTrue(validationResult.getErrorNotes().contains("Previously Paid Taxes Payment end date should not be before the actual year"));
    }

    @Test
    @DisplayName("TaxValidatorHelper.validatePreviouslyPayedTaxes should not add errors when both amount and end date are valid")
    void testValidatePreviouslyPayedTaxes_NoErrors() {
        // Given
        ValidationResult validationResult = new ValidationResult();
        Tax tax = DomainTestData.createAnalogTax();
        // Set valid amount
        tax.getPaymentInfo().setAmount(0.0);
        // Set valid end date (current year)
        int currentYear = LocalDate.now().getYear();
        tax.setValidTo(LocalDate.of(currentYear, 12, 31));

        // When
        TaxValidatorHelper.validatePreviouslyPayedTaxes(Collections.singletonList(tax), validationResult);

        // Then
        assertFalse(validationResult.hasErrors());
        assertEquals(0, validationResult.getErrorNotes().size());
    }


}
