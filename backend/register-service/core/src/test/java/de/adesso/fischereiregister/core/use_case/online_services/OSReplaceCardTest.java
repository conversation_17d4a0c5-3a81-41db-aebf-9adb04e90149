package de.adesso.fischereiregister.core.use_case.online_services;

import de.adesso.fischereiregister.core.commands.DigitizeRegularLicenseCommand;
import de.adesso.fischereiregister.core.commands.OSReplaceCardCommand;
import de.adesso.fischereiregister.core.events.ReplacementCardOrderedEvent;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.QualificationsProof;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.consent.ConsentInfo;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.model.type.QualificationsProofType;
import de.adesso.fischereiregister.core.model.user.UserRole;
import de.adesso.fischereiregister.core.testutils.AxonFixture;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import org.axonframework.test.aggregate.AggregateTestFixture;
import org.axonframework.test.matchers.Matchers;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

import static java.util.Collections.emptyList;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;

public class OSReplaceCardTest extends OSTest{

    @Test
    @ExtendWith(AxonFixture.class)
    void withAxonTestFixture(AggregateTestFixture<RegisterEntry> fixture) {        //given
        final UUID givenId = UUID.randomUUID();
        final Person givenPerson = DomainTestData.createPersonWithAddress();

        final Jurisdiction givenJurisdiction = new Jurisdiction();
        givenJurisdiction.setFederalState("SH");

        final QualificationsProof givenQualificationsProof = new QualificationsProof();
        givenQualificationsProof.setFishingCertificateId("4711");
        givenQualificationsProof.setIssuedBy("Fischfreunde Övelgönne e.V.");
        givenQualificationsProof.setPassedOn(LocalDate.now().minusYears(1));
        givenQualificationsProof.setFederalState("SH");
        givenQualificationsProof.setType(QualificationsProofType.CERTIFICATE);

        final String givenOriginalSalt = "12345";
        final String givenReplacementSalt = "67890";

        assertNotEquals(givenOriginalSalt, givenReplacementSalt);
        final FishingLicense givenFishingLicense = new FishingLicense();
        givenFishingLicense.setNumber("LN12345");
        givenFishingLicense.getValidityPeriods().add(DomainTestData.createValidityPeriod(LocalDate.now(), LocalDate.MAX));
        givenFishingLicense.setType(LicenseType.REGULAR);
        givenFishingLicense.setIssuingFederalState(FederalState.SH);

        String transactionId = "tId";
        String serviceAccountId = "serviceAccountId";
        ConsentInfo givenConsentInfo = DomainTestData.createConsentInfo();
        String inboxReference = "inboxReference";
        List<Tax> taxes = List.of(DomainTestData.createAnalogTax());

        fixture
                .givenCommands(
                        new DigitizeRegularLicenseCommand(
                                givenId,
                                givenOriginalSalt,
                                givenPerson,
                                List.of(DomainTestData.createAnalogFee()),
                                List.of(),
                                List.of(),
                                List.of(givenQualificationsProof),
                                givenConsentInfo,
                                DomainTestData.createUserDetails(UserRole.OFFICIAL)
                        )
                )
                .when(
                        new OSReplaceCardCommand(
                                givenId,
                                givenOriginalSalt,
                                givenFishingLicense.getNumber(),
                                givenPerson,
                                DomainTestData.createConsentInfo(),
                                taxes,
                                emptyList(),
                                serviceAccountId,
                                givenJurisdiction.getFederalState(),
                                transactionId,
                                inboxReference
                        )
                )
                .expectSuccessfulHandlerExecution()
                .expectEventsMatching(Matchers.exactSequenceOf(
                        Matchers.messageWithPayload(Matchers.matches(payload ->
                                payload.getClass().isAssignableFrom(ReplacementCardOrderedEvent.class)
                        ))
                ))
                .expectState(registerEntry -> {
                    // Assert register ID
                    assertEquals(givenId, registerEntry.getRegisterId());

                    // Assert qualifications proofs
                    assertEquals(1, registerEntry.getQualificationsProofs().size());
                    assertEquals(givenQualificationsProof.getIssuedBy(), registerEntry.getQualificationsProofs().get(0).getIssuedBy());

                    // Assert fishing licenses
                    assertEquals(1, registerEntry.getFishingLicenses().size());

                    // there are 2 CARD documents, because the old card should remain valid
                    assertEquals(5, registerEntry.getIdentificationDocuments().size());

                    // check fishing online service values are saved correctly
                    checkOnlineServiceDataCorrectlySet(registerEntry, inboxReference, serviceAccountId, transactionId);
                });

    }

}
