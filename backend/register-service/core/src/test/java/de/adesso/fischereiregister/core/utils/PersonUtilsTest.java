package de.adesso.fischereiregister.core.utils;

import de.adesso.fischereiregister.core.model.Address;
import de.adesso.fischereiregister.core.model.Birthdate;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class PersonUtilsTest {
    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @DisplayName("PersonUtils.matches is successful when the requested is exactly the same.")
    public void testPersonMatchesWhenPersonTheSame(boolean reverseOrdering) {
        // GIVEN
        final Person person = DomainTestData.createPersonWithAddress();
        final Person samePerson = DomainTestData.createPersonWithAddress();

        // WHEN
        final boolean result = reverseOrdering ? PersonUtils.matches(samePerson, person) : PersonUtils.matches(person, samePerson);

        // THEN
        assertTrue(result);
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @DisplayName("PersonUtils.matches is successful when address of person is different.")
    public void testPersonMatchesWhenAddressDoesNotMatch(boolean reverseOrdering) {
        // GIVEN
        final Person person = DomainTestData.createPersonWithAddress();
        final Person personWithOtherAddress = DomainTestData.createPersonWithAddress();
        personWithOtherAddress.getAddress().setStreet("otherstreet");

        // WHEN
        final boolean result = reverseOrdering ? PersonUtils.matches(personWithOtherAddress, person) : PersonUtils.matches(person, personWithOtherAddress);

        // THEN
        assertTrue(result);
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @DisplayName("PersonUtils.matches is false when firstname mismatches")
    public void testPersonMismatchOnFirstnameMismatch(boolean reverseOrdering) {
        // GIVEN
        final Person person = DomainTestData.createPerson();
        final Person otherPerson = DomainTestData.createPerson();

        otherPerson.setFirstname("othername");

        // WHEN
        final boolean result = reverseOrdering ? PersonUtils.matches(otherPerson, person) : PersonUtils.matches(person, otherPerson);

        // THEN
        assertFalse(result);
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @DisplayName("PersonUtils.matches is false when lastname mismatches")
    public void testPersonMismatchOnLastnameMismatch(boolean reverseOrdering) {
        // GIVEN
        final Person person = DomainTestData.createPerson();
        final Person otherPerson = DomainTestData.createPerson();

        otherPerson.setLastname("othername");

        // WHEN
        final boolean result = reverseOrdering ? PersonUtils.matches(otherPerson, person) : PersonUtils.matches(person, otherPerson);

        // THEN
        assertFalse(result);
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @DisplayName("PersonUtils.matches is false when lastname mismatches")
    public void testPersonMismatchOnBirthnameMismatch(boolean reverseOrdering) {
        // GIVEN
        final Person person = DomainTestData.createPerson();
        final Person otherPerson = DomainTestData.createPerson();

        otherPerson.setBirthname("othername");

        // WHEN
        final boolean result = reverseOrdering ? PersonUtils.matches(otherPerson, person) : PersonUtils.matches(person, otherPerson);

        // THEN
        assertFalse(result);
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @DisplayName("PersonUtils.matches is false when birthplace mismatches")
    public void testPersonMismatchOnBirthplaceMismatch(boolean reverseOrdering) {
        // GIVEN
        final Person person = DomainTestData.createPerson();
        final Person otherPerson = DomainTestData.createPerson();

        otherPerson.setBirthplace("otherplace");

        // WHEN
        final boolean result = reverseOrdering ? PersonUtils.matches(otherPerson, person) : PersonUtils.matches(person, otherPerson);

        // THEN
        assertFalse(result);
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @DisplayName("PersonUtils.matches is false when birthdate mismatches")
    public void testPersonMismatchOnBirthdateMismatch(boolean reverseOrdering) {
        // GIVEN
        final Person person = DomainTestData.createPerson();
        final Person otherPerson = DomainTestData.createPerson();

        otherPerson.setBirthdate(new Birthdate(2001, 1, 1));

        // WHEN
        final boolean result = reverseOrdering ? PersonUtils.matches(otherPerson, person) : PersonUtils.matches(person, otherPerson);

        // THEN
        assertFalse(result);
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @DisplayName("PersonUtils.matches is false when title mismatches")
    public void testPersonMismatchOnTitleMismatch(boolean reverseOrdering) {
        // GIVEN
        final Person person = DomainTestData.createPerson();
        final Person otherPerson = DomainTestData.createPerson();

        otherPerson.setTitle("");

        // WHEN
        final boolean result = reverseOrdering ? PersonUtils.matches(otherPerson, person) : PersonUtils.matches(person, otherPerson);

        // THEN
        assertFalse(result);
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @DisplayName("PersonUtils.matches is false when nationality mismatches")
    public void testPersonMismatchOnNationalityMismatch(boolean reverseOrdering) {
        // GIVEN
        final Person person = DomainTestData.createPerson();
        final Person otherPerson = DomainTestData.createPerson();

        otherPerson.setNationality("Kolumbianisch");

        // WHEN
        final boolean result = reverseOrdering ? PersonUtils.matches(otherPerson, person) : PersonUtils.matches(person, otherPerson);

        // THEN
        assertFalse(result);
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @DisplayName("PersonUtils.matches is successful when either nationality is unset.")
    public void testPersonMatchesWhenNationalityUnset(boolean reverseOrdering) {
        // GIVEN
        final Person person = DomainTestData.createPerson();
        final Person otherPerson = DomainTestData.createPerson();

        otherPerson.setNationality(null);

        // WHEN
        final boolean result = reverseOrdering ? PersonUtils.matches(otherPerson, person) : PersonUtils.matches(person, otherPerson);

        // THEN
        assertTrue(result);
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @DisplayName("PersonUtils.matches is not differentiating between null or empty strings.")
    public void testPersonMatchesForNullOrEmptyStrings(boolean reverseOrdering) {
        // GIVEN
        final Person person = DomainTestData.createPerson();
        final Person otherPerson = DomainTestData.createPerson();

        person.setNationality(null);
        otherPerson.setNationality("");

        person.setTitle(null);
        otherPerson.setTitle("");

        // WHEN
        final boolean result = reverseOrdering ? PersonUtils.matches(otherPerson, person) : PersonUtils.matches(person, otherPerson);

        // THEN
        assertTrue(result);
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @DisplayName("PersonUtils.matches is considering the lastname as birthname, when no birthname was given.")
    public void testPersonMatchesBirthnameWhenOnlyLastnameGiven(boolean reverseOrdering) {
        // GIVEN
        final Person person = DomainTestData.createPerson();
        final Person otherPerson = DomainTestData.createPerson();

        person.setBirthname(person.getLastname());
        otherPerson.setBirthname(null);

        // WHEN
        final boolean result = reverseOrdering ? PersonUtils.matches(otherPerson, person) : PersonUtils.matches(person, otherPerson);

        // THEN
        assertTrue(result);
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @DisplayName("PersonUtils.matches is considering the lastname as birthname, when empty birthname was given.")
    public void testPersonMatchesBirthnameWhenEmptyBirthnameGiven(boolean reverseOrdering) {
        // GIVEN
        final Person person = DomainTestData.createPerson();
        final Person otherPerson = DomainTestData.createPerson();

        person.setBirthname(person.getLastname());
        otherPerson.setBirthname("");

        // WHEN
        final boolean result = reverseOrdering ? PersonUtils.matches(otherPerson, person) : PersonUtils.matches(person, otherPerson);

        // THEN
        assertTrue(result);
    }


    @Test
    @DisplayName("printableNameAndAddress should return full name and address when all fields are present")
    void testGetPrintableNameAndAddress_WithCompleteAddress() {
        // GIVEN
        Person person = new Person();
        person.setFirstname("John");
        person.setLastname("Doe");
        person.setTitle("Dr.");

        Address address = new Address();
        address.setDetail("Apartment 101");
        address.setStreet("Main Street");
        address.setStreetNumber("123");
        address.setPostcode("12345");
        address.setCity("Springfield");
        person.setAddress(address);

        // WHEN
        String result = PersonUtils.getPrintableNameAndAddress(person);

        // THEN
        String expected = "Dr. John Doe\n\nApartment 101\nMain Street123\n12345 Springfield";
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("printableNameAndAddress should return full name and address without details when detail is missing")
    void testGetPrintableNameAndAddress_WithoutDetail() {
        // GIVEN
        Person person = new Person();
        person.setFirstname("Jane");
        person.setLastname("Smith");

        Address address = new Address();
        address.setStreet("Elm Street");
        address.setStreetNumber("456");
        address.setPostcode("67890");
        address.setCity("Metropolis");
        person.setAddress(address);

        // WHEN
        String result = PersonUtils.getPrintableNameAndAddress(person);

        // THEN
        String expected = "Jane Smith\n\nElm Street456\n67890 Metropolis";
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("printableNameAndAddress should use office address when home address is null")
    void testGetPrintableNameAndAddress_UsesOfficeAddress() {
        // GIVEN
        Person person = new Person();
        person.setFirstname("Alice");
        person.setLastname("Johnson");

        Address officeAddress = new Address();
        officeAddress.setStreet("Corporate Blvd");
        officeAddress.setStreetNumber("789");
        officeAddress.setPostcode("54321");
        officeAddress.setCity("Gotham");
        person.setOfficeAddress(officeAddress);

        // WHEN
        String result = PersonUtils.getPrintableNameAndAddress(person);

        // THEN
        String expected = "Alice Johnson\n\nCorporate Blvd789\n54321 Gotham";
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("printableNameAndAddress should handle missing street number")
    void testGetPrintableNameAndAddress_MissingStreetNumber() {
        // GIVEN
        Person person = new Person();
        person.setFirstname("Bob");
        person.setLastname("Brown");

        Address address = new Address();
        address.setStreet("Ocean Drive");
        address.setStreetNumber("");
        address.setPostcode("98765");
        address.setCity("Atlantis");
        person.setAddress(address);

        // WHEN
        String result = PersonUtils.getPrintableNameAndAddress(person);

        // THEN
        String expected = "Bob Brown\n\nOcean Drive\n98765 Atlantis";
        assertEquals(expected, result);
    }
}
