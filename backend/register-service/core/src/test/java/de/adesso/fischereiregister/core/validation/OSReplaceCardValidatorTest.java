package de.adesso.fischereiregister.core.validation;

import de.adesso.fischereiregister.core.commands.OSReplaceCardCommand;
import de.adesso.fischereiregister.core.exceptions.ClientInputValidationException;
import de.adesso.fischereiregister.core.exceptions.SystemConfigValidationException;
import de.adesso.fischereiregister.core.interceptables.InterceptableCountryService;
import de.adesso.fischereiregister.core.interceptables.InterceptableTenantRulesValidationPort;
import de.adesso.fischereiregister.core.model.Fee;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.consent.ConsentInfo;
import de.adesso.fischereiregister.core.ports.CountryService;
import de.adesso.fischereiregister.core.ports.TenantRulesValidationPort;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

class OSReplaceCardValidatorTest {

    private OSReplaceCardCommandValidator validator;

    @BeforeEach
    void setUp() {
        CountryService countryService = new InterceptableCountryService();
        final TenantRulesValidationPort tenantRulesValidationPort = new InterceptableTenantRulesValidationPort();
        validator = new OSReplaceCardCommandValidator(countryService, tenantRulesValidationPort);
    }

    @Test
    void testValidate_MissingFields_ClientInputValidation() {
        // GIVEN
        Person person = null; // Missing person
        String federalState = "InvalidState"; // Invalid federal state
        List<Tax> taxList = List.of();
        List<Fee> feeList = List.of();
        String fishingCertificateCode = null; // Missing fishing certificate code
        String serviceAccountId = null; // Missing service account ID
        String transactionId = null; // Missing transaction ID
        String inboxReference = null; // Missing transaction ID
        ConsentInfo consentInfo = DomainTestData.createConsentInfo();

        RegisterEntry registerEntry = new RegisterEntry();

        final OSReplaceCardCommand command = new OSReplaceCardCommand(
                UUID.randomUUID(),
                UUID.randomUUID().toString(),
                fishingCertificateCode,
                person,
                consentInfo,
                taxList,
                feeList,
                serviceAccountId,
                federalState,
                transactionId,
                inboxReference); // Missing inboxReference

        // WHEN
        ClientInputValidationException exception = assertThrows(ClientInputValidationException.class, () -> validator.validateOrThrow(command, registerEntry));

        // THEN
        ValidationResult result = exception.getValidationResult();
        assertTrue(result.hasErrors());
        assertEquals(7, result.getErrorNotes().size());
        assertTrue(result.getErrorNotes().contains("Person Details is required"));
        assertTrue(result.getErrorNotes().contains("address is required"));
        assertTrue(result.getErrorNotes().contains("Federal state " + federalState + " is invalid."));
        assertTrue(result.getErrorNotes().contains("License number is required"));
        assertTrue(result.getErrorNotes().contains("Service account id is required"));
        assertTrue(result.getErrorNotes().contains("Transaction id is required"));
        assertTrue(result.getErrorNotes().contains("inboxReference is required"));
    }

    @Test
    void testValidate_MissingInboxReference_SystemConfigValidation() {
        // GIVEN
        Person person = DomainTestData.createPersonWithAddress(); // Valid person
        String federalState = "SH"; // Valid federal state
        List<Tax> taxList = DomainTestData.createAnalogTaxesWithOneTax(); // Valid taxes
        List<Fee> feeList = List.of(); // Valid fees
        String fishingCertificateCode = "FCC12345"; // Valid fishing certificate code
        String serviceAccountId = "Service123"; // Valid service account ID
        String transactionId = "Trans456"; // Valid transaction ID
        ConsentInfo consentInfo = DomainTestData.createConsentInfo(); // Valid consent info

        RegisterEntry registerEntry = new RegisterEntry();
        registerEntry.setPerson(person);

        final OSReplaceCardCommand command = new OSReplaceCardCommand(
                UUID.randomUUID(),
                null,
                fishingCertificateCode,
                person,
                consentInfo,
                taxList,
                feeList,
                serviceAccountId,
                federalState,
                transactionId,
                "inboxReference"); // Missing inboxReference

        // WHEN
        SystemConfigValidationException exception = assertThrows(SystemConfigValidationException.class, () -> validator.validateOrThrow(command, registerEntry));

        // THEN
        ValidationResult result = exception.getValidationResult();
        assertTrue(result.hasErrors());
        assertEquals(1, result.getErrorNotes().size());
        assertTrue(result.getErrorNotes().contains("Salt is required"));
    }
}