package de.adesso.fischereiregister.core.validation;

import de.adesso.fischereiregister.core.commands.BanPermanentlyCommand;
import de.adesso.fischereiregister.core.exceptions.ClientInputValidationException;
import de.adesso.fischereiregister.core.exceptions.SystemConfigValidationException;
import de.adesso.fischereiregister.core.interceptables.InterceptableCountryService;
import de.adesso.fischereiregister.core.interceptables.InterceptableTenantRulesValidationPort;
import de.adesso.fischereiregister.core.model.user.UserRole;
import de.adesso.fischereiregister.core.ports.CountryService;
import de.adesso.fischereiregister.core.ports.TenantRulesValidationPort;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

class BanPermanentlyCommandValidatorTest {

    private BanPermanentlyCommandValidator commandValidator;

    @BeforeEach
    void setUp() {
        final CountryService countryService = new InterceptableCountryService();
        final TenantRulesValidationPort tenantRulesValidationPort = new InterceptableTenantRulesValidationPort();
        commandValidator = new BanPermanentlyCommandValidator(countryService, tenantRulesValidationPort);
    }

    @Test
    @DisplayName("BanPermanentlyCommandValidator.validateOrThrow should not throw any error for a valid command")
    public void testValidateOrThrow_validCommand() {
        // GIVEN
        BanPermanentlyCommand validCommand = new BanPermanentlyCommand(
                UUID.randomUUID(),
                UUID.randomUUID(),
                "fileNumber",
                "reportedBy",
                LocalDate.now(),
                DomainTestData.createUserDetails(UserRole.OFFICIAL)
        );

        // WHEN
        // THEN
        assertDoesNotThrow(() -> commandValidator.validateOrThrow(validCommand));
    }

    @Test
    @DisplayName("BanPermanentlyCommandValidator.validateOrThrow should throw a ClientInputValidationException with the respective error messages for an invalid command")
    public void testValidateOrThrow_invalidClientInputCommand() {
        // GIVEN
        BanPermanentlyCommand invalidCommand = new BanPermanentlyCommand(
                UUID.randomUUID(),
                null,
                null,
                null,
                null,
                DomainTestData.createUserDetails(UserRole.OFFICIAL)
        );

        // WHEN
        // THEN
        ClientInputValidationException exception = assertThrows(ClientInputValidationException.class, () -> commandValidator.validateOrThrow(invalidCommand));
        ValidationResult result = exception.getValidationResult();

        assertTrue(result.hasErrors());
        assertEquals(3, result.getErrorNotes().size());
        assertTrue(result.getErrorNotes().contains("File Number is required"));
        assertTrue(result.getErrorNotes().contains("Reported By is required"));
        assertTrue(result.getErrorNotes().contains("Ban Start date (from) is required"));
    }


    @Test
    @DisplayName("BanPermanentlyCommandValidator.validateOrThrow should throw a SystemConfigValidationException when the user Details are invalid")
    public void testValidateOrThrow_invalidSystemConfigCommand() {
        // GIVEN
        BanPermanentlyCommand invalidCommand = new BanPermanentlyCommand(
                UUID.randomUUID(),
                UUID.randomUUID(),
                "fileNumber",
                "reportedBy",
                LocalDate.now(),
                null
        );

        // WHEN
        // THEN
        SystemConfigValidationException exception = assertThrows(SystemConfigValidationException.class, () -> commandValidator.validateOrThrow(invalidCommand));
        ValidationResult result = exception.getValidationResult();

        assertTrue(result.hasErrors());
        assertEquals(1, result.getErrorNotes().size());
        assertTrue(result.getErrorNotes().contains("User Details is required"));
    }
}