package de.adesso.fischereiregister.core.processors;

import de.adesso.fischereiregister.core.commands.OSCreateRegularLicenseCommand;
import de.adesso.fischereiregister.core.events.AxonEvent;
import de.adesso.fischereiregister.core.events.JurisdictionMovedEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseCreatedEvent;
import de.adesso.fischereiregister.core.interceptables.InterceptableDocumentNumberService;
import de.adesso.fischereiregister.core.interceptables.InterceptableFishingLicenseNumberService;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import de.adesso.fischereiregister.core.model.QualificationsProof;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.consent.ConsentInfo;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.IdentificationDocumentType;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.model.type.QualificationsProofType;
import de.adesso.fischereiregister.core.model.utils.FishingLicenseFactory;
import de.adesso.fischereiregister.core.model.utils.IdentificationDocumentFactory;
import de.adesso.fischereiregister.core.model.utils.IdentificationDocumentFactoryImpl;
import de.adesso.fischereiregister.core.ports.DocumentNumberService;
import de.adesso.fischereiregister.core.ports.FishingLicenseNumberService;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class OSCreateRegularLicenseCommandProcessorTest {
    private OSCreateRegularLicenseCommandProcessor processor;

    private UUID registerEntryId;

    @BeforeEach
    public void setup() throws Exception {
        FishingLicenseNumberService numberService = new InterceptableFishingLicenseNumberService();
        DocumentNumberService documentNumberService = new InterceptableDocumentNumberService();
        IdentificationDocumentFactory identificationDocumentFactory = new IdentificationDocumentFactoryImpl(documentNumberService);
        FishingLicenseFactory fishingLicenseFactory = new FishingLicenseFactory(numberService);

        processor = new OSCreateRegularLicenseCommandProcessor(identificationDocumentFactory, fishingLicenseFactory);

        registerEntryId = UUID.randomUUID();
    }

    @Test
    @DisplayName("process succeeds when jurisdiction is already matching")
    public void testProcessSuccessfulWhenJurisdictionMatching() {
        // GIVEN
        final RegisterEntry registerEntry = getInitialRegisterEntry();
        final OSCreateRegularLicenseCommand command = buildCommand();

        // WHEN
        List<AxonEvent> events = processor.process(command, registerEntry);

        // THEN
        assertEquals(1, events.size());

        final AxonEvent event = events.getFirst();
        assertInstanceOf(RegularLicenseCreatedEvent.class, event);

        final RegularLicenseCreatedEvent createdEvent = (RegularLicenseCreatedEvent) event;

        assertEquals(registerEntryId, createdEvent.registerId());
        assertNotNull(createdEvent.fishingLicense());
        assertEquals("SH", createdEvent.fishingLicense().getIssuingFederalState().toString());

        assertEquals(2, createdEvent.identificationDocuments().size());

        final Optional<IdentificationDocument> pdfDocument = createdEvent.identificationDocuments().stream()
                .filter(document -> document.getType() == IdentificationDocumentType.PDF)
                .findFirst();
        assertTrue(pdfDocument.isPresent());
        assertNotNull(pdfDocument.get().getFishingLicense());

        final Optional<IdentificationDocument> cardDocument = createdEvent.identificationDocuments().stream()
                .filter(document -> document.getType() == IdentificationDocumentType.CARD)
                .findFirst();
        assertTrue(cardDocument.isPresent());
        assertNotNull(cardDocument.get().getFishingLicense());
    }

    @Test
    @DisplayName("process returns an addition move jurisdiction event, when jurisdiction was not matching")
    public void testProcessSuccessfulWhenJurisdictionNotMatching() {
        // GIVEN
        final OSCreateRegularLicenseCommand command = buildCommand();
        RegisterEntry registerEntry = getInitialRegisterEntry();

        registerEntry.getJurisdiction().setFederalState("HH");

        // WHEN
        List<AxonEvent> events = processor.process(command, registerEntry);

        // THEN
        assertEquals(2, events.size());

        final AxonEvent event1 = events.get(0);
        assertInstanceOf(JurisdictionMovedEvent.class, event1);

        final AxonEvent event2 = events.get(1);
        assertInstanceOf(RegularLicenseCreatedEvent.class, event2);

        final JurisdictionMovedEvent movedEvent = (JurisdictionMovedEvent) event1;

        assertEquals(registerEntryId, movedEvent.registerId());
        assertEquals(0, movedEvent.identificationDocuments().size());
        assertEquals(0, movedEvent.taxes().size());
        assertEquals("HH", movedEvent.previousJurisdiction().getFederalState());
        assertEquals("SH", movedEvent.newJurisdiction().getFederalState());
        assertTrue(movedEvent.consentInfo().getProofOfMoveVerified());
    }

    @Test
    @DisplayName("process fails when a regular license already exists")
    public void testProcessFailsWhenRegularLicenseAlreadyExists() {
        // GIVEN
        final OSCreateRegularLicenseCommand command = buildCommand();
        RegisterEntry registerEntry = getInitialRegisterEntry();

        FishingLicense license = new FishingLicense();
        license.setIssuingFederalState(FederalState.SH);
        license.setType(LicenseType.REGULAR);
        registerEntry.getFishingLicenses().add(license);

        // WHEN
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> processor.process(command, registerEntry));

        // THEN
        assertEquals(exception.getMessage(), "A regular license was found, it can not be added a second one for register Id: " + registerEntryId);
    }

    @Test
    @DisplayName("process fails when no qualification proof exists")
    public void testProcessFailsWhenNoQualificationProofExists() {
// GIVEN
        final OSCreateRegularLicenseCommand command = buildCommand();
        RegisterEntry registerEntry = getInitialRegisterEntry();

        registerEntry.getQualificationsProofs().clear();

        // WHEN
        IllegalStateException exception = assertThrows(IllegalStateException.class, () -> processor.process(command, registerEntry));

        // THEN
        assertEquals(exception.getMessage(), "No qualifications proof of type fishing certificate found for register Id: " + registerEntryId);
    }

    @Test
    @DisplayName("process fails when it was submitted by third party and person data is not matching")
    public void testProcessFailsWhenThirdPartyAndPersonDataNotMatching() {
        final OSCreateRegularLicenseCommand command = buildCommand();
        RegisterEntry registerEntry = getInitialRegisterEntry();

        registerEntry.getPerson().setLastname("Different");
        command.consentInfo().setSubmittedByThirdParty(true);

        // WHEN
        IllegalStateException exception = assertThrows(IllegalStateException.class, () -> processor.process(command, registerEntry));

        // THEN
        assertEquals(exception.getMessage(), "For a third party request person data has to match, for: " + registerEntryId);
    }

    @Test
    @DisplayName("process succeeds when it was submitted by third party and person data is matching")
    public void testProcessSuccessfulWhenThirdPartyAndPersonDataMatching() {
        final OSCreateRegularLicenseCommand command = buildCommand();
        RegisterEntry registerEntry = getInitialRegisterEntry();

        command.consentInfo().setSubmittedByThirdParty(true);

        // WHEN
        // THEN
        assertDoesNotThrow(() -> processor.process(command, registerEntry));
    }

    private OSCreateRegularLicenseCommand buildCommand() {
        ConsentInfo consentInfo = new ConsentInfo();
        consentInfo.setGdprAccepted(true);
        consentInfo.setSubmittedByThirdParty(false);
        consentInfo.setSelfDisclosureAccepted(true);

        return new OSCreateRegularLicenseCommand(
                registerEntryId,
                "salt",
                "validCode",
                DomainTestData.createPerson(),
                consentInfo,
                Collections.emptyList(),
                Collections.emptyList(),
                "account-id",
                "SH",
                "transactionId",
                "inboxReference"
        );
    }

    private RegisterEntry getInitialRegisterEntry() {
        RegisterEntry registerEntry = new RegisterEntry();
        registerEntry.setRegisterId(registerEntryId);

        QualificationsProof qualificationsProof = new QualificationsProof();
        qualificationsProof.setType(QualificationsProofType.CERTIFICATE);
        qualificationsProof.setFishingCertificateId("validCode");
        registerEntry.getQualificationsProofs().add(qualificationsProof);

        Jurisdiction jurisdiction = new Jurisdiction();
        jurisdiction.setFederalState("SH");
        registerEntry.setJurisdiction(jurisdiction);

        // Register vacation type license to implicitly check that it has no effect on license creation
        FishingLicense fishingLicense = new FishingLicense();
        fishingLicense.setNumber("12345");
        fishingLicense.setIssuingFederalState(FederalState.SH);
        fishingLicense.setType(LicenseType.VACATION);

        registerEntry.setPerson(DomainTestData.createPerson());

        return registerEntry;
    }
}
