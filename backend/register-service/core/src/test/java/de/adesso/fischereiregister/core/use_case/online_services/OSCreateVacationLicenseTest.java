package de.adesso.fischereiregister.core.use_case.online_services;

import de.adesso.fischereiregister.core.commands.OSCreateVacationLicenseCommand;
import de.adesso.fischereiregister.core.events.PersonCreatedEvent;
import de.adesso.fischereiregister.core.model.Fee;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.ValidityPeriod;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.model.type.PaymentType;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.core.testutils.AxonFixture;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import org.axonframework.test.aggregate.AggregateTestFixture;
import org.axonframework.test.aggregate.TestExecutor;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

public class OSCreateVacationLicenseTest extends OSTest {
    @ExtendWith(AxonFixture.class)
    @Test
    @DisplayName("Vacation license command is handled successfully when a register entry already exists.")
    void testVacationLicenseAddedToExistingRegisterEntry(AggregateTestFixture<RegisterEntry> fixture) {
        testVacationLicenseCreation(false, fixture);

    }

    @ExtendWith(AxonFixture.class)
    @Test
    @DisplayName("Vacation license command is handled successfully when no register entry exists, i.e. a new entry is created.")
    void testVacationLicenseAddedWhenNoRegisterEntryExists(AggregateTestFixture<RegisterEntry> fixture) {
        testVacationLicenseCreation(true, fixture);

    }

    @Test
    @ExtendWith(AxonFixture.class)
    @DisplayName("Vacation license command does not change existing address but does change person data.")
    void testVacationLicenseCommandDoesNotChangeExistingAddress(AggregateTestFixture<RegisterEntry> fixture) {
        final UUID registerEntryId = UUID.randomUUID();

        final Person personBefore = DomainTestData.createPersonWithAddress();
        Person personAfter = DomainTestData.createPerson();
        personAfter.setBirthname("New Birthname");
        personAfter.setFirstname("New Firstname");
        personAfter.setLastname("New Lastname");

        final LocalDate validFrom = LocalDate.of(2021, 4, 1);
        final LocalDate validTo = LocalDate.of(2021, 4, 5);

        Fee fee = new Fee();
        fee.setValidFrom(validFrom);
        fee.setValidTo(validTo);
        fee.setFederalState(FederalState.BE.toString());
        fee.setPaymentInfo(DomainTestData.createPaymentInfo(27.0, PaymentType.ONLINE));

        ValidityPeriod validityPeriod = new ValidityPeriod();
        validityPeriod.setValidFrom(validFrom);
        validityPeriod.setValidTo(validTo);

        String federalState = FederalState.BE.toString();

        String inboxReference = "inboxReference";
        String serviceAccountId = "serviceAccountId";
        String transactionId = "transactionId";
        fixture.given(
                        new PersonCreatedEvent(
                                registerEntryId,
                                personBefore,
                                DomainTestData.createAnalogTaxesWithOneTax(),
                                List.of(),
                                "salt",
                                List.of(),
                                DomainTestData.createTaxConsentInfo(),
                                null,
                                null,
                                null,
                                null,
                                federalState,
                                SubmissionType.ANALOG
                        )
                ).when(
                        new OSCreateVacationLicenseCommand(
                                registerEntryId,
                                personAfter,
                                DomainTestData.createConsentInfo(),
                                fee,
                                "salt",
                                FederalState.BE, // Implicetely test different federal state
                                validityPeriod,
                                transactionId,
                                serviceAccountId,
                                inboxReference
                        )
                )
                .expectState(registerEntry -> {
                    final Person actualPerson = registerEntry.getPerson();

                    // Address did not change
                    assertEquals(personBefore.getAddress(), actualPerson.getAddress());

                    // Person data changed
                    actualPerson.setAddress(null);
                    personAfter.setAddress(null);
                    assertEquals(personAfter, actualPerson);

                    checkOnlineServiceDataCorrectlySet(registerEntry, inboxReference, serviceAccountId, transactionId);
                });
    }

    private void testVacationLicenseCreation(boolean createNew, AggregateTestFixture<RegisterEntry> fixture) {
        final UUID registerEntryId = UUID.randomUUID();

        final LocalDate validFrom = LocalDate.of(2021, 4, 1);
        final LocalDate validTo = LocalDate.of(2021, 4, 6);

        Fee fee = new Fee();
        fee.setValidFrom(validFrom);
        fee.setValidTo(validTo);
        fee.setFederalState(FederalState.BE.toString());
        fee.setPaymentInfo(DomainTestData.createPaymentInfo(27.0, PaymentType.ONLINE));

        ValidityPeriod validityPeriod = new ValidityPeriod();
        validityPeriod.setValidFrom(validFrom);
        validityPeriod.setValidTo(validTo);
        String federalState = FederalState.BE.toString();

        TestExecutor<RegisterEntry> executor = fixture;
        String inboxReference = "inboxReference";
        if (!createNew) {
            executor = fixture.given(
                    new PersonCreatedEvent(
                            registerEntryId,
                            DomainTestData.createPerson(),
                            List.of(),
                            List.of(),
                            "salt",
                            List.of(),
                            DomainTestData.createTaxConsentInfo(),
                            null,
                            null,
                            null,
                            null,
                            federalState,
                            SubmissionType.ONLINE
                    )
            );
        }

        String transactionId = "transactionId";
        String serviceAccountId = "serviceAccountId";
        executor.when(
                new OSCreateVacationLicenseCommand(
                        registerEntryId,
                        DomainTestData.createPerson(),
                        DomainTestData.createConsentInfo(),
                        fee,
                        "salt",
                        FederalState.BE, // Implicetely test different federal state
                        validityPeriod,
                        transactionId,
                        serviceAccountId,
                        inboxReference
                )
        ).expectState(registerEntry -> {
            // Check fee
            assertEquals(1, registerEntry.getFees().size());

            // Check taxes
            assertEquals(1, registerEntry.getTaxes().size());

            // Check license
            assertEquals(1, registerEntry.getFishingLicenses().size());
            FishingLicense license = registerEntry.getFishingLicenses().get(0);
            // we expect only one validity period because the vaction license has one validity period after it's creation
            // after an extension there however will be two validity periods (or maybe even more than two validity periods in future versions of the application)
            ValidityPeriod validityPeriodFromLicense = license.getValidityPeriods().get(0);
            assertEquals(FederalState.BE, license.getIssuingFederalState());
            assertEquals(validFrom, validityPeriodFromLicense.getValidFrom());
            assertEquals(validTo, validityPeriodFromLicense.getValidTo());
            assertEquals(LicenseType.VACATION, license.getType());

            // Created 2 documents
            assertEquals(2, registerEntry.getIdentificationDocuments().size());
            IdentificationDocument licenseDocument = registerEntry.getIdentificationDocuments().stream()
                    .filter(document -> document.getFishingLicense() != null)
                    .findAny()
                    .orElse(null);
            assertNotNull(licenseDocument);
            assertEquals(license.getNumber(), licenseDocument.getFishingLicense().getNumber());

            IdentificationDocument taxDocument = registerEntry.getIdentificationDocuments().stream()
                    .filter(document -> document.getTax() != null)
                    .findAny()
                    .orElse(null);
            assertNotNull(taxDocument);

            assertNull(registerEntry.getJurisdiction());

            checkOnlineServiceDataCorrectlySet(registerEntry, inboxReference, serviceAccountId, transactionId);
        });

    }

}
