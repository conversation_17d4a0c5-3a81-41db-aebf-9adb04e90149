package de.adesso.fischereiregister.core.use_case;

import de.adesso.fischereiregister.core.commands.ExtendLicenseCommand;
import de.adesso.fischereiregister.core.events.LicenseExtendedEvent;
import de.adesso.fischereiregister.core.events.VacationLicenseCreatedEvent;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.ValidityPeriod;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.core.model.user.UserRole;
import de.adesso.fischereiregister.core.testutils.AxonFixture;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import org.axonframework.test.aggregate.AggregateTestFixture;
import org.axonframework.test.matchers.Matchers;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class ExtendFishingLicenseTest {
    @Test
    @ExtendWith(AxonFixture.class)
    @DisplayName("Extend license command does not change existing address but does change person data.")
    void testExtendLicenseCommand_DoesNotChangeAddress(AggregateTestFixture<RegisterEntry> fixture) {
        final UUID registerEntryId = UUID.randomUUID();

        final Person personBefore = DomainTestData.createPersonWithAddress();
        Person personAfter = DomainTestData.createPerson();
        personAfter.setBirthname("New Birthname");
        personAfter.setFirstname("New Firstname");
        personAfter.setLastname("New Lastname");

        ValidityPeriod validityPeriod = createValidityPeriod();

        final FishingLicense vacationLicense = createVacationLicense();

        fixture.given(
                        new VacationLicenseCreatedEvent(
                                registerEntryId,
                                personBefore,
                                "salt",
                                DomainTestData.createConsentInfo(),
                                DomainTestData.createAnalogFeesWithOneFee(),
                                DomainTestData.createAnalogTaxesWithOneTax(),
                                List.of(),
                                vacationLicense,
                                "office",
                                null,
                                null,
                                null,
                                SubmissionType.ANALOG
                        )
                ).when(
                        new ExtendLicenseCommand(
                                registerEntryId,
                                vacationLicense.getNumber(),
                                "salt",
                                personAfter,
                                DomainTestData.createAnalogFeesWithOneFee(),
                                DomainTestData.createAnalogTaxesWithOneTax(),
                                validityPeriod,
                                DomainTestData.createConsentInfo(),
                                DomainTestData.createUserDetails(UserRole.OFFICIAL)
                        )
                )
                .expectSuccessfulHandlerExecution().expectEventsMatching(Matchers.exactSequenceOf(Matchers.messageWithPayload(Matchers.matches(payload ->
                        payload.getClass().isAssignableFrom(LicenseExtendedEvent.class)
                ))))
                .expectState(registerEntry -> {
                    final Person actualPerson = registerEntry.getPerson();

                    // Address did not change
                    assertEquals(personBefore.getAddress(), actualPerson.getAddress());

                    // Person data changed
                    actualPerson.setAddress(null);
                    personAfter.setAddress(null);
                    assertEquals(personAfter, actualPerson);
                });
    }

    @Test
    @ExtendWith(AxonFixture.class)
    @DisplayName("Extend license command is successful when license is found.")
    void testExtendLicenseCommand_successfulIfLicenseFound(AggregateTestFixture<RegisterEntry> fixture) {
        final UUID registerEntryId = UUID.randomUUID();

        final Person person = DomainTestData.createPersonWithAddress();
        ValidityPeriod validityPeriod = createValidityPeriod();

        final FishingLicense vacationLicense = createVacationLicense();

        fixture.given(
                        new VacationLicenseCreatedEvent(
                                registerEntryId,
                                person,
                                "salt",
                                DomainTestData.createConsentInfo(),
                                DomainTestData.createAnalogFeesWithOneFee(),
                                DomainTestData.createAnalogTaxesWithOneTax(),
                                List.of(),
                                vacationLicense,
                                "office",
                                null,
                                null,
                                null,
                                SubmissionType.ANALOG
                        )
                ).when(
                        new ExtendLicenseCommand(
                                registerEntryId,
                                vacationLicense.getNumber(),
                                "salt",
                                person,
                                DomainTestData.createAnalogFeesWithOneFee(),
                                DomainTestData.createAnalogTaxesWithOneTax(),
                                validityPeriod,
                                DomainTestData.createConsentInfo(),
                                DomainTestData.createUserDetails(UserRole.OFFICIAL)
                        )
                )
                .expectSuccessfulHandlerExecution().expectEventsMatching(Matchers.exactSequenceOf(Matchers.messageWithPayload(Matchers.matches(payload ->
                        payload.getClass().isAssignableFrom(LicenseExtendedEvent.class)
                ))))
                .expectState(registerEntry -> {
                    assertEquals(1, registerEntry.getFishingLicenses().size());
                    final FishingLicense fishingLicense = registerEntry.getFishingLicenses().stream().findFirst().orElse(null);

                    assertNotNull(fishingLicense);
                    assertEquals(vacationLicense.getNumber(), fishingLicense.getNumber());

                    assertTrue(fishingLicense.getValidityPeriods().contains(validityPeriod));

                    assertEquals(2, registerEntry.getFees().size());
                    assertEquals(2, registerEntry.getTaxes().size());

                    // Check new license document is generated correctly
                    assertEquals(2, registerEntry.getIdentificationDocuments().size());
                    IdentificationDocument licensePdfDocument = registerEntry.getIdentificationDocuments().stream()
                            .filter(document -> document.getFishingLicense() != null)
                            .findAny()
                            .orElse(null);

                    assertNotNull(licensePdfDocument);
                    assertEquals(fishingLicense, licensePdfDocument.getFishingLicense());
                    assertEquals(validityPeriod.getValidFrom(), licensePdfDocument.getValidFrom());
                    assertEquals(validityPeriod.getValidTo(), licensePdfDocument.getValidTo());
                });
    }


    private static ValidityPeriod createValidityPeriod() {
        final LocalDate validFrom = LocalDate.of(2021, 5, 1);
        final LocalDate validTo = LocalDate.of(2021, 5, 28);

        ValidityPeriod validityPeriod = new ValidityPeriod();
        validityPeriod.setValidFrom(validFrom);
        validityPeriod.setValidTo(validTo);
        return validityPeriod;
    }

    private FishingLicense createVacationLicense() {
        final FishingLicense fishingLicense = new FishingLicense();
        fishingLicense.setType(LicenseType.VACATION);

        ValidityPeriod validityPeriod = DomainTestData.createValidityPeriod(LocalDate.of(2021, 4, 1),
                LocalDate.of(2021, 4, 28));

        fishingLicense.getValidityPeriods().add(validityPeriod);
        fishingLicense.setIssuingFederalState(FederalState.SH);

        fishingLicense.setNumber("number");

        return fishingLicense;
    }

}
