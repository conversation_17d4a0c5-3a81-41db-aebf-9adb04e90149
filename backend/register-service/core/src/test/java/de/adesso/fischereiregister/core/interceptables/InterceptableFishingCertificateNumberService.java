package de.adesso.fischereiregister.core.interceptables;

import de.adesso.fischereiregister.core.ports.FishingCertificateNumberService;

import java.util.UUID;

public class InterceptableFishingCertificateNumberService  implements FishingCertificateNumberService, ResetableAdapter {
    @Override
    public void reset() {

    }
    @Override
    public String createNewAvailableFishingCertificateNumber(UUID registerEntryId) {
        return UUID.randomUUID().toString();
    }
}
