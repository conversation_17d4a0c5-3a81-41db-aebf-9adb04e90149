package de.adesso.fischereiregister.core.validation.helper;

import de.adesso.fischereiregister.core.model.consent.ConsentInfo;
import de.adesso.fischereiregister.core.model.consent.JurisdictionConsentInfo;
import de.adesso.fischereiregister.core.validation.ValidationResult;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

class ConsentInfoValidatorHelperTest {

    @Test
    void testValidate_NullConsentInfo_ShouldAddRequiredError() {
        ValidationResult validationResult = new ValidationResult();

        ConsentInfoValidatorHelper.validate(null, validationResult);

        assertEquals(1, validationResult.getErrorNotes().size());
        assertTrue(validationResult.getErrorNotes().contains("consentInfo is required"));
    }

    @Test
    void testValidate_ConsentInfoWithNullFields_ShouldAddFieldErrors() {
        ConsentInfo consentInfo = new ConsentInfo();
        ValidationResult validationResult = new ValidationResult();

        ConsentInfoValidatorHelper.validate(consentInfo, validationResult);

        assertEquals(5, validationResult.getErrorNotes().size());
        assertTrue(validationResult.getErrorNotes().contains("submittedByThirdParty is required"));
        assertTrue(validationResult.getErrorNotes().contains("gdprAccepted is required"));
        assertTrue(validationResult.getErrorNotes().contains("gdprAccepted must be set to true"));
        assertTrue(validationResult.getErrorNotes().contains("selfDisclosureAccepted is required"));
        assertTrue(validationResult.getErrorNotes().contains("selfDisclosureAccepted must be set to true"));
    }

    @Test
    void testValidate_JurisdictionConsentInfoWithNullFields_ShouldAddFieldErrors() {
        JurisdictionConsentInfo jurisdictionConsentInfo = new JurisdictionConsentInfo();
        ValidationResult validationResult = new ValidationResult();

        ConsentInfoValidatorHelper.validate(jurisdictionConsentInfo, validationResult);

        assertEquals(7, validationResult.getErrorNotes().size());
        assertTrue(validationResult.getErrorNotes().contains("submittedByThirdParty is required"));
        assertTrue(validationResult.getErrorNotes().contains("gdprAccepted is required"));
        assertTrue(validationResult.getErrorNotes().contains("gdprAccepted must be set to true"));
        assertTrue(validationResult.getErrorNotes().contains("selfDisclosureAccepted is required"));
        assertTrue(validationResult.getErrorNotes().contains("selfDisclosureAccepted must be set to true"));
        assertTrue(validationResult.getErrorNotes().contains("proofOfMoveVerified is required"));
        assertTrue(validationResult.getErrorNotes().contains("proofOfMoveVerified must be set to true"));
    }

    @Test
    void testValidate_ConsentInfoWithValidFields_ShouldNotAddErrors() {
        ConsentInfo consentInfo = new ConsentInfo();
        consentInfo.setGdprAccepted(true);
        consentInfo.setSelfDisclosureAccepted(true);
        consentInfo.setSubmittedByThirdParty(true);
        ValidationResult validationResult = new ValidationResult();

        ConsentInfoValidatorHelper.validate(consentInfo, validationResult);

        assertFalse(validationResult.hasErrors());
    }
}