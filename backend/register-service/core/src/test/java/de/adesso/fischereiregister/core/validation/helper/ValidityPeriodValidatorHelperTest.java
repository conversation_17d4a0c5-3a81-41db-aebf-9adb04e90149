package de.adesso.fischereiregister.core.validation.helper;

import de.adesso.fischereiregister.core.model.ValidityPeriod;
import de.adesso.fischereiregister.core.validation.ValidationResult;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class ValidityPeriodValidatorHelperTest {
    @Test
    @DisplayName("ValidityPeriodValidatorHelperTest.validate should add error when validity Period is null")
    void testValidate_UserDetailsIsNull() {
        // Given
        ValidationResult validationResult = new ValidationResult();

        // When
        ValidityPeriodValidatorHelper.validate(null, validationResult);

        // Then
        assertTrue(validationResult.hasErrors());
        assertEquals(1, validationResult.getErrorNotes().size());
        assertTrue(validationResult.getErrorNotes().contains("Validity Period is required"));
    }


    @Test
    @DisplayName("ValidityPeriodValidatorHelperTest.validate should be successful when validFrom and validTo are set")
    void testValidate_allSet() {
        // Given
        ValidationResult validationResult = new ValidationResult();

        ValidityPeriod period = new ValidityPeriod();
        period.setValidFrom(LocalDate.of(2021, 4, 1));
        period.setValidTo(LocalDate.of(2021, 4, 2));

        // When
        ValidityPeriodValidatorHelper.validate(period, validationResult);

        // Then
        assertFalse(validationResult.hasErrors());
    }

    @Test
    @DisplayName("ValidityPeriodValidatorHelperTest.validate should add error when validFrom and validTo is unset")
    void testValidate_validFromNull() {
        // Given
        ValidationResult validationResult = new ValidationResult();

        ValidityPeriod period = new ValidityPeriod();

        // When
        ValidityPeriodValidatorHelper.validate(period, validationResult);

        // Then
        assertTrue(validationResult.hasErrors());
        assertEquals(2, validationResult.getErrorNotes().size());
    }

    @Test
    @DisplayName("ValidityPeriodValidatorHelperTest.validate should add error when validFrom is after validTo")
    void testValidate_validFromBeforeValidTo() {
        // Given
        ValidationResult validationResult = new ValidationResult();

        ValidityPeriod period = new ValidityPeriod();
        period.setValidFrom(LocalDate.of(2021, 4, 2));
        period.setValidTo(LocalDate.of(2021, 4, 1));

        // When
        ValidityPeriodValidatorHelper.validate(period, validationResult);

        // Then
        assertTrue(validationResult.hasErrors());
    }
}
