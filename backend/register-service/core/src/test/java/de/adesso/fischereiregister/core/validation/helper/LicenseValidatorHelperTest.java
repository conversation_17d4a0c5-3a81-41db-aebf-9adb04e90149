package de.adesso.fischereiregister.core.validation.helper;

import de.adesso.fischereiregister.core.exceptions.LicenseNotExtendableException;
import de.adesso.fischereiregister.core.exceptions.LicenseNotFoundException;
import de.adesso.fischereiregister.core.exceptions.RulesProcessingException;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.ValidityPeriod;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.model.type.PaymentType;
import de.adesso.fischereiregister.core.ports.TenantInformationPort;
import de.adesso.fischereiregister.core.ports.contracts.LicenseInformation;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import de.adesso.fischereiregister.core.validation.ValidationResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class LicenseValidatorHelperTest {

    private LicenseValidatorHelper licenseValidatorHelper;

    private TenantInformationPort tenantInformationPort;

    @BeforeEach
    void setUp() {
        tenantInformationPort = mock(TenantInformationPort.class);
        licenseValidatorHelper = new LicenseValidatorHelper(tenantInformationPort);
    }

    @Test
    void testValidateLicenseExtensionWhenTenantInformationForbids() throws RulesProcessingException {
        // GIVEN
        when(tenantInformationPort.getLicenseInformation(any(), any(), any()))
                .thenReturn(new LicenseInformation(BigDecimal.ZERO, LocalDate.MAX, false, true));

        ValidationResult validationResult = new ValidationResult();
        final RegisterEntry registerEntry = createRegisterEntry();

        // WHEN
        // THEN
        assertThrows(LicenseNotExtendableException.class, () -> licenseValidatorHelper.validateLicenseIsExtendable(registerEntry, "1", createNewValidityPeriod(), FederalState.SH, PaymentType.ONLINE, validationResult));
    }

    @Test
    void testValidateLicenseExtensionWhenLicenseNotFound() throws RulesProcessingException {
        // GIVEN
        ValidationResult validationResult = new ValidationResult();
        final RegisterEntry registerEntry = createRegisterEntry();

        // WHEN
        assertThrows(
                LicenseNotFoundException.class,
                () -> licenseValidatorHelper.validateLicenseIsExtendable(
                        registerEntry,
                        "2",
                        createNewValidityPeriod(),
                        FederalState.SH,
                        PaymentType.ONLINE,
                        validationResult));
    }

    @Test
    void testValidateLicenseExtensionWhenLicenseAlreadyExtended() throws RulesProcessingException {
        // GIVEN
        when(tenantInformationPort.getLicenseInformation(any(), any(), any()))
                .thenReturn(new LicenseInformation(BigDecimal.ZERO, LocalDate.MAX, true, true));

        ValidationResult validationResult = new ValidationResult();
        final RegisterEntry registerEntry = createRegisterEntry();
        registerEntry.getFishingLicenses().getFirst().getValidityPeriods().add(new ValidityPeriod());

        // WHEN
        licenseValidatorHelper.validateLicenseIsExtendable(registerEntry, "1", createNewValidityPeriod(), FederalState.SH, PaymentType.ONLINE, validationResult);

        // THEN
        assertTrue(validationResult.hasErrors());
        assertEquals(1, validationResult.getErrorNotes().size());
    }

    @Test
    void testValidateLicenseExtensionWhenLicenseFederalStateMismatch() throws RulesProcessingException {
        // GIVEN
        when(tenantInformationPort.getLicenseInformation(any(), any(), any()))
                .thenReturn(new LicenseInformation(BigDecimal.ZERO, LocalDate.MAX, true, true));

        ValidationResult validationResult = new ValidationResult();
        final RegisterEntry registerEntry = createRegisterEntry();

        // WHEN
        licenseValidatorHelper.validateLicenseIsExtendable(registerEntry, "1", createNewValidityPeriod(), FederalState.HH, PaymentType.ONLINE, validationResult);

        // THEN
        assertTrue(validationResult.hasErrors());
        assertEquals(1, validationResult.getErrorNotes().size());
    }

    @Test
    void testValidateLicenseExtensionWhenLicenseIsExtendable() throws RulesProcessingException {
        // GIVEN
        when(tenantInformationPort.getLicenseInformation(any(), any(), any()))
                .thenReturn(new LicenseInformation(BigDecimal.ZERO, LocalDate.MAX, true, true));

        ValidationResult validationResult = new ValidationResult();
        final RegisterEntry registerEntry = createRegisterEntry();

        // WHEN
        licenseValidatorHelper.validateLicenseIsExtendable(registerEntry, "1", createNewValidityPeriod(), FederalState.SH, PaymentType.ONLINE, validationResult);

        // THEN
        assertFalse(validationResult.hasErrors());
    }

    @Test
    void testValidateLicenseExtensionWhenValidityNotSameYear() throws RulesProcessingException {
        // GIVEN
        when(tenantInformationPort.getLicenseInformation(any(), any(), any()))
                .thenReturn(new LicenseInformation(BigDecimal.ZERO, LocalDate.MAX, true, true));

        ValidationResult validationResult = new ValidationResult();
        final RegisterEntry registerEntry = createRegisterEntry();

        ValidityPeriod newPeriod = new ValidityPeriod();
        newPeriod.setValidFrom(LocalDate.of(2026, 1, 1));
        newPeriod.setValidTo(LocalDate.of(2026, 1, 31));

        // WHEN
        licenseValidatorHelper.validateLicenseIsExtendable(registerEntry, "1", newPeriod, FederalState.SH, PaymentType.ONLINE, validationResult);

        // THEN
        assertTrue(validationResult.hasErrors());
        assertEquals(1, validationResult.getErrorNotes().size());
    }

    @Test
    void testValidateLicenseExtensionWhenValidityOverlaps() throws RulesProcessingException {
        // GIVEN
        when(tenantInformationPort.getLicenseInformation(any(), any(), any()))
                .thenReturn(new LicenseInformation(BigDecimal.ZERO, LocalDate.MAX, true, true));

        ValidationResult validationResult = new ValidationResult();
        final RegisterEntry registerEntry = createRegisterEntry();

        ValidityPeriod newPeriod = new ValidityPeriod();
        newPeriod.setValidFrom(LocalDate.of(2025, 1, 15));
        newPeriod.setValidTo(LocalDate.of(2025, 2, 15));

        // WHEN
        licenseValidatorHelper.validateLicenseIsExtendable(registerEntry, "1", newPeriod, FederalState.SH, PaymentType.ONLINE, validationResult);

        // THEN
        assertTrue(validationResult.hasErrors());
        assertEquals(1, validationResult.getErrorNotes().size());
    }

    private RegisterEntry createRegisterEntry() {
        RegisterEntry registerEntry = new RegisterEntry();

        FishingLicense fishingLicense = new FishingLicense();
        fishingLicense.setIssuingFederalState(FederalState.SH);
        fishingLicense.setNumber("1");
        fishingLicense.setType(LicenseType.VACATION);

        fishingLicense.getValidityPeriods().add(DomainTestData.createValidityPeriod(LocalDate.of(2025, 1, 1),
                LocalDate.of(2025, 1, 31)));

        registerEntry.getFishingLicenses().add(fishingLicense);

        return registerEntry;
    }

    private ValidityPeriod createNewValidityPeriod() {
        ValidityPeriod validityPeriod = new ValidityPeriod();
        validityPeriod.setValidFrom(LocalDate.of(2025, 2, 1));
        validityPeriod.setValidTo(LocalDate.of(2025, 2, 28));
        return validityPeriod;
    }
}
