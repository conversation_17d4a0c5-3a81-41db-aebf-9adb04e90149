package de.adesso.fischereiregister.core.validation;

import de.adesso.fischereiregister.core.commands.CreatePersonCommand;
import de.adesso.fischereiregister.core.exceptions.ClientInputValidationException;
import de.adesso.fischereiregister.core.exceptions.SystemConfigValidationException;
import de.adesso.fischereiregister.core.interceptables.InterceptableCountryService;
import de.adesso.fischereiregister.core.interceptables.InterceptableTenantRulesValidationPort;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.user.UserDetails;
import de.adesso.fischereiregister.core.model.user.UserRole;
import de.adesso.fischereiregister.core.ports.CountryService;
import de.adesso.fischereiregister.core.ports.TenantRulesValidationPort;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class CreatePersonCommandValidatorTest {
    private CreatePersonCommandValidator createPersonCommandValidator;

    @BeforeEach
    public void setUp() {
        final CountryService countryService = new InterceptableCountryService();
        final TenantRulesValidationPort tenantRulesValidationPort = new InterceptableTenantRulesValidationPort();
        createPersonCommandValidator = new CreatePersonCommandValidator(countryService, tenantRulesValidationPort);
    }

    @Test
    @DisplayName("createPersonCommandValidator.validateOrThrow does not throw exception when a minimal valid command is given")
    public void testValidPersonData() {
        // GIVEN
        Person person = DomainTestData.createPerson();
        person.setAddress(null);

        CreatePersonCommand command = new CreatePersonCommand(
                UUID.randomUUID(),
                DomainTestData.createPerson(),
                List.of(DomainTestData.createAnalogTax()),
                List.of(),
                "anySalt",
                DomainTestData.createTaxConsentInfo(),
                DomainTestData.createUserDetails(UserRole.OFFICIAL)
        );

        // WHEN
        // THEN
        assertDoesNotThrow(() -> createPersonCommandValidator.validateOrThrow(command));
    }

    @Test
    @DisplayName("createPersonCommandValidator.validateOrThrow should throw a ClientInputValidationException for invalid data")
    public void testValidateOrThrow_invalidClientInputCommand() {
        // GIVEN
        CreatePersonCommand invalidCommand = new CreatePersonCommand(
                UUID.randomUUID(),
                null, // Invalid: Person is null
                Collections.emptyList(),
                Collections.emptyList(),
                "anySalt",
                null,
                DomainTestData.createUserDetails(UserRole.OFFICIAL)
        );

        // WHEN
        ClientInputValidationException exception = assertThrows(ClientInputValidationException.class,
                () -> createPersonCommandValidator.validateOrThrow(invalidCommand));

        // THEN
        ValidationResult result = exception.getValidationResult();
        assertTrue(result.hasErrors());
        assertEquals(3, result.getErrorNotes().size());
        assertTrue(result.getErrorNotes().contains("Person Details is required"));
        assertTrue(result.getErrorNotes().contains("consentInfo is required"));
        assertTrue(result.getErrorNotes().contains("Taxes are required"));
    }

    @Test
    @DisplayName("createPersonCommandValidator.validateOrThrow should throw a SystemConfigValidationException when user details are invalid")
    public void testValidateOrThrow_invalidSystemConfigCommand() {
        // GIVEN
        CreatePersonCommand invalidCommand = new CreatePersonCommand(
                UUID.randomUUID(),
                DomainTestData.createPerson(),
                List.of(DomainTestData.createAnalogTax()),
                Collections.emptyList(),
                null,
                DomainTestData.createTaxConsentInfo(),
                null // Invalid: User details are null
        );

        // WHEN
        SystemConfigValidationException exception = assertThrows(SystemConfigValidationException.class,
                () -> createPersonCommandValidator.validateOrThrow(invalidCommand));

        // THEN
        ValidationResult result = exception.getValidationResult();
        assertTrue(result.hasErrors());
        assertEquals(2, result.getErrorNotes().size());
        assertTrue(result.getErrorNotes().contains("User Details is required"));
        assertTrue(result.getErrorNotes().contains("Salt is required"));
    }

    @Test
    @DisplayName("createPersonCommandValidator.validateOrThrow should throw a SystemConfigValidationException  when user details are present but invalid")
    public void testValidateOrThrow_missingOffice() {
        // GIVEN
        // Create user details with null office
        UserDetails userDetailsWithoutOffice = new UserDetails(null,null,null,null,null, Set.of(UserRole.OFFICIAL));

        CreatePersonCommand invalidCommand = new CreatePersonCommand(
                UUID.randomUUID(),
                DomainTestData.createPerson(),
                List.of(DomainTestData.createAnalogTax()),
                Collections.emptyList(),
                UUID.randomUUID().toString(),
                DomainTestData.createTaxConsentInfo(),
                userDetailsWithoutOffice
        );

        // WHEN
        SystemConfigValidationException exception = assertThrows(SystemConfigValidationException.class,
                () -> createPersonCommandValidator.validateOrThrow(invalidCommand));

        // THEN
        ValidationResult result = exception.getValidationResult();
        assertTrue(result.hasErrors());
        assertTrue(result.getErrorNotes().contains("User Office is required"));
    }
}
