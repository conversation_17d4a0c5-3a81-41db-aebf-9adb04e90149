package de.adesso.fischereiregister.core.validation.helper;

import de.adesso.fischereiregister.core.model.QualificationsProof;
import de.adesso.fischereiregister.core.model.type.QualificationsProofType;
import de.adesso.fischereiregister.core.validation.ValidationResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

class QualificationsProofValidatorHelperTest {

    private ValidationResult validationResult;

    @BeforeEach
    void setUp() {
        validationResult = new ValidationResult();
    }

    @Test
    @DisplayName("QualificationsProofValidatorHelper.validateQualificationsProofs should add error notes when qualificationsProofs list is empty")
    void validateQualificationsProofs_emptyList_addsErrorNotes() {
        // Given
        List<QualificationsProof> qualificationsProofs = Collections.emptyList();

        // When
        QualificationsProofValidatorHelper.validateQualificationsProofs(qualificationsProofs, validationResult);

        // Then
        assertFalse(validationResult.hasErrors()); // No error notes for empty list
    }

    @Test
    @DisplayName("QualificationsProofValidatorHelper.validateQualificationsProofs should add error notes when required fields are null")
    void validateQualificationsProofs_nullRequiredFields_addsErrorNote() {
        // Given
        QualificationsProof qualificationsProof = new QualificationsProof();
        qualificationsProof.setIssuedBy(null);
        qualificationsProof.setFederalState(null);
        qualificationsProof.setPassedOn(null);
        qualificationsProof.setType(QualificationsProofType.CERTIFICATE); // Set type to CERTIFICATE

        List<QualificationsProof> qualificationsProofs = Collections.singletonList(qualificationsProof);

        // When
        QualificationsProofValidatorHelper.validateQualificationsProofs(qualificationsProofs, validationResult);

        // Then
        assertEquals(3, validationResult.getErrorNotes().size());
        assertTrue(validationResult.getErrorNotes().contains("QualificationsProof.PassedOn is required"));
        assertTrue(validationResult.getErrorNotes().contains("QualificationsProof.IssuedBy is required"));
        assertTrue(validationResult.getErrorNotes().contains("QualificationsProof.FederalState is required"));
    }


    @Test
    @DisplayName("QualificationsProofValidatorHelper.validateQualificationsProofs should add error notes when issuedBy exceeds max length")
    void validateQualificationsProofs_issuedByExceedsMaxLength_addsErrorNote() {
        // Given
        QualificationsProof qualificationsProof = new QualificationsProof();
        qualificationsProof.setIssuedBy("a".repeat(201)); // Exceeds max length of 200
        qualificationsProof.setFederalState("FederalState");
        qualificationsProof.setPassedOn(LocalDate.now());
        qualificationsProof.setType(QualificationsProofType.CERTIFICATE); // Set type to CERTIFICATE

        List<QualificationsProof> qualificationsProofs = Collections.singletonList(qualificationsProof);

        // When
        QualificationsProofValidatorHelper.validateQualificationsProofs(qualificationsProofs, validationResult);

        // Then
        assertEquals(1, validationResult.getErrorNotes().size());
        assertTrue(validationResult.getErrorNotes().contains("QualificationsProof.IssuedBy is too long (max length: 200)"));
    }

    @Test
    @DisplayName("QualificationsProofValidatorHelper.validateQualificationsProofs should not add error notes when qualificationsProof is valid")
    void validateQualificationsProofs_validQualificationsProof_noErrorNotesAdded() {
        // Given
        QualificationsProof qualificationsProof = new QualificationsProof();
        qualificationsProof.setIssuedBy("Issuer");
        qualificationsProof.setFederalState("FederalState");
        qualificationsProof.setPassedOn(LocalDate.now());
        qualificationsProof.setType(QualificationsProofType.CERTIFICATE); // Set type to CERTIFICATE

        List<QualificationsProof> qualificationsProofs = Collections.singletonList(qualificationsProof);

        // When
        QualificationsProofValidatorHelper.validateQualificationsProofs(qualificationsProofs, validationResult);

        // Then
        assertFalse(validationResult.hasErrors());
    }

    @Test
    @DisplayName("QualificationsProofValidatorHelper.validateQualificationsProofs should add error notes for multiple invalid qualificationsProofs")
    void validateQualificationsProofs_multipleInvalidProofs_addsErrorNotes() {
        // Given
        QualificationsProof proof1 = new QualificationsProof();
        proof1.setIssuedBy(null); // Invalid
        proof1.setFederalState("FederalState");
        proof1.setPassedOn(LocalDate.now());
        proof1.setType(QualificationsProofType.CERTIFICATE); // Set type to CERTIFICATE

        QualificationsProof proof2 = new QualificationsProof();
        proof2.setIssuedBy("Issuer");
        proof2.setFederalState(null); // Invalid
        proof2.setPassedOn(LocalDate.now());
        proof2.setType(QualificationsProofType.LEGACY_LICENSE); // Set type to LEGACY_LICENSE

        List<QualificationsProof> qualificationsProofs = List.of(proof1, proof2);

        // When
        QualificationsProofValidatorHelper.validateQualificationsProofs(qualificationsProofs, validationResult);

        // Then
        assertEquals(2, validationResult.getErrorNotes().size());
        assertTrue(validationResult.getErrorNotes().contains("QualificationsProof.IssuedBy is required"));
        assertTrue(validationResult.getErrorNotes().contains("QualificationsProof.FederalState is required"));
    }

    @Test
    @DisplayName("QualificationsProofValidatorHelper.validateQualificationsProofs should not validate federalState when type is OTHER")
    void validateQualificationsProofs_typeOther_doesNotValidateFederalState() {
        // Given
        QualificationsProof qualificationsProof = new QualificationsProof();
        qualificationsProof.setIssuedBy("Issuer");
        qualificationsProof.setFederalState(null); // Would be invalid for other types
        qualificationsProof.setPassedOn(LocalDate.now());
        qualificationsProof.setType(QualificationsProofType.OTHER); // Set type to OTHER

        List<QualificationsProof> qualificationsProofs = Collections.singletonList(qualificationsProof);

        // When
        QualificationsProofValidatorHelper.validateQualificationsProofs(qualificationsProofs, validationResult);

        // Then
        assertFalse(validationResult.hasErrors()); // No errors because federalState is not validated for type OTHER
    }

    @ParameterizedTest(name = "Type {0} should validate federalState")
    @EnumSource(value = QualificationsProofType.class, names = {"CERTIFICATE", "LEGACY_LICENSE"})
    @DisplayName("QualificationsProofValidatorHelper.validateQualificationsProofs should require federalState for CERTIFICATE and LEGACY_LICENSE")
    void validateQualificationsProofs_requiresFederalState(QualificationsProofType proofType) {
        // Given
        validationResult = new ValidationResult(); // Reset validation result for each test case
        QualificationsProof qualificationsProof = new QualificationsProof();
        qualificationsProof.setIssuedBy("Issuer");
        qualificationsProof.setFederalState(null); // Missing federalState
        qualificationsProof.setPassedOn(LocalDate.now());
        qualificationsProof.setType(proofType);

        List<QualificationsProof> qualificationsProofs = Collections.singletonList(qualificationsProof);

        // When
        QualificationsProofValidatorHelper.validateQualificationsProofs(qualificationsProofs, validationResult);

        // Then
        assertEquals(1, validationResult.getErrorNotes().size());
        assertTrue(validationResult.getErrorNotes().contains("QualificationsProof.FederalState is required"));
    }
}
