package de.adesso.fischereiregister.core.use_case.online_services;

import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.type.PaymentType;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class OSTest {
    protected void assertTransactionIdInFeesAndTaxes(String transactionId, RegisterEntry registerEntry) {
        registerEntry.getTaxes().stream()
                .filter(tax -> tax.getPaymentInfo().getType().equals(PaymentType.ONLINE))
                .forEach(tax -> assertEquals(transactionId, tax.getPaymentInfo().getTransactionId()));

        registerEntry.getFees().stream()
                .filter(fee -> fee.getPaymentInfo().getType().equals(PaymentType.ONLINE))
                .forEach(fee -> assertEquals(transactionId, fee.getPaymentInfo().getTransactionId()));
    }

    protected void checkOnlineServiceDataCorrectlySet(RegisterEntry registerEntry, String inboxReference, String serviceAccountId, String transactionId) {
        assertEquals(inboxReference, registerEntry.getInboxReference());
        assertEquals(serviceAccountId, registerEntry.getServiceAccountId());
        assertTransactionIdInFeesAndTaxes(transactionId, registerEntry);
    }
}
