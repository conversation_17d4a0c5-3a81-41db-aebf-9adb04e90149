package de.adesso.fischereiregister.core.interceptables;

import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.ports.FishingLicenseNumberService;

import java.util.UUID;

public class InterceptableFishingLicenseNumberService implements FishingLicenseNumberService, ResetableAdapter {
    @Override
    public void reset() {

    }

    @Override
    public String createNewAvailableFishingLicenseNumber(UUID registerEntryId, FederalState federalState) {
        return "LN12345";
    }
}
