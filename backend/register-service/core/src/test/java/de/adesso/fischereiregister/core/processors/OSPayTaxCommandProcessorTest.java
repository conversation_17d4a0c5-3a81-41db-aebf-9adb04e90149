package de.adesso.fischereiregister.core.processors;

import de.adesso.fischereiregister.core.commands.OSPayTaxCommand;
import de.adesso.fischereiregister.core.events.AxonEvent;
import de.adesso.fischereiregister.core.events.FishingTaxPayedEvent;
import de.adesso.fischereiregister.core.exceptions.PersonNotChangeableException;
import de.adesso.fischereiregister.core.interceptables.InterceptableDocumentNumberService;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.consent.TaxConsentInfo;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.model.utils.IdentificationDocumentFactoryImpl;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertThrows;

public class OSPayTaxCommandProcessorTest {
    private OSPayTaxCommandProcessor processor;

    private UUID registerEntryId;

    @BeforeEach
    public void setup() throws Exception {
        InterceptableDocumentNumberService documentNumberService = new InterceptableDocumentNumberService();
        IdentificationDocumentFactoryImpl identificationDocumentFactory = new IdentificationDocumentFactoryImpl(documentNumberService);
        processor = new OSPayTaxCommandProcessor(identificationDocumentFactory);

        registerEntryId = UUID.randomUUID();
    }

    @Test
    @DisplayName("OSPayTaxCommandProcessor.process never change core person data. If a person mismatches the according request will be rejected (S4;F2 & S3;F2)")
    public void testCommandRejectionOnPersonDataMismatch() {
        // GIVEN
        Person registerPerson = DomainTestData.createPerson();
        Person commandPerson = DomainTestData.createPerson();
        commandPerson.setLastname("otherlastname");

        RegisterEntry registerEntry = buildRegisterEntry(registerPerson);
        OSPayTaxCommand command = buildCommand(commandPerson);

        // WHEN
        // THEN
        assertThrows(PersonNotChangeableException.class, () -> processor.process(command, registerEntry));
    }

    @Test
    @DisplayName("OSPayTaxCommandProcessor.process is successful when data correct.")
    public void testCommandSuccessfullyHandled() {
        // GIVEN
        Person person = DomainTestData.createPerson();
        RegisterEntry registerEntry = buildRegisterEntry(person);
        OSPayTaxCommand command = buildCommand(person);

        // WHEN
        List<AxonEvent> events = processor.process(command, registerEntry);

        // THEN
        assertEquals(1, events.size());
        AxonEvent axonEvent = events.get(0);
        assertInstanceOf(FishingTaxPayedEvent.class, axonEvent);
    }

    private OSPayTaxCommand buildCommand(Person person) {
        return new OSPayTaxCommand(
                registerEntryId,
                person,
                new TaxConsentInfo(),
                "salt",
                DomainTestData.createAnalogTaxesWithOneTax(),
                "serviceAccountId",
                "transactionId",
                FederalState.SH.name(),
                "inboxReference"
        );
    }

    private RegisterEntry buildRegisterEntry(Person person) {
        RegisterEntry registerEntry = new RegisterEntry();
        registerEntry.setRegisterId(registerEntryId);

        registerEntry.setPerson(person);

        Jurisdiction jurisdiction = new Jurisdiction();
        jurisdiction.setFederalState(FederalState.HH.toString());
        registerEntry.setJurisdiction(jurisdiction);

        FishingLicense fishingLicense = new FishingLicense();
        fishingLicense.setIssuingFederalState(FederalState.BE);
        fishingLicense.setNumber("1");
        fishingLicense.setType(LicenseType.REGULAR);
        fishingLicense.getValidityPeriods().add(DomainTestData.createValidityPeriod(LocalDate.of(2025, 1, 1), null));

        registerEntry.getFishingLicenses().add(fishingLicense);

        return registerEntry;
    }

}
