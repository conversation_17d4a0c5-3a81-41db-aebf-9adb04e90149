package de.adesso.fischereiregister.core.use_case.online_services;

import de.adesso.fischereiregister.core.commands.OSCreateVacationLicenseCommand;
import de.adesso.fischereiregister.core.commands.OSExtendFishingLicenseCommand;
import de.adesso.fischereiregister.core.events.PersonCreatedEvent;
import de.adesso.fischereiregister.core.model.Fee;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.ValidityPeriod;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.PaymentType;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.core.testutils.AxonFixture;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import org.axonframework.test.aggregate.AggregateTestFixture;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicReference;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class OSExtendVacationLicenseTest extends OSTest {

    @Test
    @ExtendWith(AxonFixture.class)
    @DisplayName("Extend Vacation license command test if extension succeeds and if result register entry is correct")
    void testExtendVacationLicenseOS(AggregateTestFixture<RegisterEntry> fixture) {
        final UUID registerEntryId = UUID.randomUUID();

        final Person personBefore = DomainTestData.createPersonWithAddress();

        final LocalDate validFrom = LocalDate.of(2021, 4, 1);
        final LocalDate validTo = LocalDate.of(2021, 4, 5);

        FederalState federalState = FederalState.BE;
        Fee fee = new Fee();
        fee.setValidFrom(validFrom);
        fee.setValidTo(validTo);
        fee.setFederalState(federalState.toString());
        fee.setPaymentInfo(DomainTestData.createPaymentInfo(27.0, PaymentType.ONLINE));

        ValidityPeriod validityPeriod = new ValidityPeriod();
        validityPeriod.setValidFrom(validTo);
        validityPeriod.setValidTo(validTo);

        ValidityPeriod newPeriod = new ValidityPeriod();
        newPeriod.setValidFrom(validTo.plusDays(1));
        newPeriod.setValidTo(validTo.plusDays(10));

        AtomicReference<String> licenseNumber = new AtomicReference<>();

        String transactionId = "transactionId";
        String serviceAccountId = "serviceAccountId";
        String inboxReference = "inboxReference";
        fixture.given(
                        new PersonCreatedEvent(
                                registerEntryId,
                                personBefore,
                                DomainTestData.createAnalogTaxesWithOneTax(),
                                List.of(),
                                "salt",
                                List.of(),
                                DomainTestData.createTaxConsentInfo(),
                                null,
                                null,
                                null,
                                null,
                                federalState.toString(),
                                SubmissionType.ANALOG
                        )

                ).when(
                        new OSCreateVacationLicenseCommand(
                                registerEntryId,
                                personBefore,
                                DomainTestData.createConsentInfo(),
                                fee,
                                "salt",
                                federalState, // Implicitly test different federal state
                                validityPeriod,
                                transactionId,
                                serviceAccountId,
                                inboxReference
                        )

                )
                .expectState(registerEntry -> {
                    final Person actualPerson = registerEntry.getPerson();
                    assertEquals(1, registerEntry.getFishingLicenses().size());
                    licenseNumber.set(registerEntry.getFishingLicenses().getFirst().getNumber());
                    // Address did not change
                    assertEquals(personBefore.getAddress(), actualPerson.getAddress());

                    // Person data changed
                    actualPerson.setAddress(null);

                    checkOnlineServiceDataCorrectlySet(registerEntry, inboxReference, serviceAccountId, transactionId);
                });


        fixture
                .when(
                        new OSExtendFishingLicenseCommand(
                                registerEntryId,
                                "salt",
                                licenseNumber.toString(),
                                DomainTestData.createConsentInfo(),
                                fee, // Implicitly test different federal state
                                federalState,
                                newPeriod,
                                serviceAccountId,
                                transactionId,
                                inboxReference
                        )

                )
                .expectState(registerEntry -> {
                    assertEquals(1, registerEntry.getFishingLicenses().size());
                    licenseNumber.set(registerEntry.getFishingLicenses().getFirst().getNumber());

                    checkOnlineServiceDataCorrectlySet(registerEntry, inboxReference, serviceAccountId, transactionId);
                });
    }
}
