package de.adesso.fischereiregister.core.validation;

import de.adesso.fischereiregister.core.commands.ESCreateFishingCertificateCommand;
import de.adesso.fischereiregister.core.exceptions.RulesProcessingException;
import de.adesso.fischereiregister.core.interceptables.InterceptableCountryService;
import de.adesso.fischereiregister.core.interceptables.InterceptableTenantRulesValidationPort;
import de.adesso.fischereiregister.core.model.user.UserRole;
import de.adesso.fischereiregister.core.ports.CountryService;
import de.adesso.fischereiregister.core.ports.TenantRulesValidationPort;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;

class ESCreateFishingCertificateCommandValidatorTest {
    private ESCreateFishingCertificateCommandValidator validator;
    private TenantRulesValidationPort tenantRulesValidationPort;

    @BeforeEach
    public void setUp() {
        final CountryService countryService = new InterceptableCountryService();
        tenantRulesValidationPort = spy(new InterceptableTenantRulesValidationPort()); // Wrap in a spy
        validator = new ESCreateFishingCertificateCommandValidator(countryService, tenantRulesValidationPort);
    }

    @Test
    public void testTenantRulesValidationPortIsCalled() throws RulesProcessingException {
        // GIVEN
        var command = new ESCreateFishingCertificateCommand(
                UUID.randomUUID(),
                LocalDate.now(),
                DomainTestData.createPerson(),
                "SH",
                DomainTestData.createUserDetails(UserRole.OFFICIAL)
        );

        // WHEN
        assertDoesNotThrow(() -> validator.validateOrThrow(command));

        // THEN
        verify(tenantRulesValidationPort).validateUsingQualificationProofRules(any(), any(), any());
    }
}