package de.adesso.fischereiregister.core.use_case;

import de.adesso.fischereiregister.core.commands.CreateVacationLicenseCommand;
import de.adesso.fischereiregister.core.events.PersonCreatedEvent;
import de.adesso.fischereiregister.core.events.VacationLicenseCreatedEvent;
import de.adesso.fischereiregister.core.model.Fee;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.ValidityPeriod;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.model.type.PaymentType;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.core.model.user.UserRole;
import de.adesso.fischereiregister.core.testutils.AxonFixture;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import org.axonframework.test.aggregate.AggregateTestFixture;
import org.axonframework.test.aggregate.TestExecutor;
import org.axonframework.test.matchers.Matchers;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

public class CreateVacationLicenseTest {
    @ExtendWith(AxonFixture.class)
    @Test
    @DisplayName("Vacation license command is handled successfully when a register entry already exists.")
    void testVacationLicenseAddedToExistingRegisterEntry(AggregateTestFixture<RegisterEntry> fixture) {
        testVacationLicenseCreation(false, fixture);

    }

    @ExtendWith(AxonFixture.class)
    @Test
    @DisplayName("Vacation license command is handled successfully when no register entry exists, i.e. a new entry is created.")
    void testVacationLicenseAddedWhenNoRegisterEntryExists(AggregateTestFixture<RegisterEntry> fixture) {
        testVacationLicenseCreation(true, fixture);

    }

    @Test
    @ExtendWith(AxonFixture.class)
    @DisplayName("Vacation license command does not change existing address but does change person data.")
    void testVacationLicenseCommandDoesNotChangeExistingAddress(AggregateTestFixture<RegisterEntry> fixture) {
        final UUID registerEntryId = UUID.randomUUID();

        final Person personBefore = DomainTestData.createPersonWithAddress();
        Person personAfter = DomainTestData.createPerson();
        personAfter.setBirthname("New Birthname");
        personAfter.setFirstname("New Firstname");
        personAfter.setLastname("New Lastname");

        final LocalDate validFrom = LocalDate.of(2021, 4, 1);
        final LocalDate validTo = LocalDate.of(2021, 4, 5);

        Fee fee = new Fee();
        fee.setValidFrom(validFrom);
        fee.setValidTo(validTo);
        fee.setFederalState(FederalState.SH.toString());
        fee.setPaymentInfo(DomainTestData.createPaymentInfo(27.0, PaymentType.ONLINE));

        ValidityPeriod validityPeriod = new ValidityPeriod();
        validityPeriod.setValidFrom(validFrom);
        validityPeriod.setValidTo(validTo);

        String federalState = FederalState.SH.toString();

        fixture.given(
                        new PersonCreatedEvent(
                                registerEntryId,
                                personBefore,
                                DomainTestData.createAnalogTaxesWithOneTax(),
                                List.of(),
                                "salt",
                                List.of(),
                                DomainTestData.createTaxConsentInfo(),
                                null,
                                null,
                                null,
                                null,
                                federalState,
                                SubmissionType.ANALOG
                        )
                ).when(
                        new CreateVacationLicenseCommand(
                                registerEntryId,
                                "salt",
                                personAfter,
                                DomainTestData.createAnalogFeesWithOneFee(),
                                DomainTestData.createAnalogTaxesWithOneTax(),
                                DomainTestData.createConsentInfo(),
                                validityPeriod,
                                DomainTestData.createUserDetails(UserRole.OFFICIAL)
                        )
                )
                .expectSuccessfulHandlerExecution().expectEventsMatching(Matchers.exactSequenceOf(Matchers.messageWithPayload(Matchers.matches(payload ->
                        payload.getClass().isAssignableFrom(VacationLicenseCreatedEvent.class)
                ))))
                .expectState(registerEntry -> {
                    final Person actualPerson = registerEntry.getPerson();

                    // Address did not change
                    assertEquals(personBefore.getAddress(), actualPerson.getAddress());

                    // Person data changed
                    actualPerson.setAddress(null);
                    personAfter.setAddress(null);
                    assertEquals(personAfter, actualPerson);
                });
    }

    private void testVacationLicenseCreation(boolean createNew, AggregateTestFixture<RegisterEntry> fixture) {
        final UUID registerEntryId = UUID.randomUUID();

        final LocalDate validFrom = LocalDate.of(2021, 4, 1);
        final LocalDate validTo = LocalDate.of(2021, 4, 6);

        Fee fee = new Fee();
        fee.setValidFrom(validFrom);
        fee.setValidTo(validTo);
        fee.setFederalState(FederalState.SH.toString());
        fee.setPaymentInfo(DomainTestData.createPaymentInfo(27.0, PaymentType.ONLINE));

        ValidityPeriod validityPeriod = new ValidityPeriod();
        validityPeriod.setValidFrom(validFrom);
        validityPeriod.setValidTo(validTo);
        String federalState = FederalState.SH.toString();

        TestExecutor<RegisterEntry> executor = fixture;
        if (!createNew) {
            executor = fixture.given(
                    new PersonCreatedEvent(
                            registerEntryId,
                            DomainTestData.createPerson(),
                            List.of(),
                            List.of(),
                            "salt",
                            List.of(),
                            DomainTestData.createTaxConsentInfo(),
                            null,
                            null,
                            null,
                            null,
                            federalState,
                            SubmissionType.ONLINE
                    )
            );
        }

        executor.when(
                        new CreateVacationLicenseCommand(
                                registerEntryId,
                                "salt",
                                DomainTestData.createPerson(),
                                DomainTestData.createAnalogFeesWithOneFee(),
                                DomainTestData.createAnalogTaxesWithOneTax(),
                                DomainTestData.createConsentInfo(),
                                validityPeriod,
                                DomainTestData.createUserDetails(UserRole.OFFICIAL)
                        )
                )
                .expectSuccessfulHandlerExecution().expectEventsMatching(Matchers.exactSequenceOf(Matchers.messageWithPayload(Matchers.matches(payload ->
                        payload.getClass().isAssignableFrom(VacationLicenseCreatedEvent.class)
                ))))
                .expectState(registerEntry -> {
                    // Check fee
                    assertEquals(1, registerEntry.getFees().size());

                    // Check taxes
                    assertEquals(1, registerEntry.getTaxes().size());

                    // Check license
                    assertEquals(1, registerEntry.getFishingLicenses().size());
                    FishingLicense license = registerEntry.getFishingLicenses().getFirst();
                    // we expect only one validity period because the vaction license has one validity period after it's creation
                    // after an extension there however will be two validity periods (or maybe even more than two validity periods in future versions of the application)
                    ValidityPeriod validityPeriodFromLicense = license.getValidityPeriods().getFirst();
                    assertEquals(FederalState.SH, license.getIssuingFederalState());
                    assertEquals(validFrom, validityPeriodFromLicense.getValidFrom());
                    assertEquals(validTo, validityPeriodFromLicense.getValidTo());
                    assertEquals(LicenseType.VACATION, license.getType());

                    // Created 2 documents
                    assertEquals(2, registerEntry.getIdentificationDocuments().size());
                    IdentificationDocument licenseDocument = registerEntry.getIdentificationDocuments().stream()
                            .filter(document -> document.getFishingLicense() != null)
                            .findAny()
                            .orElse(null);
                    assertNotNull(licenseDocument);
                    assertEquals(license.getNumber(), licenseDocument.getFishingLicense().getNumber());

                    IdentificationDocument taxDocument = registerEntry.getIdentificationDocuments().stream()
                            .filter(document -> document.getTax() != null)
                            .findAny()
                            .orElse(null);
                    assertNotNull(taxDocument);

                    assertNull(registerEntry.getJurisdiction());
                });

    }

}
