package de.adesso.fischereiregister.core.validation.helper;

import de.adesso.fischereiregister.core.model.Fee;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import de.adesso.fischereiregister.core.validation.ValidationResult;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

class FeeValidatorHelperTest {

    @Test
    @DisplayName("FeeValidatorHelper.validateFees should return errors for invalid fees")
    void testValidateFees_withErrors() {
        // Given
        ValidationResult validationResult = new ValidationResult();
        Fee validFee = DomainTestData.createAnalogFee();
        Fee invalidFee = DomainTestData.createAnalogFee();
        invalidFee.setPaymentInfo(null);
        invalidFee.setValidFrom(null);
        invalidFee.setFederalState(null);

        // When
        FeeValidatorHelper.validateFees(List.of(validFee,invalidFee), validationResult);

        // Then
        assertTrue(validationResult.hasErrors());
        assertTrue(validationResult.getErrorNotes().contains(FeeValidatorHelper.FEDERAL_STATE + " is required"));
        assertTrue(validationResult.getErrorNotes().contains(FeeValidatorHelper.VALID_FROM + " is required"));
        assertTrue(validationResult.getErrorNotes().contains(FeeValidatorHelper.PAYMENT_INFO + " is required"));
    }

    @Test
    @DisplayName("FeeValidatorHelper.validateFees should return no errors if everything is valid")
    void testValidateFees_withoutErrors() {
        // Given
        ValidationResult validationResult = new ValidationResult();
        Fee validFee = DomainTestData.createAnalogFee();

        // When
        FeeValidatorHelper.validateFees(List.of(validFee), validationResult);

        // Then
        assertFalse(validationResult.hasErrors());
    }
}
