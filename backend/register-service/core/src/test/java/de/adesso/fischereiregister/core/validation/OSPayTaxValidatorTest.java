package de.adesso.fischereiregister.core.validation;

import de.adesso.fischereiregister.core.commands.OSPayTaxCommand;
import de.adesso.fischereiregister.core.exceptions.AggregateValidationException;
import de.adesso.fischereiregister.core.interceptables.InterceptableCountryService;
import de.adesso.fischereiregister.core.interceptables.InterceptableTenantRulesValidationPort;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.consent.TaxConsentInfo;
import de.adesso.fischereiregister.core.ports.CountryService;
import de.adesso.fischereiregister.core.ports.TenantRulesValidationPort;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

class OSPayTaxValidatorTest {

    private OSTaxCommandValidator validator;
    private CountryService countryService;

    @BeforeEach
    void setUp() {
        countryService = new InterceptableCountryService();
        final TenantRulesValidationPort tenantRulesValidationPort = new InterceptableTenantRulesValidationPort();
        validator = new OSTaxCommandValidator(countryService, tenantRulesValidationPort);
    }

    @Test
    void testValidate_AllFieldsValid() {
        // Arrange
        Person person = DomainTestData.createPersonWithAddress(); // Mock or real instance
        String federalState = "SH"; // Valid federal state
        List<Tax> taxList = DomainTestData.createAnalogTaxesWithOneTax();// Mock or real list
        String serviceAccountId = "Service123";
        String transactionId = "Trans456";
        TaxConsentInfo consentInfo = DomainTestData.createTaxConsentInfo();

        final OSPayTaxCommand command = new OSPayTaxCommand(UUID.randomUUID(), person, consentInfo, "salt", taxList, serviceAccountId, transactionId, federalState,"inboxReference");

        // Act
        assertDoesNotThrow(() -> validator.validateOrThrow(command));
    }


    @Test
    void testValidate_MissingFields() {
        // Arrange
        Person person = null; // Missing person
        String federalState = "InvalidState"; // Invalid federal state
        List<Tax> taxList = List.of();
        String serviceAccountId = null; // Missing service account ID
        String transactionId = null; // Missing transaction ID
        TaxConsentInfo consentInfo = DomainTestData.createTaxConsentInfo();

        final OSPayTaxCommand command = new OSPayTaxCommand(UUID.randomUUID(),  person, consentInfo, "salt", taxList, serviceAccountId, transactionId, federalState, "");

        // Act
        AggregateValidationException exception = assertThrows(AggregateValidationException.class, () -> validator.validateOrThrow(command));
        ValidationResult result = exception.getValidationResult();

        // Assert
        assertEquals(5, result.getErrorNotes().size()); // Adjust based on expected error count
        assertTrue(result.getErrorNotes().contains("Person Details is required"));
        assertTrue(result.getErrorNotes().contains("Federal state " + federalState + " is invalid."));
        assertTrue(result.getErrorNotes().contains("serviceAccountId is required"));
        assertTrue(result.getErrorNotes().contains("transactionId is required"));
        assertTrue(result.getErrorNotes().contains("inboxReference is required"));
    }

    @Test
    void testValidate_InvalidTaxes() {
        // Arrange
        Person person = new Person(); // Mock or real instance
        String federalState = "SH"; // Valid federal state
        String serviceAccountId = "Service123";
        String transactionId = "Trans456";

        List<Tax> taxList = new ArrayList<>(); // Mock list with invalid tax
        Tax invalidTax = DomainTestData.createAnalogTax();
        invalidTax.setValidTo(LocalDate.of(2025, 03, 21));
        taxList.add(invalidTax);

        TaxConsentInfo consentInfo = DomainTestData.createTaxConsentInfo();

        final OSPayTaxCommand command = new OSPayTaxCommand(UUID.randomUUID(),  person, consentInfo, "salt", taxList, serviceAccountId, transactionId, federalState, "inboxReference");

        // Act
        AggregateValidationException exception = assertThrows(AggregateValidationException.class, () -> validator.validateOrThrow(command));
        ValidationResult result = exception.getValidationResult();

        // Assert
        assertTrue(result.getErrorNotes().contains("Tax payment should be for a full year (01.01.XXXX – 31.12.XXXX) but it's not."));
    }

    @Test
    void testValidate_PersonWithInvalidFields() {
        // Arrange
        Person person = DomainTestData.createPerson();
        person.setFirstname(null);
        String federalState = "SH";
        List<Tax> taxList = DomainTestData.createAnalogTaxesWithOneTax();
        String serviceAccountId = "Service123";
        String transactionId = "Trans456";
        TaxConsentInfo consentInfo = DomainTestData.createTaxConsentInfo();

        final OSPayTaxCommand command = new OSPayTaxCommand(UUID.randomUUID(),  person, consentInfo, "salt", taxList, serviceAccountId, transactionId, federalState, "inboxReference");

        // Act
        AggregateValidationException exception = assertThrows(AggregateValidationException.class, () -> validator.validateOrThrow(command));
        ValidationResult result = exception.getValidationResult();

        // Assert
        assertTrue(result.getErrorNotes().contains("Firstname is required"));
    }
}
