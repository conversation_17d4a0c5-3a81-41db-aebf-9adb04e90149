package de.adesso.fischereiregister.core.validation;

import de.adesso.fischereiregister.core.commands.OSCreateLimitedLicenseApplicationCommand;
import de.adesso.fischereiregister.core.exceptions.ClientInputValidationException;
import de.adesso.fischereiregister.core.exceptions.LicenseTypeNotSupportedException;
import de.adesso.fischereiregister.core.interceptables.InterceptableCountryService;
import de.adesso.fischereiregister.core.interceptables.InterceptableTenantRulesValidationPort;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.ports.CountryService;
import de.adesso.fischereiregister.core.ports.TenantRulesValidationPort;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import de.adesso.fischereiregister.core.validation.helper.LicenseValidatorHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class OSCreateLimitedLicenseApplicationCommandValidatorTest {

    private OSCreateLimitedLicenseApplicationCommandValidator commandValidator;

    private final CountryService countryService = new InterceptableCountryService();

    private final TenantRulesValidationPort tenantRulesValidationPort = new InterceptableTenantRulesValidationPort();

    private final LicenseValidatorHelper licenseValidatorHelper = mock(LicenseValidatorHelper.class);

    @BeforeEach
    void setUp() {
        commandValidator = new OSCreateLimitedLicenseApplicationCommandValidator(countryService, tenantRulesValidationPort, licenseValidatorHelper);
    }

    @Test
    @DisplayName("testValidateOrThrow_validCommand should pass a valid command without throwing an exception")
    public void testValidateOrThrow_validCommand() throws Exception {
        // GIVEN
        final RegisterEntry registerEntry = new RegisterEntry();

        when(licenseValidatorHelper.isLicenseAvailable(any(), any())).thenReturn(true);

        final OSCreateLimitedLicenseApplicationCommand validCommand = new OSCreateLimitedLicenseApplicationCommand(
                DomainTestData.registerId,
                FederalState.SH,
                DomainTestData.createPersonWithAddress(),
                DomainTestData.createDigitalFee(),
                DomainTestData.createConsentInfo(),
                "disabilityCertificateFileURL",
                "inboxReference",
                "serviceAccountId",
                "transactionId"
        );

        // WHEN
        // THEN
        assertDoesNotThrow(() -> commandValidator.validateOrThrow(validCommand, registerEntry));
    }

    @Test
    @DisplayName("testValidateOrThrow throws an exception when no disability certificate file URL is provided")
    public void testValidateOrThrow_noDisabilityCertificateFileURL() throws Exception {
        // GIVEN
        final RegisterEntry registerEntry = new RegisterEntry();

        when(licenseValidatorHelper.isLicenseAvailable(any(), any())).thenReturn(true);

        final OSCreateLimitedLicenseApplicationCommand invalidCommand = new OSCreateLimitedLicenseApplicationCommand(
                DomainTestData.registerId,
                FederalState.SH,
                DomainTestData.createPersonWithAddress(),
                DomainTestData.createDigitalFee(),
                DomainTestData.createConsentInfo(),
                "", // No disability certificate file URL
                "inboxReference",
                "serviceAccountId",
                "transactionId"
        );

        // WHEN
        // THEN
        ClientInputValidationException exception = assertThrows(ClientInputValidationException.class, () -> commandValidator.validateOrThrow(invalidCommand, registerEntry));
        assertEquals(1, exception.getValidationResult().getErrorNotes().size());
    }

    @Test
    @DisplayName("testValidateOrThrows throws an exception when no address is provided")
    public void testValidateOrThrow_noAddress() throws Exception {
        // GIVEN
        final RegisterEntry registerEntry = new RegisterEntry();

        when(licenseValidatorHelper.isLicenseAvailable(any(), any())).thenReturn(true);

        final OSCreateLimitedLicenseApplicationCommand invalidCommand = new OSCreateLimitedLicenseApplicationCommand(
                DomainTestData.registerId,
                FederalState.SH,
                DomainTestData.createPerson(),
                DomainTestData.createDigitalFee(),
                DomainTestData.createConsentInfo(),
                "disabilityCertificateFileURL",
                "inboxReference",
                "serviceAccountId",
                "transactionId"
        );

        // WHEN
        // THEN
        ClientInputValidationException exception = assertThrows(ClientInputValidationException.class, () -> commandValidator.validateOrThrow(invalidCommand, registerEntry));
        assertEquals(1, exception.getValidationResult().getErrorNotes().size());
    }

    @Test
    @DisplayName("testValidateOrThrow throws exceptions when mandatory fields are missing")
    public void testValidateOrThrow_missingMandatoryFields() throws Exception {
        // GIVEN
        final RegisterEntry registerEntry = new RegisterEntry();

        when(licenseValidatorHelper.isLicenseAvailable(any(), any())).thenReturn(true);

        final OSCreateLimitedLicenseApplicationCommand invalidCommand = new OSCreateLimitedLicenseApplicationCommand(
                UUID.randomUUID(),
                null, // No federal state
                null, // No person
                null, // No fee
                null, // No consent info
                null, // No disability certificate file URL
                null, // No inbox reference
                null, // No service account ID
                null  // No transaction ID
        );

        // WHEN
        // THEN
        ClientInputValidationException exception = assertThrows(ClientInputValidationException.class, () -> commandValidator.validateOrThrow(invalidCommand, registerEntry));
        assertEquals(8, exception.getValidationResult().getErrorNotes().size());
    }

    @Test
    @DisplayName("testValidateOrThrow throws an exception when the federal state does not support limited licenses")
    public void testValidateOrThrow_federalStateNotSupported() throws Exception {
        // GIVEN
        final RegisterEntry registerEntry = new RegisterEntry();

        when(licenseValidatorHelper.isLicenseAvailable(any(), any())).thenReturn(false);

        final OSCreateLimitedLicenseApplicationCommand invalidCommand = new OSCreateLimitedLicenseApplicationCommand(
                DomainTestData.registerId,
                FederalState.BW,
                DomainTestData.createPersonWithAddress(),
                DomainTestData.createDigitalFee(),
                DomainTestData.createConsentInfo(),
                "disabilityCertificateFileURL",
                "inboxReference",
                "serviceAccountId",
                "transactionId"
        );

        // WHEN
        // THEN
        assertThrows(LicenseTypeNotSupportedException.class, () -> commandValidator.validateOrThrow(invalidCommand, registerEntry));
    }
}
