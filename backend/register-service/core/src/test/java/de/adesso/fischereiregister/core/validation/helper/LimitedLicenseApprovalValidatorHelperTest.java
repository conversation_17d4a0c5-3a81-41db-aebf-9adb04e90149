package de.adesso.fischereiregister.core.validation.helper;

import de.adesso.fischereiregister.core.model.LimitedLicenseApproval;
import de.adesso.fischereiregister.core.model.SigningEmployee;
import de.adesso.fischereiregister.core.model.ValidityPeriod;
import de.adesso.fischereiregister.core.validation.ValidationResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.assertTrue;

class LimitedLicenseApprovalValidatorHelperTest {

    private ValidationResult validationResult;
    private LimitedLicenseApproval limitedLicenseApproval;
    private ValidityPeriod validityPeriod;

    @BeforeEach
    void setUp() {
        validationResult = new ValidationResult();

        SigningEmployee signingEmployee = new SigningEmployee();
        signingEmployee.setPersonalSign("ValidSign");
        signingEmployee.setName("Valid Name");
        signingEmployee.setEmail("<EMAIL>");
        signingEmployee.setPhone("123456789");

        limitedLicenseApproval = new LimitedLicenseApproval();
        limitedLicenseApproval.setCreatedAt(LocalDate.now());
        limitedLicenseApproval.setSigningEmployee(signingEmployee);
        limitedLicenseApproval.setFileNumber("12345");
        limitedLicenseApproval.setCashRegisterSign("ValidSign");
        limitedLicenseApproval.setJustificationForLimitedDurationNotice("Valid justification");

        validityPeriod = new ValidityPeriod();
        validityPeriod.setValidFrom(LocalDate.now());
        validityPeriod.setValidTo(LocalDate.now().plusDays(30));
    }

    @Test
    void validateLimitedLicenseApproval_withValidData_shouldPass() {
        LimitedLicenseApprovalValidatorHelper.validateLimitedLicenseApproval(limitedLicenseApproval, validityPeriod, validationResult);

        assertTrue(validationResult.getErrorNotes().isEmpty(), "Validation should pass with no errors");
    }

    @Test
    void validateLimitedLicenseApproval_withMissingRequiredField_shouldFail() {
        limitedLicenseApproval.setFileNumber(null);

        LimitedLicenseApprovalValidatorHelper.validateLimitedLicenseApproval(limitedLicenseApproval, validityPeriod, validationResult);

        assertTrue(validationResult.hasErrors(), "Validation should fail due to missing required field");
    }

    @Test
    void validateLimitedLicenseApproval_withInvalidEmail_shouldFail() {
        limitedLicenseApproval.getSigningEmployee().setEmail("invalid-email");

        LimitedLicenseApprovalValidatorHelper.validateLimitedLicenseApproval(limitedLicenseApproval, validityPeriod, validationResult);

        assertTrue(validationResult.hasErrors(), "Validation should fail due to invalid email format");
    }

    @Test
    void validateLimitedLicenseApproval_withValidToSetAndNullJustificationForLimitedDurationNotice_shouldFail() {
        validityPeriod.setValidTo(LocalDate.now());
        limitedLicenseApproval.setJustificationForLimitedDurationNotice(null);
        LimitedLicenseApprovalValidatorHelper.validateLimitedLicenseApproval(limitedLicenseApproval, validityPeriod, validationResult);

        assertTrue(validationResult.hasErrors(), "Validation should fail due to missing justificationForLimitedDurationNotice");
    }

    @Test
    void validateLimitedLicenseApproval_withNullLimitedLicenseApproval_shouldFail() {
        LimitedLicenseApprovalValidatorHelper.validateLimitedLicenseApproval(null, validityPeriod, validationResult);

        assertTrue(validationResult.hasErrors(), "Validation should fail due to null LimitedLicenseApproval");
    }
}