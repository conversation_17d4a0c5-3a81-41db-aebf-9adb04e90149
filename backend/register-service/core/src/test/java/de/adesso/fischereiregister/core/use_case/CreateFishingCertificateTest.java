package de.adesso.fischereiregister.core.use_case;

import de.adesso.fischereiregister.core.commands.CreateFishingCertificateCommand;
import de.adesso.fischereiregister.core.events.QualificationsProofCreatedEvent;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.user.UserRole;
import de.adesso.fischereiregister.core.testutils.AxonFixture;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import org.axonframework.test.aggregate.AggregateTestFixture;
import org.axonframework.test.matchers.Matchers;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;

public class CreateFishingCertificateTest {

    @Test
    @DisplayName("Test whether aggregate handlers OrderReplacementCardCommand of are successful.")
    @ExtendWith(AxonFixture.class)
    void withAxonTestFixture(AggregateTestFixture<RegisterEntry> fixture) {        //given
        final UUID givenId = UUID.randomUUID();
        final Person givenPerson = DomainTestData.createPersonWithAddress();

        final String givenOriginalSalt = "12345";
        final String givenReplacementSalt = "67890";

        assertNotEquals(givenOriginalSalt, givenReplacementSalt);

        fixture.givenNoPriorActivity()
                .when(new CreateFishingCertificateCommand(givenId,
                        DomainTestData.jan_2024,
                        givenPerson,
                        DomainTestData.createUserDetails(UserRole.EXAM_DATA_CREATOR)
                ))
                .expectSuccessfulHandlerExecution().expectEventsMatching(Matchers.exactSequenceOf(Matchers.messageWithPayload(Matchers.matches(payload -> {
                    return payload.getClass().isAssignableFrom(QualificationsProofCreatedEvent.class);
                }))))
                .expectState(registerEntry -> {
                    assertEquals(givenId, registerEntry.getRegisterId());
                    assertEquals(1, registerEntry.getQualificationsProofs().size());
                    assertEquals(DomainTestData.issuedBy, registerEntry.getQualificationsProofs().get(0).getIssuedBy());
                });
    }

}
