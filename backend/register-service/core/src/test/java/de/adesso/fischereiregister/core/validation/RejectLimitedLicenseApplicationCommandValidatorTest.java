package de.adesso.fischereiregister.core.validation;

import de.adesso.fischereiregister.core.commands.RejectLimitedLicenseApplicationCommand;
import de.adesso.fischereiregister.core.exceptions.LicenseApplicationNotRejectableException;
import de.adesso.fischereiregister.core.exceptions.LicenseTypeNotSupportedException;
import de.adesso.fischereiregister.core.interceptables.InterceptableCountryService;
import de.adesso.fischereiregister.core.interceptables.InterceptableTenantRulesValidationPort;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.type.LimitedLicenseApplicationStatus;
import de.adesso.fischereiregister.core.model.user.UserRole;
import de.adesso.fischereiregister.core.ports.CountryService;
import de.adesso.fischereiregister.core.ports.TenantRulesValidationPort;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import de.adesso.fischereiregister.core.validation.helper.LicenseValidatorHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class RejectLimitedLicenseApplicationCommandValidatorTest {

    private RejectLimitedLicenseApplicationCommandValidator commandValidator;

    private final CountryService countryService = new InterceptableCountryService();

    private final TenantRulesValidationPort tenantRulesValidationPort = new InterceptableTenantRulesValidationPort();

    private final LicenseValidatorHelper licenseValidatorHelper = mock(LicenseValidatorHelper.class);

    @BeforeEach
    void setUp() {
        commandValidator = new RejectLimitedLicenseApplicationCommandValidator(countryService, tenantRulesValidationPort, licenseValidatorHelper);
    }


    @Test
    @DisplayName("Test validateOrThrow with valid command and register entry")
    public void testValidateOrThrow_validCommand() throws Exception {
        // GIVEN
        final RegisterEntry registerEntry = new RegisterEntry();
        registerEntry.setLimitedLicenseApplication(DomainTestData.createLimitedLicenseApplication());
        registerEntry.setPerson(DomainTestData.createPerson());
        registerEntry.setJurisdiction(DomainTestData.createJurisdiction());

        when(licenseValidatorHelper.isLicenseAvailable(any(), any())).thenReturn(true);

        final RejectLimitedLicenseApplicationCommand validCommand = new RejectLimitedLicenseApplicationCommand(
                registerEntry.getRegisterId(),
                DomainTestData.createUserDetails(UserRole.LIMITED_LICENSE_CREATOR)
        );

        // WHEN
        // THEN
        assertDoesNotThrow(() -> commandValidator.validateOrThrow(validCommand, registerEntry));
    }

    @Test
    @DisplayName("Test validateOrThrow on registerEntry with no limited license application")
    public void testValidateOrThrow_noLimitedLicenseApplication() throws Exception {
        // GIVEN
        final RegisterEntry registerEntry = new RegisterEntry();
        registerEntry.setPerson(DomainTestData.createPerson());
        registerEntry.setJurisdiction(DomainTestData.createJurisdiction());

        when(licenseValidatorHelper.isLicenseAvailable(any(), any())).thenReturn(true);

        final RejectLimitedLicenseApplicationCommand validCommand = new RejectLimitedLicenseApplicationCommand(
                registerEntry.getRegisterId(),
                DomainTestData.createUserDetails(UserRole.LIMITED_LICENSE_CREATOR)
        );

        // WHEN
        // THEN
        assertThrows(LicenseApplicationNotRejectableException.class, () -> commandValidator.validateOrThrow(validCommand, registerEntry));
    }


    @Test
    @DisplayName("Test validateOrThrow with limited license application not in PENDING status")
    public void testValidateOrThrow_limitedLicenseApplicationNotPending() throws Exception {
        // GIVEN
        final RegisterEntry registerEntry = new RegisterEntry();
        registerEntry.setLimitedLicenseApplication(DomainTestData.createLimitedLicenseApplication());
        registerEntry.getLimitedLicenseApplication().setStatus(LimitedLicenseApplicationStatus.REJECTED); // Set to a status other than PENDING
        registerEntry.setPerson(DomainTestData.createPerson());
        registerEntry.setJurisdiction(DomainTestData.createJurisdiction());

        when(licenseValidatorHelper.isLicenseAvailable(any(), any())).thenReturn(true);

        final RejectLimitedLicenseApplicationCommand validCommand = new RejectLimitedLicenseApplicationCommand(
                registerEntry.getRegisterId(),
                DomainTestData.createUserDetails(UserRole.LIMITED_LICENSE_CREATOR)
        );

        // WHEN
        // THEN
        assertThrows(LicenseApplicationNotRejectableException.class, () -> commandValidator.validateOrThrow(validCommand, registerEntry));
    }

    @Test
    @DisplayName("Test validateOrThrow in invalid tenant")
    public void testValidateOrThrow_invalidTenant() throws Exception {
        // GIVEN
        final RegisterEntry registerEntry = new RegisterEntry();
        registerEntry.setPerson(DomainTestData.createPerson());
        registerEntry.setJurisdiction(DomainTestData.createJurisdiction());

        when(licenseValidatorHelper.isLicenseAvailable(any(), any())).thenReturn(false);

        final RejectLimitedLicenseApplicationCommand validCommand = new RejectLimitedLicenseApplicationCommand(
                registerEntry.getRegisterId(),
                DomainTestData.createUserDetails(UserRole.LIMITED_LICENSE_CREATOR)
        );

        // WHEN
        // THEN
        assertThrows(LicenseTypeNotSupportedException.class, () -> commandValidator.validateOrThrow(validCommand, registerEntry));
    }
}
