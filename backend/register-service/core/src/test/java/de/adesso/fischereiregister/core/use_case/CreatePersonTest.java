package de.adesso.fischereiregister.core.use_case;

import de.adesso.fischereiregister.core.commands.CreatePersonCommand;
import de.adesso.fischereiregister.core.events.PersonCreatedEvent;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.user.UserRole;
import de.adesso.fischereiregister.core.testutils.AxonFixture;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import org.axonframework.test.aggregate.AggregateTestFixture;
import org.axonframework.test.matchers.Matchers;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

public class CreatePersonTest {

    @Test
    @ExtendWith(AxonFixture.class)
    void withAxonTestFixture(AggregateTestFixture<RegisterEntry> fixture) {        //given
        final UUID givenId = UUID.randomUUID();
        final Person givenPerson = DomainTestData.createPerson();

        fixture
                .when(
                     new CreatePersonCommand(
                             givenId,
                             givenPerson,
                            DomainTestData.createAnalogTaxesWithOneTax(),
                            List.of(),
                            "anySalt",
                            DomainTestData.createTaxConsentInfo(),
                            DomainTestData.createUserDetails(UserRole.OFFICIAL)
                    )
                )
                .expectSuccessfulHandlerExecution()
                .expectEventsMatching(Matchers.exactSequenceOf(
                        Matchers.messageWithPayload(Matchers.matches(payload ->
                                payload.getClass().isAssignableFrom(PersonCreatedEvent.class)
                        ))
                ))
                .expectState(registerEntry -> {
                    // Assert register ID
                    assertEquals(givenId, registerEntry.getRegisterId());

                    // Assert person is created
                    assertNotNull(registerEntry.getPerson());
                    assertEquals(givenPerson, registerEntry.getPerson());

                    assertEquals(1, registerEntry.getIdentificationDocuments().size());

                });

    }
}
