package de.adesso.fischereiregister.core.use_case;

import de.adesso.fischereiregister.core.commands.BanTemporarilyCommand;
import de.adesso.fischereiregister.core.commands.CreateFishingCertificateCommand;
import de.adesso.fischereiregister.core.commands.CreateRegularLicenseCommand;
import de.adesso.fischereiregister.core.commands.MoveJurisdictionCommand;
import de.adesso.fischereiregister.core.commands.UnbanCommand;
import de.adesso.fischereiregister.core.events.UnbannedEvent;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.model.user.UserDetails;
import de.adesso.fischereiregister.core.model.user.UserRole;
import de.adesso.fischereiregister.core.testutils.AxonFixture;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import org.axonframework.test.aggregate.AggregateTestFixture;
import org.axonframework.test.matchers.Matchers;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

import static java.util.Collections.emptyList;
import static org.junit.jupiter.api.Assertions.assertNull;

public class UnbanTest {

    private UserDetails givenUserDetails;

    @BeforeEach
    void setUp() {
        givenUserDetails = DomainTestData.createUserDetails(UserRole.OFFICIAL);
    }

    @Test
    @ExtendWith(AxonFixture.class)
    void testUnbanCommandWithPreexistingBan(AggregateTestFixture<RegisterEntry> fixture) {
        //given
        final UUID givenId = UUID.randomUUID();

        fixture
                .givenCommands(
                        getCertificateCommand(givenId),
                        new MoveJurisdictionCommand(givenId, DomainTestData.createJurisdictionConsentInfo(), "anySalt", emptyList(), givenUserDetails),
                        getLicenseCommand(givenId),
                        new BanTemporarilyCommand(givenId, UUID.randomUUID(), "fileNumber", "reportedBy", LocalDate.now().minusYears(2), LocalDate.now().plusYears(1), givenUserDetails)
                )
                .when(new UnbanCommand(givenId, givenUserDetails))
                .expectSuccessfulHandlerExecution().expectEventsMatching(Matchers.exactSequenceOf(Matchers.messageWithPayload(Matchers.matches(payload ->
                        payload.getClass().isAssignableFrom(UnbannedEvent.class)
                ))))
                .expectState(registerEntry ->
                        assertNull(registerEntry.getBan()));
    }

    @Test
    @ExtendWith(AxonFixture.class)
    void testUnbanCommandWithNoExistingBan(AggregateTestFixture<RegisterEntry> fixture) {
        final UUID givenId = UUID.randomUUID();

        fixture
                .givenCommands(
                        getCertificateCommand(givenId),
                        new MoveJurisdictionCommand(givenId, DomainTestData.createJurisdictionConsentInfo(), "anySalt", emptyList(), givenUserDetails),
                        getLicenseCommand(givenId)
                )
                .when(new UnbanCommand(givenId, givenUserDetails))
                .expectException(IllegalStateException.class);
    }

    private CreateFishingCertificateCommand getCertificateCommand(UUID registerEntryId) {
        final Person givenPerson = DomainTestData.createPersonWithAddress();

        return new CreateFishingCertificateCommand(registerEntryId,
                DomainTestData.jan_2024,
                givenPerson,
                DomainTestData.createUserDetails(UserRole.EXAM_DATA_CREATOR));
    }

    private CreateRegularLicenseCommand getLicenseCommand(UUID registerEntryId) {
        final Person givenPerson = DomainTestData.createPersonWithAddress();

        final String givenOriginalSalt = "12345";

        final FishingLicense givenFishingLicense = new FishingLicense();
        givenFishingLicense.setType(LicenseType.REGULAR);
        givenFishingLicense.setNumber("LN12345");

        return new CreateRegularLicenseCommand(registerEntryId, givenOriginalSalt, DomainTestData.createConsentInfo(), givenPerson, List.of(DomainTestData.createAnalogFee()), emptyList(), givenUserDetails);
    }

}
