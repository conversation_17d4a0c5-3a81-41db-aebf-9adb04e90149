package de.adesso.fischereiregister.core.interceptables;

import de.adesso.fischereiregister.core.exceptions.RulesProcessingException;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.model.type.PaymentType;
import de.adesso.fischereiregister.core.ports.TenantInformationPort;
import de.adesso.fischereiregister.core.ports.contracts.LicenseInformation;
import de.adesso.fischereiregister.core.ports.contracts.TaxPriceInformation;

import java.math.BigDecimal;
import java.time.LocalDate;

public class InterceptableTenantInformationPort implements TenantInformationPort {

    @Override
    public TaxPriceInformation getTaxPriceInformation(FederalState federalState, int years, boolean officeFeeAlreadyPayed, PaymentType paymentType) throws RulesProcessingException {
        return new TaxPriceInformation(
                BigDecimal.valueOf(7.0),
                1
        );
    }

    @Override
    public LicenseInformation getLicenseInformation(FederalState federalState, LicenseType licenseType, PaymentType paymentType) throws RulesProcessingException {
        return new LicenseInformation(
                BigDecimal.valueOf(20.0),
                LocalDate.MAX,
                licenseType == LicenseType.VACATION,
                true
        );
    }
}
