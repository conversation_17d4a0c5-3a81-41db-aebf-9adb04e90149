package de.adesso.fischereiregister.core.processors;

import de.adesso.fischereiregister.core.commands.OSReplaceCardCommand;
import de.adesso.fischereiregister.core.events.AxonEvent;
import de.adesso.fischereiregister.core.events.JurisdictionMovedEvent;
import de.adesso.fischereiregister.core.events.ReplacementCardOrderedEvent;
import de.adesso.fischereiregister.core.interceptables.InterceptableDocumentNumberService;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.ValidityPeriod;
import de.adesso.fischereiregister.core.model.consent.ConsentInfo;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.IdentificationDocumentType;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.model.type.PaymentType;
import de.adesso.fischereiregister.core.model.utils.IdentificationDocumentFactoryImpl;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertThrows;

public class OSReplacementCardOrderedCommandProcessorTest {
    private final OSReplacementCardOrderedCommandProcessor creator = new OSReplacementCardOrderedCommandProcessor(new IdentificationDocumentFactoryImpl(new InterceptableDocumentNumberService()));

    @Test
    @DisplayName("createEvents is successful when validity is in the past (no PDF should be reissued).")
    public void testSuccessfulEventCreationWhenValidityIsInPast() {
        // GIVEN
        RegisterEntry registerEntry = buildRegisterEntry(1);
        final OSReplaceCardCommand command = buildCommand();
        registerEntry.setRegisterId(command.registerId());

        // WHEN
        final List<AxonEvent> events = creator.process(command, registerEntry);

        // THEN
        assertEquals(1, events.size());

        final AxonEvent axonEvent = events.get(0);
        assertInstanceOf(ReplacementCardOrderedEvent.class, axonEvent);

        final ReplacementCardOrderedEvent event = (ReplacementCardOrderedEvent) axonEvent;

        assertEquals(command.taxes(), event.taxes());
        assertEquals(FederalState.BE, event.federalState());
        assertEquals(command.fees(), event.fees());
        assertEquals(command.consentInfo(), event.consentInfo());
        assertEquals(command.person(), event.person());
        assertEquals(command.salt(), event.salt());
        assertEquals(command.registerId(), event.registerId());

        final List<IdentificationDocument> documents = event.identificationDocuments();
        assertEquals(2, documents.size());

        final List<IdentificationDocument> cardDocuments = documents.stream()
                .filter(document -> document.getType() == IdentificationDocumentType.CARD)
                .toList();
        assertEquals(1, cardDocuments.size());

        final List<IdentificationDocument> taxDocuments = documents.stream()
                .filter(document -> document.getType() == IdentificationDocumentType.PDF && document.getTax() != null)
                .toList();
        assertEquals(1, taxDocuments.size());

        // Validity of fishing license is in the past, so now license PDF should be reissued
        final List<IdentificationDocument> licensePdfDocuments = documents.stream()
                .filter(document -> document.getType() == IdentificationDocumentType.PDF && document.getFishingLicense() != null)
                .toList();
        assertEquals(0, licensePdfDocuments.size());

    }

    @Test
    @DisplayName("createEvents generates correct documents when the license is currently valid (PDF should also be re issued).")
    public void testSuccessfulEventCreationWhenCurrentlyValid() {
        // GIVEN
        RegisterEntry registerEntry = buildRegisterEntry(1);
        final OSReplaceCardCommand command = buildCommand();
        registerEntry.setRegisterId(command.registerId());
        registerEntry.getFishingLicenses().get(0).getValidityPeriods().get(0).setValidTo(LocalDate.MAX);

        // WHEN
        final List<AxonEvent> events = creator.process(command, registerEntry);

        // THEN
        assertEquals(1, events.size());

        final AxonEvent axonEvent = events.get(0);
        assertInstanceOf(ReplacementCardOrderedEvent.class, axonEvent);

        final ReplacementCardOrderedEvent event = (ReplacementCardOrderedEvent) axonEvent;

        final List<IdentificationDocument> documents = event.identificationDocuments();
        assertEquals(3, documents.size());

        final List<IdentificationDocument> cardDocuments = documents.stream()
                .filter(document -> document.getType() == IdentificationDocumentType.CARD)
                .toList();
        assertEquals(1, cardDocuments.size());

        final List<IdentificationDocument> taxDocuments = documents.stream()
                .filter(document -> document.getType() == IdentificationDocumentType.PDF && document.getTax() != null)
                .toList();
        assertEquals(1, taxDocuments.size());

        final List<IdentificationDocument> licensePdfDocuments = documents.stream()
                .filter(document -> document.getType() == IdentificationDocumentType.PDF && document.getFishingLicense() != null)
                .toList();
        assertEquals(1, licensePdfDocuments.size());

    }

    @Test
    @DisplayName("createEvents whether createFrom generates correct documents when license is valid indefinitely.")
    public void testSuccessfulEventCreationWhenValidToIsNull() {
        // GIVEN
        RegisterEntry registerEntry = buildRegisterEntry(1);
        final OSReplaceCardCommand command = buildCommand();
        registerEntry.setRegisterId(command.registerId());
        registerEntry.getFishingLicenses().get(0).getValidityPeriods().get(0).setValidTo(null);

        // WHEN
        final List<AxonEvent> events = creator.process(command, registerEntry);

        // THEN
        assertEquals(1, events.size());

        final AxonEvent axonEvent = events.get(0);
        assertInstanceOf(ReplacementCardOrderedEvent.class, axonEvent);

        final ReplacementCardOrderedEvent event = (ReplacementCardOrderedEvent) axonEvent;

        final List<IdentificationDocument> documents = event.identificationDocuments();
        assertEquals(3, documents.size());

        final List<IdentificationDocument> licensePdfDocuments = documents.stream()
                .filter(document -> document.getType() == IdentificationDocumentType.PDF && document.getFishingLicense() != null)
                .toList();
        assertEquals(1, licensePdfDocuments.size());

    }

    @Test
    @DisplayName("createEvents is successful even if the license Number does not match.")
    public void testEventCreationWithIncorrectLicenseNumber() {
        // GIVEN
        UUID registerEntryId = UUID.randomUUID();
        RegisterEntry registerEntry = buildRegisterEntry(1);
        OSReplaceCardCommand command = new OSReplaceCardCommand(
                registerEntryId,
                "",
                "something different",
                null,
                DomainTestData.createConsentInfo(),
                List.of(),
                List.of(),
                "",
                FederalState.BE.toString(),
                "",
                "inboxReference"
        );
        registerEntry.setRegisterId(registerEntryId);


        // WHEN
        // THEN
        assertDoesNotThrow(() -> creator.process(command, registerEntry));
    }

    @Test
    @DisplayName("createEvents successful, when multiple licenses are matched via license number")
    public void testEventCreationSuccessfulWhenLicenseDeterminable() {
        // GIVEN
        RegisterEntry registerEntry = buildRegisterEntry(1);
        OSReplaceCardCommand command = buildCommand();
        registerEntry.setRegisterId(command.registerId());

        FishingLicense secondRegularLicense = new FishingLicense();
        secondRegularLicense.setNumber("2");
        secondRegularLicense.setType(LicenseType.REGULAR);
        registerEntry.getFishingLicenses().add(secondRegularLicense);

        // WHEN
        final List<AxonEvent> events = creator.process(command, registerEntry);

        // THEN
        assertEquals(1, events.size());

        final AxonEvent axonEvent = events.get(0);
        assertInstanceOf(ReplacementCardOrderedEvent.class, axonEvent);

        final ReplacementCardOrderedEvent event = (ReplacementCardOrderedEvent) axonEvent;

        // THEN
        assertEquals("1", event.fishingLicense().getNumber());
    }


    @Test
    @DisplayName("createEvents fails, when no matching license can be found (multiple regular licenses)")
    public void testEventCreationFailsWhenLicenseIndeterminable() {
        // GIVEN
        UUID registerEntryId = UUID.randomUUID();
        RegisterEntry registerEntry = buildRegisterEntry(1);
        registerEntry.setRegisterId(registerEntryId);

        FishingLicense secondRegularLicense = new FishingLicense();
        secondRegularLicense.setNumber("2");
        secondRegularLicense.setType(LicenseType.REGULAR);
        registerEntry.getFishingLicenses().add(secondRegularLicense);

        OSReplaceCardCommand command = new OSReplaceCardCommand(
                registerEntryId,
                "",
                "something different",
                null,
                DomainTestData.createConsentInfo(),
                List.of(),
                List.of(),
                "",
                FederalState.BE.toString(),
                "",
                "inboxReference"
        );


        // WHEN
        IllegalStateException exception = assertThrows(IllegalStateException.class, () -> creator.process(command, registerEntry));

        // THEN
        assertEquals("Tried reordering regular fishing license card, but no matching card could be found.", exception.getMessage());
    }


    @Test
    @DisplayName("createEvents fails, when no matching license can be found (no regular licenses)")
    public void testEventCreationFailsWhenNoRegularLicensesExists() {
        // GIVEN
        RegisterEntry registerEntry = buildRegisterEntry(1, LicenseType.VACATION);
        OSReplaceCardCommand command = buildCommand();
        registerEntry.setRegisterId(command.registerId());


        // WHEN
        IllegalStateException exception = assertThrows(IllegalStateException.class, () -> creator.process(command, registerEntry));

        // THEN
        assertEquals("Tried reordering regular fishing license card, but no matching card could be found.", exception.getMessage());

    }

    @Test
    @DisplayName("createEvents creates an additional JurisdictionMoved event if the federal state of the register entry jurisdiction is not matching.")
    public void testEventCreationForImplicitJurisdictionChange() {
        // GIVEN
        RegisterEntry registerEntry = buildRegisterEntry(1);
        final OSReplaceCardCommand command = buildCommand();
        registerEntry.setRegisterId(command.registerId());
        registerEntry.getJurisdiction().setFederalState(FederalState.SH.toString());

        // WHEN
        final List<AxonEvent> events = creator.process(command, registerEntry);

        // THEN
        assertEquals(2, events.size());

        final AxonEvent axonEvent1 = events.get(0);
        assertInstanceOf(JurisdictionMovedEvent.class, axonEvent1);

        final AxonEvent axonEvent2 = events.get(1);
        assertInstanceOf(ReplacementCardOrderedEvent.class, axonEvent2);

        final JurisdictionMovedEvent movedEvent = (JurisdictionMovedEvent) axonEvent1;

        assertEquals(0, movedEvent.identificationDocuments().size());
        assertEquals(0, movedEvent.taxes().size());
        assertEquals(FederalState.SH.toString(), movedEvent.previousJurisdiction().getFederalState());
        assertEquals(FederalState.BE.toString(), movedEvent.newJurisdiction().getFederalState());
        assertEquals(true, movedEvent.consentInfo().getProofOfMoveVerified());
    }

    private OSReplaceCardCommand buildCommand() {
        UUID registerEntryId = UUID.randomUUID();

        Tax tax = new Tax();
        tax.setValidFrom(LocalDate.MIN);
        tax.setValidTo(LocalDate.MAX);
        tax.setFederalState(FederalState.BE.toString());
        tax.setPaymentInfo(DomainTestData.createPaymentInfo(27.0, PaymentType.ONLINE));

        ConsentInfo consentInfo = new ConsentInfo();
        consentInfo.setGdprAccepted(true);
        consentInfo.setSelfDisclosureAccepted(true);
        consentInfo.setSubmittedByThirdParty(false);

        return new OSReplaceCardCommand(
                registerEntryId,
                "",
                "1",
                null,
                consentInfo,
                List.of(tax),
                List.of(),
                "",
                FederalState.BE.toString(),
                "",
                "inboxReference"
        );
    }

    private RegisterEntry buildRegisterEntry(int numberOfValidityPeriods) {
        return buildRegisterEntry(numberOfValidityPeriods, LicenseType.REGULAR);
    }

    private RegisterEntry buildRegisterEntry(int numberOfValidityPeriods, LicenseType licenseType) {
        RegisterEntry registerEntry = new RegisterEntry();

        Person person = new Person();
        registerEntry.setPerson(person);

        Jurisdiction jurisdiction = new Jurisdiction();
        jurisdiction.setFederalState(FederalState.BE.toString());
        registerEntry.setJurisdiction(jurisdiction);

        FishingLicense fishingLicense = new FishingLicense();
        fishingLicense.setNumber("1");
        fishingLicense.setType(licenseType);

        List<ValidityPeriod> periods = new ArrayList<>();
        for (int i = 0; i < numberOfValidityPeriods; i++) {
            ValidityPeriod validityPeriod = new ValidityPeriod();
            validityPeriod.setValidFrom(LocalDate.of((i + 1) * 1000, 1, 1));
            validityPeriod.setValidTo(LocalDate.of((i + 2) * 1000, 1, 1));
            periods.add(validityPeriod);
        }
        fishingLicense.setValidityPeriods(periods);

        registerEntry.getFishingLicenses().add(fishingLicense);

        return registerEntry;
    }
}
