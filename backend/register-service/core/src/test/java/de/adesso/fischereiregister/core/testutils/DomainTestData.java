package de.adesso.fischereiregister.core.testutils;

import de.adesso.fischereiregister.core.model.Address;
import de.adesso.fischereiregister.core.model.Birthdate;
import de.adesso.fischereiregister.core.model.Fee;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import de.adesso.fischereiregister.core.model.LimitedLicenseApplication;
import de.adesso.fischereiregister.core.model.LimitedLicenseApproval;
import de.adesso.fischereiregister.core.model.PaymentInfo;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.QualificationsProof;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.SigningEmployee;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.ValidityPeriod;
import de.adesso.fischereiregister.core.model.consent.ConsentInfo;
import de.adesso.fischereiregister.core.model.consent.JurisdictionConsentInfo;
import de.adesso.fischereiregister.core.model.consent.LimitedLicenseConsentInfo;
import de.adesso.fischereiregister.core.model.consent.TaxConsentInfo;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.model.type.LimitedLicenseApplicationStatus;
import de.adesso.fischereiregister.core.model.type.PaymentType;
import de.adesso.fischereiregister.core.model.type.QualificationsProofType;
import de.adesso.fischereiregister.core.model.user.UserDetails;
import de.adesso.fischereiregister.core.model.user.UserRole;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.UUID;

public class DomainTestData {
    public static final String GERMAN_DATE_TIME_PATTERN = "dd.MM.yyyy";
    public static final DateTimeFormatter GERMAN_DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern(GERMAN_DATE_TIME_PATTERN);
    public final static LocalDate validFrom_Jan_2025 = LocalDate.of(2025, 1, 1);
    public final static UUID registerId = UUID.fromString("12345678-1234-1234-1234-123456789012");

    protected final static String title = "DR.";
    protected final static String firstName = "Max";
    protected final static String lastName = "Mustermann";
    protected final static String birthName = "Musterwar";
    protected final static String birthPlace = "Berlin";
    protected final static String nationality = "deutsch";
    protected final static LocalDate birthDate = LocalDate.of(1990, 5, 20);
    protected final static String street = "Street";
    protected final static String streetNumber = "1";
    protected final static String postalCode = "10000";
    protected final static String city = "Kiel";
    protected final static LocalDate validTo_Dec_2025 = LocalDate.of(2025, 12, 31);
    protected final static String federalState = "SH";
    protected final static String identificationDocumentId = "12345678-1234-1234-1234-123456789012";
    public static String userIdFromKeycloak = "Marc der Examiner";
    public static String testFederalState = "SH";
    public static LocalDate jan_2024 = LocalDate.of(2024, 1, 1);
    public static LocalDate passedOnDate = LocalDate.of(2024, 1, 1);
    public static String issuedBy = "Fischfreunde Övelgönne e.V.";
    public static String getIssuedByOfficeAddress = "Fischereibehörde Kiel, Fleethörn 29-31, 24103 Kiel"; // not in correct format
    public static String licenseNumber = "SH12345678901234";
    public static String fishingCertificateId = "4711";

    public static Person createPerson() {
        final Person returnPerson = new Person();
        returnPerson.setTitle(title);
        returnPerson.setFirstname(firstName);
        returnPerson.setLastname(lastName);
        returnPerson.setBirthname(birthName);
        returnPerson.setBirthdate(Birthdate.parse(birthDate.format(DomainTestData.GERMAN_DATE_TIME_FORMATTER)));
        returnPerson.setBirthplace(birthPlace);
        returnPerson.setNationality(nationality);

        return returnPerson;
    }

    public static Person createPersonWithAddress() {
        final Person person = createPerson();
        person.setAddress(DomainTestData.createAddress());
        return person;
    }

    public static Address createAddress() {
        final Address address = new Address();
        address.setStreet(street);
        address.setStreetNumber(streetNumber);
        address.setPostcode(postalCode);
        address.setCity(city);
        return address;
    }

    public static ConsentInfo createConsentInfo() {
        final ConsentInfo returnConsentInfo = new ConsentInfo();
        returnConsentInfo.setGdprAccepted(true);
        returnConsentInfo.setSubmittedByThirdParty(true);
        returnConsentInfo.setSelfDisclosureAccepted(true);

        return returnConsentInfo;
    }

    public static Jurisdiction createJurisdiction() {
        final Jurisdiction returnJurisdiction = new Jurisdiction();
        returnJurisdiction.setFederalState(federalState);

        return returnJurisdiction;
    }

    public static JurisdictionConsentInfo createJurisdictionConsentInfo() {
        final JurisdictionConsentInfo returnConsentInfo = new JurisdictionConsentInfo();
        returnConsentInfo.setGdprAccepted(true);
        returnConsentInfo.setSubmittedByThirdParty(true);
        returnConsentInfo.setSelfDisclosureAccepted(true);
        returnConsentInfo.setProofOfMoveVerified(true);

        return returnConsentInfo;
    }

    public static LimitedLicenseConsentInfo createLimitedLicenseConsentInfo() {
        final LimitedLicenseConsentInfo returnConsentInfo = new LimitedLicenseConsentInfo();
        returnConsentInfo.setGdprAccepted(true);
        returnConsentInfo.setSubmittedByThirdParty(true);
        returnConsentInfo.setSelfDisclosureAccepted(true);
        returnConsentInfo.setDisablityCertificateVerified(true);

        return returnConsentInfo;
    }

    public static LimitedLicenseApproval createLimitedLicenseApproval() {
        final LimitedLicenseApproval limitedLicenseApproval = new LimitedLicenseApproval();

        limitedLicenseApproval.setLimitedLicenseApprovalId(UUID.randomUUID());
        limitedLicenseApproval.setCreatedAt(LocalDate.now());
        limitedLicenseApproval.setFileNumber("FILE-2024-001");
        limitedLicenseApproval.setCashRegisterSign("CASH-001");
        limitedLicenseApproval.setJustificationForLimitedDurationNotice("Medical condition requires limited duration");

        SigningEmployee signingEmployee = new SigningEmployee();
        signingEmployee.setName("John Doe");
        signingEmployee.setEmail("<EMAIL>");
        signingEmployee.setPhone("+49123456789");
        signingEmployee.setPersonalSign("JD-001");

        limitedLicenseApproval.setSigningEmployee(signingEmployee);

        return limitedLicenseApproval;
    }

    public static TaxConsentInfo createTaxConsentInfo() {
        final TaxConsentInfo returnConsentInfo = new TaxConsentInfo();
        returnConsentInfo.setGdprAccepted(true);
        returnConsentInfo.setSubmittedByThirdParty(true);

        return returnConsentInfo;
    }

    public static PaymentInfo createPaymentInfo(Double amount, PaymentType paymentType) {
        final PaymentInfo paymentInfo = new PaymentInfo();
        paymentInfo.setAmount(amount);
        paymentInfo.setType(paymentType);
        return paymentInfo;
    }

    public static Tax createAnalogTax() {
        final Tax tax = new Tax();

        tax.setPaymentInfo(DomainTestData.createPaymentInfo(31.0, PaymentType.CASH));
        tax.setFederalState(federalState);
        tax.setValidFrom(birthDate);

        int currentYear = LocalDate.now().getYear();
        tax.setValidFrom(LocalDate.of(currentYear, 1, 1));
        tax.setValidTo(LocalDate.of(currentYear, 12, 31));
        return tax;
    }

    public static Tax createOnlineTax() {
        final Tax tax = new Tax();

        tax.setPaymentInfo(DomainTestData.createPaymentInfo(31.0, PaymentType.ONLINE));
        tax.setFederalState(federalState);
        tax.setValidFrom(birthDate);

        int currentYear = LocalDate.now().getYear();
        tax.setValidFrom(LocalDate.of(currentYear, 1, 1));
        tax.setValidTo(LocalDate.of(currentYear, 12, 31));
        return tax;
    }

    public static Tax createPreviouslyPayedTax() {
        final Tax tax = createAnalogTax();
        tax.setPaymentInfo(DomainTestData.createPaymentInfo(0.0, PaymentType.CASH));
        return tax;
    }

    public static List<Tax> createAnalogTaxesWithOneTax() {
        final List<Tax> taxes = new ArrayList<>();

        final Tax tax = DomainTestData.createAnalogTax();
        taxes.add(tax);
        return taxes;
    }

    public static List<Fee> createAnalogFeesWithOneFee() {
        final List<Fee> fees = new ArrayList<>();

        final Fee fee = DomainTestData.createAnalogFee();
        fees.add(fee);
        return fees;
    }

    public static Fee createAnalogFee() {
        final Fee fee = new Fee();

        fee.setPaymentInfo(DomainTestData.createPaymentInfo(20.0, PaymentType.CASH));
        fee.setFederalState(federalState);
        fee.setValidFrom(birthDate);
        fee.setValidFrom(validFrom_Jan_2025);
        fee.setValidTo(validTo_Dec_2025);
        return fee;
    }

    public static Fee createDigitalFee() {
        final Fee fee = new Fee();

        fee.setPaymentInfo(DomainTestData.createPaymentInfo(20.0, PaymentType.ONLINE));
        fee.setFederalState(federalState);
        fee.setValidFrom(birthDate);
        fee.setValidFrom(validFrom_Jan_2025);
        fee.setValidTo(validTo_Dec_2025);
        return fee;
    }

    public static UserDetails createUserDetails(UserRole userRole) {
        if (userRole == UserRole.ONLINE_SERVICE) {
            return new UserDetails("userId", "SH", null, null, null, Set.of(userRole));
        }

        return new UserDetails(
                DomainTestData.userIdFromKeycloak,
                DomainTestData.federalState,
                DomainTestData.issuedBy,
                DomainTestData.getIssuedByOfficeAddress,
                DomainTestData.issuedBy,
                Set.of(userRole));
    }

    public static RegisterEntry createRegisterEntry() {
        final RegisterEntry registerEntry = new RegisterEntry();

        registerEntry.setRegisterId(registerId);
        registerEntry.setPerson(createPerson());
        registerEntry.setJurisdiction(DomainTestData.createJurisdiction());

        final List<Tax> taxes = createAnalogTaxesWithOneTax();
        registerEntry.getTaxes().addAll(taxes);

        final List<Fee> fees = createAnalogFeesWithOneFee();
        registerEntry.getFees().addAll(fees);

        final FishingLicense fishingLicense = createLicense();

        final IdentificationDocument identificationDocument = new IdentificationDocument();
        identificationDocument.setDocumentId(identificationDocumentId);
        identificationDocument.setFishingLicense(fishingLicense);


        registerEntry.getIdentificationDocuments().add(identificationDocument);
        registerEntry.getFishingLicenses().add(fishingLicense);

        return registerEntry;
    }

    public static ValidityPeriod createValidityPeriod(LocalDate validFrom, LocalDate validTo) {
        final ValidityPeriod validityPeriod = new ValidityPeriod();
        validityPeriod.setValidFrom(validFrom);
        validityPeriod.setValidTo(validTo);
        return validityPeriod;
    }

    public static FishingLicense createLicense() {
        final FishingLicense fishingLicense = new FishingLicense();
        fishingLicense.setNumber(licenseNumber);
        fishingLicense.setIssuingFederalState(FederalState.SH);
        fishingLicense.setType(LicenseType.REGULAR);

        ValidityPeriod validityPeriod = new ValidityPeriod();
        validityPeriod.setValidFrom(LocalDate.of(2024, 1, 1));
        validityPeriod.setValidTo(LocalDate.now().plusYears(1));
        fishingLicense.getValidityPeriods().add(validityPeriod);

        return fishingLicense;
    }

    public static ValidityPeriod createValidityPeriod() {
        final ValidityPeriod validityPeriod = new ValidityPeriod();
        validityPeriod.setValidFrom(LocalDate.of(2024, 2, 1));
        validityPeriod.setValidTo(LocalDate.of(2024, 2, 28));
        return validityPeriod;
    }

    public static QualificationsProof createQualificationsProof() {
        final QualificationsProof qualificationsProof = new QualificationsProof();
        qualificationsProof.setFishingCertificateId(fishingCertificateId);
        qualificationsProof.setIssuedBy("Fischfreunde Övelgönne e.V.");
        qualificationsProof.setPassedOn(LocalDate.now().minusYears(1));
        qualificationsProof.setFederalState("SH");
        qualificationsProof.setType(QualificationsProofType.CERTIFICATE);
        return qualificationsProof;
    }

    public static LimitedLicenseApplication createLimitedLicenseApplication() {
        LimitedLicenseApplication limitedLicenseApplication = new LimitedLicenseApplication();

        limitedLicenseApplication.setId(UUID.randomUUID());
        limitedLicenseApplication.setStatus(LimitedLicenseApplicationStatus.PENDING);
        limitedLicenseApplication.setCreatedAt(LocalDate.of(2024, 1, 1));
        limitedLicenseApplication.setDisabilityCertificateFileURL("url");
        limitedLicenseApplication.setFederalState(FederalState.SH);

        return limitedLicenseApplication;
    }
}
