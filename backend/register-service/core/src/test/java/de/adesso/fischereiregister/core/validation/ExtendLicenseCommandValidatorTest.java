package de.adesso.fischereiregister.core.validation;

import de.adesso.fischereiregister.core.commands.ExtendLicenseCommand;
import de.adesso.fischereiregister.core.exceptions.ClientInputValidationException;
import de.adesso.fischereiregister.core.exceptions.RulesProcessingException;
import de.adesso.fischereiregister.core.exceptions.SystemConfigValidationException;
import de.adesso.fischereiregister.core.interceptables.InterceptableCountryService;
import de.adesso.fischereiregister.core.interceptables.InterceptableTenantRulesValidationPort;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.ValidityPeriod;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.model.user.UserDetails;
import de.adesso.fischereiregister.core.model.user.UserRole;
import de.adesso.fischereiregister.core.ports.CountryService;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import de.adesso.fischereiregister.core.validation.helper.LicenseValidatorHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

class ExtendLicenseCommandValidatorTest {

    private ExtendLicenseCommandValidator commandValidator;
    private RegisterEntry registerEntry;

    private LicenseValidatorHelper licenseValidatorHelper;

    private static final String EXTENDABLE_LICENSE_NUMBER = "extendable";
    private static final String NON_EXTENDABLE_LICENSE_NUMBER = "non-extendable";

    @BeforeEach
    void setUp() {
        final CountryService countryService = new InterceptableCountryService();
        final InterceptableTenantRulesValidationPort tenantRulesValidationPort = new InterceptableTenantRulesValidationPort();

        licenseValidatorHelper = mock(LicenseValidatorHelper.class);
        commandValidator = new ExtendLicenseCommandValidator(countryService, tenantRulesValidationPort, licenseValidatorHelper);

        registerEntry = buildRegisterEntry();
    }

    @Test
    @DisplayName("ExtendLicenseCommandValidator.validateOrThrow should not throw any error for a valid command")
    public void testValidateOrThrow_validCommand() throws RulesProcessingException {
        // GIVEN
        final ValidityPeriod validityPeriod = DomainTestData.createValidityPeriod();
        final ExtendLicenseCommand command = new ExtendLicenseCommand(
                UUID.randomUUID(),
                EXTENDABLE_LICENSE_NUMBER,
                "salt",
                DomainTestData.createPerson(),
                List.of(DomainTestData.createAnalogFee()),
                List.of(DomainTestData.createAnalogTax()),
                validityPeriod,
                DomainTestData.createConsentInfo(),
                DomainTestData.createUserDetails(UserRole.OFFICIAL)
        );

        // WHEN
        // THEN
        assertDoesNotThrow(() -> commandValidator.validateOrThrow(command, registerEntry));
        verify(licenseValidatorHelper, times(1))
                .validateLicenseIsExtendable(
                        eq(registerEntry),
                        eq(EXTENDABLE_LICENSE_NUMBER),
                        eq(validityPeriod),
                        eq(FederalState.SH),
                        eq(DomainTestData.createAnalogFee().getPaymentInfo().getType()),
                        eq(commandValidator.validationResult));
    }

    @Test
    @DisplayName("ExtendLicenseCommandValidator.validateOrThrow should throw a ClientInputValidationException with the respective error messages for an invalid command")
    public void testValidateOrThrow_invalidClientInputCommand() {
        // GIVEN
        final ExtendLicenseCommand invalidCommand = new ExtendLicenseCommand(
                UUID.randomUUID(),
                EXTENDABLE_LICENSE_NUMBER,
                "salt",
                null,
                List.of(),
                List.of(),
                null,
                null,
                DomainTestData.createUserDetails(UserRole.OFFICIAL)
        );

        // WHEN
        // THEN
        ClientInputValidationException exception = assertThrows(ClientInputValidationException.class, () -> commandValidator.validateOrThrow(invalidCommand, registerEntry));
        ValidationResult result = exception.getValidationResult();

        assertTrue(result.hasErrors());
        assertEquals(4, result.getErrorNotes().size());
        assertTrue(result.getErrorNotes().contains("Person Details is required"));
        assertTrue(result.getErrorNotes().contains("Fees are required"));
        assertTrue(result.getErrorNotes().contains("consentInfo is required"));
        assertTrue(result.getErrorNotes().contains("Validity Period is required"));
    }

    @Test
    @DisplayName("ExtendLicenseCommandValidator.validateOrThrow should throw a SystemConfigValidationException when user details are invalid or salt is missing")
    public void testValidateOrThrow_invalidSystemConfigCommand() {
        // GIVEN
        final ExtendLicenseCommand invalidCommand = new ExtendLicenseCommand(
                UUID.randomUUID(),
                EXTENDABLE_LICENSE_NUMBER,
                "",
                DomainTestData.createPerson(),
                List.of(DomainTestData.createAnalogFee()),
                List.of(DomainTestData.createAnalogTax()),
                DomainTestData.createValidityPeriod(),
                DomainTestData.createConsentInfo(),
                null
        );

        // WHEN
        // THEN
        SystemConfigValidationException exception = assertThrows(SystemConfigValidationException.class, () -> commandValidator.validateOrThrow(invalidCommand, registerEntry));
        ValidationResult result = exception.getValidationResult();

        assertTrue(result.hasErrors());
        assertEquals(2, result.getErrorNotes().size());
        assertTrue(result.getErrorNotes().contains("User Details is required"));
        assertTrue(result.getErrorNotes().contains("Salt is required"));
    }

    @Test
    @DisplayName("ExtendLicenseCommandValidator.validateOrThrow should throw a SystemConfigValidationException  when user details are present but invalid")
    public void testValidateOrThrow_missingOffice() {
        // GIVEN
        // Create user details with null office
       UserDetails userDetailsWithoutOffice = new UserDetails(null,null,null,null,null, Set.of(UserRole.OFFICIAL));

        final ExtendLicenseCommand invalidCommand = new ExtendLicenseCommand(
                UUID.randomUUID(),
                EXTENDABLE_LICENSE_NUMBER,
                UUID.randomUUID().toString(),
                DomainTestData.createPerson(),
                List.of(DomainTestData.createAnalogFee()),
                List.of(DomainTestData.createAnalogTax()),
                DomainTestData.createValidityPeriod(),
                DomainTestData.createConsentInfo(),
                userDetailsWithoutOffice
        );

        // WHEN
        SystemConfigValidationException exception = assertThrows(SystemConfigValidationException.class, () -> commandValidator.validateOrThrow(invalidCommand, registerEntry));

        // THEN
        ValidationResult result = exception.getValidationResult();
        assertTrue(result.hasErrors());
        assertTrue(result.getErrorNotes().contains("User Office is required"));
    }

    private RegisterEntry buildRegisterEntry() {
        RegisterEntry registerEntry = new RegisterEntry();

        registerEntry.setRegisterId(UUID.randomUUID());
        registerEntry.setPerson(DomainTestData.createPerson());

        final var licenses = Stream.of(true, false).map(this::buildVacationLicense).toList();
        registerEntry.getFishingLicenses().addAll(licenses);

        return registerEntry;
    }

    private FishingLicense buildVacationLicense(boolean isExtendable) {
        FishingLicense fishingLicense = new FishingLicense();
        fishingLicense.setIssuingFederalState(FederalState.SH);
        fishingLicense.setType(LicenseType.VACATION);

        final String licenseNumber = isExtendable ? EXTENDABLE_LICENSE_NUMBER : NON_EXTENDABLE_LICENSE_NUMBER;
        fishingLicense.setNumber(licenseNumber);

        fishingLicense.getValidityPeriods().add(DomainTestData.createValidityPeriod(
                LocalDate.of(2021, 4, 1),
                LocalDate.of(2021, 4, 27)
        ));

        if (!isExtendable) {
            ValidityPeriod secondPeriod = new ValidityPeriod();
            secondPeriod.setValidFrom(LocalDate.of(2021, 4, 28));
            secondPeriod.setValidTo(LocalDate.of(2021, 5, 24));
            fishingLicense.getValidityPeriods().add(secondPeriod);
        }

        return fishingLicense;
    }


}