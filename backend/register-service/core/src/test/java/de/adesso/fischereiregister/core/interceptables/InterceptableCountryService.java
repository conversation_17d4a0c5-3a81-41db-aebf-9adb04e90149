package de.adesso.fischereiregister.core.interceptables;

import de.adesso.fischereiregister.core.ports.CountryService;

public class InterceptableCountryService  implements CountryService, ResetableAdapter {
    @Override
    public void reset() {

    }

    @Override
    public boolean isNationalityValid(String nationalitySearchValue) {
        if(nationalitySearchValue != null && !nationalitySearchValue.isEmpty()) {

            if(nationalitySearchValue.equalsIgnoreCase("deutsch")) {
                return true;
            }
            if(nationalitySearchValue.equalsIgnoreCase("französisch")) {
                return true;
            }
            if(nationalitySearchValue.equalsIgnoreCase("kolumbianisch")) {
                return true;
            }
            if(nationalitySearchValue.equalsIgnoreCase("rumänisch")) {
                return true;
            }
        }
        return false;
    }
}
