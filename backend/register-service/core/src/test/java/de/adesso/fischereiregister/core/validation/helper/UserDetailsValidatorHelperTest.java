package de.adesso.fischereiregister.core.validation.helper;

import de.adesso.fischereiregister.core.model.user.UserDetails;
import de.adesso.fischereiregister.core.model.user.UserRole;
import de.adesso.fischereiregister.core.validation.ValidationResult;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

class UserDetailsValidatorHelperTest {

    @Test
    @DisplayName("UserDetailsValidatorHelper.validate should add error when userDetails is null")
    void testValidate_UserDetailsIsNull() {
        // Given
        ValidationResult validationResult = new ValidationResult();

        // When
        UserDetailsValidatorHelper.validate(null, validationResult);

        // Then
        assertTrue(validationResult.hasErrors());
        assertEquals(1, validationResult.getErrorNotes().size());
        assertTrue(validationResult.getErrorNotes().contains("User Details is required"));
    }

    @Test
    @DisplayName("UserDetailsValidatorHelper.validate should add error when userId is missing")
    void testValidate_UserIdIsMissing() {
        // Given
        UserDetails userDetails = new UserDetails(null, "SH", "issuer", "issuer address", "certification issuer", List.of(UserRole.OFFICIAL));
        ValidationResult validationResult = new ValidationResult();

        // When
        UserDetailsValidatorHelper.validate(userDetails, validationResult);

        // Then
        assertTrue(validationResult.hasErrors());
        assertEquals(1, validationResult.getErrorNotes().size());
        assertTrue(validationResult.getErrorNotes().contains("User Id is required"));
    }

    @Test
    @DisplayName("UserDetailsValidatorHelper.validate should not add errors when userDetails is valid")
    void testValidate_UserDetailsIsValid() {
        // Given
        UserDetails userDetails = new UserDetails("userId", "SH", "issuer", "issuer address", "certification issuer", List.of(UserRole.OFFICIAL));
        ValidationResult validationResult = new ValidationResult();

        // When
        UserDetailsValidatorHelper.validate(userDetails, validationResult);

        // Then
        assertFalse(validationResult.hasErrors());
    }
}
