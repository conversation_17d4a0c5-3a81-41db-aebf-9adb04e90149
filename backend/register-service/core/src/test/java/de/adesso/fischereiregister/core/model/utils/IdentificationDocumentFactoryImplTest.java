package de.adesso.fischereiregister.core.model.utils;

import de.adesso.fischereiregister.core.interceptables.InterceptableDocumentNumberService;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.LimitedLicenseApproval;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.ValidityPeriod;
import de.adesso.fischereiregister.core.model.type.IdentificationDocumentType;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.ports.DocumentNumberService;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

public class IdentificationDocumentFactoryImplTest {


    DocumentNumberService documentNumberService = new InterceptableDocumentNumberService();
    IdentificationDocumentFactory identificationDocumentFactory = new IdentificationDocumentFactoryImpl(documentNumberService);

    @Test
    @DisplayName("Test whether createIdentificationDocumentForTax is successful vor normal tax.")
    public void testTaxDocumentGeneration() {
        Tax tax = new Tax();
        tax.setTaxId("1");
        tax.setFederalState("HH");
        tax.setValidTo(LocalDate.of(2020, 1, 1));
        tax.setValidTo(LocalDate.of(2020, 12, 31));

        final IdentificationDocument document = identificationDocumentFactory.createIdentificationDocumentForTax(tax, DomainTestData.registerId);

        assertNotNull(document.getDocumentId());
        assertEquals(IdentificationDocumentType.PDF, document.getType());

        assertNull(document.getFishingLicense());
        assertEquals(tax, document.getTax());

        assertEquals(tax.getValidFrom(), document.getValidFrom());
        assertEquals(tax.getValidTo(), document.getValidTo());

        assertEquals(LocalDate.now(), document.getIssuedDate());
    }

    @Test
    @DisplayName("Test whether createPDFDocumentForValidityPeriod is successful for indefinite license.")
    public void testLicensePDFGenerationForRegularLicense() {
        FishingLicense fishingLicense = new FishingLicense();
        fishingLicense.setNumber("1");


        ValidityPeriod validityPeriod = new ValidityPeriod();
        validityPeriod.setValidFrom(LocalDate.of(2020, 1, 1));
        validityPeriod.setValidTo(null);
        fishingLicense.setValidityPeriods(List.of(validityPeriod));

        IdentificationDocument document = identificationDocumentFactory.createPDFDocumentForValidityPeriod(fishingLicense, validityPeriod, DomainTestData.registerId);

        assertEquals(IdentificationDocumentType.PDF, document.getType());
        assertNotNull(document.getDocumentId());

        assertNull(document.getTax());
        assertEquals(fishingLicense, document.getFishingLicense());

        assertEquals(validityPeriod.getValidFrom(), document.getValidFrom());
        assertNull(document.getValidTo());

        assertEquals(LocalDate.now(), document.getIssuedDate());
    }

    @Test
    @DisplayName("Test whether createIdentificationDocumentCardForFishingLicense is successful for regular indefinite license.")
    public void testLicenseCardGeneration() {
        FishingLicense fishingLicense = new FishingLicense();
        fishingLicense.setNumber("1");

        fishingLicense.getValidityPeriods().add(DomainTestData.createValidityPeriod(LocalDate.MIN, null));

        IdentificationDocument document = identificationDocumentFactory.createIdentificationDocumentCardForFishingLicense(fishingLicense, DomainTestData.registerId);

        assertNotNull(document.getDocumentId());
        assertEquals(IdentificationDocumentType.CARD, document.getType());

        assertEquals(fishingLicense, document.getFishingLicense());
        assertEquals(LocalDate.MIN, document.getValidFrom());

        assertEquals(LocalDate.now(), document.getIssuedDate());
    }

    @Test
    @DisplayName("Test whether createInitialLicenseDocuments is successful for regular indefinite license.")
    public void testInitialLicenseDocumentGenerationWithCard() {
        FishingLicense fishingLicense = new FishingLicense();
        fishingLicense.setType(LicenseType.REGULAR);
        fishingLicense.setNumber("1");
        fishingLicense.getValidityPeriods().add(DomainTestData.createValidityPeriod(LocalDate.MIN, null));

        List<IdentificationDocument> documents = identificationDocumentFactory.createInitialLicenseDocuments(fishingLicense, DomainTestData.registerId);

        assertEquals(2, documents.size());

        IdentificationDocument cardDocument = documents.stream()
                .filter(document -> document.getType() == IdentificationDocumentType.CARD)
                .findAny()
                .orElse(null);
        assertNotNull(cardDocument);

        IdentificationDocument pdfDocument = documents.stream()
                .filter(document -> document.getType() == IdentificationDocumentType.PDF)
                .findAny()
                .orElse(null);
        assertNotNull(pdfDocument);
    }

    @Test
    @DisplayName("Test whether createInitialLicenseDocuments does not create card document for vacation license.")
    public void testInitialLicenseDocumentGenerationWithoutCard() {
        FishingLicense fishingLicense = new FishingLicense();
        fishingLicense.setType(LicenseType.VACATION);
        fishingLicense.setNumber("1");
        fishingLicense.getValidityPeriods().add(DomainTestData.createValidityPeriod(LocalDate.MIN, null));

        List<IdentificationDocument> documents = identificationDocumentFactory.createInitialLicenseDocuments(fishingLicense, DomainTestData.registerId);

        assertEquals(1, documents.size());

        IdentificationDocument cardDocument = documents.stream()
                .filter(document -> document.getType() == IdentificationDocumentType.CARD)
                .findAny()
                .orElse(null);
        assertNull(cardDocument);

        IdentificationDocument pdfDocument = documents.stream()
                .filter(document -> document.getType() == IdentificationDocumentType.PDF)
                .findAny()
                .orElse(null);
        assertNotNull(pdfDocument);
    }

    @Test
    @DisplayName("Test whether createInitialLicenseDocuments creates multiple pdf documents when multiple validity periods are submitted.")
    public void testInitialLicenseGenerationWithMultipleValidityPeriods() {
        FishingLicense fishingLicense = new FishingLicense();
        fishingLicense.setType(LicenseType.VACATION);
        fishingLicense.setNumber("1");

        ValidityPeriod period1 = new ValidityPeriod();
        period1.setValidFrom(LocalDate.of(2020, 1, 1));
        period1.setValidTo(LocalDate.of(2020, 1, 14));
        ValidityPeriod period2 = new ValidityPeriod();
        period2.setValidFrom(LocalDate.of(2020, 2, 1));
        period2.setValidTo(LocalDate.of(2020, 2, 14));

        fishingLicense.setValidityPeriods(List.of(period1, period2));

        List<IdentificationDocument> documents = identificationDocumentFactory.createInitialLicenseDocuments(fishingLicense, DomainTestData.registerId);

        assertEquals(2, documents.size());

        IdentificationDocument cardDocument = documents.stream()
                .filter(document -> document.getType() == IdentificationDocumentType.CARD)
                .findAny()
                .orElse(null);
        assertNull(cardDocument);

        List<IdentificationDocument> pdfDocuments = documents.stream()
                .filter(document -> document.getType() == IdentificationDocumentType.PDF)
                .toList();
        assertEquals(2, pdfDocuments.size());
    }

    @Test
    void testCreateInitialLicenseDocumentsForLimitedLicense() {
        // Arrange
        UUID registerEntryId = UUID.randomUUID();

        FishingLicense fishingLicense = new FishingLicense();
        fishingLicense.setType(LicenseType.LIMITED);

        ValidityPeriod validityPeriod = new ValidityPeriod();
        validityPeriod.setValidFrom(LocalDate.of(2023, 1, 1));
        validityPeriod.setValidTo(LocalDate.of(2023, 12, 31));
        fishingLicense.setValidityPeriods(List.of(validityPeriod));

        LimitedLicenseApproval limitedLicenseApproval = new LimitedLicenseApproval();
        fishingLicense.setLimitedLicenseApproval(limitedLicenseApproval);

        // Act
        List<IdentificationDocument> documents = identificationDocumentFactory.createInitialLicenseDocuments(fishingLicense, registerEntryId);

        // Assert
        assertNotNull(documents);
        assertEquals(3, documents.size());

        IdentificationDocument pdfDocument = documents.stream()
                .filter(doc -> doc.getType() == IdentificationDocumentType.PDF && doc.getLimitedLicenseApproval() == null)
                .findFirst()
                .orElse(null);
        assertNotNull(pdfDocument);
        assertEquals(validityPeriod.getValidFrom(), pdfDocument.getValidFrom());
        assertEquals(validityPeriod.getValidTo(), pdfDocument.getValidTo());

        IdentificationDocument cardDocument = documents.stream()
                .filter(doc -> doc.getType() == IdentificationDocumentType.CARD)
                .findFirst()
                .orElse(null);
        assertNotNull(cardDocument);

        IdentificationDocument limitedLicenseApprovalDocument = documents.stream()
                .filter(doc -> doc.getType() == IdentificationDocumentType.PDF && doc.getLimitedLicenseApproval() != null)
                .findFirst()
                .orElse(null);
        assertNotNull(limitedLicenseApprovalDocument);
        assertEquals(limitedLicenseApproval, limitedLicenseApprovalDocument.getLimitedLicenseApproval());
    }


}
