package de.adesso.fischereiregister.core.validation;

import de.adesso.fischereiregister.core.commands.PayFishingTaxCommand;
import de.adesso.fischereiregister.core.exceptions.ClientInputValidationException;
import de.adesso.fischereiregister.core.exceptions.SystemConfigValidationException;
import de.adesso.fischereiregister.core.interceptables.InterceptableCountryService;
import de.adesso.fischereiregister.core.interceptables.InterceptableTenantRulesValidationPort;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.user.UserDetails;
import de.adesso.fischereiregister.core.model.user.UserRole;
import de.adesso.fischereiregister.core.ports.CountryService;
import de.adesso.fischereiregister.core.ports.TenantRulesValidationPort;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

class PayFishingTaxCommandValidatorTest {

    private PayFishingTaxCommandValidator commandValidator;

    private RegisterEntry registerEntry;

    @BeforeEach
    void setUp() {
        final CountryService countryService = new InterceptableCountryService();
        final TenantRulesValidationPort tenantRulesValidationPort = new InterceptableTenantRulesValidationPort();
        commandValidator = new PayFishingTaxCommandValidator(countryService, tenantRulesValidationPort);

        registerEntry = DomainTestData.createRegisterEntry();
        registerEntry.setPerson(DomainTestData.createPerson());
    }

    @Test
    @DisplayName("PayFishingTaxCommandValidator.validateOrThrow should not throw any error for a valid command")
    public void testValidateOrThrow_validCommand() {
        // GIVEN
        PayFishingTaxCommand validCommand = new PayFishingTaxCommand(
                UUID.randomUUID(),
                DomainTestData.createTaxConsentInfo(),
                null,
                UUID.randomUUID().toString(),
                List.of(DomainTestData.createAnalogTax()),
                DomainTestData.createUserDetails(UserRole.OFFICIAL)
        );

        // WHEN
        // THEN
        assertDoesNotThrow(() -> commandValidator.validateOrThrow(validCommand, registerEntry));
    }

    @Test
    @DisplayName("PayFishingTaxCommandValidator.validateOrThrow should throw a ClientInputValidationException with the respective error messages for an invalid command")
    public void testValidateOrThrow_invalidClientInputCommand() {
        // GIVEN
        PayFishingTaxCommand invalidCommand = new PayFishingTaxCommand(
                UUID.randomUUID(),
                null,
                null,
                UUID.randomUUID().toString(),
                Collections.emptyList(),
                DomainTestData.createUserDetails(UserRole.OFFICIAL)
        );

        // WHEN
        ClientInputValidationException exception = assertThrows(ClientInputValidationException.class, () -> commandValidator.validateOrThrow(invalidCommand, registerEntry));

        // THEN
        ValidationResult result = exception.getValidationResult();
        assertTrue(result.hasErrors());
        assertEquals(2, result.getErrorNotes().size());
        assertTrue(result.getErrorNotes().contains("consentInfo is required"));
        assertTrue(result.getErrorNotes().contains("Taxes are required"));
    }

    @Test
    @DisplayName("PayFishingTaxCommandValidator.validateOrThrow should throw a SystemConfigValidationException when user details are invalid")
    public void testValidateOrThrow_invalidSystemConfigCommand() {
        // GIVEN
        PayFishingTaxCommand invalidCommand = new PayFishingTaxCommand(
                UUID.randomUUID(),
                DomainTestData.createTaxConsentInfo(),
                null,
                null,
                List.of(DomainTestData.createAnalogTax()),
                null
        );

        // WHEN
        SystemConfigValidationException exception = assertThrows(SystemConfigValidationException.class, () -> commandValidator.validateOrThrow(invalidCommand, registerEntry));

        // THEN
        ValidationResult result = exception.getValidationResult();
        assertTrue(result.hasErrors());
        assertEquals(2, result.getErrorNotes().size());
        assertTrue(result.getErrorNotes().contains("User Details is required"));
        assertTrue(result.getErrorNotes().contains("Salt is required"));
    }

    @Test
    @DisplayName("PayFishingTaxCommandValidator.validateOrThrow should throw a SystemConfigValidationException  when user details are present but invalid")
    public void testValidateOrThrow_missingOffice() {
        // GIVEN
        // Create user details with null office
       UserDetails userDetailsWithoutOffice = new UserDetails(null,null,null,null,null, Set.of(UserRole.OFFICIAL));

        PayFishingTaxCommand invalidCommand = new PayFishingTaxCommand(
                UUID.randomUUID(),
                DomainTestData.createTaxConsentInfo(),
                null,
                UUID.randomUUID().toString(),
                List.of(DomainTestData.createAnalogTax()),
                userDetailsWithoutOffice
        );

        // WHEN
        SystemConfigValidationException exception = assertThrows(SystemConfigValidationException.class, () -> commandValidator.validateOrThrow(invalidCommand, registerEntry));

        // THEN
        ValidationResult result = exception.getValidationResult();
        assertTrue(result.hasErrors());
        assertTrue(result.getErrorNotes().contains("User Office is required"));
    }
}