package de.adesso.fischereiregister.core.validation.helper;

import de.adesso.fischereiregister.core.model.Birthdate;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class BirthdateValidatorHelperTest {

    @Test
    @DisplayName("BirthdateValidatorHelper.isBirthdateValid should return false when birthdate is null")
    public void testIsBirthdateValid_Null() {
        // Given
        Birthdate birthdate = null;

        // When
        boolean isValid = BirthdateValidatorHelper.isBirthdateValid(birthdate);

        // Then
        assertFalse(isValid);
    }

    @Test
    @DisplayName("BirthdateValidatorHelper.isBirthdateValid should return false when month is greater than 12")
    public void testIsBirthdateValid_InvalidMonth() {
        // Given
        Birthdate birthdate = new Birthdate(2000, 13, 1);

        // When
        boolean isValid = BirthdateValidatorHelper.isBirthdateValid(birthdate);

        // Then
        assertFalse(isValid);
    }

    @Test
    @DisplayName("BirthdateValidatorHelper.isBirthdateValid should return false when day is greater than 31")
    public void testIsBirthdateValid_InvalidDay() {
        // Given
        Birthdate birthdate = new Birthdate(2000, 1, 32);

        // When
        boolean isValid = BirthdateValidatorHelper.isBirthdateValid(birthdate);

        // Then
        assertFalse(isValid);
    }

    @Test
    @DisplayName("BirthdateValidatorHelper.isBirthdateValid should return false when age is 121")
    public void testIsBirthdateValid_InvalidAge() {
        // Given
        final LocalDate today = java.time.LocalDate.now();
        final LocalDate birthdateAsLocalDate = today.minusYears(121);
        final Birthdate birthdate = new Birthdate(birthdateAsLocalDate.getYear(), birthdateAsLocalDate.getMonthValue(), birthdateAsLocalDate.getDayOfMonth());

        // When
        boolean isValid = BirthdateValidatorHelper.isBirthdateValid(birthdate);

        // Then
        assertFalse(isValid);
    }

    @Test
    @DisplayName("BirthdateValidatorHelper.isBirthdateValid should return false when birthdate is in the future")
    public void testIsBirthdateValid_FutureDate() {
        // Given
        final LocalDate today = java.time.LocalDate.now();
        final LocalDate birthdateAsLocalDate = today.plusDays(1);
        final Birthdate birthdate = new Birthdate(birthdateAsLocalDate.getYear(), birthdateAsLocalDate.getMonthValue(), birthdateAsLocalDate.getDayOfMonth());

        // When
        boolean isValid = BirthdateValidatorHelper.isBirthdateValid(birthdate);

        // Then
        assertFalse(isValid);
    }

    @Test
    @DisplayName("BirthdateValidatorHelper.isBirthdateValid should return true when date is a partial date with unknown day")
    public void testIsBirthdateValid_PartialDate() {
        // Given
        final LocalDate today = java.time.LocalDate.now();
        final Birthdate birthdate = new Birthdate(today.getYear(), today.getMonthValue(), 0);

        // When
        boolean isValid = BirthdateValidatorHelper.isBirthdateValid(birthdate);

        // Then
        assertTrue(isValid);
    }

    @Test
    @DisplayName("BirthdateValidatorHelper.isBirthdateValid should return true when date is a partial date with unknown month and day")
    public void testIsBirthdateValid_PartialDateMonthAndDay() {
        // Given
        final LocalDate today = java.time.LocalDate.now();
        final Birthdate birthdate = new Birthdate(today.getYear(), 0, 0);

        // When
        boolean isValid = BirthdateValidatorHelper.isBirthdateValid(birthdate);

        // Then
        assertTrue(isValid);
    }

    @Test
    @DisplayName("BirthdateValidatorHelper.isBirthdateValid should return true when date is today")
    public void testIsBirthdateValid_ValidDateToday() {
        // Given
        final LocalDate today = java.time.LocalDate.now();
        final Birthdate birthdate = new Birthdate(today.getYear(), today.getMonthValue(), today.getDayOfMonth());

        // When
        boolean isValid = BirthdateValidatorHelper.isBirthdateValid(birthdate);

        // Then
        assertTrue(isValid);
    }

    @Test
    @DisplayName("BirthdateValidatorHelper.isBirthdateValid should return true when date is in past")
    public void testIsBirthdateValid_ValidDatePast() {
        // Given
        final LocalDate today = java.time.LocalDate.now();
        final Birthdate birthdate = new Birthdate(today.getYear() - 10, today.getMonthValue(), today.getDayOfMonth());

        // When
        boolean isValid = BirthdateValidatorHelper.isBirthdateValid(birthdate);

        // Then
        assertTrue(isValid);
    }
}
