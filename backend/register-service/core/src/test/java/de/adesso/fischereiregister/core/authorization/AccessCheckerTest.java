package de.adesso.fischereiregister.core.authorization;

import de.adesso.fischereiregister.core.model.Jurisdiction;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.user.UserDetails;
import de.adesso.fischereiregister.core.model.user.UserRole;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class AccessCheckerTest {

    @Test
    @DisplayName("AccessChecker.commandTaxesMatchUserJurisdiction should do nothing if all taxes are for the correct federal state")
    public void testAccessCheckerManyTaxesInUserJurisdiction() {
        //GIVEN
        final Tax tax1 = createSHTax();
        final Tax tax2 = createSHTax();
        final Tax tax3 = createSHTax();

        final List<Tax> taxes = List.of(tax1, tax2, tax3);
        final UserDetails userDetails = createUserDetails("SH");

        //WHEN
        final boolean result = AccessChecker.commandTaxesMatchUserJurisdiction(taxes, userDetails);

        //THEN
        assertTrue(result);
    }

    @Test
    @DisplayName("AccessChecker.commandTaxesMatchUserJurisdiction should throw an TaxInWrongJurisdictionException when one of the taxes is for a different federal state")
    public void testAccessCheckerManyTaxesJurisdictionNotAlreadyAssigned() {
        //GIVEN
        final Tax tax1 = createSHTax();
        final Tax tax2 = createSHTax();
        final Tax tax3 = createSHTax();
        tax3.setFederalState("BE");

        final List<Tax> taxes = List.of(tax1, tax2, tax3);
        final UserDetails userDetails = createUserDetails("SH");

        //WHEN
        final boolean result = AccessChecker.commandTaxesMatchUserJurisdiction(taxes, userDetails);

        //THEN
        assertFalse(result);
    }

    @Test
    @DisplayName("AccessChecker.commandTaxesMatchUserJurisdiction should return true for system user")
    public void testCommandTaxesMatchUserJurisdictionSystemUser() {
        //GIVEN
        final Tax tax = createSHTax();
        final List<Tax> taxes = List.of(tax);
        final UserDetails systemUser = UserDetails.SYSTEM_USER;

        //WHEN
        final boolean result = AccessChecker.commandTaxesMatchUserJurisdiction(taxes, systemUser);

        //THEN
        assertTrue(result);
    }

    @Test
    @DisplayName("AccessChecker.commandTaxesMatchUserJurisdiction should throw IllegalArgumentException for null userDetails")
    public void testCommandTaxesMatchUserJurisdictionNullUserDetails() {
        //GIVEN
        final Tax tax = createSHTax();
        final List<Tax> taxes = List.of(tax);

        //WHEN/THEN
        assertThrows(IllegalArgumentException.class,
                () -> AccessChecker.commandTaxesMatchUserJurisdiction(taxes, null));
    }

    @Test
    @DisplayName("AccessChecker.commandTaxesMatchUserJurisdiction should return true for null taxes")
    public void testCommandTaxesMatchUserJurisdictionNullTaxes() {
        //GIVEN
        final UserDetails userDetails = createUserDetails("SH");

        //WHEN
        final boolean result = AccessChecker.commandTaxesMatchUserJurisdiction(null, userDetails);

        //THEN
        assertTrue(result);
    }

    @Test
    @DisplayName("AccessChecker.registerEntryInUserJurisdiction should return true for matching jurisdictions")
    public void testRegisterEntryInUserJurisdictionMatch() {
        //GIVEN
        final Jurisdiction jurisdiction = new Jurisdiction();
        jurisdiction.setFederalState("SH");
        final UserDetails userDetails = createUserDetails("SH");

        //WHEN
        final boolean result = AccessChecker.registerEntryInUserJurisdiction(jurisdiction, userDetails);

        //THEN
        assertTrue(result);
    }

    @Test
    @DisplayName("AccessChecker.registerEntryInUserJurisdiction should return for non-matching jurisdictions")
    public void testRegisterEntryInUserJurisdictionNoMatch() {
        //GIVEN
        final Jurisdiction jurisdiction = new Jurisdiction();
        jurisdiction.setFederalState("BE");
        final UserDetails userDetails = createUserDetails("SH");

        //WHEN
        final boolean result = AccessChecker.registerEntryInUserJurisdiction(jurisdiction, userDetails);

        //THEN
        assertFalse(result);
    }

    @Test
    @DisplayName("AccessChecker.registerEntryInUserJurisdiction should return true for system user")
    public void testRegisterEntryInUserJurisdictionSystemUser() {
        //GIVEN
        final Jurisdiction jurisdiction = new Jurisdiction();
        jurisdiction.setFederalState("BE");
        final UserDetails systemUser = UserDetails.SYSTEM_USER;

        //WHEN
        final boolean result = AccessChecker.registerEntryInUserJurisdiction(jurisdiction, systemUser);

        //THEN
        assertTrue(result);
    }

    @Test
    @DisplayName("AccessChecker.registerEntryInUserJurisdiction should return false for null jurisdiction")
    public void testRegisterEntryInUserJurisdictionNullJurisdiction() {
        //GIVEN
        final UserDetails userDetails = createUserDetails("SH");

        //WHEN
        final boolean result = AccessChecker.registerEntryInUserJurisdiction(null, userDetails);

        //THEN
        assertFalse(result);
    }

    @Test
    @DisplayName("AccessChecker.registerEntryInUserJurisdiction should throw IllegalArgumentException for null userDetails")
    public void testRegisterEntryInUserJurisdictionNullUserDetails() {
        //GIVEN
        final Jurisdiction jurisdiction = new Jurisdiction();
        jurisdiction.setFederalState("SH");

        //WHEN/THEN
        assertThrows(IllegalArgumentException.class,
                () -> AccessChecker.registerEntryInUserJurisdiction(jurisdiction, null));
    }


    private Tax createSHTax() {
        final Tax tax = new Tax();
        tax.setFederalState("SH");
        return tax;
    }

    private UserDetails createUserDetails(String federalState) {
        return new UserDetails("userID", federalState, "Musterstraße 123", null, null, Set.of(UserRole.OFFICIAL));
    }

}
