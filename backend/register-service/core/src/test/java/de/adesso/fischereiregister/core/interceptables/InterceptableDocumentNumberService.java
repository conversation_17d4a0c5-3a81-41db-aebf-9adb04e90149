package de.adesso.fischereiregister.core.interceptables;

import de.adesso.fischereiregister.core.model.type.IdentificationDocumentType;
import de.adesso.fischereiregister.core.ports.DocumentNumberService;

import java.util.UUID;

public class InterceptableDocumentNumberService implements DocumentNumberService, ResetableAdapter {
    @Override
    public void reset() {

    }

    @Override
    public String createNewDocumentNumber(UUID registerEntryId, IdentificationDocumentType documentType) {
        return UUID.randomUUID().toString();
    }
}