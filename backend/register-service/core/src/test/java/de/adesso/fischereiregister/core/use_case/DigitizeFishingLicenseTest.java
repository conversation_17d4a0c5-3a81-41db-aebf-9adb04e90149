package de.adesso.fischereiregister.core.use_case;

import de.adesso.fischereiregister.core.commands.DigitizeRegularLicenseCommand;
import de.adesso.fischereiregister.core.events.RegularLicenseDigitizedEvent;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.QualificationsProof;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.consent.ConsentInfo;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.IdentificationDocumentType;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.model.type.QualificationsProofType;
import de.adesso.fischereiregister.core.model.user.UserRole;
import de.adesso.fischereiregister.core.testutils.AxonFixture;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import org.axonframework.test.aggregate.AggregateTestFixture;
import org.axonframework.test.matchers.Matchers;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class DigitizeFishingLicenseTest {

    @Test
    @DisplayName("Test whether aggregate handlers OrderReplacementCardCommand of are successful.")
    @ExtendWith(AxonFixture.class)
    void withAxonTestFixture(AggregateTestFixture<RegisterEntry> fixture) {        //given
        final UUID givenId = UUID.randomUUID();
        final Person givenPerson = DomainTestData.createPersonWithAddress();

        final Jurisdiction givenJurisdiction = new Jurisdiction();
        givenJurisdiction.setFederalState("SH");

        final QualificationsProof givenQualificationsProof = new QualificationsProof();
        givenQualificationsProof.setFishingCertificateId("4711");
        givenQualificationsProof.setIssuedBy("Fischfreunde Övelgönne e.V.");
        givenQualificationsProof.setPassedOn(LocalDate.now().minusYears(1));
        givenQualificationsProof.setFederalState("SH");
        givenQualificationsProof.setType(QualificationsProofType.CERTIFICATE);

        final String givenOriginalSalt = "12345";
        final String givenReplacementSalt = "67890";

        assertNotEquals(givenOriginalSalt, givenReplacementSalt);
        final FishingLicense givenFishingLicense = new FishingLicense();
        givenFishingLicense.setNumber("LN12345");
        givenFishingLicense.getValidityPeriods().add(DomainTestData.createValidityPeriod(LocalDate.now(), LocalDate.MAX));
        givenFishingLicense.setType(LicenseType.REGULAR);
        givenFishingLicense.setIssuingFederalState(FederalState.SH);

        ConsentInfo givenConsentInfo = DomainTestData.createConsentInfo();

        fixture.givenNoPriorActivity()
                .when(new DigitizeRegularLicenseCommand(
                                givenId,
                                givenOriginalSalt,
                                givenPerson,
                                List.of(DomainTestData.createAnalogFee()),
                                List.of(),
                                List.of(),
                                List.of(givenQualificationsProof),
                                givenConsentInfo,
                                DomainTestData.createUserDetails(UserRole.OFFICIAL)
                        )
                )
                .expectSuccessfulHandlerExecution().expectEventsMatching(Matchers.exactSequenceOf(Matchers.messageWithPayload(Matchers.matches(payload -> {
                    return payload.getClass().isAssignableFrom(RegularLicenseDigitizedEvent.class);
                }))))
                .expectState(registerEntry -> {
                    assertEquals(givenId, registerEntry.getRegisterId());
                    assertEquals(1, registerEntry.getQualificationsProofs().size());

                    final List<IdentificationDocument> identificationDocuments = registerEntry.getIdentificationDocuments();
                    assertNotNull(identificationDocuments);
                    assertFalse(identificationDocuments.isEmpty());

                    assertEquals(2, identificationDocuments.size());
                    assertTrue(identificationDocuments.stream().anyMatch(d -> d.getType().equals(IdentificationDocumentType.PDF)));
                    assertTrue(identificationDocuments.stream().anyMatch(d -> d.getType().equals(IdentificationDocumentType.CARD)));
                });
    }

}
