package de.adesso.fischereiregister.core.validation.helper;

import de.adesso.fischereiregister.core.interceptables.InterceptableCountryService;
import de.adesso.fischereiregister.core.model.Address;
import de.adesso.fischereiregister.core.model.Birthdate;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.ports.CountryService;
import de.adesso.fischereiregister.core.testutils.AxonFixture;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import de.adesso.fischereiregister.core.validation.ValidationResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

class PersonValidatorHelperTest extends AxonFixture {

    private ValidationResult validationResult;
    private CountryService countryService;

    @BeforeEach
    void setUp() {
        validationResult = new ValidationResult();
        countryService = new InterceptableCountryService();
    }

    @Test
    @DisplayName("PersonValidatorHelper.validate should add error note when person is null")
    void validatePersonIsNull_addsErrorNote() {
        // Given
        Person person = null;

        // When
        PersonValidatorHelper.validate(person, countryService, validationResult, true);

        // Then
        assertEquals(1, validationResult.getErrorNotes().size());
        assertTrue(validationResult.getErrorNotes().contains("Person Details is required"));
    }

    @Test
    @DisplayName("PersonValidatorHelper.validate should not add error notes when person is valid")
    void validateValidPerson_noErrorNotesAdded() {
        // Given
        Person person = DomainTestData.createPersonWithAddress();

        // When
        PersonValidatorHelper.validate(person, countryService, validationResult, true);

        // Then
        assertTrue(validationResult.getErrorNotes().isEmpty());
    }

    @Test
    @DisplayName("PersonValidatorHelper.validate should add error note when firstname is invalid")
    void validateInvalidFirstname_addsErrorNote() {
        // Given
        Person person = new Person();
        person.setFirstname(null);
        person.setLastname("Doe");

        // When
        PersonValidatorHelper.validate(person, countryService, validationResult, true);

        // Then
        assertTrue(validationResult.getErrorNotes().contains("Firstname is required"));
    }

    @Test
    @DisplayName("PersonValidatorHelper.validate should add error note when nationality is null")
    void validateNullNationality_addsErrorNote() {
        // Given
        Person person = new Person();
        person.setFirstname("John");
        person.setLastname("Doe");
        person.setNationality(null);

        // When
        PersonValidatorHelper.validate(person, countryService, validationResult, true);

        // Then
        assertTrue(validationResult.getErrorNotes().contains("Nationality is not valid according to the DESTATIS List, wrong value: null"));
    }

    @Test
    @DisplayName("PersonValidatorHelper.validate should add error note when nationality is invalid")
    void validateInvalidNationality_addsErrorNote() {
        // Given
        Person person = new Person();
        person.setFirstname("John");
        person.setLastname("Doe");
        person.setNationality("INVALID");

        // When
        PersonValidatorHelper.validate(person, countryService, validationResult, true);

        // Then
        assertTrue(validationResult.getErrorNotes().contains("Nationality is not valid according to the DESTATIS List, wrong value: INVALID"));
    }

    @Test
    @DisplayName("PersonValidatorHelper.validate should add error note when title is invalid")
    void validateInvalidTitle_addsErrorNote() {
        // Given
        Person person = new Person();
        person.setFirstname("John");
        person.setLastname("Doe");
        person.setTitle("InvalidTitle");
        person.setNationality("US");

        // When
        PersonValidatorHelper.validate(person, countryService, validationResult, true);

        // Then
        assertTrue(validationResult.getErrorNotes().contains("Title is not valid"));
    }

    @Test
    @DisplayName("PersonValidatorHelper.validateAddress should add error note when address is empty")
    void validateEmptyAddress_addsErrorNote() {
        // When
        PersonValidatorHelper.validateAddress(null,validationResult);

        // Then
        assertTrue(validationResult.getErrorNotes().contains("address is required"));
    }

    @Test
    @DisplayName("PersonValidatorHelper.validateAddress should add error notes when address is invalid")
    void validateInvalidAddress_addsErrorNote() {
        // Given
        Address address = new Address();

        // When
        PersonValidatorHelper.validateAddress(address,  validationResult);

        // Then
        assertTrue(validationResult.getErrorNotes().contains("street is required"));
        assertTrue(validationResult.getErrorNotes().contains("streetNumber is required"));
        assertTrue(validationResult.getErrorNotes().contains("city is required"));
        assertTrue(validationResult.getErrorNotes().contains("postcode is required"));
    }

    @Test
    @DisplayName("PersonValidatorHelper.validate should not add error notes when title is null")
    void validateNullTitle_noErrorNotesAdded() {
        // Given
        Person person = DomainTestData.createPerson();
        person.setTitle(null);

        // When
        PersonValidatorHelper.validate(person, countryService, validationResult, true);

        // Then
        assertTrue(validationResult.getErrorNotes().isEmpty());
    }

    @Test
    @DisplayName("PersonValidatorHelper.validate should return errors when person names violate DIN norm")
    void testPatternValidationFailsOnPersonNames() {
        // GIVEN
        Person person = DomainTestData.createPerson();

        // Set illegal personal names
        person.setFirstname("1");
        person.setLastname("1");
        person.setBirthname("1");

        // WHEN
        PersonValidatorHelper.validate(person, countryService, validationResult, false);

        // THEN
        assertEquals(3, validationResult.getErrorNotes().size());
    }

    @Test
    @DisplayName("PersonValidatorHelper.validate should succeed when birthplace is valid")
    void testPatternValidationSucceedsOnBirthplace() {
        // GIVEN
        Person person = DomainTestData.createPerson();

        // Set legal birthplace
        person.setBirthplace("may contain numbers 1");

        // WHEN
        PersonValidatorHelper.validate(person, countryService, validationResult, false);

        // THEN
        assertTrue(validationResult.getErrorNotes().isEmpty());
    }

    @Test
    @DisplayName("PersonValidatorHelper.validate should return errors when birthplace is invalid")
    void testPatternValidationFailsOnBirthplace() {
        // GIVEN
        Person person = DomainTestData.createPerson();

        // Set illegal birthplace
        person.setBirthplace("鱼");

        // WHEN
        PersonValidatorHelper.validate(person, countryService, validationResult, false);

        // THEN
        assertEquals(1, validationResult.getErrorNotes().size());
    }

    @Test
    @DisplayName("PersonValidatorHelper.validateAddress should succeed when address is valid")
    void testPatternValidationSucceedsOnAddress() {
        // GIVEN
        Address address = new Address();

        // Set legal data
        address.setDeliverTo("a");
        address.setOffice("1");
        address.setStreet("1");
        address.setStreetNumber("1");
        address.setPostcode("21345");
        address.setCity("1");
        address.setDetail("1");

        // WHEN
        PersonValidatorHelper.validateAddress(address, validationResult);

        // THEN
        assertTrue(validationResult.getErrorNotes().isEmpty());
    }

    @Test
    @DisplayName("PersonValidatorHelper.validateAddress should return errors when address is invalid")
    void testPatternValidationFailsOnAddress() {
        // GIVEN
        Address address = new Address();

        // Set illegal data
        address.setDeliverTo("1");
        address.setOffice("鱼");
        address.setStreet("鱼");
        address.setStreetNumber("鱼");
        address.setPostcode("1234");
        address.setCity("鱼");
        address.setDetail("鱼");

        // WHEN
        PersonValidatorHelper.validateAddress(address, validationResult);

        // THEN
        assertEquals(7, validationResult.getErrorNotes().size());
    }

    @Test
    @DisplayName("PersonValidatorHelper.validate should succeed when strings have exact max length")
    void testMaxLengthValidationOnPersonWithExactMaxLength() {
        // GIVEN
        Person person = DomainTestData.createPerson();

        person.setFirstname(createStringOfLength(80));
        person.setLastname(createStringOfLength(120));
        person.setBirthname(createStringOfLength(120));
        person.setBirthplace(createStringOfLength(120));

        // WHEN
        PersonValidatorHelper.validate(person, countryService, validationResult, false);

        // THEN
        assertTrue(validationResult.getErrorNotes().isEmpty());
    }

    @Test
    @DisplayName("PersonValidatorHelper.validate should fail when strings exceed max length")
    void testMaxLengthValidationOnPersonWithExceedingLength() {
        // GIVEN
        Person person = DomainTestData.createPerson();

        person.setFirstname(createStringOfLength(80 + 1));
        person.setLastname(createStringOfLength(120 + 1));
        person.setBirthname(createStringOfLength(120 + 1));
        person.setBirthplace(createStringOfLength(120 + 1));

        // WHEN
        PersonValidatorHelper.validate(person, countryService, validationResult, false);

        // THEN
        assertEquals(4, validationResult.getErrorNotes().size());
    }

    @Test
    @DisplayName("PersonValidatorHelper.validateAddress should succeed when strings have exact max length")
    void testMaxLengthValidationOnAddressWithExactLength() {
        // GIVEN
        Address address = new Address();

        address.setPostcode("12345");

        address.setDeliverTo(createStringOfLength(200));
        address.setOffice(createStringOfLength(200));
        address.setStreet(createStringOfLength(55));
        address.setStreetNumber(createStringOfLength(11));
        address.setCity(createStringOfLength(50));
        address.setDetail(createStringOfLength(100));

        // WHEN
        PersonValidatorHelper.validateAddress(address, validationResult);

        // THEN
        assertTrue(validationResult.getErrorNotes().isEmpty());
    }

    @Test
    @DisplayName("PersonValidatorHelper.validateAddress should fail when strings exceed max length")
    void testMaxLengthValidationOnAddressWithExceedingLength() {
        // GIVEN
        Address address = new Address();

        address.setPostcode("12345");

        address.setDeliverTo(createStringOfLength(200 + 1));
        address.setOffice(createStringOfLength(200 + 1));
        address.setStreet(createStringOfLength(55 + 1));
        address.setStreetNumber(createStringOfLength(11 + 1));
        address.setCity(createStringOfLength(50 + 1));
        address.setDetail(createStringOfLength(100 + 1));

        // WHEN
        PersonValidatorHelper.validateAddress(address, validationResult);

        // THEN
        assertEquals(6, validationResult.getErrorNotes().size());
    } @Test
    @DisplayName("PersonValidatorHelper.validateUnchangeableDataMatches should not add error notes when unchangeable data matches")
    void validateUnchangeableDataMatches_noErrorNotesAdded() {
        // Given
        Person person1 = DomainTestData.createPerson();
        Person person2 = DomainTestData.createPerson();

        // When
        PersonValidatorHelper.validateUnchangeableDataMatches(person1, person2, validationResult);

        // Then
        assertTrue(validationResult.getErrorNotes().isEmpty());
    }

    @Test
    @DisplayName("PersonValidatorHelper.validateUnchangeableDataMatches should add error note when unchangeable data does not match")
    void validateUnchangeableDataMatches_addsErrorNote() {
        // Given
        Person person1 = DomainTestData.createPerson();
        Person person2 = DomainTestData.createPerson();

        person2.setBirthname("DifferentBirthname");
        person2.setBirthplace("DifferentBirthplace");
        person2.setBirthdate(new Birthdate(1950,11,11)); // different birthdate

        // When
        PersonValidatorHelper.validateUnchangeableDataMatches(person1, person2, validationResult);

        // Then
        assertEquals(1, validationResult.getErrorNotes().size());
        assertTrue(validationResult.getErrorNotes().contains("Unchangeable data [birthname, birthplace, birthdate] do not match"));
    }

    private String createStringOfLength(int length) {
        if (length > 0) {
            char[] array = new char[length];
            Arrays.fill(array, 'a');
            return new String(array);
        }
        return "";
    }
}