package de.adesso.fischereiregister.migrations.migrate_0_16_9_to_0_17_5;

import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import de.adesso.fischereiregister.core.events.ReplacementCardOrderedEvent;
import org.axonframework.serialization.SimpleSerializedType;
import org.axonframework.serialization.upcasting.event.IntermediateEventRepresentation;
import org.axonframework.serialization.upcasting.event.SingleEventUpcaster;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
@Order(1)
public class ReplacementCardOrderedEvent0_to_2Upcaster extends SingleEventUpcaster {
    private static final String PAYLOAD_TYPE = ReplacementCardOrderedEvent.class.getTypeName();
    private static final String EXPECTED_REVISION = null;
    private static final String UPCASTED_REVISION = "2.0";

    @Override
    protected boolean canUpcast(IntermediateEventRepresentation intermediateRepresentation) {
        return intermediateRepresentation.getType().getName().equals(PAYLOAD_TYPE) && Objects.equals(intermediateRepresentation.getType().getRevision(), EXPECTED_REVISION);
    }

    @Override
    protected IntermediateEventRepresentation doUpcast(IntermediateEventRepresentation intermediateRepresentation) {
        return intermediateRepresentation.upcastPayload(
                new SimpleSerializedType(PAYLOAD_TYPE, UPCASTED_REVISION),
                ObjectNode.class,
                event -> {
                    ArrayNode documents = (ArrayNode) event.get("identificationDocuments");
                    event.replace("identificationDocuments", DomainObject0_to_2Upcaster.upcastIdentificationDocuments(documents));

                    ObjectNode license = (ObjectNode) event.get("fishingLicense");
                    event.replace("fishingLicense", DomainObject0_to_2Upcaster.upcastLicense(license));

                    return event;
                }
        );
    }
}
