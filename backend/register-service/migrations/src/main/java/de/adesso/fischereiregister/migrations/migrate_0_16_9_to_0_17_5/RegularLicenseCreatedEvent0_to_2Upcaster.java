package de.adesso.fischereiregister.migrations.migrate_0_16_9_to_0_17_5;

import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.node.TextNode;
import de.adesso.fischereiregister.core.events.RegularLicenseCreatedEvent;
import org.axonframework.serialization.SimpleSerializedType;
import org.axonframework.serialization.upcasting.event.EventTypeUpcaster;
import org.axonframework.serialization.upcasting.event.IntermediateEventRepresentation;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Component
@Order(0)
public class RegularLicenseCreatedEvent0_to_2Upcaster extends EventTypeUpcaster {
    private static final String EXPECTED_PAYLOAD_TYPE = "de.adesso.fischereiregister.core.events.FishingLicenseCreatedEvent";
    private static final String EXPECTED_REVISION = null;

    private static final String UPCASTED_PAYLOAD_TYPE = RegularLicenseCreatedEvent.class.getTypeName();
    private static final String UPCASTED_REVISION = "2.0";

    private static final String OFFICE_DEFAULT_VALUE = "Konnte nicht ermittelt werden";

    public RegularLicenseCreatedEvent0_to_2Upcaster() {
        super(EXPECTED_PAYLOAD_TYPE, EXPECTED_REVISION, UPCASTED_PAYLOAD_TYPE, UPCASTED_REVISION);
    }

    @Override
    protected IntermediateEventRepresentation doUpcast(IntermediateEventRepresentation intermediateEventRepresentation) {
        return intermediateEventRepresentation.upcastPayload(
                new SimpleSerializedType(UPCASTED_PAYLOAD_TYPE, UPCASTED_REVISION),
                ObjectNode.class,
                event -> {
                    if (!event.has("issuedByOffice")) {
                        event.set("issuedByOffice", TextNode.valueOf(OFFICE_DEFAULT_VALUE));
                    }

                    if (event.has("federalState")) {
                        event.remove("federalState");
                    }

                    if (!event.has("submissionType")) {
                        event.set("submissionType", TextNode.valueOf("ANALOG"));
                    }

                    final ObjectNode license = (ObjectNode) event.get("fishingLicense");
                    event.replace("fishingLicense", DomainObject0_to_2Upcaster.upcastLicense(license));

                    final ArrayNode documents = (ArrayNode) event.get("identificationDocuments");
                    event.replace("identificationDocuments", DomainObject0_to_2Upcaster.upcastIdentificationDocuments(documents));

                    return event;
                }
        );
    }
}
