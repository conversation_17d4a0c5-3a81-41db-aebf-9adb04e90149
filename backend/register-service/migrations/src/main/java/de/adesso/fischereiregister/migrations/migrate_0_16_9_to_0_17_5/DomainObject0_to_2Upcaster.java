package de.adesso.fischereiregister.migrations.migrate_0_16_9_to_0_17_5;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.node.TextNode;
import de.adesso.fischereiregister.core.model.type.LicenseType;

/**
 * Note that upcasters MUST be invariant under already upcasted events.
 * <p>
 * That is why ObjectNode.has should always be checked before removing the properties.
 */
public class DomainObject0_to_2Upcaster {
    private static class FIELDS {
        private static final String ISSUED_BY = "issuedBy";
        private static final String TYPE = "type";
        private static final String VALIDITY_PERIODS = "validityPeriods";
        private static final String FEDERAL_STATE = "federalState";
        private static final String ORDER = "order";
        private static final String FISHING_LICENSE = "fishingLicense";

    }

    public static ObjectNode upcastLicense(ObjectNode license) {
        if (license.has(FIELDS.ISSUED_BY)) {
            license.remove(FIELDS.ISSUED_BY);
        }

        // Previously it was possible to create REGULAR license, but this is now forbidden. Converting all previous licenses to REGULAR
        if (!LicenseType.REGULAR.name().equals(license.get(FIELDS.TYPE).asText())) {
            license.replace(FIELDS.TYPE, TextNode.valueOf(LicenseType.REGULAR.name()));
        }

        ArrayNode validityPeriods = (ArrayNode) license.get(FIELDS.VALIDITY_PERIODS);
        license.replace(FIELDS.VALIDITY_PERIODS, upcastValidityPeriods(validityPeriods));

        return license;
    }

    private static ArrayNode upcastValidityPeriods(ArrayNode validityPeriods) {
        for (int i = 0; i < validityPeriods.size(); i++) {
            validityPeriods.set(i, upcastValidityPeriod((ObjectNode) validityPeriods.get(i)));
        }
        return validityPeriods;
    }

    private static ObjectNode upcastValidityPeriod(ObjectNode validityPeriod) {
        if (validityPeriod.has(FIELDS.FEDERAL_STATE)) {
            validityPeriod.remove(FIELDS.FEDERAL_STATE);
        }
        return validityPeriod;
    }


    public static ObjectNode upcastIdentificationDocument(ObjectNode identificationDocument) {
        if (identificationDocument.has(FIELDS.ORDER)) {
            identificationDocument.remove(FIELDS.ORDER);
        }

        JsonNode fishingLicense = identificationDocument.get(FIELDS.FISHING_LICENSE);
        if (!fishingLicense.isNull()) {
            identificationDocument.replace(FIELDS.FISHING_LICENSE, upcastLicense((ObjectNode) fishingLicense));
        }

        return identificationDocument;
    }

    public static ArrayNode upcastIdentificationDocuments(ArrayNode identificationDocuments) {
        for (int i = 0; i < identificationDocuments.size(); i++) {
            identificationDocuments.set(i, upcastIdentificationDocument((ObjectNode) identificationDocuments.get(i)));
        }
        return identificationDocuments;
    }
}
