package de.adesso.fischereiregister.migrations.migrate_0_16_9_to_0_17_5;

import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import de.adesso.fischereiregister.core.events.RegularLicenseDigitizedEvent;
import org.axonframework.serialization.SimpleSerializedType;
import org.axonframework.serialization.upcasting.event.EventTypeUpcaster;
import org.axonframework.serialization.upcasting.event.IntermediateEventRepresentation;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Component
@Order(0)
public class RegularLicenseDigititzedEvent0_to_2Upcaster extends EventTypeUpcaster {
    private static final String EXPECTED_PAYLOAD_TYPE = "de.adesso.fischereiregister.core.events.FishingLicenseDigitizedEvent";
    private static final String EXPECTED_REVISION = null;

    private static final String UPCASTED_PAYLOAD_TYPE = RegularLicenseDigitizedEvent.class.getTypeName();
    private static final String UPCASTED_REVISION = "2.0";

    private static final String OFFICE_DEFAULT_VALUE = "Konnte nicht ermittelt werden";

    public RegularLicenseDigititzedEvent0_to_2Upcaster() {
        super(EXPECTED_PAYLOAD_TYPE, EXPECTED_REVISION, UPCASTED_PAYLOAD_TYPE, UPCASTED_REVISION);
    }

    @Override
    protected boolean canUpcast(IntermediateEventRepresentation intermediateEventRepresentation) {
        return super.canUpcast(intermediateEventRepresentation);
    }

    @Override
    protected IntermediateEventRepresentation doUpcast(IntermediateEventRepresentation intermediateEventRepresentation) {
        return intermediateEventRepresentation.upcastPayload(
                new SimpleSerializedType(UPCASTED_PAYLOAD_TYPE, UPCASTED_REVISION),
                ObjectNode.class,
                event -> {
                    if (event.has("issuedByOfficeAddress")) {

                        final String issuedByOfficeAddress = event.get("issuedByOfficeAddress").asText();
                        event.remove("issuedByOfficeAddress");

                        event.put("issuedByAddress", issuedByOfficeAddress);

                        event.put("issuedByOffice", OFFICE_DEFAULT_VALUE);
                    }

                    final ObjectNode license = (ObjectNode) event.get("fishingLicense");
                    event.replace("fishingLicense", DomainObject0_to_2Upcaster.upcastLicense(license));

                    final ArrayNode documents = (ArrayNode) event.get("identificationDocuments");
                    event.replace("identificationDocuments", DomainObject0_to_2Upcaster.upcastIdentificationDocuments(documents));

                    return event;
                }
        );
    }

}