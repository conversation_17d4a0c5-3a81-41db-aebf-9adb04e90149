group = 'de.adesso.fischereiregister.migrations'
version = parent.project.version

dependencies {
    // Datatypes may be used for events that are up to date, to ensure type safety
    implementation project(':core')

    // Only include Spring annotations
    implementation 'org.springframework:spring-context:6.2.7'
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.17.1'

    configurations.all {
        exclude group: 'org.springframework.boot'
        exclude module: 'spring-boot'
        exclude module: 'spring-boot-starter'
        exclude module: 'spring-web'
    }

    api "org.axonframework:axon-spring-boot-starter:4.11.2"

    compileOnly 'org.projectlombok:lombok:1.18.32'
    annotationProcessor 'org.projectlombok:lombok:1.18.32'

    api platform('org.axonframework:axon-bom:4.10.0')
    api "org.axonframework:axon-spring"
}
