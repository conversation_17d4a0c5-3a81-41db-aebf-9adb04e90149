package de.adesso.fischereiregister.view.taxes_statistics.persistence;

import de.adesso.fischereiregister.core.model.type.SubmissionType;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface TaxesStatisticsViewRepository extends CrudRepository<TaxesStatisticsView, Long> {

    @Query("""
            SELECT view FROM TaxesStatisticsView view 
            WHERE view.federalState = :federalState
              AND COALESCE(view.office, '') = COALESCE(:office, '')
              AND view.source = :source
              AND view.duration = :duration
              AND view.year = :year
            """)
    Optional<TaxesStatisticsView> findByFederalStateAndOfficeAndSourceAndDurationAndYear(
            @Param("federalState") String federalState,
            @Param("office") String office,
            @Param("source") SubmissionType source,
            @Param("duration") int duration,
            @Param("year") int year
    );

    @Query("SELECT view FROM TaxesStatisticsView view WHERE view.federalState = :federalState AND view.year IN :years")
    List<TaxesStatisticsView> findByFederalStateAndYearIn(
            @Param("federalState") String federalState,
            @Param("years") List<Integer> years
    );

    @Query("SELECT view FROM TaxesStatisticsView view WHERE view.office = :office AND view.year IN :years")
    List<TaxesStatisticsView> findByOfficeAndYearIn(
            @Param("office") String office,
            @Param("years") List<Integer> years
    );

    @Query("SELECT view FROM TaxesStatisticsView view WHERE view.year IN :years")
    List<TaxesStatisticsView> findByYearIn(@Param("years") List<Integer> years);

    @Query("SELECT DISTINCT view.year FROM TaxesStatisticsView view ORDER BY view.year DESC")
    List<Integer> findDistinctYears();

    @Query("SELECT DISTINCT view.office FROM TaxesStatisticsView view ORDER BY view.office")
    List<String> findDistinctOffices();

    @Query("SELECT DISTINCT view.office FROM TaxesStatisticsView view WHERE view.year IN :years ORDER BY view.office")
    List<String> findDistinctOfficesByYears(@Param("years") List<Integer> years);

    @Query("SELECT DISTINCT view.office FROM TaxesStatisticsView view WHERE view.federalState = :federalState ORDER BY view.office")
    List<String> findDistinctOfficesByFederalState(@Param("federalState") String federalState);

    @Query("SELECT DISTINCT view.office FROM TaxesStatisticsView view WHERE view.federalState = :federalState AND view.year IN :years ORDER BY view.office")
    List<String> findDistinctOfficesByFederalStateAndYears(
            @Param("federalState") String federalState,
            @Param("years") List<Integer> years
    );
}
