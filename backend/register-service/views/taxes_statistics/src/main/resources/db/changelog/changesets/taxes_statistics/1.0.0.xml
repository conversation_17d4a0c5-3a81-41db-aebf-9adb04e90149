<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd"
                   context="taxes_statistics_view">

    <!-- Create TaxesStatisticsView table with constraints and indexes -->
    <changeSet id="1.0.0" author="alexis.ortiz">
        <!-- Skip if table already exists -->
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="taxes_statistics_view"/>
            </not>
        </preConditions>

        <!-- Table definition -->
        <createTable tableName="taxes_statistics_view">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="federal_state" type="VARCHAR(2)">
                <constraints nullable="false"/>
            </column>
            <column name="office" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
            <column name="source" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="year" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="duration" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="count" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="revenue" type="DOUBLE">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <!-- Unique constraint for core query fields -->
        <addUniqueConstraint tableName="taxes_statistics_view"
                             columnNames="federal_state, office, source, year, duration"
                             constraintName="unique_taxes_statistics"/>

        <!-- Index for year-based filtering -->
        <createIndex indexName="idx_taxes_statistics_year" tableName="taxes_statistics_view">
            <column name="year"/>
        </createIndex>

        <!-- Index for (federal_state, year) queries -->
        <createIndex indexName="idx_taxes_statistics_federal_state_year" tableName="taxes_statistics_view">
            <column name="federal_state"/>
            <column name="year"/>
        </createIndex>

        <!-- Index for (office, year) queries -->
        <createIndex indexName="idx_taxes_statistics_office_year" tableName="taxes_statistics_view">
            <column name="office"/>
            <column name="year"/>
        </createIndex>

        <!-- Index for filtering by submission type -->
        <createIndex indexName="idx_taxes_statistics_source" tableName="taxes_statistics_view">
            <column name="source"/>
        </createIndex>

        <!-- Index for duration-specific filters -->
        <createIndex indexName="idx_taxes_statistics_duration" tableName="taxes_statistics_view">
            <column name="duration"/>
        </createIndex>

        <!-- Composite index for filtering by year and duration -->
        <createIndex indexName="idx_taxes_statistics_year_duration" tableName="taxes_statistics_view">
            <column name="year"/>
            <column name="duration"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>
