package de.adesso.fischereiregister.view.taxes_statistics.eventhandling;

import de.adesso.fischereiregister.core.events.FishingTaxPayedEvent;
import de.adesso.fischereiregister.core.events.LicenseExtendedEvent;
import de.adesso.fischereiregister.core.events.PersonCreatedEvent;
import de.adesso.fischereiregister.core.events.PersonalDataChangedEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseCreatedEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseDigitizedEvent;
import de.adesso.fischereiregister.core.events.ReplacementCardOrderedEvent;
import de.adesso.fischereiregister.core.events.VacationLicenseCreatedEvent;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import de.adesso.fischereiregister.core.model.PaymentInfo;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.ValidityPeriod;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.PaymentType;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.view.taxes_statistics.services.TaxesStatisticsViewService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;

@ExtendWith(MockitoExtension.class)
class TaxesStatisticsViewEventHandlerTest {

    private static final String FEDERAL_STATE = "SH";
    private static final String OFFICE = "TestOffice";
    private static final SubmissionType SUBMISSION_TYPE = SubmissionType.ANALOG;
    private static final Instant FIXED_TIMESTAMP = Instant.parse("2024-01-15T10:15:30.00Z");
    private static final int FIXED_YEAR = 2024;
    private static final double TAX_AMOUNT = 30.0;
    private static final double ZERO_AMOUNT = 0.0;
    private static final int TAX_DURATION = 1; // 1 year duration

    @Mock
    private TaxesStatisticsViewService taxesStatisticsViewServiceMock;

    private TaxesStatisticsViewEventHandler eventHandler;

    @BeforeEach
    void setUp() {
        eventHandler = new TaxesStatisticsViewEventHandler(taxesStatisticsViewServiceMock);
    }

    @Test
    @DisplayName("TaxesStatisticsViewEventHandler.onReset should call deleteAll on TaxesStatisticsViewService")
    void testOnReset() {
        // When
        eventHandler.onReset();

        // Then
        verify(taxesStatisticsViewServiceMock).deleteAll();
    }


    @Test
    @DisplayName("TaxesStatisticsViewEventHandler.on(FishingTaxPayedEvent) should call updateOrCreateStatistic with correct parameters")
    void testOnFishingTaxPayedEvent() {
        // Given
        Tax tax = createTax();

        final FishingTaxPayedEvent event = new FishingTaxPayedEvent(
                UUID.randomUUID(),
                null,
                null,
                List.of(tax),
                null,
                List.of(),
                OFFICE,
                null,
                null,
                null,
                SUBMISSION_TYPE
        );

        // When
        eventHandler.on(event, FIXED_TIMESTAMP);

        // Then
        verify(taxesStatisticsViewServiceMock).updateOrCreateStatistic(
                FEDERAL_STATE,
                OFFICE,
                SUBMISSION_TYPE,
                TAX_DURATION,
                FIXED_YEAR,
                TAX_AMOUNT
        );
    }

    @Test
    @DisplayName("TaxesStatisticsViewEventHandler.on(FishingTaxPayedEvent) should not process taxes with zero amount")
    void testOnFishingTaxPayedEvent_ZeroAmount() {
        // Given
        Tax tax = createTaxWithZeroAmount();

        final FishingTaxPayedEvent event = new FishingTaxPayedEvent(
                UUID.randomUUID(),
                null,
                null,
                List.of(tax),
                null,
                List.of(),
                OFFICE,
                null,
                null,
                null,
                SUBMISSION_TYPE
        );

        // When
        eventHandler.on(event, FIXED_TIMESTAMP);

        // Then
        verifyNoInteractions(taxesStatisticsViewServiceMock);
    }

    @Test
    @DisplayName("TaxesStatisticsViewEventHandler.on(RegularLicenseCreatedEvent) should call updateOrCreateStatistic with correct parameters")
    void testOnRegularLicenseCreatedEvent() {
        // Given
        Tax tax = createTax();

        final RegularLicenseCreatedEvent event = new RegularLicenseCreatedEvent(
                UUID.randomUUID(),
                null,
                null,
                null,
                List.of(),
                List.of(tax),
                null,
                List.of(),
                null,
                OFFICE,
                null,
                null,
                null,
                null,
                SUBMISSION_TYPE
        );

        // When
        eventHandler.on(event, FIXED_TIMESTAMP);

        // Then
        verify(taxesStatisticsViewServiceMock).updateOrCreateStatistic(
                FEDERAL_STATE,
                OFFICE,
                SUBMISSION_TYPE,
                TAX_DURATION,
                FIXED_YEAR,
                TAX_AMOUNT
        );
    }

    @Test
    @DisplayName("TaxesStatisticsViewEventHandler.on(RegularLicenseCreatedEvent) should not call updateOrCreateStatistic when taxes list is empty")
    void testOnRegularLicenseCreatedEvent_EmptyTaxes() {
        // Given
        final RegularLicenseCreatedEvent event = new RegularLicenseCreatedEvent(
                UUID.randomUUID(),
                null,
                null,
                null,
                List.of(),
                List.of(),
                null,
                List.of(),
                null,
                OFFICE,
                null,
                null,
                null,
                null,
                SUBMISSION_TYPE
        );

        // When
        eventHandler.on(event, FIXED_TIMESTAMP);

        // Then
        verifyNoInteractions(taxesStatisticsViewServiceMock);
    }

    @Test
    @DisplayName("TaxesStatisticsViewEventHandler.on(RegularLicenseCreatedEvent) should not process taxes with zero amount")
    void testOnRegularLicenseCreatedEvent_ZeroAmount() {
        // Given
        Tax tax = createTaxWithZeroAmount();

        final RegularLicenseCreatedEvent event = new RegularLicenseCreatedEvent(
                UUID.randomUUID(),
                null,
                null,
                null,
                List.of(),
                List.of(tax),
                null,
                List.of(),
                null,
                OFFICE,
                null,
                null,
                null,
                null,
                SUBMISSION_TYPE
        );

        // When
        eventHandler.on(event, FIXED_TIMESTAMP);

        // Then
        verifyNoInteractions(taxesStatisticsViewServiceMock);
    }

    @Test
    @DisplayName("TaxesStatisticsViewEventHandler.on(RegularLicenseDigitizedEvent) should call updateOrCreateStatistic with correct parameters")
    void testOnRegularLicenseDigitizedEvent() {
        // Given
        Tax tax = createTax();

        Jurisdiction jurisdiction = new Jurisdiction();
        jurisdiction.setFederalState(FEDERAL_STATE);

        FishingLicense fishingLicense = new FishingLicense();

        final RegularLicenseDigitizedEvent event = new RegularLicenseDigitizedEvent(
                UUID.randomUUID(),
                null,
                null,
                jurisdiction,
                fishingLicense,
                List.of(),
                List.of(tax),
                List.of(),
                List.of(),
                null,
                OFFICE,
                null
        );

        // When
        eventHandler.on(event, FIXED_TIMESTAMP);

        // Then
        verify(taxesStatisticsViewServiceMock).updateOrCreateStatistic(
                FEDERAL_STATE,
                OFFICE,
                SubmissionType.ANALOG,
                TAX_DURATION,
                FIXED_YEAR,
                TAX_AMOUNT
        );
    }

    @Test
    @DisplayName("TaxesStatisticsViewEventHandler.on(RegularLicenseDigitizedEvent) should not process taxes with zero amount")
    void testOnRegularLicenseDigitizedEvent_ZeroAmount() {
        // Given
        Tax tax = createTaxWithZeroAmount();

        Jurisdiction jurisdiction = new Jurisdiction();
        jurisdiction.setFederalState(FEDERAL_STATE);

        FishingLicense fishingLicense = new FishingLicense();

        final RegularLicenseDigitizedEvent event = new RegularLicenseDigitizedEvent(
                UUID.randomUUID(),
                null,
                null,
                jurisdiction,
                fishingLicense,
                List.of(),
                List.of(tax),
                List.of(),
                List.of(),
                null,
                OFFICE,
                null
        );

        // When
        eventHandler.on(event, FIXED_TIMESTAMP);

        // Then
        verifyNoInteractions(taxesStatisticsViewServiceMock);
    }

    @Test
    @DisplayName("TaxesStatisticsViewEventHandler.on(VacationLicenseCreatedEvent) should call updateOrCreateStatistic with correct parameters")
    void testOnVacationLicenseCreatedEvent() {
        // Given
        Tax tax = createTax();

        final VacationLicenseCreatedEvent event = new VacationLicenseCreatedEvent(
                UUID.randomUUID(),
                null,
                null,
                null,
                List.of(),
                List.of(tax),
                List.of(),
                null,
                OFFICE,
                null,
                null,
                null,
                SUBMISSION_TYPE
        );

        // When
        eventHandler.on(event, FIXED_TIMESTAMP);

        // Then
        verify(taxesStatisticsViewServiceMock).updateOrCreateStatistic(
                FEDERAL_STATE,
                OFFICE,
                SUBMISSION_TYPE,
                TAX_DURATION,
                FIXED_YEAR,
                TAX_AMOUNT
        );
    }

    @Test
    @DisplayName("TaxesStatisticsViewEventHandler.on(VacationLicenseCreatedEvent) should not process taxes with zero amount")
    void testOnVacationLicenseCreatedEvent_ZeroAmount() {
        // Given
        Tax tax = createTaxWithZeroAmount();

        final VacationLicenseCreatedEvent event = new VacationLicenseCreatedEvent(
                UUID.randomUUID(),
                null,
                null,
                null,
                List.of(),
                List.of(tax),
                List.of(),
                null,
                OFFICE,
                null,
                null,
                null,
                SUBMISSION_TYPE
        );

        // When
        eventHandler.on(event, FIXED_TIMESTAMP);

        // Then
        verifyNoInteractions(taxesStatisticsViewServiceMock);
    }

    @Test
    @DisplayName("TaxesStatisticsViewEventHandler.on(LicenseExtendedEvent) should call updateOrCreateStatistic with correct parameters")
    void testOnLicenseExtendedEvent() {
        // Given
        Tax tax = createTax();

        ValidityPeriod validityPeriod = new ValidityPeriod();
        validityPeriod.setValidFrom(LocalDate.now());
        validityPeriod.setValidTo(LocalDate.now().plusYears(1));

        final LicenseExtendedEvent event = new LicenseExtendedEvent(
                UUID.randomUUID(),
                null,
                null,
                null,
                List.of(),
                List.of(tax),
                "12345",
                validityPeriod,
                List.of(),
                null,
                null,
                null,
                OFFICE,
                SUBMISSION_TYPE
        );

        // When
        eventHandler.on(event, FIXED_TIMESTAMP);

        // Then
        verify(taxesStatisticsViewServiceMock).updateOrCreateStatistic(
                FEDERAL_STATE,
                OFFICE,
                SUBMISSION_TYPE,
                TAX_DURATION,
                FIXED_YEAR,
                TAX_AMOUNT
        );
    }

    @Test
    @DisplayName("TaxesStatisticsViewEventHandler.on(LicenseExtendedEvent) should not process taxes with zero amount")
    void testOnLicenseExtendedEvent_ZeroAmount() {
        // Given
        Tax tax = createTaxWithZeroAmount();

        ValidityPeriod validityPeriod = new ValidityPeriod();
        validityPeriod.setValidFrom(LocalDate.now());
        validityPeriod.setValidTo(LocalDate.now().plusYears(1));

        final LicenseExtendedEvent event = new LicenseExtendedEvent(
                UUID.randomUUID(),
                null,
                null,
                null,
                List.of(),
                List.of(tax),
                "12345",
                validityPeriod,
                List.of(),
                null,
                null,
                null,
                OFFICE,
                SUBMISSION_TYPE
        );

        // When
        eventHandler.on(event, FIXED_TIMESTAMP);

        // Then
        verifyNoInteractions(taxesStatisticsViewServiceMock);
    }

    @Test
    @DisplayName("TaxesStatisticsViewEventHandler.on(ReplacementCardOrderedEvent) should call updateOrCreateStatistic with correct parameters")
    void testOnReplacementCardOrderedEvent() {
        // Given
        Tax tax = createTax();

        final ReplacementCardOrderedEvent event = new ReplacementCardOrderedEvent(
                UUID.randomUUID(),
                null,
                null,
                List.of(),
                null,
                FederalState.valueOf(FEDERAL_STATE),
                OFFICE,
                null, // issuedByAddress
                List.of(),
                List.of(tax),
                null,
                null, // inboxReference
                null,
                null, // transactionId
                SUBMISSION_TYPE
        );

        // When
        eventHandler.on(event, FIXED_TIMESTAMP);

        // Then
        verify(taxesStatisticsViewServiceMock).updateOrCreateStatistic(
                FEDERAL_STATE,
                OFFICE,
                SUBMISSION_TYPE,
                TAX_DURATION,
                FIXED_YEAR,
                TAX_AMOUNT
        );
    }

    @Test
    @DisplayName("TaxesStatisticsViewEventHandler.on(ReplacementCardOrderedEvent) should not process taxes with zero amount")
    void testOnReplacementCardOrderedEvent_ZeroAmount() {
        // Given
        Tax tax = createTaxWithZeroAmount();

        final ReplacementCardOrderedEvent event = new ReplacementCardOrderedEvent(
                UUID.randomUUID(),
                null,
                null,
                List.of(),
                null,
                FederalState.valueOf(FEDERAL_STATE),
                OFFICE,
                null, // issuedByAddress
                List.of(),
                List.of(tax),
                null,
                null, // inboxReference
                null,
                null,
                SUBMISSION_TYPE
        );

        // When
        eventHandler.on(event, FIXED_TIMESTAMP);

        // Then
        verifyNoInteractions(taxesStatisticsViewServiceMock);
    }

    @Test
    @DisplayName("TaxesStatisticsViewEventHandler.on(PersonCreatedEvent) should call updateOrCreateStatistic with correct parameters")
    void testOnPersonCreatedEvent() {
        // Given
        Tax tax = createTax();

        final PersonCreatedEvent event = new PersonCreatedEvent(
                UUID.randomUUID(),
                null,
                List.of(tax),
                List.of(),
                null,
                List.of(),
                null,
                OFFICE,
                null,
                null,
                null,
                FEDERAL_STATE,
                SUBMISSION_TYPE
        );

        // When
        eventHandler.on(event, FIXED_TIMESTAMP);

        // Then
        verify(taxesStatisticsViewServiceMock).updateOrCreateStatistic(
                FEDERAL_STATE,
                OFFICE,
                SUBMISSION_TYPE,
                TAX_DURATION,
                FIXED_YEAR,
                TAX_AMOUNT
        );
    }

    @Test
    @DisplayName("TaxesStatisticsViewEventHandler.on(PersonCreatedEvent) should not process taxes with zero amount")
    void testOnPersonCreatedEvent_ZeroAmount() {
        // Given
        Tax tax = createTaxWithZeroAmount();

        final PersonCreatedEvent event = new PersonCreatedEvent(
                UUID.randomUUID(),
                null,
                List.of(tax),
                List.of(),
                null,
                List.of(),
                null,
                OFFICE,
                null,
                null,
                null,
                FEDERAL_STATE,
                SUBMISSION_TYPE
        );

        // When
        eventHandler.on(event, FIXED_TIMESTAMP);

        // Then
        verifyNoInteractions(taxesStatisticsViewServiceMock);
    }

    @Test
    @DisplayName("TaxesStatisticsViewEventHandler.on(PersonalDataChangedEvent) should call updateOrCreateStatistic with correct parameters")
    void testOnPersonalDataChangedEvent() {
        // Given
        Tax tax = createTax();

        final PersonalDataChangedEvent event = new PersonalDataChangedEvent(
                UUID.randomUUID(),
                null,
                List.of(tax),
                null,
                null,
                List.of(),
                OFFICE
        );

        // When
        eventHandler.on(event, FIXED_TIMESTAMP);

        // Then
        verify(taxesStatisticsViewServiceMock).updateOrCreateStatistic(
                FEDERAL_STATE,
                OFFICE,
                SubmissionType.ANALOG,
                TAX_DURATION,
                FIXED_YEAR,
                TAX_AMOUNT
        );
    }

    @Test
    @DisplayName("TaxesStatisticsViewEventHandler.on(PersonalDataChangedEvent) should not process taxes with zero amount")
    void testOnPersonalDataChangedEvent_ZeroAmount() {
        // Given
        Tax tax = createTaxWithZeroAmount();

        final PersonalDataChangedEvent event = new PersonalDataChangedEvent(
                UUID.randomUUID(),
                null,
                List.of(tax),
                null,
                null,
                List.of(),
                OFFICE
        );

        // When
        eventHandler.on(event, FIXED_TIMESTAMP);

        // Then
        verifyNoInteractions(taxesStatisticsViewServiceMock);
    }

    private Tax createTax() {
        Tax tax = new Tax();
        tax.setFederalState(FEDERAL_STATE);
        tax.setValidFrom(LocalDate.of(2024, 1, 1));
        tax.setValidTo(LocalDate.of(2024, 12, 31));

        PaymentInfo paymentInfo = new PaymentInfo();
        paymentInfo.setAmount(TAX_AMOUNT);
        paymentInfo.setType(PaymentType.ONLINE);

        tax.setPaymentInfo(paymentInfo);

        return tax;
    }

    private Tax createTaxWithZeroAmount() {
        Tax tax = new Tax();
        tax.setFederalState(FEDERAL_STATE);
        tax.setValidFrom(LocalDate.of(2024, 1, 1));
        tax.setValidTo(LocalDate.of(2024, 12, 31));

        PaymentInfo paymentInfo = new PaymentInfo();
        paymentInfo.setAmount(ZERO_AMOUNT);
        paymentInfo.setType(PaymentType.ONLINE);

        tax.setPaymentInfo(paymentInfo);

        return tax;
    }
}
