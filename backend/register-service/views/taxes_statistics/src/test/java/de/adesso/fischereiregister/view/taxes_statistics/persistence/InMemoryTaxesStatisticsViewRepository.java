package de.adesso.fischereiregister.view.taxes_statistics.persistence;

import de.adesso.fischereiregister.core.model.type.SubmissionType;

import java.util.*;
import java.util.stream.Collectors;

public class InMemoryTaxesStatisticsViewRepository implements TaxesStatisticsViewRepository {

    private final Map<Long, TaxesStatisticsView> database = new HashMap<>();
    private long idCounter = 1;

    @Override
    public Optional<TaxesStatisticsView> findByFederalStateAndOfficeAndSourceAndDurationAndYear(
            String federalState, String office, SubmissionType source, int duration, int year) {
        return database.values().stream()
                .filter(view -> view.getFederalState().equals(federalState))
                .filter(view -> Objects.equals(view.getOffice(), office))
                .filter(view -> view.getSource().equals(source))
                .filter(view -> view.getDuration() == duration)
                .filter(view -> view.getYear() == year)
                .findFirst();
    }

    @Override
    public List<TaxesStatisticsView> findByFederalStateAndYearIn(String federalState, List<Integer> years) {
        return database.values().stream()
                .filter(view -> view.getFederalState().equals(federalState))
                .filter(view -> years.contains(view.getYear()))
                .collect(Collectors.toList());
    }

    @Override
    public List<TaxesStatisticsView> findByOfficeAndYearIn(String office, List<Integer> years) {
        return database.values().stream()
                .filter(view -> Objects.equals(view.getOffice(), office))
                .filter(view -> years.contains(view.getYear()))
                .collect(Collectors.toList());
    }

    @Override
    public List<TaxesStatisticsView> findByYearIn(List<Integer> years) {
        return database.values().stream()
                .filter(view -> years.contains(view.getYear()))
                .collect(Collectors.toList());
    }

    @Override
    public List<Integer> findDistinctYears() {
        return database.values().stream()
                .map(TaxesStatisticsView::getYear)
                .distinct()
                .sorted(Comparator.reverseOrder())
                .collect(Collectors.toList());
    }

    @Override
    public List<String> findDistinctOffices() {
        return database.values().stream()
                .map(TaxesStatisticsView::getOffice)
                .distinct()
                .sorted()
                .collect(Collectors.toList());
    }

    @Override
    public List<String> findDistinctOfficesByYears(List<Integer> years) {
        return database.values().stream()
                .filter(view -> years.contains(view.getYear()))
                .map(TaxesStatisticsView::getOffice)
                .distinct()
                .sorted()
                .collect(Collectors.toList());
    }

    @Override
    public List<String> findDistinctOfficesByFederalState(String federalState) {
        return database.values().stream()
                .filter(view -> view.getFederalState().equals(federalState))
                .map(TaxesStatisticsView::getOffice)
                .distinct()
                .sorted()
                .collect(Collectors.toList());
    }

    @Override
    public List<String> findDistinctOfficesByFederalStateAndYears(String federalState, List<Integer> years) {
        return database.values().stream()
                .filter(view -> view.getFederalState().equals(federalState))
                .filter(view -> years.contains(view.getYear()))
                .map(TaxesStatisticsView::getOffice)
                .distinct()
                .sorted()
                .collect(Collectors.toList());
    }

    @Override
    public <S extends TaxesStatisticsView> S save(S entity) {
        if (entity.getId() == null) {
            entity.setId(idCounter++);
        }
        database.put(entity.getId(), entity);
        return entity;
    }

    @Override
    public <S extends TaxesStatisticsView> Iterable<S> saveAll(Iterable<S> entities) {
        entities.forEach(this::save);
        return entities;
    }

    @Override
    public Optional<TaxesStatisticsView> findById(Long id) {
        return Optional.ofNullable(database.get(id));
    }

    @Override
    public boolean existsById(Long id) {
        return database.containsKey(id);
    }

    @Override
    public Iterable<TaxesStatisticsView> findAll() {
        return database.values();
    }

    @Override
    public Iterable<TaxesStatisticsView> findAllById(Iterable<Long> ids) {
        List<TaxesStatisticsView> result = new ArrayList<>();
        for (Long id : ids) {
            TaxesStatisticsView view = database.get(id);
            if (view != null) {
                result.add(view);
            }
        }
        return result;
    }

    @Override
    public long count() {
        return database.size();
    }

    @Override
    public void deleteById(Long id) {
        database.remove(id);
    }

    @Override
    public void delete(TaxesStatisticsView entity) {
        database.remove(entity.getId());
    }

    @Override
    public void deleteAllById(Iterable<? extends Long> ids) {
        for (Long id : ids) {
            database.remove(id);
        }
    }

    @Override
    public void deleteAll(Iterable<? extends TaxesStatisticsView> entities) {
        for (TaxesStatisticsView entity : entities) {
            database.remove(entity.getId());
        }
    }

    @Override
    public void deleteAll() {
        database.clear();
    }
}
