package de.adesso.fischereiregister.view.licenses_statistics.eventhandling;

import de.adesso.fischereiregister.core.events.LimitedLicenseCreatedEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseCreatedEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseDigitizedEvent;
import de.adesso.fischereiregister.core.events.VacationLicenseCreatedEvent;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import de.adesso.fischereiregister.view.licenses_statistics.services.LicensesStatisticsViewService;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Instant;
import java.time.ZoneId;
import java.util.List;
import java.util.UUID;

import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class LicensesStatisticsViewEventHandlerTest {

    @Mock
    private LicensesStatisticsViewService licensesStatisticsViewServiceMock;

    @InjectMocks
    private LicensesStatisticsViewEventHandler eventHandler;

    private static final String FEDERAL_STATE = "SH";
    private static final String OFFICE = "office";
    private static final LicenseType LICENSE_TYPE = LicenseType.REGULAR;
    private static final SubmissionType SUBMISSION_TYPE = SubmissionType.ANALOG;
    private static final Instant FIXED_TIMESTAMP = Instant.parse("2023-10-01T12:00:00Z");
    private static final int FIXED_YEAR = FIXED_TIMESTAMP.atZone(ZoneId.systemDefault()).getYear();


    @Test
    @DisplayName("LicensesStatisticsViewEventHandler.on(FishingLicenseDigitizedEvent) should call updateOrCreateStatistic with correct parameters")
    void testOnFishingLicenseDigitizedEvent() {
        // Given
        Jurisdiction jurisdiction = new Jurisdiction();
        jurisdiction.setFederalState(FEDERAL_STATE);

        FishingLicense fishingLicense = new FishingLicense();
        fishingLicense.setType(LICENSE_TYPE);

        final RegularLicenseDigitizedEvent event = new RegularLicenseDigitizedEvent(
                UUID.randomUUID(),
                null,
                null,
                jurisdiction,
                fishingLicense,
                List.of(),
                List.of(),
                List.of(),
                List.of(),
                null,
                OFFICE,
                null
        );

        // When
        eventHandler.on(event, FIXED_TIMESTAMP);

        // Then
        verify(licensesStatisticsViewServiceMock).updateOrCreateStatistic(
                LICENSE_TYPE, FEDERAL_STATE,
                OFFICE,
                SubmissionType.ANALOG, // Digitization always uses ANALOG
                FIXED_YEAR
        );
    }

    @Test
    @DisplayName("LicensesStatisticsViewEventHandler.on(FishingLicenseCreatedEvent) should call updateOrCreateStatistic with correct parameters")
    void testOnFishingLicenseCreatedEvent() {
        // Given
        Jurisdiction jurisdiction = new Jurisdiction();
        jurisdiction.setFederalState(FEDERAL_STATE);

        FishingLicense fishingLicense = new FishingLicense();
        fishingLicense.setType(LICENSE_TYPE);

        final RegularLicenseCreatedEvent event = new RegularLicenseCreatedEvent(
                UUID.randomUUID(),
                null,
                null,
                null,
                List.of(),
                List.of(),
                fishingLicense,
                List.of(),
                jurisdiction,
                OFFICE,
                null,
                null,
                null,
                null,
                SUBMISSION_TYPE
        );

        // When
        eventHandler.on(event, FIXED_TIMESTAMP);

        // Then
        verify(licensesStatisticsViewServiceMock).updateOrCreateStatistic(
                LICENSE_TYPE,
                FEDERAL_STATE,
                OFFICE,
                SUBMISSION_TYPE,
                FIXED_YEAR
        );
    }


    @Test
    @DisplayName("LicensesStatisticsViewEventHandler.on(VacationLicenseCreatedEvent) should call updateOrCreateStatistic with correct parameters")
    void testOnVacationLicenseCreatedEvent() {
        // Given
        FishingLicense fishingLicense = new FishingLicense();
        fishingLicense.setIssuingFederalState(FederalState.valueOf(FEDERAL_STATE));

        final VacationLicenseCreatedEvent event = new VacationLicenseCreatedEvent(
                UUID.randomUUID(),
                null,
                null,
                null,
                List.of(),
                List.of(),
                List.of(),
                fishingLicense,
                OFFICE,
                null,
                null,
                null,
                SUBMISSION_TYPE
        );

        // When
        eventHandler.on(event, FIXED_TIMESTAMP);

        // Then
        verify(licensesStatisticsViewServiceMock).updateOrCreateStatistic(
                fishingLicense.getType(),
                FEDERAL_STATE,
                OFFICE,
                SUBMISSION_TYPE,
                FIXED_YEAR
        );
    }



    @Test
    public void testLimitedLicenseCreation() {

        FishingLicense fishingLicense = new FishingLicense();
        fishingLicense.setIssuingFederalState(FederalState.valueOf(FEDERAL_STATE));
        Jurisdiction jurisdiction = DomainTestData.createJurisdiction();
        jurisdiction.setFederalState(FEDERAL_STATE);
        //GIVEN

        final LimitedLicenseCreatedEvent event = new LimitedLicenseCreatedEvent(
                UUID.randomUUID(),
                null,
                null,
                null,
                List.of(),
                List.of(),
                fishingLicense,
                List.of(),
                jurisdiction,
                OFFICE,
                "",
                null,
                null,
                null,
                SUBMISSION_TYPE
        );
        //WHEN
        eventHandler.on(event, FIXED_TIMESTAMP);

        //THEN
        // Then
        verify(licensesStatisticsViewServiceMock).updateOrCreateStatistic(
                fishingLicense.getType(),
                FEDERAL_STATE,
                OFFICE,
                SUBMISSION_TYPE,
                FIXED_YEAR
        );
    }
}