package de.adesso.fischereiregister.view.licenses_statistics.services;

import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.view.licenses_statistics.persistance.InMemoryLicensesStatisticsViewRepository;
import de.adesso.fischereiregister.view.licenses_statistics.persistance.LicensesStatisticsView;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class LicensesStatisticsViewServiceImplTest {

    private InMemoryLicensesStatisticsViewRepository repository;
    private LicensesStatisticsViewServiceImpl licensesStatisticsViewServiceImpl;

    @BeforeEach
    void setUp() {
        repository = new InMemoryLicensesStatisticsViewRepository();
        licensesStatisticsViewServiceImpl = new LicensesStatisticsViewServiceImpl(repository);
    }

    @Test
    @DisplayName("LicensesStatisticsViewServiceImpl.updateOrCreateStatistic should create a new entry if none exists")
    void testUpdateOrCreateStatistic_NewEntry() {
        // Given
        String federalState = "SH";
        String office = "office";
        SubmissionType source = SubmissionType.ANALOG;
        LicenseType licenseType = LicenseType.REGULAR;
        int year = 2024;

        // When
        licensesStatisticsViewServiceImpl.updateOrCreateStatistic(licenseType, federalState, office, source, year);

        // Then
        Optional<LicensesStatisticsView> result = repository.findByLicenseTypeAndFederalStateAndOfficeAndSourceAndYear(licenseType, federalState, office, source, year);
        assertTrue(result.isPresent());
        assertEquals(1, result.get().getCount());
    }

    @Test
    @DisplayName("LicensesStatisticsViewServiceImpl.updateOrCreateStatistic should update an existing entry by incrementing its count")
    void testUpdateOrCreateStatistic_UpdateExistingEntry() {
        // Given
        String federalState = "SH";
        String office = "office";
        SubmissionType source = SubmissionType.ANALOG;
        LicenseType licenseType = LicenseType.REGULAR;
        int year = 2024;

        LicensesStatisticsView existingEntry = new LicensesStatisticsView();
        existingEntry.setFederalState(federalState);
        existingEntry.setOffice(office);
        existingEntry.setSource(source);
        existingEntry.setLicenseType(licenseType);
        existingEntry.setYear(year);
        existingEntry.setCount(5);
        repository.save(existingEntry);

        // When
        licensesStatisticsViewServiceImpl.updateOrCreateStatistic(licenseType, federalState, office, source, year);

        // Then
        Optional<LicensesStatisticsView> result = repository.findByLicenseTypeAndFederalStateAndOfficeAndSourceAndYear(licenseType, federalState, office, source, year);
        assertTrue(result.isPresent());
        assertEquals(6, result.get().getCount());
    }

    @Test
    @DisplayName("LicensesStatisticsViewServiceImpl.getAvailableOffices Should get available offices without filters")
    void shouldGetAvailableOfficesWithoutFilters() {
        // given
        createTestData(1L, "SH", "Office1", LicenseType.REGULAR, SubmissionType.ONLINE, 2023);
        createTestData(2L, "NI", "Office2", LicenseType.REGULAR, SubmissionType.ANALOG, 2024);
        createTestData(3L, "SH", "Office1", LicenseType.VACATION, SubmissionType.ONLINE, 2024); // Duplicate office

        // when
        var result = licensesStatisticsViewServiceImpl.getAvailableOffices();

        // then
        assertEquals(2, result.size());
        assertTrue(result.contains("Office1"));
        assertTrue(result.contains("Office2"));
    }

    @Test
    @DisplayName("LicensesStatisticsViewServiceImpl.getAvailableOfficesByYears Should get available offices filtered by years")
    void shouldGetAvailableOfficesFilteredByYears() {
        // given
        createTestData(4L, "SH", "Office1", LicenseType.REGULAR, SubmissionType.ONLINE, 2023);
        createTestData(5L, "NI", "Office2", LicenseType.REGULAR, SubmissionType.ANALOG, 2023);
        createTestData(6L, "SH", "Office3", LicenseType.VACATION, SubmissionType.ONLINE, 2024);

        // when
        var result = licensesStatisticsViewServiceImpl.getAvailableOfficesByYears(java.util.List.of(2023));

        // then
        assertEquals(2, result.size());
        assertTrue(result.contains("Office1"));
        assertTrue(result.contains("Office2"));
    }

    @Test
    @DisplayName("LicensesStatisticsViewServiceImpl.getAvailableOfficesByFederalState Should get available offices filtered by federal state")
    void shouldGetAvailableOfficesFilteredByFederalState() {
        // given
        createTestData(7L, "SH", "Office1", LicenseType.REGULAR, SubmissionType.ONLINE, 2023);
        createTestData(8L, "NI", "Office2", LicenseType.REGULAR, SubmissionType.ANALOG, 2023);
        createTestData(9L, "SH", "Office3", LicenseType.VACATION, SubmissionType.ONLINE, 2024);

        // when
        var result = licensesStatisticsViewServiceImpl.getAvailableOfficesByFederalState("SH");

        // then
        assertEquals(2, result.size());
        assertTrue(result.contains("Office1"));
        assertTrue(result.contains("Office3"));
    }

    @Test
    @DisplayName("LicensesStatisticsViewServiceImpl.getAvailableOfficesByYearsAndFederalState Should get available offices filtered by years and federal state")
    void shouldGetAvailableOfficesFilteredByYearsAndFederalState() {
        // given
        createTestData(10L, "SH", "Office1", LicenseType.REGULAR, SubmissionType.ONLINE, 2023);
        createTestData(11L, "NI", "Office2", LicenseType.REGULAR, SubmissionType.ANALOG, 2023);
        createTestData(12L, "SH", "Office3", LicenseType.VACATION, SubmissionType.ONLINE, 2024);

        // when
        var result = licensesStatisticsViewServiceImpl.getAvailableOfficesByYearsAndFederalState(java.util.List.of(2023), "SH");

        // then
        assertEquals(1, result.size());
        assertTrue(result.contains("Office1"));
    }

    private void createTestData(Long id, String federalState, String office, LicenseType licenseType, SubmissionType source, int year) {
        LicensesStatisticsView entry = new LicensesStatisticsView();
        entry.setId(id);
        entry.setFederalState(federalState);
        entry.setOffice(office);
        entry.setLicenseType(licenseType);
        entry.setSource(source);
        entry.setYear(year);
        entry.setCount(1);
        repository.save(entry);
    }
}