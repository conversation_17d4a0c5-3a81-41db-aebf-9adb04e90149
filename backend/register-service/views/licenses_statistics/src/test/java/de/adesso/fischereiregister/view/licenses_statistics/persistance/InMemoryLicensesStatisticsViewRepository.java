package de.adesso.fischereiregister.view.licenses_statistics.persistance;

import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.testutils.inmemory.jpa.InMemoryCrudRepository;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

public class InMemoryLicensesStatisticsViewRepository extends InMemoryCrudRepository<LicensesStatisticsView, Long> implements LicensesStatisticsViewRepository {

    @Override
    protected Long getID(LicensesStatisticsView entity) {
        return entity.getId();
    }

    @Override
    public Optional<LicensesStatisticsView> findByLicenseTypeAndFederalStateAndOfficeAndSourceAndYear(
            LicenseType licenseType, String federalState, String office, SubmissionType source, int year) {
        return save.values().stream()
                .filter(entry -> entry.getLicenseType().equals(licenseType))
                .filter(entry -> entry.getFederalState().equals(federalState))
                .filter(entry -> Objects.equals(entry.getOffice(), office))
                .filter(entry -> entry.getSource().equals(source))
                .filter(entry -> entry.getYear() == year)
                .findFirst();
    }

    @Override
    public List<LicensesStatisticsView> findByLicenseTypeAndFederalStateAndYearIn(
            LicenseType licenseType, String federalState, List<Integer> years) {
        return save.values().stream()
                .filter(entry -> entry.getLicenseType().equals(licenseType))
                .filter(entry -> entry.getFederalState().equals(federalState))
                .filter(entry -> years.contains(entry.getYear()))
                .collect(Collectors.toList());
    }

    @Override
    public List<LicensesStatisticsView> findByLicenseTypeAndOfficeAndYearIn(
            LicenseType licenseType, String office, List<Integer> years) {
        return save.values().stream()
                .filter(entry -> entry.getLicenseType().equals(licenseType))
                .filter(entry -> Objects.equals(entry.getOffice(), office))
                .filter(entry -> years.contains(entry.getYear()))
                .collect(Collectors.toList());
    }

    @Override
    public List<LicensesStatisticsView> findByLicenseTypeAndYearIn(
            LicenseType licenseType, List<Integer> years) {
        return save.values().stream()
                .filter(entry -> entry.getLicenseType().equals(licenseType))
                .filter(entry -> years.contains(entry.getYear()))
                .collect(Collectors.toList());
    }

    @Override
    public List<Integer> findDistinctYears() {
        return save.values().stream()
                .map(LicensesStatisticsView::getYear)
                .distinct()
                .sorted()
                .collect(Collectors.toList());
    }

    @Override
    public List<LicensesStatisticsView> findByFederalStateAndYearIn(String federalState, List<Integer> years) {
        return save.values().stream()
                .filter(entry -> entry.getFederalState().equals(federalState))
                .filter(entry -> years.contains(entry.getYear()))
                .collect(Collectors.toList());
    }

    @Override
    public List<LicensesStatisticsView> findByYearIn(List<Integer> years) {
        return save.values().stream()
                .filter(entry -> years.contains(entry.getYear()))
                .collect(Collectors.toList());
    }

    @Override
    public List<String> findDistinctOffices() {
        return save.values().stream()
                .map(LicensesStatisticsView::getOffice)
                .distinct()
                .sorted()
                .collect(Collectors.toList());
    }

    @Override
    public List<String> findDistinctOfficesByYears(List<Integer> years) {
        return save.values().stream()
                .filter(entry -> years.contains(entry.getYear()))
                .map(LicensesStatisticsView::getOffice)
                .distinct()
                .sorted()
                .collect(Collectors.toList());
    }

    @Override
    public List<String> findDistinctOfficesByFederalState(String federalState) {
        return save.values().stream()
                .filter(entry -> entry.getFederalState().equals(federalState))
                .map(LicensesStatisticsView::getOffice)
                .distinct()
                .sorted()
                .collect(Collectors.toList());
    }

    @Override
    public List<String> findDistinctOfficesByFederalStateAndYears(String federalState, List<Integer> years) {
        return save.values().stream()
                .filter(entry -> entry.getFederalState().equals(federalState))
                .filter(entry -> years.contains(entry.getYear()))
                .map(LicensesStatisticsView::getOffice)
                .distinct()
                .sorted()
                .collect(Collectors.toList());
    }
}
