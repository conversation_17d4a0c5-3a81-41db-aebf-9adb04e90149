package de.adesso.fischereiregister.view.licenses_statistics.services;

import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.view.licenses_statistics.persistance.LicensesStatisticsView;

import java.util.List;

/**
 * Manages license statistics, allowing updates or creation of entries.
 */
public interface LicensesStatisticsViewService {
    /**
     * Deletes all license statistics entries. Use only when Reset of view should happen, and replay of events is ensured.
     */
    void deleteAll();

    /**
     * Updates or creates a license statistic entry. If an entry exists for the given parameters, its count is incremented.
     * Otherwise, a new entry is created with a count of 1.
     *
     * @param licenseType  The type of license (e.g., Regular, Vacation or Limited).
     * @param federalState The federal state for the statistic.
     * @param office       The office for the statistic.
     * @param source       The submission source (e.g., online, in-person).
     * @param year         The year that the statistics account for
     */
    void updateOrCreateStatistic(LicenseType licenseType, String federalState, String office, SubmissionType source, int year);


    /**
     * Retrieves a list of license statistics based on the provided license type, federal state, and years.
     * This allows fetching statistics specific to a license type and federal state across multiple years.
     *
     * @param licenseType  The type of license (e.g., Regular, Vacation, or Limited) to filter the statistics by.
     * @param federalState The federal state for which the statistics are retrieved.
     * @param years        The list of years for which the statistics are requested.
     * @return A list of LicensesStatisticsView objects containing the requested statistics.
     */
    List<LicensesStatisticsView> getStatisticsByLicenseTypeAndFederalStateAndYears(LicenseType licenseType, String federalState, List<Integer> years);

    /**
     * Retrieves a list of license statistics based on the provided license type, office, and years.
     * This allows fetching statistics specific to a license type and office across multiple years.
     *
     * @param licenseType The type of license (e.g., Regular, Vacation, or Limited) to filter the statistics by.
     * @param office      The office for which the statistics are retrieved.
     * @param years       The list of years for which the statistics are requested.
     * @return A list of LicensesStatisticsView objects containing the requested statistics.
     */
    List<LicensesStatisticsView> getStatisticsByLicenseTypeAndOfficeAndYears(LicenseType licenseType, String office, List<Integer> years);

    /**
     * Retrieves a list of license statistics based on the provided license type and years.
     * This allows fetching statistics for a given license type across multiple years without filtering by office or federal state.
     *
     * @param licenseType The type of license (e.g., Regular, Vacation, or Limited) to filter the statistics by.
     * @param years       The list of years for which the statistics are requested.
     * @return A list of LicensesStatisticsView objects containing the requested statistics.
     */
    List<LicensesStatisticsView> getStatisticsByLicenseTypeAndYears(LicenseType licenseType, List<Integer> years);

    /**
     * Retrieves a list of available years for which license statistics data is available.
     * This allows determining which years have data stored for license statistics.
     *
     * @return A list of integers representing the years for which statistics are available.
     */
    List<Integer> getAvailableYears();

    /**
     * Retrieves all available license offices without any filtering.
     *
     * @return A list of distinct office names sorted alphabetically.
     */
    List<String> getAvailableOffices();

    /**
     * Retrieves available license offices filtered by years.
     *
     * @param years The years to filter by.
     * @return A list of distinct office names sorted alphabetically.
     */
    List<String> getAvailableOfficesByYears(List<Integer> years);

    /**
     * Retrieves available license offices filtered by federal state.
     *
     * @param federalState The federal state to filter by.
     * @return A list of distinct office names sorted alphabetically.
     */
    List<String> getAvailableOfficesByFederalState(String federalState);

    /**
     * Retrieves available license offices filtered by both years and federal state.
     *
     * @param years        The years to filter by.
     * @param federalState The federal state to filter by.
     * @return A list of distinct office names sorted alphabetically.
     */
    List<String> getAvailableOfficesByYearsAndFederalState(List<Integer> years, String federalState);
}
