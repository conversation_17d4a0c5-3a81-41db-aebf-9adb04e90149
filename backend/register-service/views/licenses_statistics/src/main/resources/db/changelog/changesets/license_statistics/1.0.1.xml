<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd"
                   context="licenses_statistics_view">

    <!-- ChangeSet for adding indexes to LicensesStatisticsView Table -->
    <changeSet id="1.0.1-indexes" author="alexis.ortiz">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="licenses_statistics_view"/>
        </preConditions>
        
        <!-- Index for year queries - used in findDistinctYears -->
        <createIndex indexName="idx_licenses_statistics_year" tableName="licenses_statistics_view">
            <column name="year"/>
        </createIndex>
        
        <!-- Index for license_type and year queries - used in findByLicenseTypeAndYearIn -->
        <createIndex indexName="idx_licenses_statistics_license_type_year" tableName="licenses_statistics_view">
            <column name="license_type"/>
            <column name="year"/>
        </createIndex>
        
        <!-- Index for federal_state, license_type and year queries - used in findByLicenseTypeAndFederalStateAndYearIn -->
        <createIndex indexName="idx_licenses_statistics_federal_state_license_type_year" tableName="licenses_statistics_view">
            <column name="federal_state"/>
            <column name="license_type"/>
            <column name="year"/>
        </createIndex>
        
        <!-- Index for office, license_type and year queries - used in findByLicenseTypeAndOfficeAndYearIn -->
        <createIndex indexName="idx_licenses_statistics_office_license_type_year" tableName="licenses_statistics_view">
            <column name="office"/>
            <column name="license_type"/>
            <column name="year"/>
        </createIndex>
        
        <!-- Index for source field which is used in various queries -->
        <createIndex indexName="idx_licenses_statistics_source" tableName="licenses_statistics_view">
            <column name="source"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>
