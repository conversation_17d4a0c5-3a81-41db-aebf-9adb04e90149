package de.adesso.fischereiregister.view.register_entry_search.mapper;

import de.adesso.fischereiregister.core.model.Birthdate;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.utils.DateUtils;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.openapitools.model.SearchItemPerson;

import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.*;

class SearchItemMapperTest {

    @DisplayName(" tests that the SearchItemMapper can map the Person to SearchItemPerson")
    @Test
    void testToSearchViewPerson() {
        // given
        Person person = new Person();
        person.setTitle("Dr.");
        person.setFirstname("John");
        person.setLastname("Doe");
        person.setBirthdate(Birthdate.parse("01.01.1980"));
        person.setBirthname("<PERSON><PERSON><PERSON>");
        person.setBirthplace("New York");

        // when
        SearchItemPerson searchPerson = SearchItemMapper.INSTANCE.toSearchItemPerson(person);

        // then
        assertNotNull(searchPerson);
        assertEquals("Dr.", searchPerson.getTitle());
        assertEquals("John", searchPerson.getFirstname());
        assertEquals("Doe", searchPerson.getLastname());
        assertEquals(LocalDate.of(1980, 1, 1).format(DateUtils.GERMAN_DATE_TIME_FORMATTER), searchPerson.getBirthdate());
        assertEquals("Johnathan", searchPerson.getBirthname());
        assertEquals("New York", searchPerson.getBirthplace());
    }

}