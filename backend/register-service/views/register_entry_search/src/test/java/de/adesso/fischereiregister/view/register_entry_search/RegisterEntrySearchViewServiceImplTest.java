package de.adesso.fischereiregister.view.register_entry_search;

import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.QualificationsProof;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import de.adesso.fischereiregister.view.register_entry_search.adapter.InMemoryRegisterEntrySearchViewRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.openapitools.model.SearchItem;
import org.openapitools.model.SearchItemFishingLicensesInner;
import org.openapitools.model.SearchItemPerson;
import org.openapitools.model.SearchItemQualificationsProofsInner;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class RegisterEntrySearchViewServiceImplTest {

    static final String IDENTIFICATION_NUMBER = "XX34-**************";
    static final String NAME = "Max";
    static final String BIRTHPLACE = "Musterstadt";
    static final String BIRTHDATE = "20.05.1990";
    static final UUID REGISTER_ID = UUID.randomUUID();

    SearchItem entry;

    @Mock
    private RegisterEntrySearchViewRepository repository;

    @Mock
    private JdbcTemplate jdbcTemplate;

    @InjectMocks
    private RegisterEntrySearchViewServiceImpl service;

    @Captor
    private ArgumentCaptor<RegisterEntrySearchView> viewCaptor;

    @BeforeEach
    void setUp() {
        repository.deleteAll();

        entry = new SearchItem();
        SearchItemPerson person = new SearchItemPerson();
        person.setTitle("DR.");
        person.setFirstname(NAME);
        person.setLastname("Mustermann");
        person.setBirthname("Max");
        person.setBirthplace(BIRTHPLACE);
        person.setBirthdate(BIRTHDATE);

        entry.setPerson(person);

        SearchItemFishingLicensesInner fishingLicense = new SearchItemFishingLicensesInner();
        fishingLicense.setNumber(IDENTIFICATION_NUMBER);
        entry.setFishingLicenses(List.of(fishingLicense));

        SearchItemQualificationsProofsInner qualificationsProof = new SearchItemQualificationsProofsInner();
        qualificationsProof.setFishingCertificateId("123456789");
        entry.setQualificationsProofs(List.of(qualificationsProof));
    }

    @Test
    @DisplayName("Test search - Identification Number")
    void testSearchIdentificationNumber() {
        // Given
        when(repository.findByIdentificationNumber("XX34567812345678"))
                .thenReturn(Optional.of(entry));

        // When
        List<SearchItem> result = service.search(IDENTIFICATION_NUMBER);

        // Then
        assertEquals(List.of(entry), result);
    }

    @Test
    @DisplayName("Test RegisterEntrySearchView")
    void testCreateRegisterEntrySearchView() {
        // Given
        Person person = DomainTestData.createPerson();

        // When
        service.createRegisterEntrySearchView(
                RegisterEntrySearchViewServiceParameters.builder()
                        .registerEntryId(DomainTestData.registerId)
                        .person(person)
                        .build());

        // Then
        verify(repository).save(viewCaptor.capture());

        RegisterEntrySearchView capturedView = viewCaptor.getValue();
        assertEquals(DomainTestData.registerId, capturedView.getRegisterId());
        assertEquals(person.getBirthdate().toString(), capturedView.getBirthdate());
    }

    @Test
    @DisplayName("Test search - Empty")
    void testSearchEmpty() {
        // Given
        String query = "";

        // When
        List<SearchItem> result = service.search(query);

        // Then
        assertEquals(0, result.size());
    }

    @Test
    @DisplayName("Test search - Unknown License Number")
    void testSearchUnknownLicenseNumber() {
        // Given
        String query = "SH85-5986-3119-1522";

        when(repository.findByIdentificationNumber("SH85598631191522")).thenReturn(Optional.empty());

        // When
        List<SearchItem> result = service.search(query);

        // Then
        assertEquals(0, result.size());
    }


    @Test
    @DisplayName("RegisterEntrySearchViewServiceImpl.findRegisterEntryIdByIdentificationNumber should return the RegisterEntryId given an identificationNumber")
    void testFindRegisterEntryIdByIdentificationNumberSuccess() {
        // Given
        String identificationNumber = "SH85598631191522";
        UUID registerEntryId = UUID.fromString("12345678-1234-1234-1234-123456789012");
        Person person = DomainTestData.createPerson();

        RegisterEntrySearchView view = new RegisterEntrySearchView();
        view.setRegisterId(registerEntryId);
        view.addIdentificationNumber(identificationNumber);

        view.setTitle(person.getTitle());
        view.setFirstname(person.getFirstname());
        view.setLastname(person.getLastname());
        view.setBirthname(person.getBirthname());
        view.setBirthplace(person.getBirthplace());
        view.setBirthdate(person.getBirthdate().toString());
        view.setNationality(person.getNationality());

        InMemoryRegisterEntrySearchViewRepository repository = new InMemoryRegisterEntrySearchViewRepository();
        repository.save(view);

        RegisterEntrySearchViewService service1 = new RegisterEntrySearchViewServiceImpl(repository, jdbcTemplate);

        // When
        Optional<UUID> result = service1.findRegisterEntryIdByIdentificationNumber(identificationNumber);

        // Then
        assertFalse(result.isEmpty());
        assertEquals(result.get(), registerEntryId);
    }


    @Test
    @DisplayName("RegisterEntrySearchViewServiceImpl.findRegisterEntryIdsByPersonDetails should return a list with a RegisterEntryId given Person Details")
    void testFindRegisterEntryIdsByPersonDetails_Success() {
        // Given
        String identificationNumber = "SH85598631191522";
        UUID registerEntryId = UUID.fromString("12345678-1234-1234-1234-123456789012");
        Person person = DomainTestData.createPerson();

        RegisterEntrySearchView view = new RegisterEntrySearchView();
        view.setRegisterId(registerEntryId);
        view.addIdentificationNumber(identificationNumber);

        view.setTitle(person.getTitle());
        view.setFirstname(person.getFirstname());
        view.setLastname(person.getLastname());
        view.setBirthname(person.getBirthname());
        view.setBirthplace(person.getBirthplace());
        view.setBirthdate(person.getBirthdate().toString());
        view.setNationality(person.getNationality());

        InMemoryRegisterEntrySearchViewRepository repository = new InMemoryRegisterEntrySearchViewRepository();
        repository.save(view);

        RegisterEntrySearchViewServiceImpl service1 = new RegisterEntrySearchViewServiceImpl(repository, jdbcTemplate);

        // When
        List<UUID> result = service1.findRegisterEntryIdsByPersonDetails(person);

        // Then
        assertFalse(result.isEmpty());
        assertEquals(result.getFirst(), registerEntryId);
    }

    @Test
    @DisplayName("RegisterEntrySearchViewServiceImpl.findRegisterEntryIdsByPersonDetails should return an empty list if Person is null")
    void testFindRegisterEntryIdsByPersonDetails_NullPerson() {
        // Given
        RegisterEntrySearchViewServiceImpl service1 = new RegisterEntrySearchViewServiceImpl(new InMemoryRegisterEntrySearchViewRepository(), jdbcTemplate);

        // When
        List<UUID> result = service1.findRegisterEntryIdsByPersonDetails(null);

        // Then
        assertTrue(result.isEmpty());
    }

    @Test
    @DisplayName("RegisterEntrySearchViewServiceImpl.findRegisterEntryIdsByPersonDetails should return empty list if no match is found")
    void testFindRegisterEntryIdsByPersonDetails_NoMatch() {
        // Given
        Person person = DomainTestData.createPerson();
        InMemoryRegisterEntrySearchViewRepository repository = new InMemoryRegisterEntrySearchViewRepository();

        RegisterEntrySearchViewServiceImpl service1 = new RegisterEntrySearchViewServiceImpl(repository, jdbcTemplate);

        // When
        List<UUID> result = service1.findRegisterEntryIdsByPersonDetails(person);

        // Then
        assertTrue(result.isEmpty());
    }

    @Test
    @DisplayName("RegisterEntrySearchViewServiceImpl.findRegisterEntryIdsByPersonDetails should default birthname to lastname if birthname is null")
    void testFindRegisterEntryIdsByPersonDetails_BirthnameDefaultsToLastname() {
        // Given
        Person person = DomainTestData.createPerson();
        UUID registerEntryId = UUID.fromString("12345678-1234-1234-1234-123456789012");

        RegisterEntrySearchView view = new RegisterEntrySearchView();
        view.setRegisterId(registerEntryId);

        view.setTitle(person.getTitle());
        view.setFirstname(person.getFirstname());
        view.setLastname(person.getLastname());
        view.setBirthname(person.getLastname()); // this person has birthname same as lastname
        view.setBirthplace(person.getBirthplace());
        view.setBirthdate(person.getBirthdate().toString());
        view.setNationality(person.getNationality());

        InMemoryRegisterEntrySearchViewRepository repository = new InMemoryRegisterEntrySearchViewRepository();
        repository.save(view);

        person.setBirthname(null);

        RegisterEntrySearchViewServiceImpl service1 = new RegisterEntrySearchViewServiceImpl(repository, jdbcTemplate);

        // When
        List<UUID> result = service1.findRegisterEntryIdsByPersonDetails(person);

        // Then
        assertFalse(result.isEmpty());
        assertEquals(1, result.size());
        assertEquals(registerEntryId, result.getFirst());
    }

    @Test
    void testUpdateRegisterEntrySearchViewWithInMemoryRepositories() {
        UUID registerID = UUID.fromString("12345678-1234-1234-1234-123456789012");
        RegisterEntrySearchView view = new RegisterEntrySearchView();
        view.setRegisterId(registerID);
        view.setNormalizedName("max muller");

        SearchItem viewData = new SearchItem();
        SearchItemPerson searchItemPerson = new SearchItemPerson();
        viewData.setPerson(searchItemPerson);

        view.setData(viewData);

        InMemoryRegisterEntrySearchViewRepository repository = new InMemoryRegisterEntrySearchViewRepository();
        repository.save(view);

        RegisterEntrySearchViewService service1 = new RegisterEntrySearchViewServiceImpl(repository, jdbcTemplate);
        Person person = DomainTestData.createPersonWithAddress();
        person.setFirstname("Tim");
        service1.updateRegisterEntrySearchView(
                RegisterEntrySearchViewServiceParameters.builder()
                        .registerEntryId(registerID)
                        .person(person).build());

        List<SearchItem> result = service1.search("Tim");
        assertEquals(1, result.size());
        assertEquals("Tim", result.getFirst().getPerson().getFirstname());
    }

    @Test
    @DisplayName("Test whether a person with taxes will be successfully created, and no jurisdiction is allowed to be set in this case")
    public void testCreateRegisterEntryViaTax() {
        Person person = DomainTestData.createPerson();

        service.createRegisterEntrySearchView(RegisterEntrySearchViewServiceParameters.builder()
                .registerEntryId(REGISTER_ID)
                .person(person)
                .build());

        ArgumentCaptor<RegisterEntrySearchView> argument = ArgumentCaptor.forClass(RegisterEntrySearchView.class);
        verify(repository).save(argument.capture());

        assertEquals(REGISTER_ID, argument.getValue().getRegisterId());
        assertTrue(argument.getValue().getIdentificationNumbers().isEmpty());

        assertSearchPersonEqual(person, argument.getValue());
        assertTrue(argument.getValue().getData().getFishingLicenses().isEmpty());
        assertEquals(0, argument.getValue().getData().getQualificationsProofs().size());
    }

    @Test
    @DisplayName("Test whether entering a person via the Digitization workflow is successful.")
    public void testCreateRegisterEntryViaLicense() {
        Person person = DomainTestData.createPerson();
        FishingLicense license = DomainTestData.createLicense();

        service.createRegisterEntrySearchView(RegisterEntrySearchViewServiceParameters.builder()
                .registerEntryId(REGISTER_ID)
                .person(person)
                .license(license)
                .build());

        ArgumentCaptor<RegisterEntrySearchView> argument = ArgumentCaptor.forClass(RegisterEntrySearchView.class);
        verify(repository).save(argument.capture());

        assertEquals(REGISTER_ID, argument.getValue().getRegisterId());
        assertTrue(argument.getValue().getIdentificationNumbers().contains(license.getNumber()));

        assertSearchPersonEqual(person, argument.getValue());
        assertEquals(0, argument.getValue().getData().getQualificationsProofs().size());

        assertEquals(1, argument.getValue().getData().getFishingLicenses().size());
        SearchItemFishingLicensesInner actualLicense = argument.getValue().getData().getFishingLicenses().getFirst();
        assertEquals(license.getNumber(), actualLicense.getNumber());
    }

    @Test
    @DisplayName("Test whether entering a person via a certificate from the examiner is successfull.")
    public void testCreateRegisterEntryViaCertificate() {
        Person person = DomainTestData.createPerson();
        QualificationsProof certificate = DomainTestData.createQualificationsProof();
        String fishingCertificateId = certificate.getFishingCertificateId();

        service.createRegisterEntrySearchView(RegisterEntrySearchViewServiceParameters.builder()
                .registerEntryId(REGISTER_ID)
                .person(person)
                .certificate(certificate)
                .build());

        ArgumentCaptor<RegisterEntrySearchView> argument = ArgumentCaptor.forClass(RegisterEntrySearchView.class);
        verify(repository).save(argument.capture());

        assertEquals(REGISTER_ID, argument.getValue().getRegisterId());
        assertTrue(argument.getValue().getIdentificationNumbers().contains(fishingCertificateId));

        assertSearchPersonEqual(person, argument.getValue());
        assertTrue(argument.getValue().getData().getFishingLicenses().isEmpty());

        assertEquals(1, argument.getValue().getData().getQualificationsProofs().size());
        SearchItemQualificationsProofsInner actualProof = argument.getValue().getData().getQualificationsProofs().getFirst();
        assertEquals(fishingCertificateId, actualProof.getFishingCertificateId());
    }


    private void assertSearchPersonEqual(Person person, RegisterEntrySearchView registerEntrySearchView) {
        assertEquals(person.getBirthdate().toString(), registerEntrySearchView.getBirthdate());
        assertEquals("max mustermann", registerEntrySearchView.getNormalizedName());
        assertEquals("berlin", registerEntrySearchView.getNormalizedBirthplace());

        SearchItemPerson searchRegisterEntryPerson = registerEntrySearchView.getData().getPerson();

        assertEquals(person.getFirstname(), searchRegisterEntryPerson.getFirstname());
        assertEquals(person.getLastname(), searchRegisterEntryPerson.getLastname());
        assertEquals(person.getBirthname(), searchRegisterEntryPerson.getBirthname());
        assertEquals(person.getBirthdate().toString(), searchRegisterEntryPerson.getBirthdate());
        assertEquals(person.getBirthplace(), searchRegisterEntryPerson.getBirthplace());
    }

    @Test
    @DisplayName("Test update person and fishing license when there is no preexisting license.")
    public void testUpdateRegisterEntrySearchView_Person_FishingLicense() {
        UUID registerEntryId = DomainTestData.registerId;
        InMemoryRegisterEntrySearchViewRepository repository = new InMemoryRegisterEntrySearchViewRepository();

        RegisterEntrySearchView registerEntrySearchView = getRegisterEntrySearchView(registerEntryId, null);
        repository.save(registerEntrySearchView);
        RegisterEntrySearchViewService service = new RegisterEntrySearchViewServiceImpl(repository, jdbcTemplate);
        Person expectedPerson = DomainTestData.createPerson();
        FishingLicense expectedFishingLicense = DomainTestData.createLicense();
        // Act
        service.updateRegisterEntrySearchView(RegisterEntrySearchViewServiceParameters.builder()
                .registerEntryId(registerEntryId)
                .person(expectedPerson)
                .license(expectedFishingLicense)
                .build());

        // Assert
        SearchItem updatedEntry = repository.findById(registerEntryId).get().getData();
        assertEquals(expectedPerson.getFirstname(), updatedEntry.getPerson().getFirstname());
        assertEquals(expectedPerson.getLastname(), updatedEntry.getPerson().getLastname());
        assertEquals(expectedPerson.getBirthname(), updatedEntry.getPerson().getBirthname());
        assertEquals(expectedFishingLicense.getNumber(), updatedEntry.getFishingLicenses().getFirst().getNumber());
    }

    @Test
    @DisplayName("Test update person and fishing license when there is a preexisting license.")
    public void testUpdateRegisterEntrySearchView_Person_FishingLicenseWithPreExistingData() {
        UUID registerEntryId = DomainTestData.registerId;
        InMemoryRegisterEntrySearchViewRepository repository = new InMemoryRegisterEntrySearchViewRepository();
        FishingLicense fishingLicense = DomainTestData.createLicense();
        RegisterEntrySearchView registerEntrySearchView = getRegisterEntrySearchView(registerEntryId, fishingLicense);
        repository.save(registerEntrySearchView);
        RegisterEntrySearchViewService service = new RegisterEntrySearchViewServiceImpl(repository, jdbcTemplate);
        Person expectedPerson = DomainTestData.createPerson();
        FishingLicense expectedFishingLicense = DomainTestData.createLicense();
        // Act
        service.updateRegisterEntrySearchView(RegisterEntrySearchViewServiceParameters.builder()
                .registerEntryId(registerEntryId)
                .person(expectedPerson)
                .license(expectedFishingLicense)
                .build());

        // Assert
        SearchItem updatedEntry = repository.findById(registerEntryId).get().getData();
        assertEquals(expectedPerson.getFirstname(), updatedEntry.getPerson().getFirstname());
        assertEquals(expectedPerson.getLastname(), updatedEntry.getPerson().getLastname());
        assertEquals(expectedPerson.getBirthname(), updatedEntry.getPerson().getBirthname());
        assertEquals(expectedFishingLicense.getNumber(), updatedEntry.getFishingLicenses().getFirst().getNumber());
    }

    @Test
    @DisplayName("Test update of birthname setting if the birthname is null.")
    public void testBirthnameSettingBirthnameNull() {
        UUID registerEntryId = DomainTestData.registerId;
        InMemoryRegisterEntrySearchViewRepository repository = new InMemoryRegisterEntrySearchViewRepository();
        FishingLicense fishingLicense = DomainTestData.createLicense();
        RegisterEntrySearchView registerEntrySearchView = getRegisterEntrySearchView(registerEntryId, fishingLicense);
        repository.save(registerEntrySearchView);
        RegisterEntrySearchViewServiceImpl service = new RegisterEntrySearchViewServiceImpl(repository, jdbcTemplate);
        Person expectedPerson = DomainTestData.createPerson();
        expectedPerson.setBirthname(null);
        FishingLicense expectedFishingLicense = DomainTestData.createLicense();
        // Act
        service.updateRegisterEntrySearchView(RegisterEntrySearchViewServiceParameters.builder()
                .registerEntryId(registerEntryId)
                .person(expectedPerson)
                .license(expectedFishingLicense)
                .build());

        // Assert
        SearchItem updatedEntry = repository.findById(registerEntryId).get().getData();
        assertEquals(updatedEntry.getPerson().getLastname(), updatedEntry.getPerson().getBirthname());
    }

    @Test
    @DisplayName("Test update of birthname setting if the birthname is empty.")
    public void testBirthnameSettingBirthnameEmpty() {
        UUID registerEntryId = DomainTestData.registerId;
        InMemoryRegisterEntrySearchViewRepository repository = new InMemoryRegisterEntrySearchViewRepository();
        FishingLicense fishingLicense = DomainTestData.createLicense();
        RegisterEntrySearchView registerEntrySearchView = getRegisterEntrySearchView(registerEntryId, fishingLicense);
        repository.save(registerEntrySearchView);
        RegisterEntrySearchViewService service = new RegisterEntrySearchViewServiceImpl(repository, jdbcTemplate);
        Person expectedPerson = DomainTestData.createPerson();
        expectedPerson.setBirthname("");
        FishingLicense expectedFishingLicense = DomainTestData.createLicense();
        // Act
        service.updateRegisterEntrySearchView(RegisterEntrySearchViewServiceParameters.builder()
                .registerEntryId(registerEntryId)
                .person(expectedPerson)
                .license(expectedFishingLicense)
                .build());

        // Assert
        SearchItem updatedEntry = repository.findById(registerEntryId).get().getData();
        assertEquals(updatedEntry.getPerson().getLastname(), updatedEntry.getPerson().getBirthname());
    }

    private static RegisterEntrySearchView getRegisterEntrySearchView(UUID registerEntryId, FishingLicense fishingLicense) {
        RegisterEntrySearchView registerEntrySearchView = new RegisterEntrySearchView();
        registerEntrySearchView.setRegisterId(registerEntryId);

        SearchItem searchRegisterEntry = new SearchItem();
        searchRegisterEntry.setRegisterId(registerEntryId.toString());
        searchRegisterEntry.setPerson(new SearchItemPerson());

        if (fishingLicense != null) {
            SearchItemFishingLicensesInner searchRegisterEntryFishingLicense = new SearchItemFishingLicensesInner();
            searchRegisterEntryFishingLicense.setNumber(fishingLicense.getNumber());
            searchRegisterEntry.setFishingLicenses(new ArrayList<>(List.of(searchRegisterEntryFishingLicense)));
        }
        registerEntrySearchView.setData(searchRegisterEntry);

        return registerEntrySearchView;
    }

    @Test
    public void testUpdateOrCreationOfRegisterEntrySearchView() {
        // Given
        UUID registerEntryId = UUID.randomUUID();
        Person person = DomainTestData.createPerson();
        FishingLicense fishingLicense = DomainTestData.createLicense();
        RegisterEntrySearchView registerEntrySearchView = new RegisterEntrySearchView();
        registerEntrySearchView.setRegisterId(registerEntryId);

        SearchItem searchItem = new SearchItem();
        SearchItemPerson searchItemPerson = new SearchItemPerson();
        searchItem.setPerson(searchItemPerson);
        registerEntrySearchView.setData(searchItem);

        InMemoryRegisterEntrySearchViewRepository repository = new InMemoryRegisterEntrySearchViewRepository();
        repository.save(registerEntrySearchView);
        RegisterEntrySearchViewService service = new RegisterEntrySearchViewServiceImpl(repository, jdbcTemplate);

        // When
        service.createOrUpdateRegisterEntrySearchView(RegisterEntrySearchViewServiceParameters.builder()
                .registerEntryId(registerEntryId)
                .person(person)
                .license(fishingLicense)
                .build());

        // Then
        SearchItem updatedEntry = repository.findById(registerEntryId).get().getData();
        assertEquals(person.getFirstname(), updatedEntry.getPerson().getFirstname());
        assertEquals(person.getLastname(), updatedEntry.getPerson().getLastname());
        assertEquals(person.getBirthname(), updatedEntry.getPerson().getBirthname());
        assertEquals(person.getBirthdate().toString(), updatedEntry.getPerson().getBirthdate());
        assertEquals(person.getBirthplace(), updatedEntry.getPerson().getBirthplace());
        assertEquals(fishingLicense.getNumber(), updatedEntry.getFishingLicenses().getFirst().getNumber());
    }

    @Test
    @DisplayName("Test updateRegisterEntrySearchView when getIssuedDateValidFrom returns null")
    void testUpdateRegisterEntrySearchView_NullValidFrom() {
        // Given
        UUID registerId = UUID.randomUUID();
        Person person = DomainTestData.createPerson();
        FishingLicense fishingLicense = new FishingLicense();
        fishingLicense.setNumber("FL123456");
        fishingLicense.setValidityPeriods(Collections.emptyList()); // No validity periods

        RegisterEntrySearchView view = new RegisterEntrySearchView();
        view.setRegisterId(registerId);

        SearchItem searchItem = new SearchItem();
        SearchItemPerson searchItemPerson = new SearchItemPerson();
        searchItem.setPerson(searchItemPerson);
        view.setData(searchItem);

        when(repository.findByRegisterId(registerId)).thenReturn(Optional.of(view));

        // When
        service.updateRegisterEntrySearchView(RegisterEntrySearchViewServiceParameters.builder()
                .registerEntryId(registerId)
                .person(person)
                .license(fishingLicense)
                .build());

        // Then
        ArgumentCaptor<RegisterEntrySearchView> captor = ArgumentCaptor.forClass(RegisterEntrySearchView.class);
        verify(repository).save(captor.capture());

        RegisterEntrySearchView updatedView = captor.getValue();
        SearchItemFishingLicensesInner updatedLicense = updatedView.getData().getFishingLicenses().getFirst();

        assertEquals("FL123456", updatedLicense.getNumber());
    }

}
