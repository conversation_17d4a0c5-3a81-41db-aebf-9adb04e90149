package de.adesso.fischereiregister.view.register_entry_search.adapter;


import de.adesso.fischereiregister.testutils.inmemory.jpa.InMemoryCrudRepository;
import de.adesso.fischereiregister.view.register_entry_search.RegisterEntrySearchView;
import de.adesso.fischereiregister.view.register_entry_search.RegisterEntrySearchViewRepository;
import org.apache.commons.lang3.NotImplementedException;
import org.openapitools.model.SearchItem;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public class InMemoryRegisterEntrySearchViewRepository extends InMemoryCrudRepository<RegisterEntrySearchView, UUID> implements RegisterEntrySearchViewRepository {

    @Override
    protected UUID getID(RegisterEntrySearchView entity) {
        return entity.getRegisterId();
    }

    @Override
    public Optional<RegisterEntrySearchView> findByRegisterId(UUID id) {
        return Optional.of((save.entrySet().stream()
                .filter(entry -> entry.getValue().getRegisterId().equals(id)).findFirst().get().getValue()));
    }

    @Override
    public Optional<SearchItem> findByIdentificationNumber(String identificationNumber) {
        return Optional.of((save.entrySet().stream()
                .filter(entry -> entry.getValue().getIdentificationNumbers().contains(identificationNumber)).findFirst().get().getValue().getData()));
    }

    @Override
    public Optional<UUID> findRegisterEntryIdByIdentificationNumber(String identificationNumber) {
        return Optional.of((save.entrySet().stream()
                .filter(entry -> entry.getValue().getIdentificationNumbers().contains(identificationNumber)).findFirst().get().getValue().getRegisterId()));
    }

    @Override
    public List<SearchItem> findByNormalizedPersonDetails(String name, String birthplace, String birthdate) {
        return List.of((save.entrySet().stream()
                .filter(entry -> entry.getValue().getNormalizedName().contains(name)).findFirst().get().getValue().getData()));
    }

    @Override
    public List<UUID> findRegisterEntryIdsByPersonDetails(String title, String firstname, String lastname, String birthname, String birthplace, String nationality, String birthdate) {
        return save.entrySet().stream()
                .filter(entry ->
                        (title == null || entry.getValue().getTitle().equals(title)) &&
                                (firstname == null || entry.getValue().getFirstname().contains(firstname)) &&
                                (lastname == null || entry.getValue().getLastname().equals(lastname)) &&
                                (birthname == null || entry.getValue().getBirthname().equals(birthname)) &&
                                (birthplace == null || entry.getValue().getBirthplace().equals(birthplace)) &&
                                (birthdate == null || entry.getValue().getBirthdate().toString().equals(birthdate)) &&
                                (nationality == null || entry.getValue().getNationality().equals(nationality))
                )
                .map(entry -> entry.getValue().getRegisterId())
                .toList();
    }

    @Override
    public <S extends RegisterEntrySearchView> S save(S entity) {
        return super.save(entity);
    }

    @Override
    public <S extends RegisterEntrySearchView> List<S> saveAll(Iterable<S> entities) {
        throw new NotImplementedException("Not implemented yet.");
    }

    @Override
    public void delete(RegisterEntrySearchView entity) {
        throw new NotImplementedException("Not implemented yet.");
    }

    @Override
    public void deleteAll(Iterable<? extends RegisterEntrySearchView> entities) {
        throw new NotImplementedException("Not implemented yet.");
    }
}
