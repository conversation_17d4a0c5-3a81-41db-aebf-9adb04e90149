<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <!-- Since this view was created on 05.08.2025, the initial Liquibase scripts for register_entry_search_view still reside—and will continue to reside—in the application module. -->

    <changeSet id="1.0.0" author="alexis.ortiz">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="register_entry_search_view"/>
            <columnExists tableName="register_entry_search_view" columnName="identification_number"/>
        </preConditions>

        <renameColumn tableName="register_entry_search_view"
                      oldColumnName="identification_number"
                      newColumnName="identification_numbers"/>

        <modifyDataType tableName="register_entry_search_view"
                        columnName="identification_numbers"
                        newDataType="mediumtext"/>
    </changeSet>

</databaseChangeLog>
