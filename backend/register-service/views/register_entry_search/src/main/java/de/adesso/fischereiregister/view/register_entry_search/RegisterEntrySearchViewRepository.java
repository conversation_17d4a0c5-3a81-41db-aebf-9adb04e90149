package de.adesso.fischereiregister.view.register_entry_search;

import org.openapitools.model.SearchItem;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface RegisterEntrySearchViewRepository extends CrudRepository<RegisterEntrySearchView, UUID> {

    Optional<RegisterEntrySearchView> findByRegisterId(UUID id);

    @Transactional(readOnly = true)
    @Query("SELECT r.data FROM RegisterEntrySearchView r WHERE LOWER(r.identificationNumbers) LIKE LOWER(CONCAT('%', :identificationNumber, '%'))")
    Optional<SearchItem> findByIdentificationNumber(
            @Param("identificationNumber") String identificationNumber
    );


    @Query("SELECT r.registerId FROM RegisterEntrySearchView r WHERE LOWER(r.identificationNumbers) LIKE LOWER(CONCAT('%', :identificationNumber, '%'))")
    Optional<UUID> findRegisterEntryIdByIdentificationNumber(
            @Param("identificationNumber") String identificationNumber
    );

    /**
     * The COALESCE function safely handles null values by returning the first non-null argument.
     * If a parameter (e.g., name, birthplace) is null, it defaults to the column value, making the condition always true.
     * Otherwise, it returns the provided argument, ensuring flexible search functionality.This allows the query to
     * function even if only one parameter is provided, filtering results based on the given input while ignoring null parameters.
     */
    @Query("SELECT r.data FROM RegisterEntrySearchView r " +
            "WHERE r.normalizedName ILIKE CONCAT('%',COALESCE(:name, r.normalizedName),'%') " + // the names are partially matched
            "AND r.normalizedBirthplace ILIKE COALESCE(:birthplace, r.normalizedBirthplace) " + // the birthplaces are exactly matched
            "AND r.birthdate = COALESCE(:birthdate, r.birthdate)")
    List<SearchItem> findByNormalizedPersonDetails(
            @Param("name") String name,
            @Param("birthplace") String birthplace,
            @Param("birthdate") String birthdate
    );

    @Query("SELECT r.registerId FROM RegisterEntrySearchView r " +
            "WHERE COALESCE(r.title, '') ILIKE COALESCE(:title, '') " + // COALESCE because the title value can be null
            "AND r.firstname ILIKE :firstname " +
            "AND r.lastname ILIKE :lastname " +
            "AND r.birthname ILIKE :birthname " +
            "AND r.birthplace ILIKE :birthplace " +
            "AND COALESCE(r.nationality, :nationality) ILIKE :nationality " + // When nationality is not set in the database, assume this person is meant
            "AND r.birthdate = :birthdate")
    List<UUID> findRegisterEntryIdsByPersonDetails(
            @Param("title") String title,
            @Param("firstname") String firstname,
            @Param("lastname") String lastname,
            @Param("birthname") String birthname,
            @Param("birthplace") String birthplace,
            @Param("nationality") String nationality,
            @Param("birthdate") String birthdate
    );
}
