package de.adesso.fischereiregister.view.register_entry_search;

import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.utils.DateUtils;
import de.adesso.fischereiregister.utils.StringNormalizer;
import de.adesso.fischereiregister.view.register_entry_search.mapper.RegisterEntrySearchViewMapper;
import de.adesso.fischereiregister.view.register_entry_search.mapper.SearchItemMapper;
import jakarta.persistence.EntityNotFoundException;
import lombok.AllArgsConstructor;
import org.openapitools.model.SearchItem;
import org.openapitools.model.SearchItemFishingLicensesInner;
import org.openapitools.model.SearchItemPerson;
import org.openapitools.model.SearchItemQualificationsProofsInner;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Service
@AllArgsConstructor
class RegisterEntrySearchViewServiceImpl implements RegisterEntrySearchViewService {

    private final RegisterEntrySearchViewRepository repository;
    private final JdbcTemplate jdbcTemplate;

    @Override
    @Transactional
    public void truncateView() {
        jdbcTemplate.execute("TRUNCATE TABLE register_entry_view");
    }

    @Override
    @Transactional
    public List<SearchItem> search(String query) {

        List<String> fields = Arrays.stream(query.split(",")).map(String::trim).toList();

        Optional<String> identificationNumber = fields.stream()
                .map(this::removeFormat)
                .filter(this::isIdentificationNumber)
                .findFirst();

        if (identificationNumber.isPresent()) {
            // if an identification number is found, the search must only be performed by identification number
            // All the other fields are ignored in this case
            return findByIdentificationNumber(identificationNumber.get());
        }

        String birthdate = null;
        String name = null;
        String birthplace = null;

        for (String field : fields) {
            if (DateUtils.isDate(field)) {
                birthdate = field;
            } else if (name == null) {
                name = StringNormalizer.normalize(field);
            } else {
                birthplace = StringNormalizer.normalize(field);
            }
        }

        return findByPersonDetails(name, birthplace, birthdate);

    }

    @Override
    public Optional<UUID> findRegisterEntryIdByIdentificationNumber(String identificationNumber) {
        return repository.findRegisterEntryIdByIdentificationNumber(identificationNumber);
    }

    @Override
    public List<UUID> findRegisterEntryIdsByPersonDetails(Person person) {
        if (person == null) {
            return Collections.emptyList();
        }

        String title = person.getTitle() != null ? person.getTitle().trim() : null;
        String firstname = person.getFirstname() != null ? person.getFirstname().trim() : null;
        String lastname = person.getLastname() != null ? person.getLastname().trim() : null;
        // the birthname is not a required field, in that case if left empty we match assuming that the request lastname must match with the birthname in the register
        String birthname = person.getBirthname() != null ? person.getBirthname().trim() : lastname;
        String birthplace = person.getBirthplace() != null ? person.getBirthplace().trim() : null;
        String nationality = person.getNationality() != null ? person.getNationality().trim() : null;
        String birthdate = person.getBirthdate() != null ? person.getBirthdate().toString() : null;

        return repository.findRegisterEntryIdsByPersonDetails(
                title,
                firstname,
                lastname,
                birthname,
                birthplace,
                nationality,
                birthdate
        );
    }

    private String removeFormat(String field) {
        return field.replaceAll("[^a-zA-Z0-9]", "");
    }

    private boolean isIdentificationNumber(String field) {
        return field.matches("[A-Za-z]{2}\\d{14}"); // an Identification Number is composed of two letters followed by 14 numbers
    }

    private List<SearchItem> findByIdentificationNumber(String identificationNumber) {
        return repository.findByIdentificationNumber(identificationNumber).map(List::of).orElse(Collections.emptyList());
    }

    private List<SearchItem> findByPersonDetails(String name, String birthplace, String birthdate) {
        if (isNotBlank(name) || isNotBlank(birthplace) || birthdate != null) {
            return repository.findByNormalizedPersonDetails(name, birthplace, birthdate);
        }
        return Collections.emptyList();
    }

    private RegisterEntrySearchView getRegisterEntrySearchView(RegisterEntrySearchViewServiceParameters parameters) {
        RegisterEntrySearchView registerEntrySearchView = RegisterEntrySearchViewMapper.INSTANCE.toView(parameters.getPerson(), Collections.emptyList());

        final UUID registerEntryId = parameters.getRegisterEntryId();

        registerEntrySearchView.setRegisterId(registerEntryId);

        SearchItem searchItem = new SearchItem();
        searchItem.setRegisterId(registerEntryId.toString());
        searchItem.setQualificationsProofs(new ArrayList<>());
        searchItem.setFishingLicenses(new ArrayList<>());
        searchItem.setPerson(new SearchItemPerson());

        registerEntrySearchView.setData(searchItem);

        updateRegisterEntryView(registerEntrySearchView, parameters);

        return registerEntrySearchView;
    }

    /**
     * Modifies the values of the RegisterEntrySearchView object without saving.
     */
    private void updateRegisterEntryView(RegisterEntrySearchView viewToUpdate, RegisterEntrySearchViewServiceParameters parameters) {
        if (parameters.getPerson() != null) {
            final Person person = parameters.getPerson();

            updateViewWithPersonData(viewToUpdate, person);
            
            viewToUpdate.getData().setPerson(SearchItemMapper.INSTANCE.toSearchItemPerson(person));
            setBirthnameIfMissing(viewToUpdate.getData().getPerson());
        }

        if (parameters.getLicense() != null) {
            final FishingLicense license = parameters.getLicense();

            SearchItemFishingLicensesInner searchItemFishingLicensesInnerFishingLicense = new SearchItemFishingLicensesInner();
            searchItemFishingLicensesInnerFishingLicense.setNumber(license.getNumber());

            viewToUpdate.addIdentificationNumber(license.getNumber());

            viewToUpdate.getData().getFishingLicenses().add(searchItemFishingLicensesInnerFishingLicense);
        }

        if (parameters.getCertificate() != null) {
            final String fishingCertificateId = parameters.getCertificate().getFishingCertificateId();

            viewToUpdate.addIdentificationNumber(fishingCertificateId);
            viewToUpdate.getData().getQualificationsProofs().add(new SearchItemQualificationsProofsInner(fishingCertificateId));
        }
    }

    private RegisterEntrySearchView findByRegisterId(UUID registerId) {
        return repository.findByRegisterId(registerId).orElseThrow(EntityNotFoundException::new);
    }

    private void updateViewWithPersonData(RegisterEntrySearchView viewToUpdate, Person person) {
        final String firstname = StringNormalizer.normalize(person.getFirstname());
        final String lastname = StringNormalizer.normalize(person.getLastname());
        final String birthplace = StringNormalizer.normalize(person.getBirthplace());

        viewToUpdate.setNormalizedName(firstname + " " + lastname);
        viewToUpdate.setNormalizedBirthplace(birthplace);

        viewToUpdate.setTitle(person.getTitle());
        viewToUpdate.setFirstname(person.getFirstname());
        viewToUpdate.setLastname(person.getLastname());
        viewToUpdate.setBirthname(person.getBirthname());
        viewToUpdate.setBirthplace(person.getBirthplace());
        viewToUpdate.setBirthdate(person.getBirthdate().toString());
        viewToUpdate.setNationality(person.getNationality());
    }


    @Override
    public void createRegisterEntrySearchView(RegisterEntrySearchViewServiceParameters parameters) {
        RegisterEntrySearchView registerEntrySearchView = getRegisterEntrySearchView(parameters);

        repository.save(registerEntrySearchView);
    }

    @Override
    public void updateRegisterEntrySearchView(RegisterEntrySearchViewServiceParameters parameters) {
        RegisterEntrySearchView viewToUpdate = findByRegisterId(parameters.getRegisterEntryId());

        updateRegisterEntryView(viewToUpdate, parameters);

        repository.save(viewToUpdate);
    }


    @Override
    public void createOrUpdateRegisterEntrySearchView(RegisterEntrySearchViewServiceParameters parameters) {
        RegisterEntrySearchView viewToUpdate = repository.findByRegisterId(parameters.getRegisterEntryId()).orElse(null);

        if (viewToUpdate == null) {
            viewToUpdate = getRegisterEntrySearchView(parameters);
        } else {
            updateRegisterEntryView(viewToUpdate, parameters);
        }

        repository.save(viewToUpdate);
    }

    private void setBirthnameIfMissing(SearchItemPerson person) {
        if (person.getBirthname() == null || person.getBirthname().isEmpty()) {
            person.setBirthname(person.getLastname());
        }
    }
}
