package de.adesso.fischereiregister.view.register_entry_search.mapper;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.utils.StringNormalizer;
import de.adesso.fischereiregister.view.register_entry_search.RegisterEntrySearchView;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;
import org.openapitools.model.SearchItem;

import java.util.List;

@Mapper(uses = {SearchItemMapper.class})
public interface RegisterEntrySearchViewMapper {

    RegisterEntrySearchViewMapper INSTANCE = Mappers.getMapper(RegisterEntrySearchViewMapper.class);

    @Named("mapData")
    default String mapData(SearchItem data) throws JsonProcessingException {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        return objectMapper.writeValueAsString(data);
    }

    //mapLatestFishingLicenseNumber
    @Mapping(target = ".", source = "person")
    @Mapping(target = "normalizedName", source = "person", qualifiedByName = "normalizeName")
    @Mapping(target = "normalizedBirthplace", ignore = true)
    @Mapping(target = "identificationNumbers", ignore = true)
    @Mapping(target = "registerId", ignore = true)
    @Mapping(target = "data", ignore = true)
    @Mapping(target = "birthdate", expression = "java(person.getBirthdate().toString())")
    RegisterEntrySearchView toView(Person person, List<FishingLicense> fishingLicenses);

    @Named("normalizeName")
    default String normalizeName(Person person) {
        String firstname = StringNormalizer.normalize(person.getFirstname());
        String lastname = StringNormalizer.normalize(person.getLastname());
        return firstname + " " + lastname;
    }
}
