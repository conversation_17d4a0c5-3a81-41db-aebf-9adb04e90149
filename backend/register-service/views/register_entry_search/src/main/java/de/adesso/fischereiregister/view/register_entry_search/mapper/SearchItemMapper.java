package de.adesso.fischereiregister.view.register_entry_search.mapper;

import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.QualificationsProof;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import org.openapitools.model.SearchItemFishingLicensesInner;
import org.openapitools.model.SearchItemPerson;
import org.openapitools.model.SearchItemQualificationsProofsInner;

import java.util.List;

@Mapper
public interface SearchItemMapper {

    SearchItemMapper INSTANCE = Mappers.getMapper(SearchItemMapper.class);

    @Mapping(source = "fishingCertificateId", target = "fishingCertificateId")
    SearchItemQualificationsProofsInner toSearchItemQualificationsProofsInner(QualificationsProof qualificationsProof);

    List<SearchItemQualificationsProofsInner> toSearchItemQualificationsProofsInnerList(List<QualificationsProof> qualificationsProof);

    List<SearchItemFishingLicensesInner> toSearchItemFishingLicensesInnerList(List<FishingLicense> licenses);

    @Mapping(target = "birthdate", expression = "java(person.getBirthdate().toString())")
    SearchItemPerson toSearchItemPerson(Person person);

}