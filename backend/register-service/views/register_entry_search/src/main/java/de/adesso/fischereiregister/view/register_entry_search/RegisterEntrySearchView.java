package de.adesso.fischereiregister.view.register_entry_search;

import de.adesso.fischereiregister.view.register_entry_search.converter.IdentificationNumbersConverter;
import de.adesso.fischereiregister.view.register_entry_search.converter.SearchRegisterEntryConverter;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Lob;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.openapitools.model.SearchItem;

import java.util.HashSet;
import java.util.Set;
import java.util.UUID;


@Entity
@Table
@Getter
@Setter
public class RegisterEntrySearchView {

	@Id
	private UUID registerId;

	@Column
	private String title;

	@Column
	private String firstname;

	@Column
	private String lastname;

	@Column
	private String birthname;

	@Column
	private String normalizedName;

	@Column
	private String birthplace;

	@Column
	private String normalizedBirthplace;

	@Column
	private String birthdate;

	@Column
	private String nationality;

	@Column(columnDefinition = "TEXT")
	@Convert(converter = IdentificationNumbersConverter.class)
	private Set<String> identificationNumbers = new HashSet<>();

	@Lob
	@Column(columnDefinition = "TEXT")
	@Convert(converter = SearchRegisterEntryConverter.class)
	private SearchItem data;

	public void addIdentificationNumber(String number) {
		if (this.identificationNumbers == null) {
			this.identificationNumbers = new HashSet<>();
		}
		this.identificationNumbers.add(number);
	}
}
