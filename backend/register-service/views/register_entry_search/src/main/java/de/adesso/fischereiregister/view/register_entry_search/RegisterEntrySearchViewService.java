package de.adesso.fischereiregister.view.register_entry_search;

import de.adesso.fischereiregister.core.model.Person;
import org.openapitools.model.SearchItem;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Service interface for handling operations related to
 * {@link RegisterEntrySearchView}.
 */
public interface RegisterEntrySearchViewService {

    void truncateView();

    /**
     * Searches for register entries based on the provided query.
     * The query can contain multiple fields separated by commas.
     * The fields can be identification number of format XXXX-XXXX-XXXX-XXXX,
     * birthdate of format dd.MM.yyyy, name, and birthplace.
     *
     * @param query the query string containing the fields to search for
     * @return a list of SearchRegisterEntry objects matching the search criteria
     */
    List<SearchItem> search(String query);

    Optional<UUID> findRegisterEntryIdByIdentificationNumber(String identificationNumber);

    List<UUID> findRegisterEntryIdsByPersonDetails(Person person);

    void createRegisterEntrySearchView(RegisterEntrySearchViewServiceParameters parameters);

    void updateRegisterEntrySearchView(RegisterEntrySearchViewServiceParameters parameters);

    void createOrUpdateRegisterEntrySearchView(RegisterEntrySearchViewServiceParameters parameters);
}
