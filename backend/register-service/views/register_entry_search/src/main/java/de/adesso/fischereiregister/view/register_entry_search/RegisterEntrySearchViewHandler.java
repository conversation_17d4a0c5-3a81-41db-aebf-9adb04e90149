package de.adesso.fischereiregister.view.register_entry_search;

import de.adesso.fischereiregister.core.events.FishingTaxPayedEvent;
import de.adesso.fischereiregister.core.events.LicenseExtendedEvent;
import de.adesso.fischereiregister.core.events.LimitedLicenseApplicationCreatedEvent;
import de.adesso.fischereiregister.core.events.LimitedLicenseCreatedEvent;
import de.adesso.fischereiregister.core.events.PersonCreatedEvent;
import de.adesso.fischereiregister.core.events.PersonalDataChangedEvent;
import de.adesso.fischereiregister.core.events.QualificationsProofCreatedEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseCreatedEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseDigitizedEvent;
import de.adesso.fischereiregister.core.events.ReplacementCardOrderedEvent;
import de.adesso.fischereiregister.core.events.VacationLicenseCreatedEvent;
import jakarta.transaction.Transactional;
import org.axonframework.eventhandling.EventHandler;
import org.axonframework.eventhandling.ResetHandler;
import org.springframework.stereotype.Component;

@Component
public class RegisterEntrySearchViewHandler {

    private final RegisterEntrySearchViewService registerEntrySearchViewService;

    public RegisterEntrySearchViewHandler(RegisterEntrySearchViewService registerEntrySearchViewService) {
        this.registerEntrySearchViewService = registerEntrySearchViewService;
    }

    @ResetHandler
    @Transactional
    public void onReset() {
        registerEntrySearchViewService.truncateView();
    }

    @EventHandler
    @Transactional
    void on(PersonCreatedEvent event) {
        registerEntrySearchViewService.createRegisterEntrySearchView(
                RegisterEntrySearchViewServiceParameters.builder()
                        .registerEntryId(event.registerId())
                        .person(event.person())
                        .build());
    }


    @EventHandler
    @Transactional
    void on(RegularLicenseDigitizedEvent event) {
        registerEntrySearchViewService.createRegisterEntrySearchView(
                RegisterEntrySearchViewServiceParameters.builder()
                        .registerEntryId(event.registerId())
                        .person(event.person())
                        .license(event.fishingLicense())
                        .build()
        );
    }

    @EventHandler
    @Transactional
    void on(QualificationsProofCreatedEvent event) {
        registerEntrySearchViewService.createOrUpdateRegisterEntrySearchView(
                RegisterEntrySearchViewServiceParameters.builder()
                        .registerEntryId(event.registerEntryId())
                        .person(event.person())
                        .certificate(event.qualificationsProof())
                        .build()
        );
    }

    @EventHandler
    @Transactional
    void on(PersonalDataChangedEvent event) {
        registerEntrySearchViewService.updateRegisterEntrySearchView(
                RegisterEntrySearchViewServiceParameters.builder()
                        .registerEntryId(event.registerId())
                        .person(event.person())
                        .build()
        );
    }

    @EventHandler
    @Transactional
    void on(FishingTaxPayedEvent event) {
        registerEntrySearchViewService.updateRegisterEntrySearchView(
                RegisterEntrySearchViewServiceParameters.builder()
                        .registerEntryId(event.registerId())
                        .person(event.person())
                        .build()
        );
    }

    @EventHandler
    @Transactional
    void on(RegularLicenseCreatedEvent event) {
        registerEntrySearchViewService.updateRegisterEntrySearchView(
                RegisterEntrySearchViewServiceParameters.builder()
                        .registerEntryId(event.registerId())
                        .person(event.person())
                        .license(event.fishingLicense())
                        .build()
        );
    }

    @EventHandler
    @Transactional
    void on(LimitedLicenseApplicationCreatedEvent event) {
        registerEntrySearchViewService.createOrUpdateRegisterEntrySearchView(
                RegisterEntrySearchViewServiceParameters.builder()
                        .registerEntryId(event.registerEntryId())
                        .person(event.person())
                        .build()
        );
    }

    @EventHandler
    @Transactional
    void on(LimitedLicenseCreatedEvent event) {
        registerEntrySearchViewService.createOrUpdateRegisterEntrySearchView(
                RegisterEntrySearchViewServiceParameters.builder()
                        .registerEntryId(event.registerId())
                        .person(event.person())
                        .license(event.fishingLicense())
                        .build()
        );
    }

    @EventHandler
    @Transactional
    void on(ReplacementCardOrderedEvent event) {
        registerEntrySearchViewService.updateRegisterEntrySearchView(
                RegisterEntrySearchViewServiceParameters.builder()
                        .registerEntryId(event.registerId())
                        .person(event.person())
                        .build()
        );
    }

    @EventHandler
    @Transactional
    void on(VacationLicenseCreatedEvent event) {
        registerEntrySearchViewService.createOrUpdateRegisterEntrySearchView(
                RegisterEntrySearchViewServiceParameters.builder()
                        .registerEntryId(event.registerEntryId())
                        .person(event.person())
                        .license(event.fishingLicense())
                        .build()
        );
    }

    @EventHandler
    @Transactional
    void on(LicenseExtendedEvent event) {
        registerEntrySearchViewService.updateRegisterEntrySearchView(
                RegisterEntrySearchViewServiceParameters.builder()
                        .registerEntryId(event.registerEntryId())
                        .person(event.person())
                        .build()
        );
    }
}
