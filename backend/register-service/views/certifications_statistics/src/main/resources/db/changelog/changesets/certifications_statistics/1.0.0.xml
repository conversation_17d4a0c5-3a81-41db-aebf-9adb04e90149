<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.5.xsd"
        context="certifications_statistics_view">

    <!-- ChangeSet for CertificationsStatisticsView Table -->
    <changeSet id="1.0.0" author="alexis.ortiz">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="certifications_statistics_view"/>
            </not>
        </preConditions>
        <createTable tableName="certifications_statistics_view">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="federal_state" type="VARCHAR(2)">
                <constraints nullable="false"/>
            </column>
            <column name="issuer" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="year" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="count" type="INTEGER">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <!-- Add unique constraint to ensure no duplicate combinations-->
        <addUniqueConstraint tableName="certifications_statistics_view"
                             columnNames="federal_state, issuer, year"
                             constraintName="unique_certifications_statistics"/>
    </changeSet>

    <!-- ChangeSet for adding indexes to CertificationsStatisticsView Table -->
    <changeSet id="1.0.0-indexes" author="alexis.ortiz">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="certifications_statistics_view"/>
        </preConditions>

        <!-- Index for year queries - used in findDistinctYears and findByYearIn -->
        <createIndex indexName="idx_certifications_statistics_year" tableName="certifications_statistics_view">
            <column name="year"/>
        </createIndex>

        <!-- Index for federal_state and year queries - used in findByFederalStateAndYearIn -->
        <createIndex indexName="idx_certifications_statistics_federal_state_year" tableName="certifications_statistics_view">
            <column name="federal_state"/>
            <column name="year"/>
        </createIndex>

        <!-- Index for issuer and year queries - used in findByIssuerAndYearIn -->
        <createIndex indexName="idx_certifications_statistics_issuer_year" tableName="certifications_statistics_view">
            <column name="issuer"/>
            <column name="year"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>
