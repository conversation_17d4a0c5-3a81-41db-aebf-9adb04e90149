package de.adesso.fischereiregister.view.certifications_statistics.services;

import de.adesso.fischereiregister.view.certifications_statistics.persistence.CertificationsStatisticsView;
import de.adesso.fischereiregister.view.certifications_statistics.persistence.CertificationsStatisticsViewRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Implementation of the CertificationsStatisticsViewService.
 * Manages certification statistics, allowing updates or creation of entries.
 */
@Service
@AllArgsConstructor
public class CertificationsStatisticsViewServiceImpl implements CertificationsStatisticsViewService {

    private final CertificationsStatisticsViewRepository repository;

    @Override
    public void updateOrCreateStatistic(String federalState, String issuer, int year) {
        // Try to find an existing entry
        CertificationsStatisticsView view = repository.findByFederalStateAndIssuerAndYear(federalState, issuer, year)
                .orElse(null);

        if (view != null) {
            view.setCount(view.getCount() + 1);
        } else {
            view = new CertificationsStatisticsView();
            view.setFederalState(federalState);
            view.setIssuer(issuer);
            view.setYear(year);
            view.setCount(1);
        }

        repository.save(view);
    }

    @Override
    public List<CertificationsStatisticsView> getStatisticsByFederalStateAndYears(String federalState, List<Integer> years) {
        return repository.findByFederalStateAndYearIn(federalState, years);
    }

    @Override
    public List<CertificationsStatisticsView> getStatisticsByIssuerAndYears(String issuer, List<Integer> years) {
        return repository.findByIssuerAndYearIn(issuer, years);
    }

    @Override
    public List<CertificationsStatisticsView> getStatisticsByYears(List<Integer> years) {
        return repository.findByYearIn(years);
    }

    @Override
    public List<Integer> getAvailableYears() {
        return repository.findDistinctYears();
    }

    @Override
    public List<String> getAvailableIssuers() {
        return repository.findDistinctIssuers();
    }

    @Override
    public List<String> getAvailableIssuersByYears(List<Integer> years) {
        return repository.findDistinctIssuersByYears(years);
    }

    @Override
    public List<String> getAvailableIssuersByFederalState(String federalState) {
        return repository.findDistinctIssuersByFederalState(federalState);
    }

    @Override
    public List<String> getAvailableIssuersByYearsAndFederalState(List<Integer> years, String federalState) {
        return repository.findDistinctIssuersByFederalStateAndYears(federalState, years);
    }

    @Override
    public void deleteAll() {
        repository.deleteAll();
    }
}
