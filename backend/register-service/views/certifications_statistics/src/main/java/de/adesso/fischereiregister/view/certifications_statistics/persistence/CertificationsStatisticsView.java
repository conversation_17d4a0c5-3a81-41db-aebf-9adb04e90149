package de.adesso.fischereiregister.view.certifications_statistics.persistence;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

/**
 * Entity representing a certification statistics view.
 * Stores statistics for certifications by federal state, issuer, and year.
 */
@Entity
@Table
@Getter
@Setter
public class CertificationsStatisticsView {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false)
    private String federalState;

    @Column(nullable = false)
    private String issuer;

    @Column(nullable = false)
    private int year;

    @Column(nullable = false)
    private int count;
}
