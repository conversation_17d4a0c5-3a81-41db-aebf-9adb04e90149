package de.adesso.fischereiregister.view.certifications_statistics.services;

import de.adesso.fischereiregister.view.certifications_statistics.persistence.CertificationsStatisticsView;
import de.adesso.fischereiregister.view.certifications_statistics.persistence.InMemoryCertificationsStatisticsViewRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

class CertificationsStatisticsViewServiceImplTest {

    private InMemoryCertificationsStatisticsViewRepository repository;
    private CertificationsStatisticsViewServiceImpl certificationsStatisticsViewServiceImpl;

    private static final String FEDERAL_STATE_SH = "SH";
    private static final String FEDERAL_STATE_NW = "NW";
    private static final String OFFICE_1 = "Office1";
    private static final String OFFICE_2 = "Office2";
    private static final int YEAR_2022 = 2022;
    private static final int YEAR_2023 = 2023;
    private static final int YEAR_2024 = 2024;
    private static final long ID_1 = 1L;
    private static final long ID_2 = 2L;
    private static final long ID_3 = 3L;
    private static final long ID_4 = 4L;

    @BeforeEach
    void setUp() {
        repository = new InMemoryCertificationsStatisticsViewRepository();
        certificationsStatisticsViewServiceImpl = new CertificationsStatisticsViewServiceImpl(repository);
    }

    @Test
    @DisplayName("CertificationsStatisticsViewServiceImpl.updateOrCreateStatistic should create a new entry when none exists")
    void updateOrCreateStatistic_createsNewEntry() {
        // When
        certificationsStatisticsViewServiceImpl.updateOrCreateStatistic(FEDERAL_STATE_SH, OFFICE_1, YEAR_2024);

        // Then
        Optional<CertificationsStatisticsView> result = repository.findByFederalStateAndIssuerAndYear(
                FEDERAL_STATE_SH, OFFICE_1, YEAR_2024);

        assertTrue(result.isPresent());
        CertificationsStatisticsView view = result.get();
        assertNotNull(view.getId());
        assertEquals(1, view.getCount());
        assertEquals(FEDERAL_STATE_SH, view.getFederalState());
        assertEquals(OFFICE_1, view.getIssuer());
        assertEquals(YEAR_2024, view.getYear());
    }

    @Test
    @DisplayName("CertificationsStatisticsViewServiceImpl.updateOrCreateStatistic should update existing entry by incrementing count")
    void updateOrCreateStatistic_updatesExistingEntry() {
        // Given
        CertificationsStatisticsView existingEntry = createView(
                ID_1, FEDERAL_STATE_SH, OFFICE_1, YEAR_2024, 5);
        repository.save(existingEntry);

        // When
        certificationsStatisticsViewServiceImpl.updateOrCreateStatistic(FEDERAL_STATE_SH, OFFICE_1, YEAR_2024);

        // Then
        Optional<CertificationsStatisticsView> result = repository.findByFederalStateAndIssuerAndYear(
                FEDERAL_STATE_SH, OFFICE_1, YEAR_2024);

        assertTrue(result.isPresent());
        assertEquals(6, result.get().getCount());
    }

    @Test
    @DisplayName("CertificationsStatisticsViewServiceImpl.getAvailableYears should return all distinct years in descending order")
    void getAvailableYears_returnsYearsInDescendingOrder() {
        // Given
        repository.save(createView(ID_1, FEDERAL_STATE_SH, OFFICE_1, YEAR_2023, 1));
        repository.save(createView(ID_2, FEDERAL_STATE_SH, OFFICE_1, YEAR_2023, 2));
        repository.save(createView(ID_3, FEDERAL_STATE_SH, OFFICE_1, YEAR_2024, 3));
        repository.save(createView(ID_4, FEDERAL_STATE_NW, OFFICE_2, YEAR_2022, 1));

        // When
        List<Integer> result = certificationsStatisticsViewServiceImpl.getAvailableYears();

        // Then
        assertEquals(3, result.size());
        assertEquals(List.of(YEAR_2024, YEAR_2023, YEAR_2022), result);
    }

    @Test
    @DisplayName("CertificationsStatisticsViewServiceImpl.deleteAll should delete all entries")
    void deleteAll_removesAllEntries() {
        // Given
        repository.save(createView(ID_1, FEDERAL_STATE_SH, OFFICE_1, YEAR_2023, 1));
        repository.save(createView(ID_2, FEDERAL_STATE_NW, OFFICE_2, YEAR_2023, 2));

        // When
        certificationsStatisticsViewServiceImpl.deleteAll();

        // Then
        assertEquals(0, repository.count());
    }

    @Test
    @DisplayName("CertificationsStatisticsViewServiceImpl.getAvailableIssuers should return issuers without filters")
    void getAvailableIssuers_returnsIssuersWithoutFilters() {
        // Given
        repository.save(createView(ID_1, FEDERAL_STATE_SH, "Office1", YEAR_2023, 1));
        repository.save(createView(ID_2, FEDERAL_STATE_NW, "Office2", YEAR_2024, 1));

        // When
        List<String> result = certificationsStatisticsViewServiceImpl.getAvailableIssuers();

        // Then
        assertEquals(2, result.size());
        assertTrue(result.contains("Office1"));
        assertTrue(result.contains("Office2"));
    }

    @Test
    @DisplayName("CertificationsStatisticsViewServiceImpl.getAvailableIssuersByYears should return issuers filtered by years")
    void getAvailableIssuersByYears_returnsFilteredIssuers() {
        // Given
        repository.save(createView(ID_1, FEDERAL_STATE_SH, "Office1", YEAR_2023, 1));
        repository.save(createView(ID_2, FEDERAL_STATE_NW, "Office3", YEAR_2023, 1));

        // When
        List<String> result = certificationsStatisticsViewServiceImpl.getAvailableIssuersByYears(List.of(YEAR_2023));

        // Then
        assertEquals(2, result.size());
        assertTrue(result.contains("Office1"));
        assertTrue(result.contains("Office3"));
    }

    @Test
    @DisplayName("CertificationsStatisticsViewServiceImpl.getAvailableIssuersByFederalState should return issuers filtered by federal state")
    void getAvailableIssuersByFederalState_returnsFilteredIssuers() {
        // Given
        repository.save(createView(ID_1, FEDERAL_STATE_SH, "Office1", YEAR_2023, 1));
        repository.save(createView(ID_2, FEDERAL_STATE_SH, "Office4", YEAR_2024, 1));

        // When
        List<String> result = certificationsStatisticsViewServiceImpl.getAvailableIssuersByFederalState(FEDERAL_STATE_SH);

        // Then
        assertEquals(2, result.size());
        assertTrue(result.contains("Office1"));
        assertTrue(result.contains("Office4"));
    }

    @Test
    @DisplayName("CertificationsStatisticsViewServiceImpl.getAvailableIssuersByYearsAndFederalState should return issuers filtered by both years and federal state")
    void getAvailableIssuersByYearsAndFederalState_returnsFilteredIssuers() {
        // Given
        repository.save(createView(ID_1, FEDERAL_STATE_SH, "Office1", YEAR_2023, 1));

        // When
        List<String> result = certificationsStatisticsViewServiceImpl.getAvailableIssuersByYearsAndFederalState(List.of(YEAR_2023), FEDERAL_STATE_SH);

        // Then
        assertEquals(1, result.size());
        assertTrue(result.contains("Office1"));
    }

    @Test
    @DisplayName("CertificationsStatisticsViewServiceImpl.getStatisticsByFederalStateAndYears should return statistics for specified federal state and years")
    void getStatisticsByFederalStateAndYears_returnsCorrectStatistics() {
        // Given
        repository.save(createView(ID_1, FEDERAL_STATE_SH, OFFICE_1, YEAR_2023, 1));
        repository.save(createView(ID_2, FEDERAL_STATE_SH, OFFICE_1, YEAR_2023, 2));
        repository.save(createView(ID_3, FEDERAL_STATE_SH, OFFICE_1, YEAR_2024, 3));
        repository.save(createView(ID_4, FEDERAL_STATE_NW, OFFICE_2, YEAR_2023, 1));

        // When
        List<CertificationsStatisticsView> result = certificationsStatisticsViewServiceImpl.getStatisticsByFederalStateAndYears(
                FEDERAL_STATE_SH, List.of(YEAR_2023, YEAR_2024));

        // Then
        assertEquals(3, result.size());
        assertTrue(result.stream().allMatch(view -> view.getFederalState().equals(FEDERAL_STATE_SH)));
        assertTrue(result.stream().allMatch(view -> List.of(YEAR_2023, YEAR_2024).contains(view.getYear())));
    }

    @Test
    @DisplayName("CertificationsStatisticsViewServiceImpl.getStatisticsByIssuerAndYears should return statistics for specified issuer and years")
    void getStatisticsByIssuerAndYears_returnsCorrectStatistics() {
        // Given
        repository.save(createView(ID_1, FEDERAL_STATE_SH, OFFICE_1, YEAR_2023, 1));
        repository.save(createView(ID_2, FEDERAL_STATE_SH, OFFICE_1, YEAR_2023, 2));
        repository.save(createView(ID_3, FEDERAL_STATE_SH, OFFICE_1, YEAR_2024, 3));
        repository.save(createView(ID_4, FEDERAL_STATE_NW, OFFICE_2, YEAR_2023, 1));

        // When
        List<CertificationsStatisticsView> result = certificationsStatisticsViewServiceImpl.getStatisticsByIssuerAndYears(
                OFFICE_1, List.of(YEAR_2023, YEAR_2024));

        // Then
        assertEquals(3, result.size());
        assertTrue(result.stream().allMatch(view -> view.getIssuer().equals(OFFICE_1)));
        assertTrue(result.stream().allMatch(view -> List.of(YEAR_2023, YEAR_2024).contains(view.getYear())));
    }

    @Test
    @DisplayName("CertificationsStatisticsViewServiceImpl.getStatisticsByYears should return statistics for specified years")
    void getStatisticsByYears_returnsCorrectStatistics() {
        // Given
        repository.save(createView(ID_1, FEDERAL_STATE_SH, OFFICE_1, YEAR_2023, 1));
        repository.save(createView(ID_2, FEDERAL_STATE_SH, OFFICE_1, YEAR_2023, 2));
        repository.save(createView(ID_3, FEDERAL_STATE_SH, OFFICE_1, YEAR_2024, 3));
        repository.save(createView(ID_4, FEDERAL_STATE_NW, OFFICE_2, YEAR_2023, 1));

        // When
        List<CertificationsStatisticsView> result = certificationsStatisticsViewServiceImpl.getStatisticsByYears(List.of(YEAR_2023, YEAR_2024));

        // Then
        assertEquals(4, result.size());
        assertTrue(result.stream().allMatch(view -> List.of(YEAR_2023, YEAR_2024).contains(view.getYear())));
    }

    private CertificationsStatisticsView createView(long id, String federalState, String issuer, int year, int count) {
        CertificationsStatisticsView view = new CertificationsStatisticsView();
        view.setId(id);
        view.setFederalState(federalState);
        view.setIssuer(issuer);
        view.setYear(year);
        view.setCount(count);
        return view;
    }
}