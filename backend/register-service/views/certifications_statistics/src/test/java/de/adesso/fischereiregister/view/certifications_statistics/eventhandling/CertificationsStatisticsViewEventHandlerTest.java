package de.adesso.fischereiregister.view.certifications_statistics.eventhandling;

import de.adesso.fischereiregister.core.events.QualificationsProofCreatedEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseDigitizedEvent;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.QualificationsProof;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import de.adesso.fischereiregister.view.certifications_statistics.services.CertificationsStatisticsViewService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CertificationsStatisticsViewEventHandlerTest {

    @Mock
    private CertificationsStatisticsViewService certificationsStatisticsViewService;

    private CertificationsStatisticsViewEventHandler handler;

    @BeforeEach
    void setUp() {
        handler = new CertificationsStatisticsViewEventHandler(certificationsStatisticsViewService);
    }

    @Test
    @DisplayName("Should handle reset by deleting all statistics")
    void shouldHandleResetByDeletingAllStatistics() {
        // when
        handler.onReset();

        // then
        verify(certificationsStatisticsViewService).deleteAll();
    }

    @Test
    @DisplayName("Should update statistics when qualifications proof is created")
    void shouldUpdateStatisticsWhenQualificationsProofIsCreated() {
        // given
        String federalState = "SH";
        String office = "TestOffice";
        LocalDate passedOnDate = LocalDate.of(2023, 3, 15);

        QualificationsProof qualificationsProof = new QualificationsProof();
        qualificationsProof.setFederalState(federalState);
        qualificationsProof.setIssuedBy(office);
        qualificationsProof.setPassedOn(passedOnDate);

        Person person = DomainTestData.createPerson();
        UUID registerEntryId = UUID.randomUUID();

        QualificationsProofCreatedEvent event = new QualificationsProofCreatedEvent(registerEntryId, qualificationsProof, person);

        // when
        handler.on(event);

        // then
        verify(certificationsStatisticsViewService).updateOrCreateStatistic(
                federalState,
                office,
                passedOnDate.getYear() // Should use passedOn year (2023)
        );
    }

    @Test
    @DisplayName("Should update statistics for each qualification proof in RegularLicenseDigitizedEvent")
    void shouldUpdateStatisticsForEachQualificationProofInRegularLicenseDigitizedEvent() {
        // given
        String federalState1 = "SH";
        String office1 = "TestOffice1";
        String federalState2 = "BE";
        String office2 = "TestOffice2";
        int year1 = 2022; // Year for first proof
        int year2 = 2023; // Year for second proof

        // Create two qualification proofs with different passedOn dates
        QualificationsProof proof1 = new QualificationsProof();
        proof1.setFederalState(federalState1);
        proof1.setIssuedBy(office1);
        proof1.setPassedOn(LocalDate.of(year1, 5, 10)); // 2022

        QualificationsProof proof2 = new QualificationsProof();
        proof2.setFederalState(federalState2);
        proof2.setIssuedBy(office2);
        proof2.setPassedOn(LocalDate.of(year2, 8, 20)); // 2023

        // Create the RegularLicenseDigitizedEvent with the two proofs
        RegularLicenseDigitizedEvent event = new RegularLicenseDigitizedEvent(
                UUID.randomUUID(), // registerId
                "salt",
                new Person(), // person
                new Jurisdiction(), // jurisdiction
                null, // fishingLicense
                List.of(), // fees
                List.of(), // taxes
                List.of(proof1, proof2), // qualificationsProofs
                List.of(), // identificationDocuments
                null, // consentInfo
                "TestOffice", // issuedByOffice
                "TestAddress" // issuedByAddress
        );

        // when
        handler.on(event);

        // then
        verify(certificationsStatisticsViewService).updateOrCreateStatistic(
                federalState1,
                office1,
                year1 // Should use first proof's passedOn year (2022)
        );

        verify(certificationsStatisticsViewService).updateOrCreateStatistic(
                federalState2,
                office2,
                year2 // Should use second proof's passedOn year (2023)
        );
    }
}
