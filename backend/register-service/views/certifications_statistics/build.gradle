plugins {
    id 'java'
    id 'org.springframework.boot' version '3.4.6'
    id 'io.spring.dependency-management' version '1.1.6'
}

group = 'de.adesso.fischereiregister.views'
version = parent.project.version

dependencies {
    compileOnly 'org.projectlombok:lombok:1.18.36'
    annotationProcessor 'org.projectlombok:lombok:1.18.36'

    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'

    implementation project(':core')
    testImplementation platform('org.junit:junit-bom:5.10.0')
    testImplementation 'org.junit.jupiter:junit-jupiter'

    testImplementation 'org.mockito:mockito-core'
    testImplementation 'org.mockito:mockito-junit-jupiter'

    implementation 'org.mapstruct:mapstruct:1.5.5.Final'
    annotationProcessor 'org.mapstruct:mapstruct-processor:1.5.5.Final'

    testImplementation project(':testutils')

    platform('org.axonframework:axon-bom:4.10.4')
    testImplementation "org.axonframework:axon-test"
}

test {
    useJUnitPlatform()
}

bootJar {
    enabled = false
}

jar {
    enabled = true
}
