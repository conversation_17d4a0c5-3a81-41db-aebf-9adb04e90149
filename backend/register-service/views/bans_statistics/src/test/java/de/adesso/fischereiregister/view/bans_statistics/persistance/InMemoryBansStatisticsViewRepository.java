package de.adesso.fischereiregister.view.bans_statistics.persistance;

import de.adesso.fischereiregister.testutils.inmemory.jpa.InMemoryCrudRepository;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

public class InMemoryBansStatisticsViewRepository extends InMemoryCrudRepository<BansStatisticsView, Long> implements BansStatisticsViewRepository {

    @Override
    protected Long getID(BansStatisticsView entity) {
        return entity.getId();
    }

    @Override
    public Optional<BansStatisticsView> findByFederalStateAndYear(
            String federalState, int year) {
        return save.values().stream()
                .filter(view -> view.getFederalState().equals(federalState))
                .filter(view -> view.getYear() == year)
                .findFirst();
    }

    @Override
    public List<BansStatisticsView> findByFederalStateAndYearIn(
            String federalState, List<Integer> years) {
        return save.values().stream()
                .filter(view -> view.getFederalState().equals(federalState))
                .filter(view -> years.contains(view.getYear()))
                .collect(Collectors.toList());
    }

    @Override
    public List<BansStatisticsView> findByYearIn(List<Integer> years) {
        return save.values().stream()
                .filter(view -> years.contains(view.getYear()))
                .collect(Collectors.toList());
    }

    @Override
    public List<Integer> findDistinctYears() {
        return save.values().stream()
                .map(BansStatisticsView::getYear)
                .distinct()
                .sorted()
                .collect(Collectors.toList());
    }
}
