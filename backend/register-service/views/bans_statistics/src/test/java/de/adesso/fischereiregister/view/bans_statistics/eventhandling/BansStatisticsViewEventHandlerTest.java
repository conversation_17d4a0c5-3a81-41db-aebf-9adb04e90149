package de.adesso.fischereiregister.view.bans_statistics.eventhandling;

import de.adesso.fischereiregister.core.events.BannedEvent;
import de.adesso.fischereiregister.core.events.JurisdictionMovedEvent;
import de.adesso.fischereiregister.core.events.UnbannedEvent;
import de.adesso.fischereiregister.core.model.Ban;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import de.adesso.fischereiregister.core.model.consent.JurisdictionConsentInfo;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.view.bans_statistics.services.BansStatisticsViewService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.Instant;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.UUID;

import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

class BansStatisticsViewEventHandlerTest {

    private BansStatisticsViewEventHandler eventHandler;
    private BansStatisticsViewService viewService;

    @BeforeEach
    void setUp() {
        viewService = mock(BansStatisticsViewService.class);
        eventHandler = new BansStatisticsViewEventHandler(viewService);
    }

    @Test
    @DisplayName("BansStatisticsViewEventHandler.on(BannedEvent) Should handle BannedEvent with temporary ban")
    void testHandleBannedEventWithTemporaryBan() {
        // given
        LocalDate startDate = LocalDate.of(2022, 3, 15);
        LocalDate endDate = startDate.plusMonths(6); // Temporary ban
        LocalDate atDate = LocalDate.of(2022, 2, 10);

        Ban ban = new Ban();
        ban.setReportedBy("Test Office");
        ban.setFrom(startDate);
        ban.setTo(endDate);
        ban.setAt(atDate);

        UUID registerId = UUID.randomUUID();
        Jurisdiction jurisdiction = new Jurisdiction();
        jurisdiction.setFederalState("BY");

        BannedEvent event = new BannedEvent(registerId, ban, jurisdiction);

        // when
        eventHandler.on(event);

        // then
        verify(viewService, times(1)).updateOrCreateIssuedStatistic(
                eq("BY"),
                eq(2022) // Year from at date
        );
        verify(viewService, times(1)).updateOrCreateStartedStatistic(
                eq("BY"),
                eq(2022) // Year from from date
        );
    }

    @Test
    @DisplayName("BansStatisticsViewEventHandler.on(BannedEvent) Should handle BannedEvent with permanent ban")
    void testHandleBannedEventWithPermanentBan() {
        // given
        LocalDate startDate = LocalDate.of(2023, 5, 20);
        LocalDate atDate = LocalDate.of(2023, 4, 15);

        Ban ban = new Ban();
        ban.setReportedBy("Test Office");
        ban.setFrom(startDate);
        ban.setTo(null); // Permanent ban
        ban.setAt(atDate);

        UUID registerId = UUID.randomUUID();
        Jurisdiction jurisdiction = new Jurisdiction();
        jurisdiction.setFederalState("BE");

        BannedEvent event = new BannedEvent(registerId, ban, jurisdiction);

        // when
        eventHandler.on(event);

        // then
        verify(viewService, times(1)).updateOrCreateIssuedStatistic(
                eq("BE"),
                eq(2023) // Year from at date
        );
        verify(viewService, times(1)).updateOrCreateStartedStatistic(
                eq("BE"),
                eq(2023) // Year from from date
        );
    }

    @Test
    @DisplayName("BansStatisticsViewEventHandler.on(UnbannedEvent) Should handle UnbannedEvent")
    void testHandleUnbannedEvent() {
        // given
        UUID registerId = UUID.randomUUID();
        Jurisdiction jurisdiction = new Jurisdiction();
        jurisdiction.setFederalState("SH");

        UnbannedEvent event = new UnbannedEvent(registerId, jurisdiction);
        Instant timestamp = Instant.now();

        // when
        eventHandler.on(event, timestamp);

        // then
        verify(viewService, times(1)).updateOrCreateExpiredStatistic(eq("SH"), eq(timestamp.atZone(java.time.ZoneId.systemDefault()).getYear()));
    }

    @Test
    @DisplayName("BansStatisticsViewEventHandler.on(JurisdictionMovedEvent) Should handle JurisdictionMovedEvent with active ban")
    void testHandleJurisdictionMovedEventWithActiveBan() {
        // given
        UUID registerId = UUID.randomUUID();

        Jurisdiction previousJurisdiction = new Jurisdiction();
        previousJurisdiction.setFederalState("SH");

        Jurisdiction newJurisdiction = new Jurisdiction();
        newJurisdiction.setFederalState("BY");

        LocalDate atDate = LocalDate.of(2022, 5, 15);
        LocalDate fromDate = LocalDate.of(2022, 6, 1);
        Ban ban = new Ban();
        ban.setReportedBy("Test Office");
        ban.setFrom(fromDate);
        ban.setTo(null);
        ban.setAt(atDate);

        JurisdictionMovedEvent event = new JurisdictionMovedEvent(
                registerId,
                previousJurisdiction,
                newJurisdiction,
                mock(JurisdictionConsentInfo.class),
                ban,
                new ArrayList<>(),
                "salt",
                "Test Office",
                new ArrayList<>(),
                SubmissionType.ANALOG
        );

        // when
        eventHandler.on(event);

        // then
        verify(viewService, times(1)).decreaseIssuedStatistic(eq("SH"), eq(2022));
        verify(viewService, times(1)).updateOrCreateIssuedStatistic(eq("BY"), eq(2022));
        verify(viewService, times(1)).decreaseStartedStatistic(eq("SH"), eq(2022));
        verify(viewService, times(1)).updateOrCreateStartedStatistic(eq("BY"), eq(2022));
    }

    @Test
    @DisplayName("BansStatisticsViewEventHandler.on(JurisdictionMovedEvent) Should not update statistics when JurisdictionMovedEvent has no ban")
    void testHandleJurisdictionMovedEventWithNoBan() {
        // given
        UUID registerId = UUID.randomUUID();

        Jurisdiction previousJurisdiction = new Jurisdiction();
        previousJurisdiction.setFederalState("SH");

        Jurisdiction newJurisdiction = new Jurisdiction();
        newJurisdiction.setFederalState("BY");

        JurisdictionMovedEvent event = new JurisdictionMovedEvent(
                registerId,
                previousJurisdiction,
                newJurisdiction,
                mock(JurisdictionConsentInfo.class),
                null,
                new ArrayList<>(),
                "salt",
                "Test Office",
                new ArrayList<>(),
                SubmissionType.ANALOG
        );

        // when
        eventHandler.on(event);

        // then
        verify(viewService, never()).decreaseIssuedStatistic(eq("SH"), eq(2022));
        verify(viewService, never()).updateOrCreateIssuedStatistic(eq("BY"), eq(2022));
        verify(viewService, never()).decreaseStartedStatistic(eq("SH"), eq(2022));
        verify(viewService, never()).updateOrCreateStartedStatistic(eq("BY"), eq(2022));
    }
}
