package de.adesso.fischereiregister.view.bans_statistics.persistance;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * Entity representing bans statistics for a specific year and federal state.
 * <p>
 * This class provides a yearly breakdown of bans data, including the number
 * of bans issued, started, and expired.
 */
@Entity
@Table
@Getter
@Setter
public class BansStatisticsView {

    /**
     * Unique identifier for the bans statistics record.
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * The federal state to which this bans statistics entry applies.
     */
    @Column(nullable = false)
    private String federalState;

    /**
     * The year for which bans statistics are aggregated.
     */
    @Column(nullable = false)
    private int year;

    /**
     * The number of bans issued in the given year.
     */
    @Column(nullable = false)
    private int issuedCount;

    /**
     * The number of bans that started taking effect in the given year.
     */
    @Column(nullable = false)
    private int startedCount;

    /**
     * The number of bans that expired in the given year.
     */
    @Column(nullable = false)
    private int expiredCount;
}
