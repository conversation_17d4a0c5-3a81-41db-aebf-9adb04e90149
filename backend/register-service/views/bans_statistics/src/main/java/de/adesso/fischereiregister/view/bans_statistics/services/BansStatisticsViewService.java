package de.adesso.fischereiregister.view.bans_statistics.services;

import de.adesso.fischereiregister.view.bans_statistics.persistance.BansStatisticsView;

import java.util.List;

/**
 * Manages ban statistics, allowing updates or creation of entries.
 */
public interface BansStatisticsViewService {
    /**
     * Deletes all ban statistics entries. Use only when Reset of view should happen, and replay of events is ensured.
     */
    void deleteAll();

    /**
     * Updates or creates a ban statistic entry for an issued ban. If an entry exists for the given parameters,
     * its issuedCount is incremented. Otherwise, a new entry is created with issuedCount of 1 and expiredCount of 0.
     *
     * @param federalState The federal state for the statistic.
     * @param year         The year that the statistics account for
     */
    void updateOrCreateIssuedStatistic(String federalState, int year);

    /**
     * Updates or creates a ban statistic entry for an expired ban. If an entry exists for the given parameters,
     * its expiredCount is incremented. Otherwise, a new entry is created with expiredCount of 1 and issuedCount of 0.
     *
     * @param federalState The federal state for the statistic.
     * @param year         The year that the statistics account for
     */
    void updateOrCreateExpiredStatistic(String federalState, int year);

    /**
     * Decreases the issued ban count for a specific federal state and year. If the count becomes zero or negative,
     * the entry is removed. This is used when a person with an active ban moves to a different federal state.
     *
     * @param federalState The federal state for which to decrease the statistic.
     * @param year         The year that the statistics account for
     */
    void decreaseIssuedStatistic(String federalState, int year);

    /**
     * Updates or creates a ban statistic entry for a ban that started taking place in a specific year.
     * If an entry exists for the given parameters, its startedCount is incremented.
     * Otherwise, a new entry is created with startedCount of 1 and other counts set to 0.
     *
     * @param federalState The federal state for the statistic.
     * @param year         The year that the ban started taking place
     */
    void updateOrCreateStartedStatistic(String federalState, int year);

    /**
     * Decreases the started ban count for a specific federal state and year.
     * This is used when a person with an active ban moves to a different federal state.
     *
     * @param federalState The federal state for which to decrease the statistic.
     * @param year         The year that the ban started taking place
     */
    void decreaseStartedStatistic(String federalState, int year);

    /**
     * Retrieves a list of ban statistics based on the provided federal state and years.
     *
     * @param federalState The federal state for which the statistics are retrieved.
     * @param years        The list of years for which the statistics are requested.
     * @return A list of BansStatisticsView objects containing the requested statistics.
     */
    List<BansStatisticsView> getStatisticsByFederalStateAndYears(String federalState, List<Integer> years);

    /**
     * Retrieves a list of ban statistics for the given years.
     *
     * @param years The list of years for which the statistics are requested.
     * @return A list of BansStatisticsView objects containing the requested statistics.
     */
    List<BansStatisticsView> getStatisticsByYears(List<Integer> years);

    /**
     * Retrieves a list of available years for which ban statistics data is available.
     *
     * @return A list of integers representing the years for which statistics are available.
     */
    List<Integer> getAvailableYears();
}
