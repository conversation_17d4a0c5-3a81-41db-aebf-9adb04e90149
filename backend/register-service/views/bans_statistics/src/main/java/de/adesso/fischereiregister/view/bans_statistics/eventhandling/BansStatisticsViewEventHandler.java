package de.adesso.fischereiregister.view.bans_statistics.eventhandling;

import de.adesso.fischereiregister.core.events.BannedEvent;
import de.adesso.fischereiregister.core.events.JurisdictionMovedEvent;
import de.adesso.fischereiregister.core.events.UnbannedEvent;
import de.adesso.fischereiregister.core.model.Ban;
import de.adesso.fischereiregister.view.bans_statistics.services.BansStatisticsViewService;
import lombok.AllArgsConstructor;
import org.axonframework.eventhandling.EventHandler;
import org.axonframework.eventhandling.ResetHandler;
import org.axonframework.eventhandling.Timestamp;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.ZoneId;

@Component
@AllArgsConstructor
public class BansStatisticsViewEventHandler {

    private final BansStatisticsViewService bansStatisticsViewService;

    @ResetHandler
    public void onReset() {
        bansStatisticsViewService.deleteAll();
    }

    @EventHandler
    public void on(BannedEvent event) {
        String federalState = event.jurisdiction().getFederalState();
        Ban ban = event.ban();

        // Get the year when the ban was issued (at date)
        int issuedYear = ban.getAt().getYear();

        // Update issued statistics based on the ban's at date
        bansStatisticsViewService.updateOrCreateIssuedStatistic(
                federalState,
                issuedYear
        );

        // Get the year when the ban started taking place (from date)
        int startYear = ban.getFrom().getYear();

        // Update started statistics based on the ban's from date
        bansStatisticsViewService.updateOrCreateStartedStatistic(
                federalState,
                startYear
        );
    }

    @EventHandler
    public void on(UnbannedEvent event, @Timestamp Instant timestamp) {
        // The deletion year of the ban is here relevant so no data from the ban is used or needed. And we want to keep the events clean of unnecessary data.
        String federalState = event.jurisdiction().getFederalState();
        int timestampYear = getTimestampYear(timestamp);

        bansStatisticsViewService.updateOrCreateExpiredStatistic(
                federalState,
                timestampYear
        );
    }

    @EventHandler
    public void on(JurisdictionMovedEvent event) {
        Ban ban = event.ban();

        // Only process if there is an active ban
        if (ban != null) {
            String previousFederalState = event.previousJurisdiction().getFederalState();
            String newFederalState = event.newJurisdiction().getFederalState();

            // Get the year when the ban was issued
            int issuedYear = ban.getAt().getYear();

            // Decrease the issued count for the previous federal state
            bansStatisticsViewService.decreaseIssuedStatistic(
                    previousFederalState,
                    issuedYear
            );

            // Increase the issued count for the new federal state
            bansStatisticsViewService.updateOrCreateIssuedStatistic(
                    newFederalState,
                    issuedYear
            );

            // Get the year when the ban started taking place
            int startYear = ban.getFrom().getYear();

            // Decrease the started count for the previous federal state
            bansStatisticsViewService.decreaseStartedStatistic(
                    previousFederalState,
                    startYear
            );

            // Increase the started count for the new federal state
            bansStatisticsViewService.updateOrCreateStartedStatistic(
                    newFederalState,
                    startYear
            );
        }
    }

    private int getTimestampYear(Instant timestamp) {
        return timestamp.atZone(ZoneId.systemDefault()).getYear();
    }
}
