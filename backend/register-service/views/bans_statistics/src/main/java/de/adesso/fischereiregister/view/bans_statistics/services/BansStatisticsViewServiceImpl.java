package de.adesso.fischereiregister.view.bans_statistics.services;

import de.adesso.fischereiregister.view.bans_statistics.persistance.BansStatisticsView;
import de.adesso.fischereiregister.view.bans_statistics.persistance.BansStatisticsViewRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
@AllArgsConstructor
public class BansStatisticsViewServiceImpl implements BansStatisticsViewService {

    private final BansStatisticsViewRepository repository;

    @Override
    public void deleteAll() {
        repository.deleteAll();
    }

    @Override
    public void updateOrCreateIssuedStatistic(String federalState, int year) {
        Optional<BansStatisticsView> existingStatistic = repository.findByFederalStateAndYear(
                federalState, year
        );

        if (existingStatistic.isPresent()) {
            BansStatisticsView statistic = existingStatistic.get();
            statistic.setIssuedCount(statistic.getIssuedCount() + 1);
            repository.save(statistic);
        } else {
            BansStatisticsView newStatistic = new BansStatisticsView();
            newStatistic.setFederalState(federalState);
            newStatistic.setYear(year);
            newStatistic.setIssuedCount(1);
            newStatistic.setExpiredCount(0);
            newStatistic.setStartedCount(0);
            repository.save(newStatistic);
        }
    }

    @Override
    public void updateOrCreateStartedStatistic(String federalState, int year) {
        Optional<BansStatisticsView> existingStatistic = repository.findByFederalStateAndYear(
                federalState, year
        );

        if (existingStatistic.isPresent()) {
            BansStatisticsView statistic = existingStatistic.get();
            statistic.setStartedCount(statistic.getStartedCount() + 1);
            repository.save(statistic);
        } else {
            BansStatisticsView newStatistic = new BansStatisticsView();
            newStatistic.setFederalState(federalState);
            newStatistic.setYear(year);
            newStatistic.setIssuedCount(0);
            newStatistic.setExpiredCount(0);
            newStatistic.setStartedCount(1);
            repository.save(newStatistic);
        }
    }

    @Override
    public void decreaseStartedStatistic(String federalState, int year) {
        Optional<BansStatisticsView> existingStatistic = repository.findByFederalStateAndYear(
                federalState, year
        );

        if (existingStatistic.isPresent()) {
            BansStatisticsView statistic = existingStatistic.get();
            statistic.setStartedCount(statistic.getStartedCount() - 1);
            repository.save(statistic);
        }
        // If no statistic exists, there's nothing to decrease
    }

    @Override
    public void updateOrCreateExpiredStatistic(String federalState, int year) {
        Optional<BansStatisticsView> existingStatistic = repository.findByFederalStateAndYear(
                federalState, year
        );

        if (existingStatistic.isPresent()) {
            BansStatisticsView statistic = existingStatistic.get();
            statistic.setExpiredCount(statistic.getExpiredCount() + 1);
            repository.save(statistic);
        } else {
            BansStatisticsView newStatistic = new BansStatisticsView();
            newStatistic.setFederalState(federalState);
            newStatistic.setYear(year);
            newStatistic.setIssuedCount(0);
            newStatistic.setExpiredCount(1);
            newStatistic.setStartedCount(0);
            repository.save(newStatistic);
        }
    }

    @Override
    public List<BansStatisticsView> getStatisticsByFederalStateAndYears(String federalState, List<Integer> years) {
        return repository.findByFederalStateAndYearIn(federalState, years);
    }

    @Override
    public List<BansStatisticsView> getStatisticsByYears(List<Integer> years) {
        return repository.findByYearIn(years);
    }

    @Override
    public List<Integer> getAvailableYears() {
        return repository.findDistinctYears();
    }

    @Override
    public void decreaseIssuedStatistic(String federalState, int year) {
        Optional<BansStatisticsView> existingStatistic = repository.findByFederalStateAndYear(
                federalState, year
        );

        if (existingStatistic.isPresent()) {
            BansStatisticsView statistic = existingStatistic.get();
            statistic.setIssuedCount(statistic.getIssuedCount() - 1);
            repository.save(statistic);
        }
        // If no statistic exists, there's nothing to decrease
    }
}
