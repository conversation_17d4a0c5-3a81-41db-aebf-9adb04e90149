package de.adesso.fischereiregister.view.bans_statistics.persistance;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface BansStatisticsViewRepository extends CrudRepository<BansStatisticsView, Long> {
    @Query("""
            SELECT view FROM BansStatisticsView view
            WHERE view.federalState = :federalState
              AND view.year = :year
            """)
    Optional<BansStatisticsView> findByFederalStateAndYear(
            @Param("federalState") String federalState,
            @Param("year") int year
    );

    @Query("""
            SELECT view FROM BansStatisticsView view
            WHERE view.federalState = :federalState
              AND view.year IN :years
            """)
    List<BansStatisticsView> findByFederalStateAndYearIn(
            @Param("federalState") String federalState,
            @Param("years") List<Integer> years
    );

    @Query("""
            SELECT view FROM BansStatisticsView view
            WHERE view.year IN :years
            """)
    List<BansStatisticsView> findByYearIn(
            @Param("years") List<Integer> years
    );

    @Query("""
            SELECT DISTINCT view.year FROM BansStatisticsView view
            ORDER BY view.year
            """)
    List<Integer> findDistinctYears();
}
