<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd"
                   context="bans_statistics_view">

    <!-- ChangeSet for BansStatisticsView Table -->
    <changeSet id="1.0.0" author="alexis.ortiz">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="bans_statistics_view"/>
            </not>
        </preConditions>
        <createTable tableName="bans_statistics_view">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="federal_state" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="year" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="issued_count" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="started_count" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="expired_count" type="INTEGER">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <!-- Add unique constraint to ensure no duplicate combinations -->
        <addUniqueConstraint tableName="bans_statistics_view"
                             columnNames="federal_state, year"
                             constraintName="unique_bans_statistics"/>
    </changeSet>

    <!-- ChangeSet for adding indexes to BansStatisticsView Table -->
    <changeSet id="1.0.0-indexes" author="alexis.ortiz">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="bans_statistics_view"/>
        </preConditions>

        <!-- Index for year queries -->
        <createIndex indexName="idx_bans_statistics_year" tableName="bans_statistics_view">
            <column name="year"/>
        </createIndex>

        <!-- Index for federal_state and year queries -->
        <createIndex indexName="idx_bans_statistics_federal_state_year" tableName="bans_statistics_view">
            <column name="federal_state"/>
            <column name="year"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>
