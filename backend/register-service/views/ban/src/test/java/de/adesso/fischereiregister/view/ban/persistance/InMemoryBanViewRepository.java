package de.adesso.fischereiregister.view.ban.persistance;

import de.adesso.fischereiregister.testutils.inmemory.jpa.InMemoryCrudRepository;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

public class InMemoryBanViewRepository extends InMemoryCrudRepository<BanView, UUID> implements BanViewRepository {

    @Override
    public List<UUID> findRegisterEntryIdsWithBansExpiringBefore(LocalDate expirationDate) {
        return save.values().stream()
                .filter(banView -> {
                    LocalDate toDate = banView.getTo();
                    return toDate != null && toDate.isBefore(expirationDate);
                })
                .map(BanView::getRegisterEntryId)
                .toList();
    }

    @Override
    public Integer countActiveBansByFederalState(String federalState, LocalDate currentDate) {
        return (int) save.values().stream()
                .filter(banView -> federalState.equals(banView.getFederalState()))
                .filter(banView -> {
                    LocalDate fromDate = banView.getFrom();
                    LocalDate toDate = banView.getTo();
                    return fromDate != null && !fromDate.isAfter(currentDate) &&
                           (toDate == null || !toDate.isBefore(currentDate));
                })
                .count();
    }

    @Override
    public Integer countActiveBans(LocalDate currentDate) {
        return (int) save.values().stream()
                .filter(banView -> {
                    LocalDate fromDate = banView.getFrom();
                    LocalDate toDate = banView.getTo();
                    return fromDate != null && !fromDate.isAfter(currentDate) &&
                           (toDate == null || !toDate.isBefore(currentDate));
                })
                .count();
    }

    @Override
    public List<Integer> findDistinctIssuedYears() {
        return save.values().stream()
                .filter(banView -> banView.getAt() != null)
                .map(banView -> banView.getAt().getYear())
                .distinct()
                .sorted()
                .toList();
    }

    @Override
    public Integer countIssuedByFederalStateAndYear(String federalState, Integer year) {
        return (int) save.values().stream()
                .filter(banView -> banView.getAt() != null)
                .filter(banView -> banView.getAt().getYear() == year)
                .filter(banView -> federalState.equals(banView.getFederalState()))
                .count();
    }

    @Override
    public Integer countIssuedByYear(Integer year) {
        return (int) save.values().stream()
                .filter(banView -> banView.getAt() != null)
                .filter(banView -> banView.getAt().getYear() == year)
                .count();
    }

    @Override
    public Integer countStartedByFederalStateAndYear(String federalState, Integer year) {
        return (int) save.values().stream()
                .filter(banView -> banView.getFrom() != null)
                .filter(banView -> banView.getFrom().getYear() == year)
                .filter(banView -> federalState.equals(banView.getFederalState()))
                .count();
    }

    @Override
    public Integer countStartedByYear(Integer year) {
        return (int) save.values().stream()
                .filter(banView -> banView.getFrom() != null)
                .filter(banView -> banView.getFrom().getYear() == year)
                .count();
    }

    @Override
    public Integer countExpiredByFederalStateAndYear(String federalState, Integer year) {
        return (int) save.values().stream()
                .filter(banView -> banView.getTo() != null)
                .filter(banView -> banView.getTo().getYear() == year)
                .filter(banView -> federalState.equals(banView.getFederalState()))
                .count();
    }

    @Override
    public Integer countExpiredByYear(Integer year) {
        return (int) save.values().stream()
                .filter(banView -> banView.getTo() != null)
                .filter(banView -> banView.getTo().getYear() == year)
                .count();
    }

    @Override
    protected UUID getID(BanView entity) {
        return entity.getBanId();
    }
}
