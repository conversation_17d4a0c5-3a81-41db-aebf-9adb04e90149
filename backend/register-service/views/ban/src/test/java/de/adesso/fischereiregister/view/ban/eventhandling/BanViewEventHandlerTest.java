package de.adesso.fischereiregister.view.ban.eventhandling;

import de.adesso.fischereiregister.core.events.BannedEvent;
import de.adesso.fischereiregister.core.events.UnbannedEvent;
import de.adesso.fischereiregister.core.model.Ban;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import de.adesso.fischereiregister.view.ban.services.BanViewService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.util.UUID;

import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class BanViewEventHandlerTest {

    @Mock
    BanViewService service;

    @InjectMocks
    BanViewEventHandler eventHandler;

    @Test
    void testBanEventHandling() {
        UUID registerEntryId = UUID.randomUUID();
        LocalDate dateTo = LocalDate.of(2020, 1, 1);
        Jurisdiction jurisdiction = new Jurisdiction();
        jurisdiction.setFederalState("SH");

        Ban ban = new Ban();
        ban.setTo(dateTo);
        BannedEvent event = new BannedEvent(registerEntryId, ban, jurisdiction);

        eventHandler.on(event);

        verify(service, times(1)).create(registerEntryId, ban, "SH");
    }

    @Test
    void testUnbanEventHandling() {
        UUID registerEntryId = UUID.randomUUID();
        Jurisdiction jurisdiction = new Jurisdiction();
        jurisdiction.setFederalState("SH");

        UnbannedEvent event = new UnbannedEvent(registerEntryId, jurisdiction);

        eventHandler.on(event);

        verify(service, times(1)).deleteByRegisterEntryId(registerEntryId);
    }

}
