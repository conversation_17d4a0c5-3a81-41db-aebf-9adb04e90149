<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd"
                   context="ban_view">

    <!-- Drop ban_expiration_view Table If It Exists -->
    <changeSet id="0.9.0-drop-ban-expiration-view" author="alexis.ortiz">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="ban_expiration_view"/>
        </preConditions>
        <dropTable tableName="ban_expiration_view"/>
    </changeSet>

    <!-- ChangeSet for BanView Table -->
    <changeSet id="1.0.0" author="alexis.ortiz">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="ban_view"/>
            </not>
        </preConditions>
        <createTable tableName="ban_view">

            <column name="register_entry_id" type="uuid">
                <constraints primaryKey="true" nullable="false"/>
            </column>

            <column name="ban_id" type="uuid">
                <constraints nullable="true"/>
            </column>

            <column name="file_number" type="varchar(255)">
                <constraints nullable="true"/>
            </column>

            <column name="reported_by" type="varchar(255)">
                <constraints nullable="true"/>
            </column>

            <column name="at" type="date">
                <constraints nullable="true"/>
            </column>

            <column name="from_date" type="date">
                <constraints nullable="true"/>
            </column>

            <column name="federal_state" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>

            <column name="to_date" type="date">
                <constraints nullable="true"/>
            </column>
        </createTable>
    </changeSet>

    <!-- Create indexes for performance -->
    <changeSet id="1.0.0-indexes" author="alexis.ortiz">
        <preConditions onFail="MARK_RAN">
            <not>
                <indexExists indexName="idx_ban_view_to" tableName="ban_view"/>
            </not>
        </preConditions>
        <createIndex indexName="idx_ban_view_at" tableName="ban_view">
            <column name="at"/>
        </createIndex>
        <createIndex indexName="idx_ban_view_to" tableName="ban_view">
            <column name="to_date"/>
        </createIndex>
        <createIndex indexName="idx_ban_view_from" tableName="ban_view">
            <column name="from_date"/>
        </createIndex>
        <createIndex indexName="idx_ban_view_federal_state" tableName="ban_view">
            <column name="federal_state"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>
