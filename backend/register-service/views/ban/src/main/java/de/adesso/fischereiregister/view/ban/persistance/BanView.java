package de.adesso.fischereiregister.view.ban.persistance;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.util.UUID;

/**
 * Entity representing a comprehensive view of ban data.
 * This view stores all ban information for efficient querying and operations.
 */
@Entity
@Table(name = "ban_view")
@Getter
@Setter
public class BanView {

    /**
     * The register entry ID that this ban applies to.
     * This serves as the primary key since each register entry can have at most one active ban.
     */
    @Id
    private UUID registerEntryId;

    /**
     * Unique identifier for the ban itself.
     */
    private UUID banId;

    /**
     * The file number associated with the ban (Aktenzeichen).
     */
    private String fileNumber;

    /**
     * The name of the person or entity who reported the ban (Zuständigkeit).
     */
    private String reportedBy;

    /**
     * The date the ban was reported.
     */
    private LocalDate at;

    /**
     * The start date of the ban.
     */
    @Column(name = "from_date") // because 'from' is an SQL reserved word
    private LocalDate from;

    /**
     * The end date of the ban. If null, the ban is permanent.
     */
    @Column(name = "to_date") // same for 'to'
    private LocalDate to;

    /**
     * The federal state where this ban applies.
     */
    private String federalState;

}
