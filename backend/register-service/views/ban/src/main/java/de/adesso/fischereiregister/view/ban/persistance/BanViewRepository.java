package de.adesso.fischereiregister.view.ban.persistance;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

@Repository
public interface BanViewRepository extends CrudRepository<BanView, UUID> {

    /**
     * Finds all register entry ids which have expired bans before the given date.
     */
    @Query("""
                SELECT bv.registerEntryId FROM BanView bv
                WHERE (bv.to IS NOT NULL AND bv.to < :expirationDate)
            """)
    List<UUID> findRegisterEntryIdsWithBansExpiringBefore(
            @Param("expirationDate") LocalDate expirationDate
    );

    /**
     * Counts the number of active bans in a specific federal state.
     * A ban is active if the current date is between the from date (inclusive) and to date (inclusive),
     * or if the to date is null (permanent ban).
     */
    @Query("""
                SELECT COUNT(bv) FROM BanView bv
                WHERE bv.federalState = :federalState
                  AND bv.from <= :currentDate
                  AND (bv.to IS NULL OR bv.to >= :currentDate)
            """)
    Integer countActiveBansByFederalState(
            @Param("federalState") String federalState,
            @Param("currentDate") LocalDate currentDate
    );

    /**
     * Counts the total number of active bans.
     * A ban is active if the current date is between the from date (inclusive) and to date (inclusive),
     * or if the to date is null (permanent ban).
     */
    @Query("""
                SELECT COUNT(bv) FROM BanView bv
                WHERE bv.from <= :currentDate
                  AND (bv.to IS NULL OR bv.to >= :currentDate)
            """)
    Integer countActiveBans(@Param("currentDate") LocalDate currentDate);

    /**
     * Gets distinct years from ban issue dates (at field).
     */
    @Query("""
                SELECT DISTINCT EXTRACT(YEAR FROM bv.at) FROM BanView bv
                WHERE bv.at IS NOT NULL
                ORDER BY EXTRACT(YEAR FROM bv.at)
            """)
    List<Integer> findDistinctIssuedYears();

    /**
     * Counts bans issued in a specific federal state for a given year.
     */
    @Query("""
                SELECT COUNT(bv) FROM BanView bv
                WHERE bv.federalState = :federalState
                  AND EXTRACT(YEAR FROM bv.at) = :year
                  AND bv.at IS NOT NULL
            """)
    Integer countIssuedByFederalStateAndYear(
            @Param("federalState") String federalState,
            @Param("year") Integer year
    );

    /**
     * Counts total bans issued for a given year.
     */
    @Query("""
                SELECT COUNT(bv) FROM BanView bv
                WHERE EXTRACT(YEAR FROM bv.at) = :year
                  AND bv.at IS NOT NULL
            """)
    Integer countIssuedByYear(@Param("year") Integer year);

    /**
     * Counts bans that started taking effect in a specific federal state for a given year.
     */
    @Query("""
                SELECT COUNT(bv) FROM BanView bv
                WHERE bv.federalState = :federalState
                  AND EXTRACT(YEAR FROM bv.from) = :year
                  AND bv.from IS NOT NULL
            """)
    Integer countStartedByFederalStateAndYear(
            @Param("federalState") String federalState,
            @Param("year") Integer year
    );

    /**
     * Counts total bans that started taking effect for a given year.
     */
    @Query("""
                SELECT COUNT(bv) FROM BanView bv
                WHERE EXTRACT(YEAR FROM bv.from) = :year
                  AND bv.from IS NOT NULL
            """)
    Integer countStartedByYear(@Param("year") Integer year);

    /**
     * Counts bans that expired in a specific federal state for a given year.
     */
    @Query("""
                SELECT COUNT(bv) FROM BanView bv
                WHERE bv.federalState = :federalState
                  AND EXTRACT(YEAR FROM bv.to) = :year
                  AND bv.to IS NOT NULL
            """)
    Integer countExpiredByFederalStateAndYear(
            @Param("federalState") String federalState,
            @Param("year") Integer year
    );

    /**
     * Counts total bans that expired for a given year.
     */
    @Query("""
                SELECT COUNT(bv) FROM BanView bv
                WHERE EXTRACT(YEAR FROM bv.to) = :year
                  AND bv.to IS NOT NULL
            """)
    Integer countExpiredByYear(@Param("year") Integer year);

}
