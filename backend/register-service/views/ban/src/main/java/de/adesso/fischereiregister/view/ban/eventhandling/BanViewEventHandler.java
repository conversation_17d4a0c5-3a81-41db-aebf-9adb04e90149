package de.adesso.fischereiregister.view.ban.eventhandling;

import de.adesso.fischereiregister.core.events.BannedEvent;
import de.adesso.fischereiregister.core.events.UnbannedEvent;
import de.adesso.fischereiregister.view.ban.services.BanViewService;
import lombok.AllArgsConstructor;
import org.axonframework.eventhandling.EventHandler;
import org.axonframework.eventhandling.ResetHandler;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class BanViewEventHandler {

    private final BanViewService banViewService;

    @ResetHandler
    public void onReset() {
        banViewService.deleteAll();
    }

    @EventHandler
    public void on(BannedEvent event) {
        String federalState = event.jurisdiction().getFederalState();
        banViewService.create(event.registerId(), event.ban(), federalState);
    }

    @EventHandler
    public void on(UnbannedEvent event) {
        banViewService.deleteByRegisterEntryId(event.registerEntryId());
    }
    
}
