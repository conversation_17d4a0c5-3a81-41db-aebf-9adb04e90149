package de.adesso.fischereiregister.view.ban.services;

import de.adesso.fischereiregister.core.model.Ban;
import de.adesso.fischereiregister.view.ban.persistance.BanView;
import de.adesso.fischereiregister.view.ban.persistance.BanViewRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

@Service
@AllArgsConstructor
class BanViewServiceImpl implements BanViewService {

    private final BanViewRepository repository;

    @Override
    public void deleteAll() {
        repository.deleteAll();
    }

    @Override
    public void create(UUID registerEntryId, Ban ban, String federalState) {
        final BanView banView = new BanView();
        banView.setRegisterEntryId(registerEntryId);
        banView.setBanId(ban.getBanId());
        banView.setFileNumber(ban.getFileNumber());
        banView.setReportedBy(ban.getReportedBy());
        banView.setAt(ban.getAt());
        banView.setFrom(ban.getFrom());
        banView.setTo(ban.getTo());
        banView.setFederalState(federalState);
        repository.save(banView);
    }

    @Override
    public void deleteByRegisterEntryId(UUID registerEntryId) {
        repository.deleteById(registerEntryId);
    }

    @Override
    public List<UUID> findRegisterEntryIdsWithExpiredBans() {
        return repository.findRegisterEntryIdsWithBansExpiringBefore(LocalDate.now());
    }

    @Override
    public List<Integer> getAvailableYears() {
        return repository.findDistinctIssuedYears();
    }

    @Override
    public Integer getIssuedAmountByFederalStateAndYear(String federalState, Integer year) {
        return repository.countIssuedByFederalStateAndYear(federalState, year);
    }

    @Override
    public Integer getIssuedAmountByYear(Integer year) {
        return repository.countIssuedByYear(year);
    }

    @Override
    public Integer getStartedAmountByFederalStateAndYear(String federalState, Integer year) {
        return repository.countStartedByFederalStateAndYear(federalState, year);
    }

    @Override
    public Integer getStartedAmountByYear(Integer year) {
        return repository.countStartedByYear(year);
    }

    @Override
    public Integer getExpiredAmountByFederalStateAndYear(String federalState, Integer year) {
        return repository.countExpiredByFederalStateAndYear(federalState, year);
    }

    @Override
    public Integer getExpiredAmountByYear(Integer year) {
        return repository.countExpiredByYear(year);
    }

    @Override
    public Integer getActiveBansAmountByFederalState(String federalState) {
        return repository.countActiveBansByFederalState(federalState, LocalDate.now());
    }

    @Override
    public Integer getActiveBansAmount() {
        return repository.countActiveBans(LocalDate.now());
    }

}
