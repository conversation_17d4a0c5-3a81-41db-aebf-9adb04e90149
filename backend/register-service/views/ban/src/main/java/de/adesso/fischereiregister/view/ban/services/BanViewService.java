package de.adesso.fischereiregister.view.ban.services;

import de.adesso.fischereiregister.core.model.Ban;

import java.util.List;
import java.util.UUID;

public interface BanViewService {
    /**
     * Deletes all entries in the ban expiration view. Use only if Axon replay ensured to happen after.
     */
    void deleteAll();

    /**
     * Creates a new entry in the ban view.
     *
     * @param registerEntryId The ID of the register entry associated with the ban.
     * @param ban             The ban object containing the ban details.
     * @param federalState    The federal state where this ban applies.
     */
    void create(UUID registerEntryId, Ban ban, String federalState);

    /**
     * Deletes an entry by the register entry ID.
     *
     * @param registerEntryId The ID of the register entry associated with the ban.
     */
    void deleteByRegisterEntryId(UUID registerEntryId);

    /**
     * Retrieves a list of register entry IDs that have expired bans.
     * is always compared with the current system date.
     *
     * @return A list of register entry IDs with expired bans.
     */
    List<UUID> findRegisterEntryIdsWithExpiredBans();


    /**
     * Retrieves a list of available years for which ban data is available.
     *
     * @return the list of available years
     */
    List<Integer> getAvailableYears();

    /**
     * Retrieves the number of bans issued in a specific federal state for a given year.
     *
     * @param federalState The federal state for which to retrieve the ban count.
     * @param year         The year for which to retrieve the ban count.
     * @return The number of bans issued in the specified federal state for the given year.
     */
    Integer getIssuedAmountByFederalStateAndYear(String federalState, Integer year);

    /**
     * Retrieves the number of bans issued for a given year.
     *
     * @param year The year for which to retrieve the ban count.
     * @return The number of bans issued for the given year.
     */
    Integer getIssuedAmountByYear(Integer year);

    /**
     * Retrieves the number of bans that started taking effect in a specific federal state for a given year.
     *
     * @param federalState The federal state for which to retrieve the ban count.
     * @param year         The year for which to retrieve the ban count.
     * @return The number of bans that started taking effect in the specified federal state for the given year.
     */
    Integer getStartedAmountByFederalStateAndYear(String federalState, Integer year);

    /**
     * Retrieves the number of bans that started taking effect for a given year.
     *
     * @param year The year for which to retrieve the ban count.
     * @return The number of bans that started taking effect for the given year.
     */
    Integer getStartedAmountByYear(Integer year);

    /**
     * Retrieves the number of bans that expired in a specific federal state for a given year.
     *
     * @param federalState The federal state for which to retrieve the ban count.
     * @param year         The year for which to retrieve the ban count.
     * @return The number of bans that expired in the specified federal state for the given year.
     */
    Integer getExpiredAmountByFederalStateAndYear(String federalState, Integer year);

    /**
     * Retrieves the number of bans that expired for a given year.
     *
     * @param year The year for which to retrieve the ban count.
     * @return The number of bans that expired for the given year.
     */
    Integer getExpiredAmountByYear(Integer year);

    /**
     * Retrieves the number of active bans in this very moment in a specific federal state.
     * the actual current date is used for the calculation
     *
     * @param federalState The federal state for which to retrieve the active ban count.
     * @return The number of active bans in the specified federal state.
     */
    Integer getActiveBansAmountByFederalState(String federalState);

    /**
     * Retrieves the number of active bans in this very moment.
     * the actual current date is used for the calculation
     *
     * @return The number of active bans.
     */
    Integer getActiveBansAmount();

}
