package de.adesso.fischereiregister.view.fees_statistics.services;

import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.view.fees_statistics.persistence.FeesStatisticsView;
import de.adesso.fischereiregister.view.fees_statistics.persistence.FeesStatisticsViewRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@AllArgsConstructor
public class FeesStatisticsViewServiceImpl implements FeesStatisticsViewService {

    private final FeesStatisticsViewRepository repository;

    @Override
    public void updateOrCreateStatistic(String federalState, String office, SubmissionType source, int year, double revenue) {
        // Try to find an existing entry
        FeesStatisticsView view = repository.findByFederalStateAndOfficeAndSourceAndYear(federalState, office, source, year)
                .orElse(null);

        if (view != null) {
            view.setCount(view.getCount() + 1);
            view.setRevenue(view.getRevenue() + revenue);
        } else {
            view = new FeesStatisticsView();
            view.setFederalState(federalState);
            view.setOffice(office);
            view.setSource(source);
            view.setYear(year);
            view.setCount(1);
            view.setRevenue(revenue);
        }

        repository.save(view);
    }

    @Override
    public List<FeesStatisticsView> getStatisticsByFederalStateAndYears(String federalState, List<Integer> years) {
        return repository.findByFederalStateAndYearIn(federalState, years);
    }

    @Override
    public List<FeesStatisticsView> getStatisticsByOfficeAndYears(String office, List<Integer> years) {
        return repository.findByOfficeAndYearIn(office, years);
    }

    @Override
    public List<FeesStatisticsView> getStatisticsByYears(List<Integer> years) {
        return repository.findByYearIn(years);
    }

    @Override
    public List<Integer> getAvailableYears() {
        return repository.findDistinctYears();
    }

    @Override
    public void deleteAll() {
        repository.deleteAll();
    }
}
