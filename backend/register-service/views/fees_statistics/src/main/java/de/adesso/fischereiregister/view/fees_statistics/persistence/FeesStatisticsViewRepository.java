package de.adesso.fischereiregister.view.fees_statistics.persistence;

import de.adesso.fischereiregister.core.model.type.SubmissionType;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface FeesStatisticsViewRepository extends CrudRepository<FeesStatisticsView, Long> {

    @Query("""
            SELECT view FROM FeesStatisticsView view
            WHERE view.federalState = :federalState
              AND COALESCE(view.office, '') = COALESCE(:office, '')
              AND view.source = :source
              AND view.year = :year
            """)
    Optional<FeesStatisticsView> findByFederalStateAndOfficeAndSourceAndYear(
            @Param("federalState") String federalState,
            @Param("office") String office,
            @Param("source") SubmissionType source,
            @Param("year") int year
    );

    @Query("SELECT view FROM FeesStatisticsView view WHERE view.federalState = :federalState AND view.year IN :years")
    List<FeesStatisticsView> findByFederalStateAndYearIn(
            @Param("federalState") String federalState,
            @Param("years") List<Integer> years
    );

    @Query("SELECT view FROM FeesStatisticsView view WHERE view.office = :office AND view.year IN :years")
    List<FeesStatisticsView> findByOfficeAndYearIn(
            @Param("office") String office,
            @Param("years") List<Integer> years
    );

    @Query("SELECT view FROM FeesStatisticsView view WHERE view.year IN :years")
    List<FeesStatisticsView> findByYearIn(@Param("years") List<Integer> years);

    @Query("SELECT DISTINCT view.year FROM FeesStatisticsView view ORDER BY view.year DESC")
    List<Integer> findDistinctYears();
}
