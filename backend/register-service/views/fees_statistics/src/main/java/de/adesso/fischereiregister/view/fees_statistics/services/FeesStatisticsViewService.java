package de.adesso.fischereiregister.view.fees_statistics.services;

import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.view.fees_statistics.persistence.FeesStatisticsView;

import java.util.List;

public interface FeesStatisticsViewService {
    /**
     * Updates or creates a fee statistics entry. If an entry exists for the given parameters,
     * its count is incremented, and the revenue is increased by the specified amount.
     * Otherwise, a new entry is created with a count of 1.
     *
     * @param federalState The federal state for the statistic.
     * @param office       The office responsible for the statistic (nullable).
     * @param source       The submission source (e.g., online, in-person).
     * @param year         The year for which the statistics are recorded.
     * @param revenue      The total revenue generated for the fee.
     */
    void updateOrCreateStatistic(String federalState, String office, SubmissionType source, int year, double revenue);

    /**
     * Retrieves fee statistics for the specified federal state and years.
     *
     * @param federalState The federal state to filter by.
     * @param years        The years to include in the results.
     * @return A list of fee statistics views matching the criteria.
     */
    List<FeesStatisticsView> getStatisticsByFederalStateAndYears(String federalState, List<Integer> years);

    /**
     * Retrieves fee statistics for the specified office and years.
     *
     * @param office The office to filter by.
     * @param years  The years to include in the results.
     * @return A list of fee statistics views matching the criteria.
     */
    List<FeesStatisticsView> getStatisticsByOfficeAndYears(String office, List<Integer> years);

    /**
     * Retrieves fee statistics for the specified years.
     *
     * @param years The years to include in the results.
     * @return A list of fee statistics views matching the criteria.
     */
    List<FeesStatisticsView> getStatisticsByYears(List<Integer> years);

    /**
     * Retrieves all available years for which fee statistics exist.
     *
     * @return A list of years.
     */
    List<Integer> getAvailableYears();

    /**
     * Deletes all Fees Statistics entries in the View (Projection).
     */
    void deleteAll();
}
