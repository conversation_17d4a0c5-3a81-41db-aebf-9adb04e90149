package de.adesso.fischereiregister.view.fees_statistics.persistence;

import de.adesso.fischereiregister.core.model.type.SubmissionType;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table
@Getter
@Setter
public class FeesStatisticsView {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false)
    private String federalState;

    @Column()
    private String office;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private SubmissionType source;

    @Column(nullable = false)
    private int year;

    @Column(nullable = false)
    private int count;

    @Column(nullable = false)
    private double revenue;
}
