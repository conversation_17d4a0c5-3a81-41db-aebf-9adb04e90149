<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd"
                   context="taxes_statistics_view">

    <!-- Create FeesStatisticsView table with constraints and indexes -->
    <changeSet id="1.0.0" author="alexis.ortiz">
        <!-- Skip if table already exists -->
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="fees_statistics_view"/>
            </not>
        </preConditions>

        <!-- Table definition -->
        <createTable tableName="fees_statistics_view">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="federal_state" type="VARCHAR(2)">
                <constraints nullable="false"/>
            </column>
            <column name="office" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
            <column name="source" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="year" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="count" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="revenue" type="DOUBLE">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <!-- Unique constraint for main lookup query -->
        <addUniqueConstraint tableName="fees_statistics_view"
                             columnNames="federal_state, office, source, year"
                             constraintName="unique_fee_statistics"/>

        <!-- Index for year-based filtering -->
        <createIndex indexName="idx_fees_statistics_year" tableName="fees_statistics_view">
            <column name="year"/>
        </createIndex>

        <!-- Index for (federal_state, year) queries -->
        <createIndex indexName="idx_fees_statistics_federal_state_year" tableName="fees_statistics_view">
            <column name="federal_state"/>
            <column name="year"/>
        </createIndex>

        <!-- Index for (office, year) queries -->
        <createIndex indexName="idx_fees_statistics_office_year" tableName="fees_statistics_view">
            <column name="office"/>
            <column name="year"/>
        </createIndex>

        <!-- Index for optional source filtering -->
        <createIndex indexName="idx_fees_statistics_source" tableName="fees_statistics_view">
            <column name="source"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>
