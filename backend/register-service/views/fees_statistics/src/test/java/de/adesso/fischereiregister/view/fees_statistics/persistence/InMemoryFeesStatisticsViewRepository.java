package de.adesso.fischereiregister.view.fees_statistics.persistence;

import de.adesso.fischereiregister.core.model.type.SubmissionType;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

public class InMemoryFeesStatisticsViewRepository implements FeesStatisticsViewRepository {

    private final Map<Long, FeesStatisticsView> database = new HashMap<>();
    private long idCounter = 1;

    @Override
    public Optional<FeesStatisticsView> findByFederalStateAndOfficeAndSourceAndYear(
            String federalState, String office, SubmissionType source, int year) {
        return database.values().stream()
                .filter(view -> view.getFederalState().equals(federalState))
                .filter(view -> Objects.equals(view.getOffice(), office))
                .filter(view -> view.getSource().equals(source))
                .filter(view -> view.getYear() == year)
                .findFirst();
    }

    @Override
    public List<FeesStatisticsView> findByFederalStateAndYearIn(String federalState, List<Integer> years) {
        return database.values().stream()
                .filter(view -> view.getFederalState().equals(federalState))
                .filter(view -> years.contains(view.getYear()))
                .collect(Collectors.toList());
    }

    @Override
    public List<FeesStatisticsView> findByOfficeAndYearIn(String office, List<Integer> years) {
        return database.values().stream()
                .filter(view -> Objects.equals(view.getOffice(), office))
                .filter(view -> years.contains(view.getYear()))
                .collect(Collectors.toList());
    }

    @Override
    public List<FeesStatisticsView> findByYearIn(List<Integer> years) {
        return database.values().stream()
                .filter(view -> years.contains(view.getYear()))
                .collect(Collectors.toList());
    }

    @Override
    public List<Integer> findDistinctYears() {
        return database.values().stream()
                .map(FeesStatisticsView::getYear)
                .distinct()
                .sorted(Comparator.reverseOrder())
                .collect(Collectors.toList());
    }

    @Override
    public <S extends FeesStatisticsView> S save(S entity) {
        if (entity.getId() == null) {
            entity.setId(idCounter++);
        }
        database.put(entity.getId(), entity);
        return entity;
    }

    @Override
    public <S extends FeesStatisticsView> Iterable<S> saveAll(Iterable<S> entities) {
        entities.forEach(this::save);
        return entities;
    }

    @Override
    public Optional<FeesStatisticsView> findById(Long id) {
        return Optional.ofNullable(database.get(id));
    }

    @Override
    public boolean existsById(Long id) {
        return database.containsKey(id);
    }

    @Override
    public Iterable<FeesStatisticsView> findAll() {
        return database.values();
    }

    @Override
    public Iterable<FeesStatisticsView> findAllById(Iterable<Long> ids) {
        List<FeesStatisticsView> result = new ArrayList<>();
        ids.forEach(id -> {
            if (database.containsKey(id)) {
                result.add(database.get(id));
            }
        });
        return result;
    }

    @Override
    public long count() {
        return database.size();
    }

    @Override
    public void deleteById(Long id) {
        database.remove(id);
    }

    @Override
    public void delete(FeesStatisticsView entity) {
        if (entity.getId() != null) {
            database.remove(entity.getId());
        }
    }

    @Override
    public void deleteAllById(Iterable<? extends Long> ids) {
        ids.forEach(database::remove);
    }

    @Override
    public void deleteAll(Iterable<? extends FeesStatisticsView> entities) {
        entities.forEach(this::delete);
    }

    @Override
    public void deleteAll() {
        database.clear();
    }
}
