package de.adesso.fischereiregister.view.fees_statistics.eventhandling;

import de.adesso.fischereiregister.core.events.LicenseExtendedEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseCreatedEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseDigitizedEvent;
import de.adesso.fischereiregister.core.events.ReplacementCardOrderedEvent;
import de.adesso.fischereiregister.core.events.VacationLicenseCreatedEvent;
import de.adesso.fischereiregister.core.model.Fee;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import de.adesso.fischereiregister.core.model.PaymentInfo;
import de.adesso.fischereiregister.core.model.ValidityPeriod;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.PaymentType;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.view.fees_statistics.services.FeesStatisticsViewService;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;

@ExtendWith(MockitoExtension.class)
class FeesStatisticsViewEventHandlerTest {

    private static final String FEDERAL_STATE = "SH";
    private static final String OFFICE = "TestOffice";
    private static final SubmissionType SUBMISSION_TYPE = SubmissionType.ANALOG;
    private static final Instant FIXED_TIMESTAMP = Instant.parse("2024-01-15T10:15:30.00Z");
    private static final int FIXED_YEAR = 2024;
    private static final double FEE_AMOUNT = 25.0;

    @Mock
    private FeesStatisticsViewService feesStatisticsViewServiceMock;

    @InjectMocks
    private FeesStatisticsViewEventHandler eventHandler;

    @Test
    @DisplayName("FeesStatisticsViewEventHandler.onReset should call deleteAll on FeesStatisticsViewService")
    void testOnReset() {
        // When
        eventHandler.onReset();

        // Then
        verify(feesStatisticsViewServiceMock).deleteAll();
    }

    @Test
    @DisplayName("FeesStatisticsViewEventHandler.on(RegularLicenseCreatedEvent) should call updateOrCreateStatistic with correct parameters")
    void testOnRegularLicenseCreatedEvent() {
        // Given
        Fee fee = createFee();

        final RegularLicenseCreatedEvent event = new RegularLicenseCreatedEvent(
                UUID.randomUUID(),
                null,
                null,
                null,
                List.of(fee),
                List.of(),
                null,
                List.of(),
                null,
                OFFICE,
                null,
                null,
                null,
                null,
                SUBMISSION_TYPE
        );

        // When
        eventHandler.on(event, FIXED_TIMESTAMP);

        // Then
        verify(feesStatisticsViewServiceMock).updateOrCreateStatistic(
                FEDERAL_STATE,
                OFFICE,
                SUBMISSION_TYPE,
                FIXED_YEAR,
                FEE_AMOUNT
        );
    }

    @Test
    @DisplayName("FeesStatisticsViewEventHandler.on(RegularLicenseCreatedEvent) should not call updateOrCreateStatistic when fees list is empty")
    void testOnRegularLicenseCreatedEvent_EmptyFees() {
        // Given
        final RegularLicenseCreatedEvent event = new RegularLicenseCreatedEvent(
                UUID.randomUUID(),
                null,
                null,
                null,
                List.of(),
                List.of(),
                null,
                List.of(),
                null,
                OFFICE,
                null,
                null,
                null,
                null,
                SUBMISSION_TYPE
        );

        // When
        eventHandler.on(event, FIXED_TIMESTAMP);

        // Then
        verifyNoInteractions(feesStatisticsViewServiceMock);
    }

    @Test
    @DisplayName("FeesStatisticsViewEventHandler.on(RegularLicenseDigitizedEvent) should call updateOrCreateStatistic with correct parameters")
    void testOnRegularLicenseDigitizedEvent() {
        // Given
        Fee fee = createFee();

        Jurisdiction jurisdiction = new Jurisdiction();
        jurisdiction.setFederalState(FEDERAL_STATE);

        FishingLicense fishingLicense = new FishingLicense();

        final RegularLicenseDigitizedEvent event = new RegularLicenseDigitizedEvent(
                UUID.randomUUID(),
                null,
                null,
                jurisdiction,
                fishingLicense,
                List.of(fee),
                List.of(),
                List.of(),
                List.of(),
                null,
                OFFICE,
                null
        );

        // When
        eventHandler.on(event, FIXED_TIMESTAMP);

        // Then
        verify(feesStatisticsViewServiceMock).updateOrCreateStatistic(
                FEDERAL_STATE,
                OFFICE,
                SubmissionType.ANALOG,
                FIXED_YEAR,
                FEE_AMOUNT
        );
    }

    @Test
    @DisplayName("FeesStatisticsViewEventHandler.on(VacationLicenseCreatedEvent) should call updateOrCreateStatistic with correct parameters")
    void testOnVacationLicenseCreatedEvent() {
        // Given
        Fee fee = createFee();

        final VacationLicenseCreatedEvent event = new VacationLicenseCreatedEvent(
                UUID.randomUUID(),
                null,
                null,
                null,
                List.of(fee),
                List.of(),
                List.of(),
                null,
                OFFICE,
                null,
                null,
                null,
                SUBMISSION_TYPE
        );

        // When
        eventHandler.on(event, FIXED_TIMESTAMP);

        // Then
        verify(feesStatisticsViewServiceMock).updateOrCreateStatistic(
                FEDERAL_STATE,
                OFFICE,
                SUBMISSION_TYPE,
                FIXED_YEAR,
                FEE_AMOUNT
        );
    }

    @Test
    @DisplayName("FeesStatisticsViewEventHandler.on(LicenseExtendedEvent) should call updateOrCreateStatistic with correct parameters")
    void testOnLicenseExtendedEvent() {
        // Given
        Fee fee = createFee();

        ValidityPeriod validityPeriod = new ValidityPeriod();
        validityPeriod.setValidFrom(LocalDate.now());
        validityPeriod.setValidTo(LocalDate.now().plusYears(1));

        final LicenseExtendedEvent event = new LicenseExtendedEvent(
                UUID.randomUUID(),
                null,
                null,
                null,
                List.of(fee),
                List.of(),
                "12345",
                validityPeriod,
                List.of(),
                null,
                null,
                null,
                OFFICE,
                SUBMISSION_TYPE
        );

        // When
        eventHandler.on(event, FIXED_TIMESTAMP);

        // Then
        verify(feesStatisticsViewServiceMock).updateOrCreateStatistic(
                FEDERAL_STATE,
                OFFICE,
                SUBMISSION_TYPE,
                FIXED_YEAR,
                FEE_AMOUNT
        );
    }

    @Test
    @DisplayName("FeesStatisticsViewEventHandler.on(ReplacementCardOrderedEvent) should call updateOrCreateStatistic with correct parameters")
    void testOnReplacementCardOrderedEvent() {
        // Given
        Fee fee = createFee();

        final ReplacementCardOrderedEvent event = new ReplacementCardOrderedEvent(
                UUID.randomUUID(),
                null,
                null,
                List.of(),
                null,
                FederalState.valueOf(FEDERAL_STATE),
                OFFICE,
                null, // issuedByAddress
                List.of(fee),
                List.of(),
                null,
                null, // inboxReference
                null,
                null,
                SUBMISSION_TYPE
        );

        // When
        eventHandler.on(event, FIXED_TIMESTAMP);

        // Then
        verify(feesStatisticsViewServiceMock).updateOrCreateStatistic(
                FEDERAL_STATE,
                OFFICE,
                SUBMISSION_TYPE,
                FIXED_YEAR,
                FEE_AMOUNT
        );
    }

    private Fee createFee() {
        Fee fee = new Fee();
        fee.setFederalState(FEDERAL_STATE);
        fee.setValidFrom(LocalDate.of(2024, 1, 1));
        fee.setValidTo(LocalDate.of(2024, 12, 31));

        PaymentInfo paymentInfo = new PaymentInfo();
        paymentInfo.setAmount(FEE_AMOUNT);
        paymentInfo.setType(PaymentType.ONLINE);

        fee.setPaymentInfo(paymentInfo);

        return fee;
    }
}
