package de.adesso.fischereiregister.view.fees_statistics.services;

import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.view.fees_statistics.persistence.FeesStatisticsView;
import de.adesso.fischereiregister.view.fees_statistics.persistence.InMemoryFeesStatisticsViewRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

class FeesStatisticsViewServiceImplTest {

    private InMemoryFeesStatisticsViewRepository repository;
    private FeesStatisticsViewService feesStatisticsViewService;

    @BeforeEach
    void setUp() {
        repository = new InMemoryFeesStatisticsViewRepository();
        feesStatisticsViewService = new FeesStatisticsViewServiceImpl(repository);
    }

    @Test
    @DisplayName("FeesStatisticsViewService.updateOrCreateStatistic should create a new entry if none exists")
    void testUpdateOrCreateStatistic_NewEntry() {
        // Given
        String federalState = "SH";
        String office = "office";
        SubmissionType source = SubmissionType.ANALOG;
        int year = 2024;
        double revenue = 50.0;

        // When
        feesStatisticsViewService.updateOrCreateStatistic(federalState, office, source, year, revenue);

        // Then
        Optional<FeesStatisticsView> result = repository.findByFederalStateAndOfficeAndSourceAndYear(federalState, office, source, year);
        assertTrue(result.isPresent());
        assertEquals(1, result.get().getCount());
        assertEquals(revenue, result.get().getRevenue());
    }

    @Test
    @DisplayName("FeesStatisticsViewService.updateOrCreateStatistic should update an existing entry by incrementing its count and adding revenue")
    void testUpdateOrCreateStatistic_UpdateExistingEntry() {
        // Given
        String federalState = "SH";
        String office = "office";
        SubmissionType source = SubmissionType.ANALOG;
        int year = 2024;
        double initialRevenue = 50.0;
        double additionalRevenue = 25.0;

        FeesStatisticsView existingEntry = new FeesStatisticsView();
        existingEntry.setFederalState(federalState);
        existingEntry.setOffice(office);
        existingEntry.setSource(source);
        existingEntry.setYear(year);
        existingEntry.setCount(5);
        existingEntry.setRevenue(initialRevenue);
        repository.save(existingEntry);

        // When
        feesStatisticsViewService.updateOrCreateStatistic(federalState, office, source, year, additionalRevenue);

        // Then
        Optional<FeesStatisticsView> result = repository.findByFederalStateAndOfficeAndSourceAndYear(federalState, office, source, year);
        assertTrue(result.isPresent());
        assertEquals(6, result.get().getCount());
        assertEquals(initialRevenue + additionalRevenue, result.get().getRevenue());
    }

    @Test
    @DisplayName("FeesStatisticsViewService.getStatisticsByFederalStateAndYears should return statistics for the specified federal state and years")
    void testGetStatisticsByFederalStateAndYears() {
        // Given
        String federalState = "SH";
        createTestData(federalState);

        // When
        List<FeesStatisticsView> result = feesStatisticsViewService.getStatisticsByFederalStateAndYears(federalState, List.of(2023, 2024));

        // Then
        assertEquals(2, result.size());
        assertTrue(result.stream().allMatch(view -> view.getFederalState().equals(federalState)));
        assertTrue(result.stream().allMatch(view -> List.of(2023, 2024).contains(view.getYear())));
    }

    @Test
    @DisplayName("FeesStatisticsViewService.getStatisticsByOfficeAndYears should return statistics for the specified office and years")
    void testGetStatisticsByOfficeAndYears() {
        // Given
        String office = "office1";
        createTestData("SH", office);
        createTestData("NW", "office2");

        // When
        List<FeesStatisticsView> result = feesStatisticsViewService.getStatisticsByOfficeAndYears(office, List.of(2023, 2024));

        // Then
        assertEquals(2, result.size());
        assertTrue(result.stream().allMatch(view -> view.getOffice().equals(office)));
        assertTrue(result.stream().allMatch(view -> List.of(2023, 2024).contains(view.getYear())));
    }

    @Test
    @DisplayName("FeesStatisticsViewService.getStatisticsByYears should return statistics for the specified years")
    void testGetStatisticsByYears() {
        // Given
        createTestData("SH");
        createTestData("NW");

        // When
        List<FeesStatisticsView> result = feesStatisticsViewService.getStatisticsByYears(List.of(2023, 2024));

        // Then
        assertEquals(4, result.size());
        assertTrue(result.stream().allMatch(view -> List.of(2023, 2024).contains(view.getYear())));
    }

    @Test
    @DisplayName("FeesStatisticsViewService.getAvailableYears should return all distinct years in descending order")
    void testGetAvailableYears() {
        // Given
        createTestData("SH");
        createTestData("NW");
        
        FeesStatisticsView entry2022 = new FeesStatisticsView();
        entry2022.setFederalState("SH");
        entry2022.setOffice("office");
        entry2022.setSource(SubmissionType.ANALOG);
        entry2022.setYear(2022);
        entry2022.setCount(1);
        entry2022.setRevenue(50.0);
        repository.save(entry2022);

        // When
        List<Integer> result = feesStatisticsViewService.getAvailableYears();

        // Then
        assertEquals(3, result.size());
        assertEquals(List.of(2024, 2023, 2022), result);
    }


    @Test
    @DisplayName("FeesStatisticsViewService.deleteAll should delete all entries")
    void testDeleteAll() {
        // Given
        createTestData("SH");
        createTestData("NW");

        // When
        feesStatisticsViewService.deleteAll();

        // Then
        assertEquals(0, repository.count());
    }

    private void createTestData(String federalState) {
        createTestData(federalState, "office1");
    }

    private void createTestData(String federalState, String office) {
        FeesStatisticsView entry2023 = new FeesStatisticsView();
        entry2023.setFederalState(federalState);
        entry2023.setOffice(office);
        entry2023.setSource(SubmissionType.ANALOG);
        entry2023.setYear(2023);
        entry2023.setCount(1);
        entry2023.setRevenue(50.0);
        repository.save(entry2023);

        FeesStatisticsView entry2024 = new FeesStatisticsView();
        entry2024.setFederalState(federalState);
        entry2024.setOffice(office);
        entry2024.setSource(SubmissionType.ONLINE);
        entry2024.setYear(2024);
        entry2024.setCount(2);
        entry2024.setRevenue(100.0);
        repository.save(entry2024);
    }
}
