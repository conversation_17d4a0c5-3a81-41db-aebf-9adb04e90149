package de.adesso.fischereiregister.card_orders.adapters.inmemory;


import de.adesso.fischereiregister.card_orders.persistence.CardOrder;
import de.adesso.fischereiregister.card_orders.persistence.CardOrderRepository;
import de.adesso.fischereiregister.testutils.inmemory.jpa.InMemoryCrudRepository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

public class InMemoryCardOrderRepository extends InMemoryCrudRepository<CardOrder, UUID> implements CardOrderRepository {

    @Override
    public List<CardOrder> findByLicenseNumber(String licenseNumber) {
        return save.values().stream()
                .filter(cardOrder -> cardOrder.getLicenseNumber().equals(licenseNumber))
                .toList();
    }

    @Override
    public List<UUID> findCardOrdersMarkedForDeletion(LocalDateTime threshold) {
        return List.of();
    }

    @Override
    protected UUID getID(CardOrder entity) {
        return entity.getOrderId();
    }
}
