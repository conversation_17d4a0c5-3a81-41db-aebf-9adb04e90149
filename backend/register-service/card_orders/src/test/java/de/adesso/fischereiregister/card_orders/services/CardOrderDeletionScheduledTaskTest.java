package de.adesso.fischereiregister.card_orders.services;

import de.adesso.fischereiregister.card_orders.adapters.inmemory.InMemoryCardOrderRepository;
import de.adesso.fischereiregister.card_orders.config.CardOrderConfig;
import de.adesso.fischereiregister.card_orders.persistence.CardOrder;
import de.adesso.fischereiregister.card_orders.persistence.CardOrderRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class CardOrderDeletionScheduledTaskTest {

    @Spy
    private CardOrderRepository cardOrderRepository = new InMemoryCardOrderRepository();

    @Mock
    private CardOrderConfig cardOrderConfig;

    @InjectMocks
    private CardOrderDeletionScheduledTask cardOrderDeletionScheduledTask;

    @BeforeEach
    public void setUp() {
        cardOrderRepository.deleteAll();
    }

    @Test
    @DisplayName("CardOrderDeletionScheduledTask.deleteFinishedOrders should only delete orders marked for deletion")
    public void testDeleteSuccessful() {
        // GIVEN
        final CardOrder orderKeep = new CardOrder();
        orderKeep.setOrderId(UUID.randomUUID());

        final CardOrder orderDelete = new CardOrder();
        orderDelete.setOrderId(UUID.randomUUID());

        cardOrderRepository.saveAll(List.of(orderKeep, orderDelete));
        when(cardOrderRepository.findCardOrdersMarkedForDeletion(any())).thenReturn(List.of(orderDelete.getOrderId()));

        // WHEN
        cardOrderDeletionScheduledTask.deleteFinishedOrders();

        // THEN
        final CardOrder actualOrderDelete = cardOrderRepository.findById(orderDelete.getOrderId()).orElse(null);
        assertNull(actualOrderDelete);

        final CardOrder actualOrderKeep = cardOrderRepository.findById(orderKeep.getOrderId()).orElse(null);
        assertNotNull(actualOrderKeep);
    }
}
