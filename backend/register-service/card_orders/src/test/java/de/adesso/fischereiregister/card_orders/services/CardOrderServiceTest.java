package de.adesso.fischereiregister.card_orders.services;

import de.adesso.fischereiregister.card_orders.adapters.inmemory.InMemoryCardOrderRepository;
import de.adesso.fischereiregister.card_orders.persistence.CardOrder;
import de.adesso.fischereiregister.card_orders.persistence.CardOrderRepository;
import de.adesso.fischereiregister.card_orders.persistence.OrderStatus;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Person;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class CardOrderServiceTest {
    @InjectMocks
    private CardOrderService cardOrderService;

    @Mock
    private HttpOrderService httpOrderService;

    @Spy
    private CardOrderRepository cardOrderRepository = new InMemoryCardOrderRepository();

    @Test
    @DisplayName("CardOrderService.issueCardOrder should call httpOrderService and save the card order.")
    public void testIssueCardOrderCallsHttpServiceAndSavesToRepository() {
        // given
        final UUID registerEntryId = UUID.randomUUID();
        final Person person = new Person();
        final FishingLicense license = new FishingLicense();
        final IdentificationDocument document = new IdentificationDocument();
        final String salt = "salt";
        final String issuedByAddress = "issuedByAddress";

        // when
        cardOrderService.issueCardOrder(registerEntryId, person, license, document, salt, issuedByAddress);

        // then
        // verify that the httpOrderService.registerOrder method is called once
        verify(httpOrderService, times(1)).registerOrder(any(), any(), eqRegisterInfo(license, person, registerEntryId, document), eq(salt), eq(issuedByAddress));
        // verify that the cardOrderRepository.save method is called twice
        verify(cardOrderRepository, times(2)).save(any());
    }

    @Test
    @DisplayName("CardOrderService.issueCardOrder should update repository correctly when httpOrder fails.")
    public void testIssueCardOrderUpdatesRepositoryCorrectlyWhenHttpOrderFails() {
        // given
        final UUID registerEntryId = UUID.randomUUID();
        final Person person = new Person();
        final FishingLicense license = new FishingLicense();
        license.setNumber("licenseNumber");
        final IdentificationDocument document = new IdentificationDocument();
        document.setDocumentId("documentId");
        final String salt = "salt";
        final String issuedByAddress = "issuedByAddress";

        doThrow(new RuntimeException("HTTP order failed")).when(httpOrderService).registerOrder(any(), any(), any(), any(), any());

        // when
        assertThrows(Exception.class, () -> cardOrderService.issueCardOrder(registerEntryId, person, license, document, salt, issuedByAddress));

        // then
        // verify that the httpOrderService.registerOrder method is called once
        verify(httpOrderService, times(1)).registerOrder(any(), any(), eqRegisterInfo(license, person, registerEntryId, document), eq(salt), eq(issuedByAddress));

        ArgumentCaptor<CardOrder> captor = ArgumentCaptor.forClass(CardOrder.class);
        verify(cardOrderRepository, times(1)).save(captor.capture());

        CardOrder cardOrder = captor.getValue();
        assertNotNull(cardOrder);
        assertNotNull(cardOrder.getOrderId());
        assertNotNull(cardOrder.getUpdatedAt());
        assertEquals(license.getNumber(), cardOrder.getLicenseNumber());
        assertEquals(document.getDocumentId(), cardOrder.getIdentificationDocumentId());
        assertEquals(OrderStatus.NOT_TRANSMITTED, cardOrder.getStatus());
        assertNotNull(cardOrder.getStatusNote());
    }

    @Test
    @DisplayName("CardOrderService.issueCardOrder should update repository correctly when httpOrder succeeds.")
    public void testIssueCardOrderUpdatesRepositoryCorrectlyWhenHttpOrderSucceeds() {
        // given
        final UUID registerEntryId = UUID.randomUUID();
        final Person person = new Person();
        final FishingLicense license = new FishingLicense();
        license.setNumber("licenseNumber");
        final IdentificationDocument document = new IdentificationDocument();
        document.setDocumentId("documentId");
        final String salt = "salt";
        final String issuedByAddress = "issuedByAddress";

        // when
        cardOrderService.issueCardOrder(registerEntryId, person, license, document, salt, issuedByAddress);

        // then
        // verify that the httpOrderService.registerOrder method is called once
        verify(httpOrderService, times(1)).registerOrder(any(), any(), eqRegisterInfo(license, person, registerEntryId, document), eq(salt), eq(issuedByAddress));

        ArgumentCaptor<CardOrder> captor = ArgumentCaptor.forClass(CardOrder.class);
        verify(cardOrderRepository, times(2)).save(captor.capture());

        CardOrder cardOrder = captor.getValue();
        assertNotNull(cardOrder);
        assertNotNull(cardOrder.getOrderId());
        assertNotNull(cardOrder.getUpdatedAt());
        assertEquals(license.getNumber(), cardOrder.getLicenseNumber());
        assertEquals(document.getDocumentId(), cardOrder.getIdentificationDocumentId());
        assertEquals(OrderStatus.TRANSMITTED, cardOrder.getStatus());
        assertNotNull(cardOrder.getStatusNote());
    }

    @Test
    @DisplayName("CardOrderService.updateCardOrderStatus should update the card order status correctly.")
    public void testUpdateCardOrderStatusSetsInformationCorrectly() {
        // given
        CardOrder initialOrder = new CardOrder();
        initialOrder.setOrderId(UUID.randomUUID());
        initialOrder.setStatus(OrderStatus.TRANSMITTED);
        cardOrderRepository.save(initialOrder);

        // when
        cardOrderService.updateCardOrderStatus(initialOrder.getOrderId(), OrderStatus.PRODUCED, "statusNote");

        // Then
        CardOrder updatedOrder = cardOrderRepository.findById(initialOrder.getOrderId()).orElse(null);

        assertNotNull(updatedOrder);
        assertEquals(OrderStatus.PRODUCED, updatedOrder.getStatus());
        assertEquals("statusNote", updatedOrder.getStatusNote());
    }

    private RegisterOrderInformation eqRegisterInfo(FishingLicense license, Person person, UUID registerEntryId, IdentificationDocument document) {
        return ArgumentMatchers.assertArg(information -> {
            assertEquals(license, information.fishingLicense());
            assertEquals(person, information.person());
            assertEquals(registerEntryId, information.registerId());
            assertEquals(document, information.identificationDocument());
        });
    }
}
