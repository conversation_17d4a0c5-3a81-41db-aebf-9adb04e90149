<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd"
                   context="card_order">

    <!-- ChangeSet for CardOrders Table -->
    <changeSet id="1.0.0" author="bjarne.wittlieb">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="card_order"/>
            </not>
        </preConditions>
        <createTable tableName="card_order">
            <column name="order_id" type="uuid">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="license_number" type="varchar(255)">
                <constraints nullable="false"/>
            </column>

            <column name="identification_document_id" type="varchar(255)">
                <constraints nullable="false" unique="true"/>
            </column>


            <column name="status" type="varchar(255)" defaultValue="NOT_TRANSMITTED">
                <constraints nullable="false"/>
            </column>

            <column name="status_note" type="varchar(255)" defaultValue="">
                <constraints nullable="false"/>
            </column>

            <column name="updated_at" type="timestamp">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="1.0.0-index" author="bjarne.wittlieb">
        <preConditions onFail="MARK_RAN">
            <not>
                <indexExists indexName="idx_card_order_license_number" tableName="card_order"/>
            </not>
        </preConditions>
        <createIndex indexName="idx_card_order_license_number" tableName="card_order">
            <column name="license_number"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>
