package de.adesso.fischereiregister.card_orders.persistence;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Repository
public interface CardOrderRepository extends CrudRepository<CardOrder, UUID> {
    List<CardOrder> findByLicenseNumber(String licenseNumber);

    @Query("SELECT co.orderId FROM CardOrder co WHERE (co.status = 'SHIPPED' OR co.status = 'UNPRODUCEABLE' OR co.status = 'UNDELIVERABLE') AND co.updatedAt > :threshold")
    List<UUID> findCardOrdersMarkedForDeletion(@Param("threshold") LocalDateTime threshold);
}
