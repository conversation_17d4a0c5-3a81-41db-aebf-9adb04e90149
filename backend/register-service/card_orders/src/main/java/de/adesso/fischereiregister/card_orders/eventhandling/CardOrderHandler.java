package de.adesso.fischereiregister.card_orders.eventhandling;

import de.adesso.fischereiregister.card_orders.services.CardOrderService;
import de.adesso.fischereiregister.core.events.LimitedLicenseCreatedEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseCreatedEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseDigitizedEvent;
import de.adesso.fischereiregister.core.events.ReplacementCardOrderedEvent;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.type.IdentificationDocumentType;
import lombok.AllArgsConstructor;
import org.axonframework.eventhandling.EventHandler;
import org.axonframework.eventhandling.ReplayStatus;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AllArgsConstructor
public class CardOrderHandler {

    private final CardOrderService cardOrderService;

    @EventHandler()
    public void on(RegularLicenseCreatedEvent event, ReplayStatus replayStatus) {
        // Check if we are in a replay
        if (replayStatus.isReplay()) {
            // Skip placing the order if this is a replay
            return;
        }

        cardOrderService.issueCardOrder(event.registerId(), event.person(), event.fishingLicense(), getCardDocument(event.identificationDocuments()), event.salt(), event.issuedByAddress());
    }

    @EventHandler()
    public void on(LimitedLicenseCreatedEvent event, ReplayStatus replayStatus) {
        // Check if we are in a replay
        if (replayStatus.isReplay()) {
            // Skip placing the order if this is a replay
            return;
        }

        cardOrderService.issueCardOrder(event.registerId(), event.person(), event.fishingLicense(), getCardDocument(event.identificationDocuments()), event.salt(), event.issuedByAddress());
    }

    @EventHandler()
    public void on(ReplacementCardOrderedEvent event, ReplayStatus replayStatus) {
        // Check if we are in a replay
        if (replayStatus.isReplay()) {
            // Skip placing the order if this is a replay
            return;
        }

        cardOrderService.issueCardOrder(event.registerId(), event.person(), event.fishingLicense(), getCardDocument(event.identificationDocuments()), event.salt(), event.issuedByAddress());
    }

    @EventHandler()
    public void on(RegularLicenseDigitizedEvent event, ReplayStatus replayStatus) {
        // Check if we are in a replay
        if (replayStatus.isReplay()) {
            // Skip placing the order if this is a replay
            return;
        }

        cardOrderService.issueCardOrder(event.registerId(), event.person(), event.fishingLicense(), getCardDocument(event.identificationDocuments()), event.salt(), event.issuedByAddress());
    }

    private static IdentificationDocument getCardDocument(List<IdentificationDocument> identificationDocuments) {
        return identificationDocuments.stream()
                .filter(doc -> IdentificationDocumentType.CARD == doc.getType())
                .findFirst()
                .orElseThrow(() -> new IllegalStateException("No document with type 'CARD' found."));
    }

}
