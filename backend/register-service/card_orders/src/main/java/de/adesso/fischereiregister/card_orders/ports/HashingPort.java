package de.adesso.fischereiregister.card_orders.ports;

import de.adesso.fischereiregister.core.model.Person;

import java.util.UUID;

public interface HashingPort {
    String calculateHash(UUID registerEntryId, Person person, String salt);

    /**
     * Generates QR-Code or NFC for fishingLicenses data that should be printed on documents.
     *
     * @param registerEntryId ID of the register entry
     * @param licenseNumber   license number
     * @param person          person of the register entry
     * @param documentId      document id
     * @param salt            salt connected to the document
     * @return data that is encoded in the QR-Code or NFC to scan and verify the document
     */
    String getQROrNFCDataForLicense(UUID registerEntryId, String licenseNumber, Person person, String documentId, String salt);

    /**
     * Generates QR-Code or NFC for taxes data that should be printed on documents.
     *
     * @param registerEntryId ID of the register entry
     * @param person          person of the register entry
     * @param documentId      document id
     * @param salt            salt connected to the document
     * @return data that is encoded in the QR-Code or NFC to scan and verify the document
     */
    String getQROrNFCDataForTax(UUID registerEntryId, Person person, String documentId, String salt);
}
