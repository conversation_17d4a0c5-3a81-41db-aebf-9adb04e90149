package de.adesso.fischereiregister.card_orders.services;

import de.adesso.fischereiregister.card_orders.config.CardOrderConfig;
import de.adesso.fischereiregister.card_orders.persistence.CardOrderRepository;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.UUID;

@Service
@AllArgsConstructor
@Slf4j
public class CardOrderDeletionScheduledTask {
    private static final String NIGHTLY = "0 0 0 * * *";

    private final CardOrderRepository cardOrderRepository;
    private final CardOrderConfig cardOrderConfig;

    @Scheduled(cron = NIGHTLY)
    public void deleteFinishedOrders() {
        log.info("Starting removal of completed orders...");

        final int daysUtilDeletion = cardOrderConfig.getDaysUntilDeletion();
        final LocalDateTime threshold = LocalDateTime.now().plusDays(daysUtilDeletion);

        log.info("Removing all orders with status 'SHIPPED', 'UNPRODUCEABLE' or 'UNDELIVERABLE' that are after {}", threshold);

        cardOrderRepository.findCardOrdersMarkedForDeletion(threshold).forEach(this::deleteOrder);

        log.info("... removal of completed orders completed");
    }

    private void deleteOrder(UUID orderId) {
        cardOrderRepository.deleteById(orderId);
    }

}
