package de.adesso.fischereiregister.card_orders.services;

import de.adesso.fischereiregister.core.model.type.FederalState;

public record OrderRequest(
        String orderId,
        String fishingLicenseId,
        String identificationDocumentId,
        OrderRequestLicenseType licenseType,
        String issueDate,
        String expirationDate,
        FederalState federalStateAbbr,
        String issuedByAddress,
        String title,
        String givenNames,
        String surname,
        String birthdate,
        String birthplace,
        String street,
        String streetnumber,
        String zipcode,
        String city,
        String template,
        String payload) {
}
