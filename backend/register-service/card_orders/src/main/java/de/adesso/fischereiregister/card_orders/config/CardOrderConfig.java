package de.adesso.fischereiregister.card_orders.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;

@Getter
@Configuration
public class CardOrderConfig {
    @Value("${order.base_urn}")
    private String baseUrl;

    @Value("${order.endpoint_path}")
    private String endpointPath;

    @Value("${order.template_version}")
    private String templateVersion;

    @Value("${order.days_until_deletion}")
    private int daysUntilDeletion;

    @Value("${order.enabled}")
    private boolean enabled;

    @Bean(name = "orderWebClient")
    public WebClient webClient() {
        return WebClient.builder().build();
    }
}
