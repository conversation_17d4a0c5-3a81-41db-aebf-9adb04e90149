package de.adesso.fischereiregister.card_orders.services;

import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Person;

import java.util.UUID;

public record RegisterOrderInformation(FishingLicense fishingLicense,
                                       Person person,
                                       UUID registerId,
                                       IdentificationDocument identificationDocument) {
}
