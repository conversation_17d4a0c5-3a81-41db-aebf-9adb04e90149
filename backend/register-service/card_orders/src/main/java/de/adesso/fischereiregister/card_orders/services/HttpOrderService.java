package de.adesso.fischereiregister.card_orders.services;

import java.time.LocalDate;
import java.util.UUID;

public interface HttpOrderService {

    // String issuedByAdress this is not all information needed??
    // template ? Bezeichnung des zu verwendenen Layouts
    // payload = Inhalt des NFC-Chips/QR-Codes (Text der auf den NFC-Chip zu schreiben / in den QR-Code umzuwandeln ist)
    void registerOrder(UUID orderId,
                       LocalDate issueDate,
                       RegisterOrderInformation registerOrderInformation,
                       String salt,
                       String issuedByOfficeAddress);

}
